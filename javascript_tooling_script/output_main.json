{"name": "filegroups", "type": "object", "title": "File groups", "properties": [{"name": "subscription_agreements", "type": "array", "title": "subscription agreements", "items": {"type": "string", "enum": ["file1", "file2"]}}, {"name": "application_form", "type": "array", "title": "application form", "items": {"type": "string", "enum": ["file1", "file2", "file3", "file4", "file5", "file6", "file7"]}}, {"name": "subscription_agreements_lux", "type": "array", "title": "subscription agreements lux", "items": {"type": "string", "enum": ["file2"]}}, {"name": "kyc_on_investor_fi_equivalent_sdd", "type": "array", "title": "kyc on investor fi equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_fi_equivalent_cdd", "type": "array", "title": "kyc on investor fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fi_equivalent_edd", "type": "array", "title": "kyc on investor fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fi_nonequiv_sdd", "type": "array", "title": "kyc on investor fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_fi_nonequiv_cdd", "type": "array", "title": "kyc on investor fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fi_nonequiv_edd", "type": "array", "title": "kyc on investor fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fi_listedequivmk_sdd", "type": "array", "title": "kyc on investor fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyc_on_investor_fi_listedequivmk_cdd", "type": "array", "title": "kyc on investor fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_investor_fi_listedequivmk_edd", "type": "array", "title": "kyc investor fi listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_on_investor_fi_listednonequivmk_sdd", "type": "array", "title": "kyc on investor fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_fi_listednonequivmk_cdd", "type": "array", "title": "kyc on investor fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fi_listednonequivmk_edd", "type": "array", "title": "kyc on investor fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_shareholders_fi_equivalent_sdd", "type": "array", "title": "kyc shareholders fi equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_shareholders_fi_equivalent_cdd", "type": "array", "title": "kyc shareholders fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholders_fi_equivalent_edd", "type": "array", "title": "kyc shareholders fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholders_fi_nonequiv_sdd", "type": "array", "title": "kyc shareholders fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_shareholders_fi_nonequiv_cdd", "type": "array", "title": "kyc shareholders fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholders_fi_nonequiv_edd", "type": "array", "title": "kyc shareholders fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_fi_listednonequivmk_sdd", "type": "array", "title": "kyc shareholder fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_shareholder_fi_listednonequivmk_cdd", "type": "array", "title": "kyc shareholder fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_fi_listednonequivmk_edd", "type": "array", "title": "kyc shareholder fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_fi_equivalent_sdd", "type": "array", "title": "kyc directors fi equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_equivalent_cdd", "type": "array", "title": "kyc directors fi equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_equivalent_edd", "type": "array", "title": "kyc directors fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_fi_nonequiv_sdd", "type": "array", "title": "kyc directors fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_nonequiv_cdd", "type": "array", "title": "kyc directors fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_fi_nonequiv_edd", "type": "array", "title": "kyc directors fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_fi_listedequivmk_sdd", "type": "array", "title": "kyc directors fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_listedequivmk_cdd", "type": "array", "title": "kyc directors fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_listedequivmk_edd", "type": "array", "title": "kyc directors fi listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_fi_listednonequivmk_sdd", "type": "array", "title": "kyc directors fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fi_listednonequivmk_cdd", "type": "array", "title": "kyc directors fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_fi_listednonequivmk_edd", "type": "array", "title": "kyc directors fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_authorized_sig_fi_equivalent_sdd", "type": "array", "title": "kyc authorized sig fi equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_authorized_sig_fi_equivalent_cdd", "type": "array", "title": "kyc authorized sig fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_authorize_sig_fi_equivalent_edd", "type": "array", "title": "kyc authorize sig fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_authorize_sig_fi_nonequiv_sdd", "type": "array", "title": "kyc authorize sig fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_authorize_sig_fi_nonequiv_cdd", "type": "array", "title": "kyc authorize sig fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_authorize_sig_fi_nonequiv_edd", "type": "array", "title": "kyc authorize sig fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_fi_listedequivmk_sdd", "type": "array", "title": "kyc auth sig fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fi_listedequivmk_cdd", "type": "array", "title": "kyc auth sig fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fi_listedequivmk_edd", "type": "array", "title": "kyc auth sig fi listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_fi_listednonequivmk_sdd", "type": "array", "title": "kyc auth sig fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fi_listednonequivmk_cdd", "type": "array", "title": "kyc auth sig fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fi_listednonequivmk_edd", "type": "array", "title": "kyc auth sig fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_ubo_fi_equivalent_sdd", "type": "array", "title": "kyc ubo fi equivalent sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kyc_ubo_fi_equivalent_cdd", "type": "array", "title": "kyc ubo fi equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fi_equivalent_edd", "type": "array", "title": "kyc ubo fi equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fi_nonequiv_sdd", "type": "array", "title": "kyc ubo fi nonequiv sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kyc_ubo_fi_nonequiv_cdd", "type": "array", "title": "kyc ubo fi nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fi_nonequiv_edd", "type": "array", "title": "kyc ubo fi nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fi_listednonequivmk_sdd", "type": "array", "title": "kyc ubo fi listednonequivmk sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kyc_ubo_fi_listednonequivmk_cdd", "type": "array", "title": "kyc ubo fi listednonequivmk cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fi_listednonequivmk_edd", "type": "array", "title": "kyc ubo fi listednonequivmk edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "acting_behalf_fi_equivalent_sdd", "type": "array", "title": "acting behalf fi equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_equivalent_cdd", "type": "array", "title": "acting behalf fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_equivalent_edd", "type": "array", "title": "acting behalf fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_nonequiv_sdd", "type": "array", "title": "acting behalf fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_nonequiv_cdd", "type": "array", "title": "acting behalf fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_nonequiv_edd", "type": "array", "title": "acting behalf fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_listednonequivmk_sdd", "type": "array", "title": "acting behalf fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_listednonequivmk_cdd", "type": "array", "title": "acting behalf fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_listednonequivmk_edd", "type": "array", "title": "acting behalf fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "acting_behalf_fi_listedequivmk_sdd", "type": "array", "title": "acting behalf fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "acting_behalf_fi_listedequivmk_cdd", "type": "array", "title": "acting behalf fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "acting_behalf_fi_listedequivmk_edd", "type": "array", "title": "acting behalf fi listedequivmk edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_docs_fi_equivalent_sdd", "type": "array", "title": "additional docs fi equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_equivalent_cdd", "type": "array", "title": "additional docs fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_equivalent_edd", "type": "array", "title": "additional docs fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_nonequiv_sdd", "type": "array", "title": "additional docs fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_nonequiv_cdd", "type": "array", "title": "additional docs fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_nonequiv_edd", "type": "array", "title": "additional docs fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listedequivmk_sdd", "type": "array", "title": "additional docs fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listedequivmk_cdd", "type": "array", "title": "additional docs fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listedequivmk_higeddis", "type": "array", "title": "additional docs fi listedequivmk higeddis", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listednonequivmk_sdd", "type": "array", "title": "additional docs fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listednonequivmk_cdd", "type": "array", "title": "additional docs fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fi_listednonequivmk_edd", "type": "array", "title": "additional docs fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_equivalent_sdd", "type": "array", "title": "docs behalf fi equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_equivalent_cdd", "type": "array", "title": "docs behalf fi equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_equivalent_edd", "type": "array", "title": "docs behalf fi equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_nonequiv_sdd", "type": "array", "title": "docs behalf fi nonequiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_nonequiv_cdd", "type": "array", "title": "docs behalf fi nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_nonequiv_edd", "type": "array", "title": "docs behalf fi nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listedequivmk_sdd", "type": "array", "title": "docs behalf fi listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listedequivmk_cdd", "type": "array", "title": "docs behalf fi listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listedequivmk_edd", "type": "array", "title": "docs behalf fi listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listednonequivmk_sdd", "type": "array", "title": "docs behalf fi listednonequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listednonequivmk_cdd", "type": "array", "title": "docs behalf fi listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "docs_behalf_fi_listednonequivmk_edd", "type": "array", "title": "docs behalf fi listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_on_investor_cc_listedequivmk_sdd", "type": "array", "title": "kyc on investor cc listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyc_on_investor_cc_listedequivmk_cdd", "type": "array", "title": "kyc on investor cc listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_on_investor_cc_listedequivmk_edd", "type": "array", "title": "kyc on investor cc listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_on_investor_cc_listednonequivmk_cdd", "type": "array", "title": "kyc on investor cc listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_cc_listednonequivmk_edd", "type": "array", "title": "kyc on investor cc listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_cc_notlisted_cdd", "type": "array", "title": "kyc on investor cc notlisted cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_cc_notlisted_edd", "type": "array", "title": "kyc on investor cc notlisted edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_shareholder_cc_listednonequivmk_cdd", "type": "array", "title": "kyc shareholder cc listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_cc_listednonequivmk_edd", "type": "array", "title": "kyc shareholder cc listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_cc_notlisted_cdd", "type": "array", "title": "kyc shareholder cc notlisted cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_cc_notlisted_edd", "type": "array", "title": "kyc shareholder cc notlisted edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_cc_listedequivmk_cdd", "type": "array", "title": "kyc directors cc listedequivmk cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_cc_listedequivmk_edd", "type": "array", "title": "kyc directors cc listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_cc_listednonequivmk_cdd", "type": "array", "title": "kyc directors cc listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_cc_listednonequivmk_edd", "type": "array", "title": "kyc directors cc listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_cc_notlisted_cdd", "type": "array", "title": "kyc directors cc notlisted cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_cc_notlisted_edd", "type": "array", "title": "kyc directors cc notlisted edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_cc_listedequivmk_sdd", "type": "array", "title": "kyc auth sig cc listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_cc_listedequivmk_cdd", "type": "array", "title": "kyc auth sig cc listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_cc_listedequivmk_edd", "type": "array", "title": "kyc auth sig cc listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_cc_listednonequivmk_cdd", "type": "array", "title": "kyc auth sig cc listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_cc_listednonequivmk_edd", "type": "array", "title": "kyc auth sig cc listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_cc_notlisted_cdd", "type": "array", "title": "kyc auth sig cc notlisted cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_cc_notlisted_edd", "type": "array", "title": "kyc auth sig cc notlisted edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_ubo_cc_listednonequivmk_cdd", "type": "array", "title": "kyc ubo cc listednonequivmk cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_cc_listednonequivmk_edd", "type": "array", "title": "kyc ubo cc listednonequivmk edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_cc_notlisted_cdd", "type": "array", "title": "kyc ubo cc notlisted cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_cc_notlisted_edd", "type": "array", "title": "kyc ubo cc notlisted edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_docs_cc_listedequivmk_sdd", "type": "array", "title": "additional docs cc listedequivmk sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_cc_listedequivmk_cdd", "type": "array", "title": "additional docs cc listedequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_cc_listedequivmk_edd", "type": "array", "title": "addi docs cc listedequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_cc_listednonequivmk_cdd", "type": "array", "title": "additional docs cc listednonequivmk cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_cc_listednonequivmk_edd", "type": "array", "title": "additional docs cc listednonequivmk edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_cc_notlisted_cdd", "type": "array", "title": "additional docs cc notlisted cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_cc_notlisted_edd", "type": "array", "title": "additional docs cc notlisted edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_on_investor_fund_equivalent_sdd", "type": "array", "title": "kyc on investor fund equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_on_investor_fund_equivalent_cdd", "type": "array", "title": "kyc on investor fund equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fund_equivalent_edd", "type": "array", "title": "kyc on investor fund equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fund_nonequiv_cdd", "type": "array", "title": "kyc on investor fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_on_investor_fund_nonequiv_edd", "type": "array", "title": "kyc on investor fund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kyc_mc_fund_equivalent_sdd", "type": "array", "title": "kyc mc fund equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_mc_fund_equivalent_cdd", "type": "array", "title": "kyc mc fund equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_mc_fund_equivalent_edd", "type": "array", "title": "kyc mc fund equivalent edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_mc_fund_nonequiv_cdd", "type": "array", "title": "kyc mc fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_mc_fund_nonequiv_edd", "type": "array", "title": "kyc mc fund nonequiv edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_shareholder_fund_equivalent_sdd", "type": "array", "title": "kyc shareholder fund equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_fund_equivalent_cdd", "type": "array", "title": "kyc shareholder fund equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_shareholder_fund_equivalent_edd", "type": "array", "title": "kyc shareholder fund equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_shareholder_fund_nonequiv_cdd", "type": "array", "title": "kyc shareholder fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5"]}}, {"name": "kyc_shareholder_fund_nonequiv_edd", "type": "array", "title": "kyc shareholder fund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5"]}}, {"name": "kyc_directors_fund_equivalent_sdd", "type": "array", "title": "kyc directors fund equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fund_equivalent_cdd", "type": "array", "title": "kyc directors fund equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_fund_equivalent_edd", "type": "array", "title": "kyc directors fund equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_fund_nonequiv_cdd", "type": "array", "title": "kyc directors fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_fund_nonequiv_edd", "type": "array", "title": "kyc directors fund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_fund_equivalent_sdd", "type": "array", "title": "kyc auth sig fund equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fund_equivalent_cdd", "type": "array", "title": "kyc auth sig fund equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fund_equivalent_edd", "type": "array", "title": "kyc auth sig fund equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_fund_nonequiv_cdd", "type": "array", "title": "kyc auth sig fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_fund_nonequiv_edd", "type": "array", "title": "kyc auth sig fund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_ubo_fund_equivalent_sdd", "type": "array", "title": "kyc ubo fund equivalent sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kyc_ubo_fund_equivalent_cdd", "type": "array", "title": "kyc ubo fund equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fund_equivalent_edd", "type": "array", "title": "kyc ubo fund equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_fund_nonequiv_cdd", "type": "array", "title": "kyc ubo fund nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kyc_ubo_fund_nonequiv_edd", "type": "array", "title": "kyc ubo fund nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_docs_fund_equivalent_sdd", "type": "array", "title": "additional docs fund equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fund_equivalent_cdd", "type": "array", "title": "additional docs fund equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fund_equivalent_edd", "type": "array", "title": "additional docs fund equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fund_nonequiv_cdd", "type": "array", "title": "additional docs fund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_fund_nonequiv_edd", "type": "array", "title": "additional docs fund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_investor_pensionfund_equiv_sdd", "type": "array", "title": "kyc investor pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyc_investor_pensionfund_equiv_cdd", "type": "array", "title": "kyc investor pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_investor_pensionfund_equiv_edd", "type": "array", "title": "kyc investor pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_investor_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc investor pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_investor_pensionfund_nonequiv_edd", "type": "array", "title": "kyc investor pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kyc_pm_pensionfund_equiv_sdd", "type": "array", "title": "kyc pm pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_pm_pensionfund_equiv_cdd", "type": "array", "title": "kyc pm pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_pm_pensionfund_equiv_edd", "type": "array", "title": "kyc pm pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_pm_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc pm pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_pm_pensionfund_nonequiv_edd", "type": "array", "title": "kyc pm pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_shareholder_pensionfund_equiv_sdd", "type": "array", "title": "kyc shareholder pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_pensionfund_equiv_cdd", "type": "array", "title": "kyc shareholder pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_shareholder_pensionfund_equiv_edd", "type": "array", "title": "kyc shareholder pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_share_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc share pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_share_pensionfund_nonequiv_edd", "type": "array", "title": "kyc share pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_pensionfund_equiv_sdd", "type": "array", "title": "kyc directors pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_pensionfund_equiv_cdd", "type": "array", "title": "kyc directors pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_pensionfund_equiv_edd", "type": "array", "title": "kyc directors pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc directors pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_pensionfund_nonequiv_edd", "type": "array", "title": "kyc directors pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_pensionfund_equiv_sdd", "type": "array", "title": "kyc auth sig pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_pensionfund_equiv_cdd", "type": "array", "title": "kyc auth sig pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_pensionfund_equiv_edd", "type": "array", "title": "kyc auth sig pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc auth sig pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_pensionfund_nonequiv_edd", "type": "array", "title": "kyc auth sig pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_ubo_pensionfund_equiv_sdd", "type": "array", "title": "kyc ubo pensionfund equiv sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_pensionfund_equiv_cdd", "type": "array", "title": "kyc ubo pensionfund equiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_pensionfund_equiv_edd", "type": "array", "title": "kyc ubo pensionfund equiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_pensionfund_nonequiv_cdd", "type": "array", "title": "kyc ubo pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_pensionfund_nonequiv_edd", "type": "array", "title": "kyc ubo pensionfund nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_docs_pensionfund_equiv_sdd", "type": "array", "title": "additional docs pensionfund equiv sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_pensionfund_equiv_cdd", "type": "array", "title": "additional docs pensionfund equiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_docs_pensionfund_equiv_edd", "type": "array", "title": "additional docs pensionfund equiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_pensionfund_nonequiv_cdd", "type": "array", "title": "addi docs pensionfund nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_pensionfund_nonequiv_edd", "type": "array", "title": "addi docs pensionfund nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_investor_stateowned_equivalent_sdd", "type": "array", "title": "kyc investor stateowned equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyc_investor_stateowned_equivalent_cdd", "type": "array", "title": "kyc investor stateowned equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_investor_stateowned_equivalent_edd", "type": "array", "title": "kyc investor stateowned equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_investor_stateowned_nonequiv_cdd", "type": "array", "title": "kyc investor stateowned nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_investor_stateowned_nonequiv_edd", "type": "array", "title": "kyc investor stateowned nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyc_share_stateowned_equivalent_sdd", "type": "array", "title": "kyc share stateowned equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_share_stateowned_equivalent_cdd", "type": "array", "title": "kyc share stateowned equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_share_stateowned_equivalent_edd", "type": "array", "title": "kyc share stateowned equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_share_stateowned_nonequiv_cdd", "type": "array", "title": "kyc share stateowned nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_share_stateowned_nonequiv_edd", "type": "array", "title": "kyc share stateowned nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kyc_directors_stateowned_equivalent_sdd", "type": "array", "title": "kyc directors stateowned equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_stateowned_equivalent_cdd", "type": "array", "title": "kyc directors stateowned equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kyc_directors_stateowned_equivalent_edd", "type": "array", "title": "kyc directors stateowned equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_directors_stateowned_nonequiv_cdd", "type": "array", "title": "kyc directors stateowned nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_directors_stateowned_nonequiv_edd", "type": "array", "title": "kyc directors stateowned nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_stateowned_equivalent_sdd", "type": "array", "title": "kyc auth sig stateowned equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_stateowned_equivalent_cdd", "type": "array", "title": "kyc auth sig stateowned equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_stateowned_equivalent_edd", "type": "array", "title": "kyc auth sig stateowned equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_auth_sig_stateowned_nonequiv_cdd", "type": "array", "title": "kyc auth sig stateowned nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyc_auth_sig_stateowned_nonequiv_edd", "type": "array", "title": "kyc auth sig stateowned nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyc_ubo_stateowned_equivalent_sdd", "type": "array", "title": "kyc ubo stateowned equivalent sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_stateowned_equivalent_cdd", "type": "array", "title": "kyc ubo stateowned equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_stateowned_equivalent_edd", "type": "array", "title": "kyc ubo stateowned equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_stateowned_nonequiv_cdd", "type": "array", "title": "kyc ubo stateowned nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kyc_ubo_stateowned_nonequiv_edd", "type": "array", "title": "kyc ubo stateowned nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "addi_docs_stateowned_equivalent_sdd", "type": "array", "title": "addi docs stateowned equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_stateowned_equivalent_cdd", "type": "array", "title": "addi docs stateowned equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_stateowned_equivalent_edd", "type": "array", "title": "addi docs stateowned equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_stateowned_nonequiv_cdd", "type": "array", "title": "addi docs stateowned nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_docs_stateowned_nonequiv_edd", "type": "array", "title": "addi docs stateowned nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycinvestor_publicsupnat_equivalent_sdd", "type": "array", "title": "kycinvestor publicsupnat equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6"]}}, {"name": "kycinvestor_publicsupnat_equivalent_cdd", "type": "array", "title": "kycinvestor publicsupnat equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycinvestor_publicsupnat_equivalent_edd", "type": "array", "title": "kycinvestor publicsupnat equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycinvestor_publicsupnat_nonequiv_cdd", "type": "array", "title": "kycinvestor publicsupnat nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycinvestor_publicsupnat_nonequiv_edd", "type": "array", "title": "kycinvestor publicsupnat nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycauthorised_publicsupnat_equivalent_sdd", "type": "array", "title": "kycauthorised publicsupnat equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_publicsupnat_equivalent_cdd", "type": "array", "title": "kycauthorised publicsupnat equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_publicsupnat_equivalent_edd", "type": "array", "title": "kycauthorised publicsupnat equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_publicsupnat_nonequiv_cdd", "type": "array", "title": "kycauthorised publicsupnat nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_publicsupnat_nonequiv_edd", "type": "array", "title": "kycauthorised publicsupnat nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_publicsupnat_equivalent_sdd", "type": "array", "title": "kycdirectors publicsupnat equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycdirectors_publicsupnat_equivalent_cdd", "type": "array", "title": "kycdirectors publicsupnat equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycdirectors_publicsupnat_equivalent_edd", "type": "array", "title": "kycdirectors publicsupnat equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_publicsupnat_nonequiv_cdd", "type": "array", "title": "kycdirectors publicsupnat nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycdirectors__publicsupnat_nonequiv_edd", "type": "array", "title": "kycdirectors  publicsupnat nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycubo_publicsupnat_equivalent_sdd", "type": "array", "title": "kycubo publicsupnat equivalent sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_publicsupnat_equivalent_cdd", "type": "array", "title": "kycubo publicsupnat equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_publicsupnat_equivalent_edd", "type": "array", "title": "kycubo publicsupnat equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_publicsupnat_nonequiv_cdd", "type": "array", "title": "kycubo publicsupnat nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_publicsupnat_nonequiv_edd", "type": "array", "title": "kycubo publicsupnat nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_publicsupnat_equivalent_sdd", "type": "array", "title": "additional publicsupnat equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_publicsupnat_equivalent_cdd", "type": "array", "title": "additional publicsupnat equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_publicsupnat_equivalent_edd", "type": "array", "title": "additional publicsupnat equivalent edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_publicsupnat_nonequiv_cdd", "type": "array", "title": "additional publicsupnat nonequiv cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_publicsupnat_nonequiv_edd", "type": "array", "title": "additional publicsupnat nonequiv edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycinvestor_lp_equivalent_sdd", "type": "array", "title": "kycinvestor lp equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10"]}}, {"name": "kycinvestor_lp_equivalent_cdd", "type": "array", "title": "kycinvestor lp equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kycinvestor_lp_equivalent_edd", "type": "array", "title": "kycinvestor lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kycinvestor_lp_nonequiv_cdd", "type": "array", "title": "kycinvestor lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kycinvestor_lp_nonequiv_edd", "type": "array", "title": "kycinvestor lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11"]}}, {"name": "kycshareholders_lp_equivalent_sdd", "type": "array", "title": "kycshareholders lp equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5"]}}, {"name": "kycshareholders_lp_equivalent_cdd", "type": "array", "title": "kycshareholders lp equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycshareholders_lp_equivalent_edd", "type": "array", "title": "kycshareholders lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycshareholders_lp_nonequiv_cdd", "type": "array", "title": "kycshareholders lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycshareholders_lp_nonequiv_edd", "type": "array", "title": "kycshareholders lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycgp_lp_equivalent_sdd", "type": "array", "title": "kycgp lp equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycgp_lp_equivalent_cdd", "type": "array", "title": "kycgp lp equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycgp_lp_equivalent_edd", "type": "array", "title": "kycgp lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycgp_lp_nonequiv_cdd", "type": "array", "title": "kycgp lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycgp_lp_nonequiv_edd", "type": "array", "title": "kycgp lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_lp_equivalent_sdd", "type": "array", "title": "kycdirectors lp equivalent sdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycdirectors_lp_equivalent_cdd", "type": "array", "title": "kycdirectors lp equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycdirectors_lp_equivalent_edd", "type": "array", "title": "kycdirectors lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_lp_nonequiv_cdd", "type": "array", "title": "kycdirectors lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycdirectors_lp_nonequiv_edd", "type": "array", "title": "kycdirectors lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_lp_equivalent_sdd", "type": "array", "title": "kycauthorised lp equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_lp_equivalent_cdd", "type": "array", "title": "kycauthorised lp equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_lp_equivalent_edd", "type": "array", "title": "kycauthorised lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_lp_nonequiv_cdd", "type": "array", "title": "kycauthorised lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycauthorised_lp_nonequiv_edd", "type": "array", "title": "kycauthorised lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycubo_lp_equivalent_sdd", "type": "array", "title": "kycubo lp equivalent sdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10"]}}, {"name": "kycubo_lp_equivalent_cdd", "type": "array", "title": "kycubo lp equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_lp_equivalent_edd", "type": "array", "title": "kycubo lp equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_lp_nonequiv_cdd", "type": "array", "title": "kycubo lp nonequiv cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_lp_nonequiv_edd", "type": "array", "title": "kycubo lp nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_lp_equivalent_sdd", "type": "array", "title": "additional lp equivalent sdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_lp_equivalent_cdd", "type": "array", "title": "additional lp equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_lp_equivalent_edd", "type": "array", "title": "additional lp equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_lp_nonequiv_cdd", "type": "array", "title": "additional lp nonequiv cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_lp_nonequiv_edd", "type": "array", "title": "additional lp nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyctrust_equivalent_cdd", "type": "array", "title": "kyctrust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyctrust_trust_equivalent_edd", "type": "array", "title": "kyctrust trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kyctrust_trust_nonequiv_edd", "type": "array", "title": "kyctrust trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8"]}}, {"name": "kycdirectors_trust_equivalent_cdd", "type": "array", "title": "kycdirectors trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_trust_equivalent_edd", "type": "array", "title": "kycdirectors trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_trust_nonequiv_edd", "type": "array", "title": "kycdirectors trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_trust_equivalent_cdd", "type": "array", "title": "kycauthorised trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_trust_equivalent_edd", "type": "array", "title": "kycauthorised trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_trust_nonequiv_edd", "type": "array", "title": "kycauthorised trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycubo_trust_equivalent_cdd", "type": "array", "title": "kycubo trust equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10"]}}, {"name": "kycubo_trust_equivalent_edd", "type": "array", "title": "kycubo trust equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10"]}}, {"name": "kycubo_trust_nonequiv_edd", "type": "array", "title": "kycubo trust nonequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10"]}}, {"name": "kyct<PERSON>ee_trust_equivalent_cdd", "type": "array", "title": "kyctrustee trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyct<PERSON>ee_trust_equivalent_edd", "type": "array", "title": "kyctrustee trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "ky<PERSON><PERSON><PERSON>_trust_nonequiv_edd", "type": "array", "title": "kyctrustee trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyctrusteelegal_trust_equivalent_cdd", "type": "array", "title": "kyctrusteelegal trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyctrusteelegal_trust_equivalent_edd", "type": "array", "title": "kyctrusteelegal trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyct<PERSON>eelegal_trust_nonequiv_edd", "type": "array", "title": "kyctrusteelegal trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyctrusteeshares_trust_equivalent_cdd", "type": "array", "title": "kyctrusteeshares trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyctrusteeshares_trust_equivalent_edd", "type": "array", "title": "kyctrusteeshares trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyctrusteeshares_trust_nonequiv_edd", "type": "array", "title": "kyctrusteeshares trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kyctrusteeubo_trust_equivalent_cdd", "type": "array", "title": "kyctrusteeubo trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kyct<PERSON><PERSON>ubo_trust_equivalent_edd", "type": "array", "title": "kyctrusteeubo trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "ky<PERSON><PERSON><PERSON><PERSON><PERSON>_trust_nonequiv_edd", "type": "array", "title": "kyctrusteeubo trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycsettlor_trust_equivalent_cdd", "type": "array", "title": "kycsettlor trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kycsettlor_trust_equivalent_edd", "type": "array", "title": "kycsettlor trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kycsettlor_trust_nonequiv_edd", "type": "array", "title": "kycsettlor trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4"]}}, {"name": "kycprotector_trust_equivalent_cdd", "type": "array", "title": "kycprotector trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycprotector_trust_equivalent_edd", "type": "array", "title": "kycprotector trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycprotector_trust_nonequiv_edd", "type": "array", "title": "kycprotector trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycbeneficiary_trust_equivalent_cdd", "type": "array", "title": "kycbeneficiary trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6"]}}, {"name": "kycbeneficiary_trust_equivalent_edd", "type": "array", "title": "kycbeneficiary trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6"]}}, {"name": "kycbeneficiary_trust_nonequiv_edd", "type": "array", "title": "kycbeneficiary trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6"]}}, {"name": "additional_trust_equivalent_cdd", "type": "array", "title": "additional trust equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_trust_equivalent_edd", "type": "array", "title": "additional trust equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "additional_trust_nonequiv_edd", "type": "array", "title": "additional trust nonequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycinvestor_assonpo_equivalent_cdd", "type": "array", "title": "kycinvestor assonpo equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11", "file_12", "file_13"]}}, {"name": "kycinvestor_assonpo_equivalent_edd", "type": "array", "title": "kycinvestor assonpo equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11", "file_12", "file_13"]}}, {"name": "kycinvestor_assonpo_notequiv_edd", "type": "array", "title": "kycinvestor assonpo notequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9", "file_10", "file_11", "file_12", "file_13"]}}, {"name": "kycdirectors_assonpo_equivalent_cdd", "type": "array", "title": "kycdirectors assonpo equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycdirectors_assonpo_equivalent_edd", "type": "array", "title": "kycdirectors assonpo equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycdirectors_assonpo_notequiv_edd", "type": "array", "title": "kycdirectors assonpo notequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_assonpo_equivalent_cdd", "type": "array", "title": "kycauthorised assonpo equivalent cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_assonpo_equivalent_edd", "type": "array", "title": "kycauthorised assonpo equivalent edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycauthorised_assonpo_notequiv_edd", "type": "array", "title": "kycauthorised assonpo notequiv edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}, {"name": "kycubo_assonpo_equivalent_cdd", "type": "array", "title": "kycubo assonpo equivalent cdd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_assonpo_equivalent_edd", "type": "array", "title": "kycubo assonpo equivalent edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "kycubo_assonpo_notequiv_edd", "type": "array", "title": "kycubo assonpo notequiv edd", "items": {"type": "string", "enum": ["ubo_1_0", "ubo_1_1", "ubo_1_2", "ubo_1_3", "ubo_1_4", "ubo_1_5", "ubo_1_6", "ubo_1_7", "ubo_1_8", "ubo_1_9", "ubo_1_10", "ubo_12_0", "ubo_12_1", "ubo_12_2", "ubo_12_3", "ubo_12_4", "ubo_12_5", "ubo_12_6", "ubo_12_7", "ubo_12_8", "ubo_12_9", "ubo_12_10", "ubo_23_0", "ubo_23_1", "ubo_23_2", "ubo_23_3", "ubo_23_4", "ubo_23_5", "ubo_23_6", "ubo_23_7", "ubo_23_8", "ubo_23_9", "ubo_23_10", "ubo_34_0", "ubo_34_1", "ubo_34_2", "ubo_34_3", "ubo_34_4", "ubo_34_5", "ubo_34_6", "ubo_34_7", "ubo_34_8", "ubo_34_9", "ubo_34_10", "ubo_45_0", "ubo_45_1", "ubo_45_2", "ubo_45_3", "ubo_45_4", "ubo_45_5", "ubo_45_6", "ubo_45_7", "ubo_45_8", "ubo_45_9", "ubo_45_10"]}}, {"name": "additional_assonpo_equivalent_cdd", "type": "array", "title": "additional assonpo equivalent cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_assonpo_equivalent_edd", "type": "array", "title": "additional assonpo equivalent edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "additional_assonpo_notequiv_edd", "type": "array", "title": "additional assonpo notequiv edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "kycinvestor_person_cdd", "type": "array", "title": "kycinvestor person cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kycinvestor_person_edd", "type": "array", "title": "kycinvestor person edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7", "file_8", "file_9"]}}, {"name": "kyccoholder_person_cdd", "type": "array", "title": "kyccoholder person cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6"]}}, {"name": "kyccoholder_person_edd", "type": "array", "title": "kyccoholder person edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5", "file_6", "file_7"]}}, {"name": "kycguardian_person_cdd", "type": "array", "title": "kycguardian person cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycguardian_person_edd", "type": "array", "title": "kycguardian person edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycproxy_person_cdd", "type": "array", "title": "kycproxy person cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "kycproxy_person_edd", "type": "array", "title": "kycproxy person edd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addresschange_person_cdd", "type": "array", "title": "addresschange person cdd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "addresschange_person_edd", "type": "array", "title": "addresschange person edd", "items": {"type": "string", "enum": ["file_1"]}}, {"name": "death_person_cdd", "type": "array", "title": "death person cdd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5"]}}, {"name": "death_person_edd", "type": "array", "title": "death person edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3", "file_4", "file_5"]}}, {"name": "addi_person_cdd", "type": "array", "title": "addi person cdd", "items": {"type": "string", "enum": ["file_1", "file_2"]}}, {"name": "addi_person_edd", "type": "array", "title": "addi person edd", "items": {"type": "string", "enum": ["file_1", "file_2", "file_3"]}}]}