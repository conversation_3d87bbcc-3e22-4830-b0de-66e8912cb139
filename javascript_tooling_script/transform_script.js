const fs = require('fs');
const path = require('path');

// Read the blueprint file
function readBlueprintFile() {
    try {
        const blueprintPath = path.join(__dirname, 'blueprint_filegroup.json');
        const data = fs.readFileSync(blueprintPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading blueprint file:', error);
        process.exit(1);
    }
}

// Transform blueprint data to main.json format
function transformToMainFormat(blueprintData) {
    const properties = blueprintData.map(item => {
        // Extract file keys from fileItems
        const fileKeys = item.fileItems.map(fileItem => fileItem.key);
        
        return {
            name: item.key,
            type: "array",
            title: item.key.replace(/_/g, ' '),
            items: {
                type: "string",
                enum: fileKeys
            }
        };
    });

    return {
        name: "filegroups",
        type: "object",
        title: "File groups",
        properties: properties
    };
}

// Transform blueprint data to widget.json format
function transformToWidgetFormat(blueprintData) {
    const widgets = {};
    
    blueprintData.forEach(item => {
        // Create files object from fileItems
        const files = {};
        item.fileItems.forEach(fileItem => {
            files[fileItem.key] = {
                description: fileItem.name,
                helpText: fileItem.helpText,
                blueprintMetadataMapping: fileItem.key
            };
        });

        // Create widget entry
        widgets[item.key] = {
            "ui:blueprintMetadataMapping": item.key,
            "ui:supportingFileGroup": {
                description: item.name,
                helpText: item.helpText,
                files: files
            },
            "ui:widget": "FileGroup"
        };
    });

    return widgets;
}

// Write output files
function writeOutputFiles(mainData, widgetData) {
    try {
        // Write main.json
        const mainPath = path.join(__dirname, 'output_main.json');
        fs.writeFileSync(mainPath, JSON.stringify(mainData, null, 4));
        console.log('✅ Generated output_main.json');

        // Write widget.json
        const widgetPath = path.join(__dirname, 'output_widget.json');
        fs.writeFileSync(widgetPath, JSON.stringify(widgetData, null, 2));
        console.log('✅ Generated output_widget.json');
        
        console.log('\n📊 Summary:');
        console.log(`- Processed ${Object.keys(widgetData).length} file groups`);
        console.log(`- Generated ${mainData.properties.length} main properties`);
        
    } catch (error) {
        console.error('Error writing output files:', error);
        process.exit(1);
    }
}

// Main execution
function main() {
    console.log('🚀 Starting transformation...');
    
    // Read blueprint data
    const blueprintData = readBlueprintFile();
    console.log(`📖 Read ${blueprintData.length} items from blueprint_filegroup.json`);
    
    // Transform data
    const mainData = transformToMainFormat(blueprintData);
    const widgetData = transformToWidgetFormat(blueprintData);
    
    // Write output files
    writeOutputFiles(mainData, widgetData);
    
    console.log('✨ Transformation completed successfully!');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    transformToMainFormat,
    transformToWidgetFormat,
    readBlueprintFile
};
