const fs = require('fs');
const path = require('path');

// Read the form schema file
function readFormSchema() {
    try {
        const schemaPath = path.join(__dirname, 'form_schema.json');
        const data = fs.readFileSync(schemaPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading form_schema.json:', error);
        process.exit(1);
    }
}

// Recursively extract names from elements with type "string" or "number"
function extractNames(obj, names = []) {
    // If current object has name and type properties
    if (obj && typeof obj === 'object' && obj.name && obj.type) {
        if (obj.type === 'string' || obj.type === 'number') {
            names.push(obj.name);
        }
    }
    
    // Recursively process all properties
    if (obj && typeof obj === 'object') {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const value = obj[key];
                
                // If value is an array, process each element
                if (Array.isArray(value)) {
                    value.forEach(item => {
                        extractNames(item, names);
                    });
                }
                // If value is an object, process it recursively
                else if (typeof value === 'object' && value !== null) {
                    extractNames(value, names);
                }
            }
        }
    }
    
    return names;
}

// Write names to output file
function writeNamesToFile(names) {
    try {
        const outputPath = path.join(__dirname, 'extracted_names.txt');
        const content = names.join('\n');
        fs.writeFileSync(outputPath, content);
        console.log(`✅ Successfully wrote ${names.length} names to extracted_names.txt`);
    } catch (error) {
        console.error('Error writing to file:', error);
        process.exit(1);
    }
}

// Main execution
function main() {
    console.log('🚀 Starting name extraction...');
    
    // Read form schema
    const schema = readFormSchema();
    console.log('📖 Successfully read form_schema.json');
    
    // Extract names
    const names = extractNames(schema);
    console.log(`🔍 Found ${names.length} elements with type "string" or "number"`);
    
    // Remove duplicates if any
    const uniqueNames = [...new Set(names)];
    if (uniqueNames.length !== names.length) {
        console.log(`📝 Removed ${names.length - uniqueNames.length} duplicate names`);
    }
    
    // Write to file
    writeNamesToFile(uniqueNames);
    
    console.log('✨ Extraction completed successfully!');
    console.log(`📄 Output saved to: extracted_names.txt`);
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    extractNames,
    readFormSchema
};
