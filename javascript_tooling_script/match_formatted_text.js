const fs = require('fs');
const path = require('path');

// Read the extracted names from text file
function readExtractedNames() {
    try {
        const namesPath = path.join(__dirname, 'extracted_names.txt');
        const data = fs.readFileSync(namesPath, 'utf8');
        return data.trim().split('\n').filter(name => name.trim() !== '');
    } catch (error) {
        console.error('Error reading extracted_names.txt:', error);
        process.exit(1);
    }
}

// Read the carlyle UI schema file
function readCarlyleUISchema() {
    try {
        const schemaPath = path.join(__dirname, 'carlyle_ui_schema.json');
        const data = fs.readFileSync(schemaPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading carlyle_ui_schema.json:', error);
        process.exit(1);
    }
}

// Escape CSV field if it contains commas, quotes, or newlines
function escapeCsvField(field) {
    if (field === null || field === undefined) {
        return '';
    }
    
    const stringField = String(field);
    
    // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n') || stringField.includes('\r')) {
        return '"' + stringField.replace(/"/g, '""') + '"';
    }
    
    return stringField;
}

// Match names with UI schema and extract formatted text
function matchNamesWithSchema(names, uiSchema) {
    const results = [];
    let matchCount = 0;
    
    names.forEach(name => {
        const trimmedName = name.trim();
        if (uiSchema.hasOwnProperty(trimmedName)) {
            const schemaEntry = uiSchema[trimmedName];
            const formattedText = schemaEntry['ui:formattedText'] || '';
            
            results.push({
                name: trimmedName,
                formattedText: formattedText
            });
            
            if (formattedText) {
                matchCount++;
            }
        } else {
            // Name not found in schema
            results.push({
                name: trimmedName,
                formattedText: ''
            });
        }
    });
    
    console.log(`📊 Found ${matchCount} names with ui:formattedText out of ${names.length} total names`);
    return results;
}

// Write results to CSV file
function writeResultsToCSV(results) {
    try {
        const outputPath = path.join(__dirname, 'formatted_text_results.csv');
        
        // Create CSV content
        let csvContent = 'Name,Formatted Text\n';
        
        results.forEach(result => {
            const escapedName = escapeCsvField(result.name);
            const escapedFormattedText = escapeCsvField(result.formattedText);
            csvContent += `${escapedName},${escapedFormattedText}\n`;
        });
        
        fs.writeFileSync(outputPath, csvContent);
        console.log(`✅ Successfully wrote ${results.length} rows to formatted_text_results.csv`);
        
    } catch (error) {
        console.error('Error writing CSV file:', error);
        process.exit(1);
    }
}

// Main execution
function main() {
    console.log('🚀 Starting formatted text matching...');
    
    // Read extracted names
    const names = readExtractedNames();
    console.log(`📖 Read ${names.length} names from extracted_names.txt`);
    
    // Read UI schema
    const uiSchema = readCarlyleUISchema();
    console.log(`📖 Read UI schema with ${Object.keys(uiSchema).length} properties`);
    
    // Match names with schema
    const results = matchNamesWithSchema(names, uiSchema);
    
    // Write results to CSV
    writeResultsToCSV(results);
    
    console.log('✨ Matching completed successfully!');
    console.log(`📄 Output saved to: formatted_text_results.csv`);
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    matchNamesWithSchema,
    readExtractedNames,
    readCarlyleUISchema
};
