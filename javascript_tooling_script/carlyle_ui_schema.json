{"additionalinformation2": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox147": {"ui:formattedText": "<p><strong>Last Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "taxinformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox79": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government Issued ID on File:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header2": {"ui:formattedText": "Header", "ui:heading": "INVESTOR HIERARCHY", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox119": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Subscription Investor ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "additionalinformation1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textarea": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Wealth Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "numberandstreet3": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "multiplecheckbox50": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Identification of UBOs:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox158": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Subscription Investor Approval Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow4": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "multiplecheckbox26": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Investing assets of any retirement plan?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textarea16": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Strategy Description:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "phone1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>General Phone:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Phone"}, "textbox216": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong><PERSON><PERSON> not responsible for AML/KYC:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "group30": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textarea4": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Bank Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "state2": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox60": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Exemption from FATCA Reporting Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date20": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Clearance Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "date45": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "commitmentdetails1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "city1": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "address8": {"ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "uszipcode3": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "textbox171": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Subscription Entity:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country22": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Country of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "city": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "multiplecheckbox37": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Inactive:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "country11": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Country Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox91": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Withholding Rate - OID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "city7": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox205": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox210": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow9": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "date16": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Original Completed Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "country1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Country Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox165": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Fund:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox160": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Common Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "zipcode": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox108": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Place of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox43": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Confirmation of investment on own behalf:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textarea12": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>892/CAI Text:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox100": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox8": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong><PERSON><PERSON> Shares:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "state8": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox143": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_FATCA Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group23": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "header39": {"ui:formattedText": "Header", "ui:heading": "SERVICING INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "customformat3": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "customformat": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Branch - GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "textbox197": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>FinCEN Control Person:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date34": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>MaxComply Id:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header9": {"ui:formattedText": "Header", "ui:heading": "SUPPLEMENTAL DATA", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox32": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Review Cycle:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox154": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox72": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Status of CRS Review:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox180": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea8": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Reason Why TIN Unavailable:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox80": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Qualified Investor Status - Entity:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country7": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "textbox96": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Record Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox8": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Investor Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country26": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox57": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>Sponsoring Entity GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "textbox19": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Website:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox7": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Negative News:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "multiplecheckbox32": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>EU Elective Professional Client</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "radio2": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>AML Data Approved:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox229": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox43": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Entity Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox169": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Currency:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date13": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Expiration of Restrictive Covenant Period:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "country15": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox59": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Joint Account Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox176": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Agreement Legal Entities External Ids:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "multiplecheckbox19": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Tax Reporting by AlpInvest:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "label1": {"ui:formattedText": "<p><strong>Mailing Address</strong></p>", "ui:marginBottom": "8px", "ui:widget": "Paragraph"}, "textbox83": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>UBS Escrow Account:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "combined_address": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox76": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Accredited Investor Status - Entities:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header28": {"ui:formattedText": "Header", "ui:heading": "TAX EXEMPT STATUS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox48": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US Tax ID Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox65": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Related Entity:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox15": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>FINRA Retail Investor:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "date38": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Refresh SOF/SOW:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox103": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Controlling Person Type 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox82": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Qualified Investor Status - Individual:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox4": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date41": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TCG Global Id:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox248": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country19": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Non-US Tax ID Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox179": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US TIN Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox186": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Incorporation / Formation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "dropdown4": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Individual or Organization:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Individual", {"formattedText": "Individual", "blueprintMetadataMapping": ""}], ["Organization", {"formattedText": "Organization", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textbox71": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>IFC Number:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country4": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Citizenship Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox192": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Evidence of Existence:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox10": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Politically Exposed Person (PEP):</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox208": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Abbreviation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox15": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Lux Tax Assessment Rating:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox69": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Record Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox222": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Region:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "contactinformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox121": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox47": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Source of Wealth:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "email": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Email:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Email"}, "textbox233": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>City:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date9": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>GIIN Status Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "group38": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "header17": {"ui:formattedText": "Header", "ui:heading": "ADDITIONAL INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox29": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Marketing Method:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "state": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox168": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox132": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Investor Registered Street Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "combined_address8": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox54": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Mailing Address:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group2": {"ui:dynamicLayout": {"sectionsWidth": [33, 33, 34], "gutter": 12}, "ui:formattedText": "<p><strong>TAO Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox114": {"ui:formattedText": "<p><strong>Last Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox26": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Address: State/Province:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header41": {"ui:formattedText": "Header", "ui:heading": "SYSTEM INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox170": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Close:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox87": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Last Modified By:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group27": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "row5": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox9": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Owner Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox110": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Subscription Investor:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox61": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>GIIN Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header23": {"ui:formattedText": "Header", "ui:heading": "TAX ACCOUNT OWNER", "ui:marginBottom": "12px", "ui:widget": "Header"}, "float": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Exempt Payee Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Integer"}, "usstate6": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "header34": {"ui:formattedText": "Header", "ui:heading": "BENEFICIAL OWNERS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox255": {"ui:formattedText": "<p><strong>First Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox219": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Product Line:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "aml2": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "fundraisinginformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox37": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Approver:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox244": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Full Address:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "subscriptioninvestorapproval": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "radio5": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Skip AML Approvals:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header31": {"ui:formattedText": "Header", "ui:heading": "FUND COMPOSITION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "customformat2": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO Sponsoring Entity GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "multiplecheckbox22": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Reportable GGY, JEY, GIB, IOM person?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox194": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Wealth Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox183": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "radio": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Has <PERSON><PERSON> Onboarding?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "group41": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox204": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>KYC Satisfactory:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "numberandstreet7": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "state6": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox125": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Team Member(s):</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header12": {"ui:formattedText": "Header", "ui:heading": "SUPPLEMENTAL DATA FOR INDIVIDUALS AND ENTITIES", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox237": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Owner:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow5": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox226": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund-Specific Paragraph:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "date27": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox10": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox50": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "multiplecheckbox33": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Exempt from US Income Tax:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox136": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Investor - Foreign/Domestic:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Foreign", {"formattedText": "Foreign", "blueprintMetadataMapping": ""}], ["Domestic", {"formattedText": "Domestic", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textbox21": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>High-Risk Factors:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date37": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Postal Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox18": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Qualified Foreign Pension Fund (QFPF):</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "uszipcode8": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "multiplecheckbox1": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Has Solutions Onboarding?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "commonname": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox166": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Registered Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date35": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "zipcode1": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox189": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>New Commitment or Transfer:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox200": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>World Check Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "row2": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox130": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Citizenship Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "usstate": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "textbox215": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AIFMD Investor:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox89": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Record Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "radio1": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>892/CAI:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header5": {"ui:formattedText": "Header", "ui:heading": "UK FATCA/CDOT STATUS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox20": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Legal Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea3": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Marketing Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "email1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong><PERSON><PERSON> (Servicing):</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Email"}, "textbox93": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Withholding Rate - FATCA:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox44": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Lux Tax Assessment:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "country25": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "controllingpersons": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox78": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>State of Tax Residence:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox53": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Residency Certificates:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country30": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox13": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Risk Rating:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "address9": {"ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "supplementaldata": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "groupshow8": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox177": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Mailing Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox29": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Japanese Resident or Solicited in Japan?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "combined_address1": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea11": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Associated Legal Entities:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox152": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Funding Parent Sub Entity:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header11": {"ui:formattedText": "Header", "ui:heading": "ADDITIONAL INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox190": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox129": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Tax Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "label": {"ui:formattedText": "<p><strong>Registered Address</strong></p>", "ui:marginBottom": "8px", "ui:widget": "Paragraph"}, "multiplecheckbox12": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Regulated Entity:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "city6": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Common Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox118": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Reporting Threshold:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox174": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investran Vehicle Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date10": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date of Organization (Formation Date):</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox86": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Exempt Subtype:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header44": {"ui:formattedText": "Header", "ui:heading": "IFC", "ui:marginBottom": "12px", "ui:widget": "Header"}, "information": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox211": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Vintage Year:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox46": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>External Account Number:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox107": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TIN Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox35": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Approval Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox155": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Agreement Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "numberandstreet8": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox56": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>External Account Institution:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox75": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Mailing Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header29": {"ui:formattedText": "Header", "ui:heading": "AML", "ui:marginBottom": "12px", "ui:widget": "Header"}, "multiplecheckbox6": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Complex Structure:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "beneficialowners": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "taxinformationfatcamonitoring": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "city2": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "date1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Blocking Removed Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "date24": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Closing Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "date15": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Date of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox97": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea7": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Reporting Countries:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox163": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Parent Transfer Agreement:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox67": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Approver:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country14": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "textbox98": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Controlling Person Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country29": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "onboarding": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "multiplecheckbox9": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Shell Bank:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textarea15": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox64": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "subscriptioninvestor": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox24": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Awareness Nexus:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "address5": {"ui:formattedText": "<p><strong>Residence Address</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "zipcode6": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "withholdingrates": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox144": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO W9 Other Entity Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "address10": {"ui:formattedText": "<p><strong>TAO_Investor Registered Street Address:</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "date25": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Final Closing Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox104": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Reporting Countries:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "date42": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header40": {"ui:formattedText": "Header", "ui:heading": "APPROVERS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "multiplecheckbox34": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Exempt from State and Local Income Tax?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "ukfatcacdotstatus": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "date26": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Final Closing Date Per LPA:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox5": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor – Foreign/Domestic:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Foreign", {"formattedText": "Foreign", "blueprintMetadataMapping": ""}], ["Domestic", {"formattedText": "Domestic", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textbox207": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Legal Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox247": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "commonnameorganizationinformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "group28": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox126": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Website:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header30": {"ui:formattedText": "Header", "ui:heading": "DETAILS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox31": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Bank Location:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox23": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Reportable/Non-reportable Account?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox111": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Effective Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox42": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Equity US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header18": {"ui:formattedText": "Header", "ui:heading": "COMMON NAME (ORGANIZATION) INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "country": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>SoW/SoF Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "zipcode9": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "aml1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox218": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Product Category:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group39": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox137": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Non-US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "address11": {"ui:formattedText": "<p><strong>TAO_ Mailing Address</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "contact": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "date19": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Review Initiated:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "country18": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "date21": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Post-Close Follow-Up Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox16": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Wealth Category:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox52": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Eligible Introducer Letter/Certificate:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox68": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Fund Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow2": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "multiplecheckbox45": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Investing in Lux AIFM Funds:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header1": {"ui:formattedText": "Header", "ui:heading": "AML", "ui:marginBottom": "12px", "ui:widget": "Header"}, "numberandstreet1": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "systeminformation1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox122": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox11": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Risk Committee:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header6": {"ui:formattedText": "Header", "ui:heading": "CRS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "row6": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "date8": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date Of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "group24": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "header33": {"ui:formattedText": "Header", "ui:heading": "FUND CLOSE", "ui:marginBottom": "12px", "ui:widget": "Header"}, "combined_address7": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea18": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "dropdown": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Citizenship:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox243": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox115": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Document ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox232": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox195": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>List of Legal Reps and Directors:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox133": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_US Taxpayer Identification Number:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox254": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Salutation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox41": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Qualified Institutional Investor (FIEL):</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "multiplecheckbox48": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Signed Structure Chart:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "date36": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>PEP Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "accountinformation": {"ui:widget": "Page"}, "header22": {"ui:formattedText": "Header", "ui:heading": "SUPPLEMENTAL DATA", "ui:marginBottom": "12px", "ui:widget": "Header"}, "country5": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Incorporation / Formation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox221": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Family:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox141": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Mailing Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group40": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textarea1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Funds Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "group35": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "numberandstreet": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "date31": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government Issued ID on File:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox184": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "usstate7": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "multiplecheckbox49": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Evidence of Each Tier of the Structure:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox139": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Exemption from FATCA Reporting Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header10": {"ui:formattedText": "Header", "ui:heading": "ADDITIONAL INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox38": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>FM Approver:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group25": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox12": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>PEP Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow7": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox178": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US Taxpayer Identification Number:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "combined_address6": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox236": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>KYC Refresh?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox227": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Managed Account: Direct Investments:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "investorhie": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "date11": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Effective Date of Self Certification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox123": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox5": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>High-Risk Industry:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "row1": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox52": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Registered Address:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox173": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Accepted Commitment Amount:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox167": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Opportunity:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "usstate3": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "textbox92": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Withholding Rate - ECI:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox28": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Individual Retirement Account (IRA):</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox199": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Control Person Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "descriptioninformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox140": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Exempt Payee Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Integer"}, "aml": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox101": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Controlling Person Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header37": {"ui:formattedText": "Header", "ui:heading": "CONTACT INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "date2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Next AML Review Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "customformat1": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "textbox225": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Focus (Additional Info):</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textarea10": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Company Description:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "zipcode2": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox188": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Exemption from FATCA Reporting Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox151": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Disregarded GP of GP:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox145": {"ui:formattedText": "<p><strong>First Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "systeminformation2": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "header26": {"ui:formattedText": "Header", "ui:heading": "ADDRESS INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox250": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact TCG Global ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox63": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Affiliated Corporation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox128": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox23": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Blocking:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox257": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Physical Address:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox238": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Politically Exposed Person:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "date32": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Last Modified By:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date29": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government ID Jurisdiction State:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country13": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textarea20": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Beneficial Owner Long Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header14": {"ui:formattedText": "Header", "ui:heading": "WITHHOLDING RATES", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox27": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header19": {"ui:formattedText": "Header", "ui:heading": "DESCRIPTION INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "multiplecheckbox46": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Source of Funds:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "city3": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "address6": {"ui:formattedText": "<p><strong>Mailing Address</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "ifc1": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "addressinformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox116": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Validation Result:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox85": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>MSSB ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "combined_address2": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox30": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Qualified Institutional Investor (FIEL)?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox99": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Controlling Person Package:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "fund1": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "percentage5": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>MROR %:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Percentage"}, "textbox45": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>State of Tax Residence:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox162": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor – Foreign/Domestic:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country28": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government ID Jurisdiction Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "dates": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "date": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Blocking Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox7": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>State of Tax Residence:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox249": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact RecordType Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox149": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO Exemption from FATCA Withholding Cod:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox105": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Controlling Person Type 3:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox24": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Does the Entity Have Controlling Persons?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox34": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Common Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox156": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "numberandstreet9": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox74": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Registered Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea14": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Post-Close Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox112": {"ui:formattedText": "<p><strong>First Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "approvers": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Parent Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header4": {"ui:formattedText": "Header", "ui:heading": "TAX INFORMATION (FATCA MONITORING)", "ui:marginBottom": "12px", "ui:widget": "Header"}, "date43": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US Tax ID Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox49": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Non-US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "combined_address3": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "state1": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox17": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Funds Category:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country6": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>Branch - GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "city9": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "zipcode8": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "textbox220": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Segment:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date14": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox138": {"ui:customFormat": "EIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Withholding Foreign Partnership EIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "__-_______", "ui:widget": "CustomFormat"}, "textbox106": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TIN:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "taxexemptstatus1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textarea19": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Refresh Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "multiplecheckbox31": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Article 63, Paragraph 1 applies?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox235": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Is Active?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header15": {"ui:formattedText": "Header", "ui:heading": "SYSTEM INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "percentage12": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Percentage of Ownership:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Percentage"}, "date7": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Opening Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "country12": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "uszipcode1": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "textbox159": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Investor Account:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox30": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>MiFID Client Category:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox41": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>FM US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox185": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Citizenship Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header36": {"ui:formattedText": "Header", "ui:heading": "AML", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox117": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CRS Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox213": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Part of Program:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox109": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Residence Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox202": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Exceptions/Waivers Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "country9": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "textbox242": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Common Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea6": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Name of Securities Market:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox6": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TCG Global Id:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox148": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO FATCA_Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "uszipcode6": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "row3": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "customformat4": {"ui:customFormat": "GIIN", "ui:enabledComment": true, "ui:formattedText": "<p><strong>Branch - GIIN:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "XXXXXX.XXXXX.XX.XXX", "ui:widget": "CustomFormat"}, "multiplecheckbox20": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Identification as a Specified UK Person:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "multiplecheckbox13": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>AML Pre-Clearance:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "country23": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "textbox231": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>AML Monitoring:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "date22": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Approval Date:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "Date Time", "ui:widget": "TextBox"}, "country2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Treaty Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "group42": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox134": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Individual or Organization:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Individual", {"formattedText": "Individual", "blueprintMetadataMapping": ""}], ["Organization", {"formattedText": "Organization", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textarea2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "fundclose": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "date18": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Date Of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox127": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Record Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox27": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Investor is BHC Partner?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "money": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Accepted Commitment Amount (USD):</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Money"}, "supplementaldataforindividualsandentities": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "multiplecheckbox38": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Disregarded Entity?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "dropdown2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Country Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "textbox212": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Investor Discretion:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textarea21": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Source of Wealth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "group36": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "country20": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Tax Country Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "header43": {"ui:formattedText": "Header", "ui:heading": "SYSTEM INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "group29": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "header7": {"ui:formattedText": "Header", "ui:heading": "ADDRESS INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "usstate8": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "textbox201": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Approval for High-Risk &amp;/or PEP:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header25": {"ui:formattedText": "Header", "ui:heading": "COMMITMENT DETAILS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "supplementaldataonboarding": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "ifc2": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "address": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "details": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox217": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Product Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox253": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "dropdown1": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Individual or Organization:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Individual", {"formattedText": "Individual", "blueprintMetadataMapping": ""}], ["Organization", {"formattedText": "Organization", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textbox206": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Product Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox16": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Eligible Introducer Letter/Certificate:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header": {"ui:formattedText": "Header", "ui:heading": "ACCOUNT INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "row7": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox196": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Identification of Authorized Signatories:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox256": {"ui:formattedText": "<p><strong>Last Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox246": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header38": {"ui:formattedText": "Header", "ui:heading": "FUNDRAISING INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "additionalinformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "date30": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government ID Number:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox228": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fund Close Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header20": {"ui:formattedText": "Header", "ui:heading": "ADDRESS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox161": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Individual or Organization:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country3": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "country21": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "multiplecheckbox42": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Article 63, Paragraph 1 applies?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox251": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Contact's CN Registered Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "address7": {"ui:formattedText": "<p><strong>Mailing Address</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "textbox240": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Street Address:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "phone2": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Business Phone:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Phone"}, "systeminformation3": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox146": {"ui:formattedText": "<p><strong>Middle Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "radio3": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>AML Approved:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox172": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Requested Commitment Amount:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Float"}, "country8": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TIN Country:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "state3": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "date44": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Website:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox39": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Form Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "integer": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Exempt Payee Code:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Integer"}, "servicinginformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "multiplecheckbox4": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Medium-High Risk Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "header27": {"ui:formattedText": "Header", "ui:heading": "TAX INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "fund": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox245": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Created By:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "usstate2": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "date33": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Last Triggered Timestamp:</strong></p>", "ui:marginBottom": "12px", "ui:placeholder": "Date / Time", "ui:widget": "TextBox"}, "numberandstreet2": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox157": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Subscription Investor:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "country10": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "textbox175": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Related Agreements Group:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date17": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Effective Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textbox239": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>State / Province:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow3": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox58": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>W9 Other Entity Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header13": {"ui:formattedText": "Header", "ui:heading": "TAX EXEMPT STATUS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox223": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Structure:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox28": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Pre-Clearance Funds:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "commitmentdetails": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox142": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Tax Form Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox51": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Exceptions/Waivers:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox224": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Currency:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox18": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Nominee/Intermediary:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea17": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Mandate Background:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textarea9": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Company Description:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "repeatable1": {"ui:enabledComment": true, "ui:formattedText": "Repeatable", "ui:marginBottom": "12px", "ui:maxLength": 5, "ui:widget": "Repeatable"}, "header3": {"ui:formattedText": "Header", "ui:heading": "SUBSCRIPTION INVESTOR APPROVAL", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox164": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Commitment Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "date12": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government ID Expiration Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "accreditedinvestorstatus": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox181": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Country Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox73": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TIN No.:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox25": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Shareholders Determine Capital Invest:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "state9": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "date39": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Risk Rating:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "uszipcode2": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "textbox209": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Fundraising Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox40": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>US Tax ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox3": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>High-Risk Jurisdiction:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "country27": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "multiplecheckbox40": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Subject to Title I:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "row4": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox234": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Created By:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox33": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Account Owner:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox153": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Funding Silo:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox131": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_Country of Incorporation/Formation:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Country"}, "address4": {"ui:formattedText": "<p><strong>Registered Address</strong></p>", "ui:indentation": "0px", "ui:marginBottom": "20px", "ui:widget": "Group"}, "textbox55": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Comments:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox198": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>World Check Results:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textarea5": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Introducer Letter/Certificate Comments:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "addressinformation1": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "repeatable": {"ui:enabledComment": true, "ui:formattedText": "Repeatable", "ui:marginBottom": "12px", "ui:maxLength": 5, "ui:widget": "Repeatable"}, "textbox150": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>ERISA Partner:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox124": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TCG Global Id:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group37": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "country24": {"ui:enabledComment": true, "ui:formattedText": "<p><span style=\"font-size: 13px;\">Country </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:indentation": "16px", "ui:marginBottom": "12px", "ui:placeholder": "Select country", "ui:required": "This field is required", "ui:widget": "Country", "ui:width": "50%"}, "header35": {"ui:formattedText": "Header", "ui:heading": "IFC", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox14": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Risk Rating 3+ Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "date23": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Inactive Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "textarea13": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Exempt Status Notes:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox47": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>FATCA Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox66": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Place (City or Town) of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox70": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Record Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox81": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Qualified Client Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox3": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Parent Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox191": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Lux Tax Assessment Score:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox84": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Residency Certificates:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "group26": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:indentation": "0px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox230": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Close Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "header8": {"ui:formattedText": "Header", "ui:heading": "ACCREDITED INVESTOR STATUS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "group": {"ui:dynamicLayout": {"sectionsWidth": [33, 33, 34], "gutter": 12}, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox187": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Country of Birth:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header16": {"ui:formattedText": "Header", "ui:heading": "CONTROLLING PERSONS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "usstate1": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "zipcode7": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "date28": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Government ID Expiration Date:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Date"}, "uszipcode": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "combined_address9": {"ui:formattedText": "TextBox", "ui:invisible": true, "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "taxaccountowner": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox44": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Tax Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox120": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Mailing Address 2:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "row8": {"ui:dynamicLayout": {"sectionsWidth": [70, 30], "gutter": 8}, "ui:enabledComment": true, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox77": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Accredited Investor Status - Individual:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox21": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Reportable UK Person?</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "systeminformation": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "textbox51": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Withholding Foreign Partnership EIN:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "radio4": {"ui:clearable": true, "ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Agreement Approved:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "usstate9": {"ui:formattedText": "<p>U.S. State/Territories <span style=\"color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:subdivisionOf": "US", "ui:widget": "State"}, "header21": {"ui:formattedText": "Header", "ui:heading": "SYSTEM INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "textbox62": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>CDOT Entity Account Holder Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "header24": {"ui:formattedText": "Header", "ui:heading": "INFORMATION", "ui:marginBottom": "12px", "ui:widget": "Header"}, "dropdown3": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Individual or Organization:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["Individual", {"formattedText": "Individual", "blueprintMetadataMapping": ""}], ["Organization", {"formattedText": "Organization", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Dropdown"}, "textbox182": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>FATCA Classification:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "multiplecheckbox14": {"ui:enabledComment": true, "ui:fixedLayout": "Inline", "ui:formattedText": "<p><strong>Broker Dealer:</strong></p>", "ui:marginBottom": "12px", "ui:multipleOption": {"options": [["true", {"formattedText": "<p>True</p>", "blueprintMetadataMapping": ""}], ["false", {"formattedText": "<p>False</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "textbox11": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>PEP Status Details:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox252": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Account Name:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox135": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>TAO_US TIN Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox22": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Type of Block:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "city8": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">City </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "textbox203": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Analyst:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "groupshow6": {"ui:indentation": "16px", "ui:marginBottom": "0px", "ui:widget": "Group"}, "textbox36": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>SI Name Approval Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox214": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>PR&amp;A Coverage:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "phone": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>General Phone:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "Phone"}, "ifc": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "header42": {"ui:formattedText": "Header", "ui:heading": "COMMITMENT DETAILS", "ui:marginBottom": "12px", "ui:widget": "Header"}, "date40": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Role:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "zipcode3": {"ui:formattedText": "<p>Zip Code/Postal Code</p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "header32": {"ui:formattedText": "Header", "ui:heading": "DATES", "ui:marginBottom": "12px", "ui:widget": "Header"}, "taxexemptstatus": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "fundprivateequityrealasset": {"ui:marginBottom": "12px", "ui:widget": "Group"}, "crs": {"ui:marginBottom": "12px", "ui:widget": "Page"}, "group5": {"ui:dynamicLayout": {"sectionsWidth": [50, 50], "gutter": 12}, "ui:marginBottom": "12px", "ui:widget": "Group"}, "textbox102": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Why TIN Unavailable:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextArea"}, "textbox241": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>IFC Status:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox113": {"ui:formattedText": "<p><strong>Middle Name</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Type:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "numberandstreet6": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">Number and Street </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:required": "This field is required", "ui:widget": "TextBox"}, "state7": {"ui:formattedText": "<p><span style=\"font-size: 13px;\">State/Province</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:widget": "TextBox"}, "uszipcode9": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}, "textbox25": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>AML Address: City:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox88": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>External ID:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "textbox193": {"ui:enabledComment": true, "ui:formattedText": "<p><strong>Evidence of Corporate Authority:</strong></p>", "ui:marginBottom": "12px", "ui:widget": "TextBox"}, "uszipcode7": {"ui:customFormat": "USZIP", "ui:formattedText": "<p><span style=\"font-size: 13px;\">Zip Code </span><span style=\"font-size: 13px; color: rgb(219, 55, 55);\">*</span></p>", "ui:marginBottom": "0px", "ui:pdfMapping": [], "ui:placeholder": "5 or 9 digits", "ui:required": "This field is required", "ui:validationError": "Please enter a valid ZIP code", "ui:widget": "CustomFormat"}}