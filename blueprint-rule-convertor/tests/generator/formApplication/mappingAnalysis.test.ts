import { expect, test, describe } from "bun:test";
import {
    getRuleAnalysis,
    toComparedRule,
} from "@/generator/formApplication/mappingAnalysis";
import { FormRule } from "@/models/stargazer/";
describe("getRuleAnalysis", () => {
    const createFormRule = (
        name: string,
        value: string,
        description: string,
    ): FormRule => ({
        value,
        name,
        defaultNamespace: "main",
        description,
        templateId: "",
    });

    const formRules: FormRule[] = [
        createFormRule("[Form] Name 1", "Random value 1", ""),
        createFormRule(
            "[Form] Name 2",
            "Random value of bluewell_spouse was changed",
            "[blueprintTarget:bluewell_spouse][blueprintTemplateId:bluewell_spouse][widgetType:Paragraph]",
        ),
        createFormRule(
            "[Form] Name 3",
            "Random value of irsform",
            "[blueprintTarget:irsform][blueprintTemplateId:irsform][widgetType:FileGroup]",
        ),
        createFormRule(
            "[Form] Name 4",
            "Random value of bluewell_w9",
            "[blueprintTarget:bluewell_w9][blueprintTemplateId:bluewell_w9][widgetType:Paragraph]",
        ),
        createFormRule(
            "[Form] Name 5",
            "Random value of bluewell_w8",
            "[blueprintTarget:bluewell_w8][blueprintTemplateId:bluewell_w8][widgetType:Paragraph]",
        ),
        createFormRule(
            "[Form] Name 6",
            "Random value of fatca_entity_crs",
            "[blueprintTarget:fatca_entity_crs][blueprintTemplateId:fatca_entity_crs]...",
        ),
    ];

    const blueprintRules: FormRule[] = [
        createFormRule(
            "[Blueprint] Name 1",
            "Random Value of bluewell_spouse",
            "[blueprintTarget:bluewell_spouse][blueprintTemplateId:bluewell_spouse][widgetType:Paragraph]",
        ),
        createFormRule(
            "[Blueprint] Name 2",
            "Random Value of irsform",
            "[blueprintTarget:irsform][blueprintTemplateId:irsform][widgetType:FileGroup]",
        ),
        createFormRule(
            "[Blueprint] Name 4",
            "Random Value of bluewell_w8",
            "[blueprintTarget:bluewell_w8][blueprintTemplateId:bluewell_w8][widgetType:Paragraph]",
        ),

        createFormRule(
            "[Blueprint] Name 5",
            "Random Value of appendixa",
            "[blueprintTarget:appendixa][blueprintTemplateId:appendixa][widgetType:Group]",
        ),

        createFormRule(
            "[Blueprint] Name 6",
            "Random Value of appendixb",
            "[blueprintTarget:appendixb][blueprintTemplateId:appendixb][widgetType:Group]",
        ),
        createFormRule(
            "[Blueprint] Name 3",
            "Random Value of bluewell_w9",
            "[blueprintTarget:bluewell_w9][blueprintTemplateId:bluewell_w9][widgetType:Paragraph]",
        ),
    ];

    test("should return empty array when no rules are provided", () => {
        const result = getRuleAnalysis([], []);

        expect(result).toMatchSnapshot();
    });

    test("should not handle form rules not related to blueprint", () => {
        const formOnlyRules = [formRules[0]];
        const result = getRuleAnalysis(formOnlyRules, []);

        expect(result).toMatchSnapshot();
    });

    test("should handle form rules only (all removing)", () => {
        const formOnlyRules = [formRules[5]];
        const result = getRuleAnalysis(formOnlyRules, []);

        expect(result).toMatchSnapshot();
    });

    test("should handle blueprint rules only (all adding)", () => {
        const blueprintOnlyRules = [blueprintRules[4]];
        const result = getRuleAnalysis([], blueprintOnlyRules);

        expect(result).toMatchSnapshot();
    });

    test("should identify existing rules with modifications (import version differences)", () => {
        const formRule = [formRules[1]];
        const blueprintRule = [blueprintRules[0]];

        const result = getRuleAnalysis(formRule, blueprintRule);

        expect(result).toMatchSnapshot();
    });

    test("should identify existing rules without modifications", () => {
        const identicalFormRule = createFormRule(
            "Test Rule",
            "function(test)\nlocal _ = import 'mixcalc_v3';\nlocal logic = afs.logic.main;\nlogic.test.show(true)",
            "[blueprintTarget:test][blueprintTemplateId:test][widgetType:TextBox]",
        );

        const identicalBlueprintRule = createFormRule(
            "Test Rule",
            "function(test)\nlocal _ = import 'mixcalc';\nlocal logic = afs.logic.main;\nlogic.test.show(true)",
            "[blueprintTarget:test][blueprintTemplateId:test][widgetType:TextBox]",
        );

        const result = getRuleAnalysis(
            [identicalFormRule],
            [identicalBlueprintRule],
        );

        expect(result).toMatchSnapshot();
    });

    test("should handle complex scenario with all operation types", () => {
        const mixedFormRules = [formRules[0], formRules[1], formRules[2]];

        const mixedBlueprintRules = [
            blueprintRules[0],
            blueprintRules[1],
            blueprintRules[4],
            blueprintRules[5],
        ];

        const result = getRuleAnalysis(mixedFormRules, mixedBlueprintRules);

        expect(result).toMatchSnapshot();
    });

    test("should maintain order of form rules first, then blueprint rules", () => {
        const result = getRuleAnalysis(formRules, blueprintRules);

        expect(result).toMatchSnapshot();
    });

    test("should handle rules with actual logic differences (not just imports)", () => {
        const formRuleWithDifferentLogic = createFormRule(
            "Test Logic Rule",
            "function(test)\nlocal _ = import 'mixcalc_v3';\nlocal logic = afs.logic.main;\nlogic.test.show(false)",
            "[blueprintTarget:test][blueprintTemplateId:test][widgetType:TextBox]",
        );

        const blueprintRuleWithDifferentLogic = createFormRule(
            "Test Logic Rule",
            "function(test)\nlocal _ = import 'mixcalc';\nlocal logic = afs.logic.main;\nlogic.test.show(true)",
            "[blueprintTarget:test][blueprintTemplateId:test][widgetType:TextBox]",
        );

        const result = getRuleAnalysis(
            [formRuleWithDifferentLogic],
            [blueprintRuleWithDifferentLogic],
        );

        expect(result).toMatchSnapshot();
    });
});

describe("toComparedRule", () => {
    test("should replace import statements with IGNORE_CONTENT", () => {
        const ruleContent = `function(test)
local _ = import 'mixcalc_v3';
local utils = import 'utils_v2';
local logic = afs.logic.main;

logic.test.show(true)`;

        const result = toComparedRule(ruleContent);

        expect(result).toMatchSnapshot();
    });
});
