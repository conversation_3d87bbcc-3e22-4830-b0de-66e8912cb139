// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`getRuleAnalysis should return empty array when no rules are provided 1`] = `[]`;

exports[`getRuleAnalysis should not handle form rules not related to blueprint 1`] = `[]`;

exports[`getRuleAnalysis should handle form rules only (all removing) 1`] = `
[
  RemovingFormRule {
    "formRuleName": "[Form] Name 6",
    "ruleId": BlueprintRuleId {
      "id": "fatca_entity_crs__fatca_entity_crs",
      "ruleTargetId": RuleTargetId {
        "id": "fatca_entity_crs",
        "targetNames": [
          "fatca_entity_crs",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "fatca_entity_crs",
        "templateKeys": [
          "fatca_entity_crs",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "RemovingFormRule",
  },
]
`;

exports[`getRuleAnalysis should handle blueprint rules only (all adding) 1`] = `
[
  AddingFormRule {
    "blueprintRuleName": "[Blueprint] Name 6",
    "ruleId": BlueprintRuleId {
      "id": "appendixb__appendixb",
      "ruleTargetId": RuleTargetId {
        "id": "appendixb",
        "targetNames": [
          "appendixb",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "appendixb",
        "templateKeys": [
          "appendixb",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "AddingFormRule",
  },
]
`;

exports[`getRuleAnalysis should identify existing rules with modifications (import version differences) 1`] = `
[
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 1",
    "formRuleName": "[Form] Name 2",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "bluewell_spouse__bluewell_spouse",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_spouse",
        "targetNames": [
          "bluewell_spouse",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_spouse",
        "templateKeys": [
          "bluewell_spouse",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
]
`;

exports[`getRuleAnalysis should identify existing rules without modifications 1`] = `
[
  ExistingFormRule {
    "blueprintRuleName": "Test Rule",
    "formRuleName": "Test Rule",
    "isModfied": false,
    "ruleId": BlueprintRuleId {
      "id": "test__test",
      "ruleTargetId": RuleTargetId {
        "id": "test",
        "targetNames": [
          "test",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "test",
        "templateKeys": [
          "test",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
]
`;

exports[`getRuleAnalysis should handle complex scenario with all operation types 1`] = `
[
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 1",
    "formRuleName": "[Form] Name 2",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "bluewell_spouse__bluewell_spouse",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_spouse",
        "targetNames": [
          "bluewell_spouse",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_spouse",
        "templateKeys": [
          "bluewell_spouse",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 2",
    "formRuleName": "[Form] Name 3",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "irsform__irsform",
      "ruleTargetId": RuleTargetId {
        "id": "irsform",
        "targetNames": [
          "irsform",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "irsform",
        "templateKeys": [
          "irsform",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  AddingFormRule {
    "blueprintRuleName": "[Blueprint] Name 6",
    "ruleId": BlueprintRuleId {
      "id": "appendixb__appendixb",
      "ruleTargetId": RuleTargetId {
        "id": "appendixb",
        "targetNames": [
          "appendixb",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "appendixb",
        "templateKeys": [
          "appendixb",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "AddingFormRule",
  },
  AddingFormRule {
    "blueprintRuleName": "[Blueprint] Name 3",
    "ruleId": BlueprintRuleId {
      "id": "bluewell_w9__bluewell_w9",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_w9",
        "targetNames": [
          "bluewell_w9",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_w9",
        "templateKeys": [
          "bluewell_w9",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "AddingFormRule",
  },
]
`;

exports[`getRuleAnalysis should maintain order of form rules first, then blueprint rules 1`] = `
[
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 1",
    "formRuleName": "[Form] Name 2",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "bluewell_spouse__bluewell_spouse",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_spouse",
        "targetNames": [
          "bluewell_spouse",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_spouse",
        "templateKeys": [
          "bluewell_spouse",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 2",
    "formRuleName": "[Form] Name 3",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "irsform__irsform",
      "ruleTargetId": RuleTargetId {
        "id": "irsform",
        "targetNames": [
          "irsform",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "irsform",
        "templateKeys": [
          "irsform",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 3",
    "formRuleName": "[Form] Name 4",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "bluewell_w9__bluewell_w9",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_w9",
        "targetNames": [
          "bluewell_w9",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_w9",
        "templateKeys": [
          "bluewell_w9",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  ExistingFormRule {
    "blueprintRuleName": "[Blueprint] Name 4",
    "formRuleName": "[Form] Name 5",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "bluewell_w8__bluewell_w8",
      "ruleTargetId": RuleTargetId {
        "id": "bluewell_w8",
        "targetNames": [
          "bluewell_w8",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "bluewell_w8",
        "templateKeys": [
          "bluewell_w8",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
  RemovingFormRule {
    "formRuleName": "[Form] Name 6",
    "ruleId": BlueprintRuleId {
      "id": "fatca_entity_crs__fatca_entity_crs",
      "ruleTargetId": RuleTargetId {
        "id": "fatca_entity_crs",
        "targetNames": [
          "fatca_entity_crs",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "fatca_entity_crs",
        "templateKeys": [
          "fatca_entity_crs",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "RemovingFormRule",
  },
  AddingFormRule {
    "blueprintRuleName": "[Blueprint] Name 5",
    "ruleId": BlueprintRuleId {
      "id": "appendixa__appendixa",
      "ruleTargetId": RuleTargetId {
        "id": "appendixa",
        "targetNames": [
          "appendixa",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "appendixa",
        "templateKeys": [
          "appendixa",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "AddingFormRule",
  },
  AddingFormRule {
    "blueprintRuleName": "[Blueprint] Name 6",
    "ruleId": BlueprintRuleId {
      "id": "appendixb__appendixb",
      "ruleTargetId": RuleTargetId {
        "id": "appendixb",
        "targetNames": [
          "appendixb",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "appendixb",
        "templateKeys": [
          "appendixb",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "AddingFormRule",
  },
]
`;

exports[`getRuleAnalysis should handle rules with actual logic differences (not just imports) 1`] = `
[
  ExistingFormRule {
    "blueprintRuleName": "Test Logic Rule",
    "formRuleName": "Test Logic Rule",
    "isModfied": true,
    "ruleId": BlueprintRuleId {
      "id": "test__test",
      "ruleTargetId": RuleTargetId {
        "id": "test",
        "targetNames": [
          "test",
        ],
        "type": "RuleTargetId",
      },
      "templateMappingId": TemplateMappingId {
        "id": "test",
        "templateKeys": [
          "test",
        ],
        "type": "TemplateMappingId",
      },
      "type": "BlueprintRuleId",
    },
    "type": "ExistingFormRule",
  },
]
`;

exports[`toComparedRule should replace import statements with IGNORE_CONTENT 1`] = `
"function(test)
local _ = import 'IGNORE_CONTENT';
local utils = import 'IGNORE_CONTENT';
local logic = afs.logic.main;

logic.test.show(true)"
`;
