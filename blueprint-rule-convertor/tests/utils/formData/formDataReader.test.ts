import $ from "lodash";
import fs from "fs";

import { expect, test, describe } from "bun:test";
import { decodeFormData } from "@/utils/formData/formDataReader";
import { encodeFormData } from "@/utils/formData/formDataWriter";

describe("formDataReader", () => {
    test("Should not change any thing form source data - 1", () => {
        const form = JSON.parse(
            fs.readFileSync(
                "tests/data/quantum_strategic_partner/quantum_getForm_default.json",
                "utf8",
            ),
        );
        const formData = $.get(form, ["formData"]);
        const result = encodeFormData(decodeFormData(formData));

        expect($.isEqual(result, formData)).toBe(true);
    });
    test("Should not change any thing form source data - 2", () => {
        const form = JSON.parse(
            fs.readFileSync(
                "tests/data/quantum_strategic_partner/quantum_getForm_emptyRule.json",
                "utf8",
            ),
        );
        const formData = $.get(form, ["formData"]);
        const result = encodeFormData(decodeFormData(formData));

        expect($.isEqual(result, formData)).toBe(true);
    });
});
