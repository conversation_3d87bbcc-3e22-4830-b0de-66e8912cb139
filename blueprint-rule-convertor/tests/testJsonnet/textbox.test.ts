import { expect, test, describe, beforeEach } from "bun:test";

import { getCatalaCode, intitilizeTemplateGeneratorTest } from "../utils";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";

describe("[Textbox]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Test 1: [fn_set_value][fn_str_neq][const:dot][const:comma][do_nothing][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition attention_formalnotcie__fn_set_value equals
                if (fn_str_neq of asa_company_primarycontact_contactinfo, blueprint_string_constants.dot) then
                    asa_fullname_investorname_generalinfo
                else
                    do_nothing
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 2: \
[fn_disable_and_set_value][fn_str_eq][fn_str_contains][fn_join][fn_label]\
[const:comma][const:slash][const:sweden][const:underscore][const:no_error]", async () => {
        const catalaCode = getCatalaCode(
            `definition attention_formalnotcie__fn_disable_and_set_value equals
                        if (fn_str_contains of individualname_trust_e_part4_individual_qualificationstatement, blueprint_string_constants.slash) then
                            fn_label of 
                                organizationtype_part5_entity_qualificationstatement,
                                [organizationtype_part5_entity_qualificationstatement_options.other_organizationtype_part5_entity_qualificationstatement],
                                [specify_other_organizationtype_part5_entity_qualificationstatement]
                        else if (fn_str_eq of asa_country_additionalcontact_contactinfo, blueprint_string_constants.sweden) then
                            fn_join of [
                                asa_firstname_additionalcontact_contactinfo_0;
                                asa_lastname_additionalcontact_contactinfo_0;
                                blueprint_string_constants.no_error
                            ], blueprint_string_constants.underscore
                        else
                            asa_fullname_investorname_generalinfo
                        `,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
});
