// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[Textbox] Test 1: [fn_set_value][fn_str_neq][const:dot][const:comma][do_nothing][scope:Individual] 1`] = `
"function(
	attention_formalnotcie,
	asa_company_primarycontact_contactinfo,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isStringNotEqual = asa_company_primarycontact_contactinfo.value != ".";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual && isStringNotEqual, disabled = false)
    .apply(logic.attention_formalnotcie, ctx = attention_formalnotcie)
"
`;

exports[`[Textbox] Test 2: [fn_disable_and_set_value][fn_str_eq][fn_str_contains][fn_join][fn_label][const:comma][const:slash][const:sweden][const:underscore][const:no_error] 1`] = `
"function(
	attention_formalnotcie,
	asa_country_additionalcontact_contactinfo,
	asa_firstname_additionalcontact_contactinfo_0,
	asa_fullname_investorname_generalinfo,
	asa_lastname_additionalcontact_contactinfo_0,
	individualname_trust_e_part4_individual_qualificationstatement,
	organizationtype_part5_entity_qualificationstatement,
	specify_other_organizationtype_part5_entity_qualificationstatement,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isStringContained = utils.stringContains(individualname_trust_e_part4_individual_qualificationstatement.value, "/");
local isStringEqual = asa_country_additionalcontact_contactinfo.value == "Sweden";

_
    .fill(utils.getLabel({
			"investmentcompany_organizationtype_part5_entity_qualificationstatement": "investmentcompany_organizationtype_part5_entity_qualificationstatement",
			"nonprofitorganization_organizationtype_part5_entity_qualificationstatement": "nonprofitorganization_organizationtype_part5_entity_qualificationstatement",
			"other_organizationtype_part5_entity_qualificationstatement": "other_organizationtype_part5_entity_qualificationstatement",
			"banking_organizationtype_part5_entity_qualificationstatement": "banking_organizationtype_part5_entity_qualificationstatement",
			"pensionplan_organizationtype_part5_entity_qualificationstatement": "pensionplan_organizationtype_part5_entity_qualificationstatement",
			"municipalgovernmententity_organizationtype_part5_entity_qualificationstatement": "municipalgovernmententity_organizationtype_part5_entity_qualificationstatement",
			"municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement": "municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement",
			"investmentcompany_organizationtype_part5_entity_qualificationstatement_4": "investmentcompany_organizationtype_part5_entity_qualificationstatement_4",
			"broker_organizationtype_part5_entity_qualificationstatement": "broker_organizationtype_part5_entity_qualificationstatement",
			"wealthfund_organizationtype_part5_entity_qualificationstatement": "wealthfund_organizationtype_part5_entity_qualificationstatement",
			"insurancecompany_organizationtype_part5_entity_qualificationstatement": "insurancecompany_organizationtype_part5_entity_qualificationstatement",
	    })(organizationtype_part5_entity_qualificationstatement.value, [
		"other_organizationtype_part5_entity_qualificationstatement",
	], [
		specify_other_organizationtype_part5_entity_qualificationstatement.value,
	]), condition = isStringContained, disabled = true)
	.fill(utils.join([
		asa_firstname_additionalcontact_contactinfo_0.value,
		asa_lastname_additionalcontact_contactinfo_0.value,
		utils.EmptyValue,
	], "_"), condition = !isStringContained && isStringEqual, disabled = true)
	.fill(asa_fullname_investorname_generalinfo.value, condition = !(isStringContained && isStringEqual), disabled = true)
    .apply(logic.attention_formalnotcie, ctx = attention_formalnotcie)
"
`;
