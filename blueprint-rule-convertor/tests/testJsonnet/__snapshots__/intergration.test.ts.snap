// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[Integration Test] Form Quantumn 1`] = `
"function(
	filegroup1,
	gr_shortcut,
	individual_blueprint_investor_type,
	investing_jointly_with_your_spouse,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";
local isOptYes = investing_jointly_with_your_spouse.value == "opt_yes";

_
    .enableFiles([
		"file1",
	], condition = isIndividual && isJointTenancy)
	.enableFiles([
		"file2",
	], condition = isIndividual && !isJointTenancy && isOptYes)
	.enableFiles([
		"file3",
	], condition = true)
    .apply(logic.filegroup1, ctx = filegroup1)

function(
	investing_jointly_with_your_spouse,
	entity_blueprint_investor_type,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isTrust = individual_blueprint_investor_type.value == "a trust";
local isTrust1 = entity_blueprint_investor_type.value == "a trust";

_
    .show(condition = isIndividual && (isTrust || isTrust1))
    .apply(logic.investing_jointly_with_your_spouse, ctx = investing_jointly_with_your_spouse)

function(
	bluewell1,
	gr_shortcut,
	investing_jointly_with_your_spouse,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isOptYes = investing_jointly_with_your_spouse.value == "opt_yes";

_
    .show(condition = isIndividual && isOptYes)
    .apply(logic.bluewell1, ctx = bluewell1)

function(
	gating_if_unincorporated_agency,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .hide(condition = isIndividual)
    .apply(logic.gating_if_unincorporated_agency, ctx = gating_if_unincorporated_agency)

function(
	attention_formalnotcie,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isIndividualHumanBeing = individual_blueprint_investor_type.value == "an individual human being";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual && (isIndividualHumanBeing || isJointTenancy), disabled = false)
    .apply(logic.attention_formalnotcie, ctx = attention_formalnotcie)

function(
	asa_company_primarycontact_contactinfo,
	organization_formalnotcie,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;



_
    .fill(organization_formalnotcie.value, condition = true, disabled = false)
    .apply(logic.asa_company_primarycontact_contactinfo, ctx = asa_company_primarycontact_contactinfo)

function(
	asa_phone_primarycontact_contactinfo,
	phone_formalnotcie,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;



_
    .fill(phone_formalnotcie.value, condition = true, disabled = false)
    .apply(logic.asa_phone_primarycontact_contactinfo, ctx = asa_phone_primarycontact_contactinfo)

function(
	asa_email_primarycontact_contactinfo,
	email_formalnotcie,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;



_
    .fill(email_formalnotcie.value, condition = true, disabled = false)
    .apply(logic.asa_email_primarycontact_contactinfo, ctx = asa_email_primarycontact_contactinfo)

function(
	asa_correspondences_primarycontact_contactinfo,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;



_
    .fill([
		"asa_capitalcall_correspondences_primarycontact_contactinfo",
		"asa_distribution_correspondences_primarycontact_contactinfo",
	], condition = true, disabled = false)
    .apply(logic.asa_correspondences_primarycontact_contactinfo, ctx = asa_correspondences_primarycontact_contactinfo)

function(
	gating_for_another_contact,
	gating_for_additional_contact_different,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isCheckIfYes = std.member(gating_for_additional_contact_different.value, "check_if_yes");

_
    .fill([
		"check_if_yes",
	], condition = isCheckIfYes, disabled = false)
    .apply(logic.gating_for_another_contact, ctx = gating_for_another_contact)

function(
	asa_company_additionalcontact_contactinfo,
	gating_for_additional_contact_different,
	organization_ifdifferent,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isCheckIfYes = std.member(gating_for_additional_contact_different.value, "check_if_yes");

_
    .fill(organization_ifdifferent.value, condition = isCheckIfYes, disabled = false)
    .apply(logic.asa_company_additionalcontact_contactinfo, ctx = asa_company_additionalcontact_contactinfo)

function(
	asa_phone_additionalcontact_contactinfo,
	gating_for_additional_contact_different,
	phone_ifdifferent,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isCheckIfYes = std.member(gating_for_additional_contact_different.value, "check_if_yes");

_
    .fill(phone_ifdifferent.value, condition = isCheckIfYes, disabled = false)
    .apply(logic.asa_phone_additionalcontact_contactinfo, ctx = asa_phone_additionalcontact_contactinfo)

function(
	asa_email_additionalcontact_contactinfo,
	email_ifdifferent,
	gating_for_additional_contact_different,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isCheckIfYes = std.member(gating_for_additional_contact_different.value, "check_if_yes");

_
    .fill(email_ifdifferent.value, condition = isCheckIfYes, disabled = false)
    .apply(logic.asa_email_additionalcontact_contactinfo, ctx = asa_email_additionalcontact_contactinfo)

function(
	gating_for_representation,
	indicate_if_subscriber_is_eea_or_uk,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isOptYes = indicate_if_subscriber_is_eea_or_uk.value == "opt_yes";

_
    .show(condition = isOptYes)
    .apply(logic.gating_for_representation, ctx = gating_for_representation)

function(
	bluewell2,
	indicate_if_subscriber_is_eea_or_uk,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isOptYes = indicate_if_subscriber_is_eea_or_uk.value == "opt_yes";

_
    .show(condition = isOptYes)
    .apply(logic.bluewell2, ctx = bluewell2)

function(
	orangewell1,
	checkbox_for_canadian_representation,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYes = std.member(checkbox_for_canadian_representation.value, "yes");

_
    .show(condition = isYes)
    .apply(logic.orangewell1, ctx = orangewell1)

function(
	b_part2_individual_qualificationstatement,
	asa_commitment_amount,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isNumberGreaterThanOrEqual = utils.forceNum(asa_commitment_amount.value) >= 1100000;

_
    .fill("true_b_part2_individual_qualificationstatement", condition = isIndividual && isNumberGreaterThanOrEqual, disabled = true)
	.fill("false_b_part2_individual_qualificationstatement", condition = isIndividual && !isNumberGreaterThanOrEqual, disabled = true)
    .apply(logic.b_part2_individual_qualificationstatement, ctx = b_part2_individual_qualificationstatement)

function(
	a2_part2_entity_qualificationstatement,
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isYesSec2a48companyAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor");
local isYesSec2a48companyAccreditedinvestor1 = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor_1");

_
    .fill("false_a2_part2_entity_qualificationstatement", condition = isEntity && (isYesSec2a48companyAccreditedinvestor || isYesSec2a48companyAccreditedinvestor1), disabled = false)
    .apply(logic.a2_part2_entity_qualificationstatement, ctx = a2_part2_entity_qualificationstatement)

function(
	b_part2_entity_qualificationstatement,
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isYesSec2a48companyAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor");

_
    .fill("false_b_part2_entity_qualificationstatement", condition = isEntity && isYesSec2a48companyAccreditedinvestor, disabled = false)
    .apply(logic.b_part2_entity_qualificationstatement, ctx = b_part2_entity_qualificationstatement)

function(
	e_part2_entity_qualificationstatement,
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isYesTrustAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_trust_accreditedinvestor");
local isYesFamilyofficeAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_familyoffice_accreditedinvestor");
local isYesOtherown5mOtherentityAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_otherown5m_otherentity_accreditedinvestor");

_
    .fill("true_e_part2_entity_qualificationstatement", condition = isEntity && (isYesTrustAccreditedinvestor || isYesFamilyofficeAccreditedinvestor || isYesOtherown5mOtherentityAccreditedinvestor), disabled = false)
    .apply(logic.e_part2_entity_qualificationstatement, ctx = e_part2_entity_qualificationstatement)

function(
	a1_part3_entity_qualificationstatement,
	asa_accreditedinvestor_accreditedinvestor,
	b_part2_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isYesSec2a48companyAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor");
local isTrueBPart2EntityQualificationstatement = b_part2_entity_qualificationstatement.value == "true_b_part2_entity_qualificationstatement";

_
    .fill("true_a1_part3_entity_qualificationstatement", condition = isEntity && isYesSec2a48companyAccreditedinvestor, disabled = true)
	.fill("true_a1_part3_entity_qualificationstatement", condition = isEntity && isTrueBPart2EntityQualificationstatement, disabled = false)
	.fill("false_a1_part3_entity_qualificationstatement", condition = isEntity && !isTrueBPart2EntityQualificationstatement, disabled = false)
    .apply(logic.a1_part3_entity_qualificationstatement, ctx = a1_part3_entity_qualificationstatement)

function(
	a2_part3_entity_qualificationstatement,
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isYesSec2a48companyAccreditedinvestor1 = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor_1");

_
    .fill("true_a2_part3_entity_qualificationstatement", condition = isEntity && isYesSec2a48companyAccreditedinvestor1, disabled = true)
	.fill("false_a2_part3_entity_qualificationstatement", condition = isEntity && !isYesSec2a48companyAccreditedinvestor1, disabled = true)
    .apply(logic.a2_part3_entity_qualificationstatement, ctx = a2_part3_entity_qualificationstatement)

function(
	b_part3_entity_qualificationstatement,
	a1_part3_entity_qualificationstatement,
	a2_part3_entity_qualificationstatement,
	asa_commitment_amount,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isFalseA1Part3EntityQualificationstatement = a1_part3_entity_qualificationstatement.value == "false_a1_part3_entity_qualificationstatement";
local isFalseA2Part3EntityQualificationstatement = a2_part3_entity_qualificationstatement.value == "false_a2_part3_entity_qualificationstatement";
local isNumberGreaterThanOrEqual = utils.forceNum(asa_commitment_amount.value) >= 1100000;

_
    .activate(condition = isEntity && isFalseA1Part3EntityQualificationstatement && isFalseA2Part3EntityQualificationstatement)
	.fill("true_b_part3_entity_qualificationstatement", condition = isEntity && isNumberGreaterThanOrEqual, disabled = false)
    .apply(logic.b_part3_entity_qualificationstatement, ctx = b_part3_entity_qualificationstatement)

function(
	c_part3_entity_qualificationstatement,
	a1_part3_entity_qualificationstatement,
	a2_part3_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isFalseA1Part3EntityQualificationstatement = a1_part3_entity_qualificationstatement.value == "false_a1_part3_entity_qualificationstatement";
local isFalseA2Part3EntityQualificationstatement = a2_part3_entity_qualificationstatement.value == "false_a2_part3_entity_qualificationstatement";
local isTrueA1Part3EntityQualificationstatement = a1_part3_entity_qualificationstatement.value == "true_a1_part3_entity_qualificationstatement";
local isTrueA2Part3EntityQualificationstatement = a2_part3_entity_qualificationstatement.value == "true_a2_part3_entity_qualificationstatement";

_
    .deactivate(condition = (isEntity && isFalseA1Part3EntityQualificationstatement && isFalseA2Part3EntityQualificationstatement) || !(isEntity && (isTrueA1Part3EntityQualificationstatement || isTrueA2Part3EntityQualificationstatement)))
    .apply(logic.c_part3_entity_qualificationstatement, ctx = c_part3_entity_qualificationstatement)

function(
	d_part3_entity_qualificationstatement,
	a1_part3_entity_qualificationstatement,
	a2_part3_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isFalseA1Part3EntityQualificationstatement = a1_part3_entity_qualificationstatement.value == "false_a1_part3_entity_qualificationstatement";
local isFalseA2Part3EntityQualificationstatement = a2_part3_entity_qualificationstatement.value == "false_a2_part3_entity_qualificationstatement";
local isTrueA1Part3EntityQualificationstatement = a1_part3_entity_qualificationstatement.value == "true_a1_part3_entity_qualificationstatement";
local isTrueA2Part3EntityQualificationstatement = a2_part3_entity_qualificationstatement.value == "true_a2_part3_entity_qualificationstatement";

_
    .deactivate(condition = (isEntity && isFalseA1Part3EntityQualificationstatement && isFalseA2Part3EntityQualificationstatement) || !(isEntity && (isTrueA1Part3EntityQualificationstatement || isTrueA2Part3EntityQualificationstatement)))
    .apply(logic.d_part3_entity_qualificationstatement, ctx = d_part3_entity_qualificationstatement)

function(
	represent_checkbox_for_part_ivb,
	gr_shortcut,
	subscriber_is_a_company_formed_april30,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isOptTrue = subscriber_is_a_company_formed_april30.value == "opt_true";

_
    .show(condition = isEntity && isOptTrue)
    .apply(logic.represent_checkbox_for_part_ivb, ctx = represent_checkbox_for_part_ivb)

function(
	a3_part5_entity_qualificationstatement,
	a1_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrueA1Part5EntityQualificationstatement = a1_part5_entity_qualificationstatement.value == "true_a1_part5_entity_qualificationstatement";

_
    .fill("false_a3_part5_entity_qualificationstatement", condition = isEntity && isTrueA1Part5EntityQualificationstatement, disabled = true)
    .apply(logic.a3_part5_entity_qualificationstatement, ctx = a3_part5_entity_qualificationstatement)

function(
	a4_part5_entity_qualificationstatement,
	a1_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrueA1Part5EntityQualificationstatement = a1_part5_entity_qualificationstatement.value == "true_a1_part5_entity_qualificationstatement";

_
    .fill("false_a4_part5_entity_qualificationstatement", condition = isEntity && isTrueA1Part5EntityQualificationstatement, disabled = true)
    .apply(logic.a4_part5_entity_qualificationstatement, ctx = a4_part5_entity_qualificationstatement)

function(
	a7_part5_entity_qualificationstatement,
	a5_part5_entity_qualificationstatement,
	a6_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrueA5Part5EntityQualificationstatement = a5_part5_entity_qualificationstatement.value == "true_a5_part5_entity_qualificationstatement";
local isTrueA6Part5EntityQualificationstatement = a6_part5_entity_qualificationstatement.value == "true_a6_part5_entity_qualificationstatement";

_
    .fill("false_a7_part5_entity_qualificationstatement", condition = isEntity && (isTrueA5Part5EntityQualificationstatement || isTrueA6Part5EntityQualificationstatement), disabled = true)
    .apply(logic.a7_part5_entity_qualificationstatement, ctx = a7_part5_entity_qualificationstatement)

function(
	group_c1_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isCorporation = entity_blueprint_investor_type.value == "a corporation";

_
    .fill([
		"c1_part5_entity_qualificationstatement",
	], condition = isEntity && isCorporation, disabled = true)
    .apply(logic.group_c1_part5_entity_qualificationstatement, ctx = group_c1_part5_entity_qualificationstatement)

function(
	group_c2_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isGeneralPartnership = entity_blueprint_investor_type.value == "a general partnership";

_
    .fill([
		"c2_part5_entity_qualificationstatement",
	], condition = isEntity && isGeneralPartnership, disabled = true)
    .apply(logic.group_c2_part5_entity_qualificationstatement, ctx = group_c2_part5_entity_qualificationstatement)

function(
	group_c3_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isLimitedPartnership = entity_blueprint_investor_type.value == "a limited partnership";

_
    .fill([
		"c3_part5_entity_qualificationstatement",
	], condition = isEntity && isLimitedPartnership, disabled = true)
    .apply(logic.group_c3_part5_entity_qualificationstatement, ctx = group_c3_part5_entity_qualificationstatement)

function(
	group_c4_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isLimitedLiabilityCompany = entity_blueprint_investor_type.value == "a limited liability company";

_
    .fill([
		"c4_part5_entity_qualificationstatement",
	], condition = isEntity && isLimitedLiabilityCompany, disabled = true)
    .apply(logic.group_c4_part5_entity_qualificationstatement, ctx = group_c4_part5_entity_qualificationstatement)

function(
	group_c6_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrustOfFollowingType = entity_blueprint_investor_type.value == "a trust of the following type";

_
    .fill([
		"c6_part5_entity_qualificationstatement",
	], condition = isEntity && isTrustOfFollowingType, disabled = true)
    .apply(logic.group_c6_part5_entity_qualificationstatement, ctx = group_c6_part5_entity_qualificationstatement)

function(
	group_c7_part5_entity_qualificationstatement,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isFollowingOtherFormOfEntity = entity_blueprint_investor_type.value == "the following other form of entity";

_
    .fill([
		"c7_part5_entity_qualificationstatement",
	], condition = isEntity && isFollowingOtherFormOfEntity, disabled = true)
    .apply(logic.group_c7_part5_entity_qualificationstatement, ctx = group_c7_part5_entity_qualificationstatement)

function(
	group_c5_part5_entity_qualificationstatement,
	gating_if_unincorporated_agency,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isCheckIfYes = std.member(gating_if_unincorporated_agency.value, "check_if_yes");

_
    .fill([
		"c5_part5_entity_qualificationstatement",
	], condition = isEntity && isCheckIfYes, disabled = true)
    .apply(logic.group_c5_part5_entity_qualificationstatement, ctx = group_c5_part5_entity_qualificationstatement)

function(
	asa_fullname_investorname_generalinfo2,
	asa_fullname_investorname_generalinfo,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;



_
    .fill(asa_fullname_investorname_generalinfo.value, condition = true, disabled = false)
    .apply(logic.asa_fullname_investorname_generalinfo2, ctx = asa_fullname_investorname_generalinfo2)

function(
	bluewell3,
	parta_kyc,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesPartaKyc = parta_kyc.value == "yes_parta_kyc";

_
    .show(condition = isYesPartaKyc)
    .apply(logic.bluewell3, ctx = bluewell3)

function(
	filegroup2,
	exemptedclient_partb_kyc,
	gating_for_part_d,
	gr_shortcut,
	group_eligibleintro_partb_kyc,
	nominee_partb_kyc,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesNomineePartbKyc = nominee_partb_kyc.value == "yes_nominee_partb_kyc";
local isEligibleintroPartbKyc = std.member(group_eligibleintro_partb_kyc.value, "eligibleintro_partb_kyc");
local isEntity = gr_shortcut.value == "entity";
local isOneExemptedclientPartbKyc = std.member(exemptedclient_partb_kyc.value, "one_exemptedclient_partb_kyc");
local isTwoExemptedclientPartbKyc = std.member(exemptedclient_partb_kyc.value, "two_exemptedclient_partb_kyc");
local isThreeExemptedclientPartbKyc = std.member(exemptedclient_partb_kyc.value, "three_exemptedclient_partb_kyc");
local isFourExemptedclientPartbKyc = std.member(exemptedclient_partb_kyc.value, "four_exemptedclient_partb_kyc");
local isFiveExemptedclientPartbKyc = std.member(exemptedclient_partb_kyc.value, "five_exemptedclient_partb_kyc");
local isCheckIfYes = std.member(gating_for_part_d.value, "check_if_yes");

_
    .enableFiles([
		"file1",
	], condition = isYesNomineePartbKyc)
	.enableFiles([
		"file2",
	], condition = isEligibleintroPartbKyc)
	.enableFiles([
		"file3",
	], condition = isEntity && isOneExemptedclientPartbKyc)
	.enableFiles([
		"file4",
	], condition = isEntity && !isOneExemptedclientPartbKyc && isTwoExemptedclientPartbKyc)
	.enableFiles([
		"file5",
	], condition = isEntity && !(isOneExemptedclientPartbKyc && isTwoExemptedclientPartbKyc) && isThreeExemptedclientPartbKyc)
	.enableFiles([
		"file6",
	], condition = isEntity && !(isOneExemptedclientPartbKyc && isTwoExemptedclientPartbKyc && isThreeExemptedclientPartbKyc) && isFourExemptedclientPartbKyc)
	.enableFiles([
		"file7",
	], condition = isEntity && !(isOneExemptedclientPartbKyc && isTwoExemptedclientPartbKyc && isThreeExemptedclientPartbKyc && isFourExemptedclientPartbKyc) && isFiveExemptedclientPartbKyc)
	.enableFiles([
		"file8",
	], condition = isEntity && isCheckIfYes)
    .apply(logic.filegroup2, ctx = filegroup2)

function(
	bluewell5,
	nominee_partb_kyc,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesNomineePartbKyc = nominee_partb_kyc.value == "yes_nominee_partb_kyc";

_
    .show(condition = isYesNomineePartbKyc)
    .apply(logic.bluewell5, ctx = bluewell5)

function(
	bluewell6,
	group_eligibleintro_partb_kyc,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEligibleintroPartbKyc = std.member(group_eligibleintro_partb_kyc.value, "eligibleintro_partb_kyc");

_
    .show(condition = isEligibleintroPartbKyc)
    .apply(logic.bluewell6, ctx = bluewell6)

function(
	asa_nationality_generalinfo,
	gating_for_nationality,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isCheckIfYes = std.member(gating_for_nationality.value, "check_if_yes");
local isEntity = gr_shortcut.value == "entity";

_
    .hide(condition = !(isIndividual && isCheckIfYes) || isEntity)
    .apply(logic.asa_nationality_generalinfo, ctx = asa_nationality_generalinfo)

function(
	exemptedclient_partb_kyc,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .hide(condition = isIndividual)
    .apply(logic.exemptedclient_partb_kyc, ctx = exemptedclient_partb_kyc)

function(
	type_of_subscriber_for_the_purpose_of_de,
	entity_blueprint_investor_type,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isEntity = gr_shortcut.value == "entity";
local isCorporation = entity_blueprint_investor_type.value == "a corporation";
local isGeneralPartnership = entity_blueprint_investor_type.value == "a general partnership";
local isLimitedPartnership = entity_blueprint_investor_type.value == "a limited partnership";
local isLimitedLiabilityCompany = entity_blueprint_investor_type.value == "a limited liability company";
local isTrustOfFollowingType = entity_blueprint_investor_type.value == "a trust of the following type";

_
    .fill("individual_natural_person", condition = isIndividual, disabled = true)
	.addDeactivatedOptions([
	    "individual_natural_person"
	], condition = isEntity)
	.fill("company", condition = isEntity && isCorporation, disabled = false)
	.fill("partnership", condition = isEntity && !isCorporation && isGeneralPartnership, disabled = false)
	.fill("partnership", condition = isEntity && !(isCorporation && isGeneralPartnership) && isLimitedPartnership, disabled = false)
	.fill("limited_liability_company", condition = isEntity && !(isCorporation && isGeneralPartnership && isLimitedPartnership) && isLimitedLiabilityCompany, disabled = false)
	.fill("trust", condition = isEntity && !(isCorporation && isGeneralPartnership && isLimitedPartnership && isLimitedLiabilityCompany) && isTrustOfFollowingType, disabled = false)
    .apply(logic.type_of_subscriber_for_the_purpose_of_de, ctx = type_of_subscriber_for_the_purpose_of_de)

function(
	filegroup3,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isIndividualNaturalPerson = type_of_subscriber_for_the_purpose_of_de.value == "individual_natural_person";

_
    .enableFiles([
		"file1",
		"file2",
	], condition = isIndividual && isIndividualNaturalPerson)
    .apply(logic.filegroup3, ctx = filegroup3)

function(
	individual_partc_kyc,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isIndividualNaturalPerson = type_of_subscriber_for_the_purpose_of_de.value == "individual_natural_person";

_
    .fill([
		"one_individual_partc_kyc",
		"two_individual_partc_kyc",
	], condition = isIndividual && isIndividualNaturalPerson, disabled = false)
    .apply(logic.individual_partc_kyc, ctx = individual_partc_kyc)

function(
	gating_for_nationality,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .hide(condition = isEntity)
    .apply(logic.gating_for_nationality, ctx = gating_for_nationality)

function(
	filegroup4,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isCompany = type_of_subscriber_for_the_purpose_of_de.value == "company";

_
    .enableFiles([
		"file1",
		"file2",
		"file3",
		"file4",
		"file5",
		"file6",
		"file7",
	], condition = isEntity && isCompany)
    .apply(logic.filegroup4, ctx = filegroup4)

function(
	company_partc_kyc,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isCompany = type_of_subscriber_for_the_purpose_of_de.value == "company";

_
    .fill([
		"one_company_partc_kyc",
		"two_company_partc_kyc",
		"three_company_partc_kyc",
		"five_company_partc_kyc",
		"six_company_partc_kyc",
		"seven_company_partc_kyc",
		"eight_company_partc_kyc",
	], condition = isEntity && isCompany, disabled = false)
    .apply(logic.company_partc_kyc, ctx = company_partc_kyc)

function(
	filegroup5,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isPartnership = type_of_subscriber_for_the_purpose_of_de.value == "partnership";

_
    .enableFiles([
		"file1",
		"file2",
		"file3",
		"file4",
		"file5",
	], condition = isEntity && isPartnership)
    .apply(logic.filegroup5, ctx = filegroup5)

function(
	partnership_partc_kyc,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isPartnership = type_of_subscriber_for_the_purpose_of_de.value == "partnership";

_
    .fill([
		"one_partnership_partc_kyc",
		"two_partnership_partc_kyc",
		"four_partnership_partc_kyc",
		"five_partnership_partc_kyc",
		"six_partnership_partc_kyc",
	], condition = isEntity && isPartnership, disabled = false)
    .apply(logic.partnership_partc_kyc, ctx = partnership_partc_kyc)

function(
	filegroup6,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrust = type_of_subscriber_for_the_purpose_of_de.value == "trust";

_
    .enableFiles([
		"file1",
		"file2",
		"file3",
		"file4",
	], condition = isEntity && isTrust)
    .apply(logic.filegroup6, ctx = filegroup6)

function(
	trust_partc_kyc,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isTrust = type_of_subscriber_for_the_purpose_of_de.value == "trust";

_
    .fill([
		"one_trust_partc_kyc",
		"two_trust_partc_kyc",
		"four_trust_partc_kyc",
		"three_trust_partc_kyc",
	], condition = isEntity && isTrust, disabled = false)
    .apply(logic.trust_partc_kyc, ctx = trust_partc_kyc)

function(
	filegroup7,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isLimitedLiabilityCompany = type_of_subscriber_for_the_purpose_of_de.value == "limited_liability_company";

_
    .enableFiles([
		"file1",
		"file2",
		"file3",
		"file4",
		"file5",
	], condition = isEntity && isLimitedLiabilityCompany)
    .apply(logic.filegroup7, ctx = filegroup7)

function(
	llc_partc_kyc,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isLimitedLiabilityCompany = type_of_subscriber_for_the_purpose_of_de.value == "limited_liability_company";

_
    .fill([
		"one_llc_partc_kyc",
		"two_llc_partc_kyc",
		"three_llc_partc_kyc",
		"four_llc_partc_kyc",
		"five_llc_partc_kyc",
	], condition = isEntity && isLimitedLiabilityCompany, disabled = false)
    .apply(logic.llc_partc_kyc, ctx = llc_partc_kyc)

function(
	filegroup8,
	gr_shortcut,
	type_of_subscriber_for_the_purpose_of_de,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isNoneOfAbove = type_of_subscriber_for_the_purpose_of_de.value == "none_of_the_above";

_
    .enableFiles([
		"file1",
		"file2",
		"file3",
		"file4",
		"file5",
	], condition = isEntity && isNoneOfAbove)
    .apply(logic.filegroup8, ctx = filegroup8)

function(
	bluewell4,
	gating_for_part_d,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isCheckIfYes = std.member(gating_for_part_d.value, "check_if_yes");

_
    .show(condition = isEntity && isCheckIfYes)
    .apply(logic.bluewell4, ctx = bluewell4)

function(
	asa_fullname_investorname_generalinfo3,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual, disabled = false)
    .apply(logic.asa_fullname_investorname_generalinfo3, ctx = asa_fullname_investorname_generalinfo3)

function(
	individualname_signaturepage,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual, disabled = false)
    .apply(logic.individualname_signaturepage, ctx = individualname_signaturepage)

function(
	individualname_aml_signaturepage,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual, disabled = false)
    .apply(logic.individualname_aml_signaturepage, ctx = individualname_aml_signaturepage)

function(
	subscribername_naturalperson_signaturepage_individual_qualificationstatement,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual, disabled = false)
    .apply(logic.subscribername_naturalperson_signaturepage_individual_qualificationstatement, ctx = subscribername_naturalperson_signaturepage_individual_qualificationstatement)

function(
	lp_individual_signatory,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isIndividualHumanBeing = individual_blueprint_investor_type.value == "an individual human being";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";

_
    .show(condition = isIndividual && (isIndividualHumanBeing || isJointTenancy))
    .apply(logic.lp_individual_signatory, ctx = lp_individual_signatory)

function(
	lp_subscriber_spouse,
	gr_shortcut,
	individual_blueprint_investor_type,
	investing_jointly_with_your_spouse,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";
local isOptYes = investing_jointly_with_your_spouse.value == "opt_yes";

_
    .show(condition = isIndividual && isJointTenancy && isOptYes)
    .apply(logic.lp_subscriber_spouse, ctx = lp_subscriber_spouse)

function(
	subscribername_alteregos_signaturepage_individual_qualificationstatement,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual, disabled = false)
    .apply(logic.subscribername_alteregos_signaturepage_individual_qualificationstatement, ctx = subscribername_alteregos_signaturepage_individual_qualificationstatement)

function(
	lp_alter_egos_of_natural_persons_signato,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isIndividualRetirementAccount = individual_blueprint_investor_type.value == "an individual retirement account";
local isRevocableTrustSoleSettlorOfWhichWas = individual_blueprint_investor_type.value == "a revocable trust the sole settlor of which was";
local isSelfDirectedRetirementPlan = individual_blueprint_investor_type.value == "a self directed retirement plan";

_
    .show(condition = isIndividual && (isIndividualRetirementAccount || isRevocableTrustSoleSettlorOfWhichWas || isSelfDirectedRetirementPlan))
    .apply(logic.lp_alter_egos_of_natural_persons_signato, ctx = lp_alter_egos_of_natural_persons_signato)

function(
	entityname_signaturepage,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isEntity, disabled = false)
    .apply(logic.entityname_signaturepage, ctx = entityname_signaturepage)

function(
	subscribername_signaturepage_part5_entity_qualificationstatement,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isEntity, disabled = false)
    .apply(logic.subscribername_signaturepage_part5_entity_qualificationstatement, ctx = subscribername_signaturepage_part5_entity_qualificationstatement)

function(
	entityname_aml_signaturepage,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isEntity, disabled = false)
    .apply(logic.entityname_aml_signaturepage, ctx = entityname_aml_signaturepage)

function(
	lp_authorized_representative,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .show(condition = isEntity)
    .apply(logic.lp_authorized_representative, ctx = lp_authorized_representative)

function(
	group_asa_own5m_entity_qualifiedpurchaser,
	group_asa_trust_qualifiedpurchaser,
	gr_shortcut,
	fakeCheckbox_group_asa_trust_qualifiedpurchaser,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local checkbox = _.fakeCheckbox
	.addKey("group_asa_own5m_entity_qualifiedpurchaser", group_asa_own5m_entity_qualifiedpurchaser, logic.group_asa_own5m_entity_qualifiedpurchaser)
	.addKey("group_asa_trust_qualifiedpurchaser", group_asa_trust_qualifiedpurchaser, logic.group_asa_trust_qualifiedpurchaser)
    .init(
        logic.fakeCheckbox_group_asa_trust_qualifiedpurchaser, fakeCheckbox_group_asa_trust_qualifiedpurchaser
    );

local isEntity = gr_shortcut.value == "entity";

_
    .excludeOptionGroups({
	    "group_asa_trust_qualifiedpurchaser": [
	        "asa_trust_qualifiedpurchaser"
	    ]
	}, {
	    "group_asa_own5m_entity_qualifiedpurchaser": [
	        "asa_own5m_entity_qualifiedpurchaser"
	    ]
	}, condition = isEntity, disabled = false)
    .applyFakeCheckbox(checkbox, [
        _
		    .unit
		    .applyKey("group_asa_trust_qualifiedpurchaser"),
		    
		_
		    .unit
		    .applyKey("group_asa_own5m_entity_qualifiedpurchaser"),
		    
    ])

function(
	group_b1_part5_entity_qualificationstatement,
	group_b2_part5_entity_qualificationstatement,
	gr_shortcut,
	fakeCheckbox_group_b2_part5_entity_qualificationstatement,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local checkbox = _.fakeCheckbox
	.addKey("group_b1_part5_entity_qualificationstatement", group_b1_part5_entity_qualificationstatement, logic.group_b1_part5_entity_qualificationstatement)
	.addKey("group_b2_part5_entity_qualificationstatement", group_b2_part5_entity_qualificationstatement, logic.group_b2_part5_entity_qualificationstatement)
    .init(
        logic.fakeCheckbox_group_b2_part5_entity_qualificationstatement, fakeCheckbox_group_b2_part5_entity_qualificationstatement
    );

local isEntity = gr_shortcut.value == "entity";

_
    .excludeOptionGroups({
	    "group_b1_part5_entity_qualificationstatement": [
	        "b1_part5_entity_qualificationstatement"
	    ]
	}, {
	    "group_b2_part5_entity_qualificationstatement": [
	        "b2_part5_entity_qualificationstatement"
	    ]
	}, condition = isEntity, disabled = false)
    .applyFakeCheckbox(checkbox, [
        _
		    .unit
		    .applyKey("group_b1_part5_entity_qualificationstatement"),
		    
		_
		    .unit
		    .applyKey("group_b2_part5_entity_qualificationstatement"),
		    
    ])
"
`;
