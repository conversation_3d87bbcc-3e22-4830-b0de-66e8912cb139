// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[MultipleCheckbox] Test 1: [fn_add_value][fn_has_option_values][blueprint_investor_types][scope:Individual] 1`] = `
"function(
	group_asa_own5m_entity_qualifiedpurchaser,
	group_asa_trust_qualifiedpurchaser,
	organizationtype_part5_entity_qualificationstatement,
	gr_shortcut,
	fakeCheckbox_organizationtype_part5_entity_qualificationstatement,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local checkbox = _.fakeCheckbox
	.addKey("group_asa_own5m_entity_qualifiedpurchaser", group_asa_own5m_entity_qualifiedpurchaser, logic.group_asa_own5m_entity_qualifiedpurchaser)
	.addKey("group_asa_trust_qualifiedpurchaser", group_asa_trust_qualifiedpurchaser, logic.group_asa_trust_qualifiedpurchaser)
	.addKey("organizationtype_part5_entity_qualificationstatement", organizationtype_part5_entity_qualificationstatement, logic.organizationtype_part5_entity_qualificationstatement)
    .init(
        logic.fakeCheckbox_organizationtype_part5_entity_qualificationstatement, fakeCheckbox_organizationtype_part5_entity_qualificationstatement
    );

local isIndividual = gr_shortcut.value == "indi";

_
    .excludeOptionGroups({
	    "group_asa_trust_qualifiedpurchaser": [
	        "asa_trust_qualifiedpurchaser"
	    ],
	    "organizationtype_part5_entity_qualificationstatement": [
	        "nonprofitorganization_organizationtype_part5_entity_qualificationstatement",
	        "municipalgovernmententity_organizationtype_part5_entity_qualificationstatement"
	    ]
	}, {
	    "group_asa_own5m_entity_qualifiedpurchaser": [
	        "asa_own5m_entity_qualifiedpurchaser"
	    ]
	}, condition = isIndividual, disabled = false)
    .applyFakeCheckbox(checkbox, [
        _
		    .unit
		    .applyKey("group_asa_trust_qualifiedpurchaser"),
		    
		_
		    .activate(condition = isIndividual)
			.fill("nonprofitorganization_organizationtype_part5_entity_qualificationstatement", condition = isIndividual, disabled = false)
		    .applyKey("organizationtype_part5_entity_qualificationstatement"),
		    
		_
		    .unit
		    .applyKey("group_asa_own5m_entity_qualifiedpurchaser"),
		    
    ])
"
`;

exports[`[MultipleCheckbox] Test 2: [fn_add_value][fn_has_option_values][blueprint_investor_types][scope:Individual] 1`] = `
"function(
	organizationtype_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .excludeOptionGroups([
	    "investmentcompany_organizationtype_part5_entity_qualificationstatement_4"
	],
	[
	    "nonprofitorganization_organizationtype_part5_entity_qualificationstatement",
	    "municipalgovernmententity_organizationtype_part5_entity_qualificationstatement"
	], condition = isIndividual, disabled = false)
    .apply(logic.organizationtype_part5_entity_qualificationstatement, ctx = organizationtype_part5_entity_qualificationstatement)
"
`;

exports[`[MultipleCheckbox] Test 3: [fn_disable_and_set_value][fn_has_option_values] - Multiple Data Source from pre-filled data 1`] = `
"function(
	group_asa_trust_qualifiedpurchaser,
	asa_accreditedinvestor_accreditedinvestor,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesSec2a48companyAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor");

_
    .fill([
		"asa_trust_qualifiedpurchaser",
		"municipalgovernmententity_organizationtype_part5_entity_qualificationstatement",
	], condition = isYesSec2a48companyAccreditedinvestor, disabled = true)
    .apply(logic.group_asa_trust_qualifiedpurchaser, ctx = group_asa_trust_qualifiedpurchaser)
"
`;

exports[`[MultipleCheckbox] Test 4: [fn_disable_and_set_value][fn_has_option_values] 1`] = `
"function(
	group_asa_trust_qualifiedpurchaser,
	asa_accreditedinvestor_accreditedinvestor,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesSec2a48companyAccreditedinvestor = std.member(asa_accreditedinvestor_accreditedinvestor.value, "asa_yes_sec2a48company_accreditedinvestor");

_
    .fill([
		"asa_trust_qualifiedpurchaser",
	], condition = isYesSec2a48companyAccreditedinvestor, disabled = true)
    .apply(logic.group_asa_trust_qualifiedpurchaser, ctx = group_asa_trust_qualifiedpurchaser)
"
`;

exports[`[MultipleCheckbox] Test 5: [fn_enable_options] 1`] = `
"function(
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .addDeactivatedOptions([
	    "asa_yes_trust_accreditedinvestor",
	    "asa_yes_familyoffice_accreditedinvestor",
	    "asa_yes_otherown5m_otherentity_accreditedinvestor"
	], condition = !isIndividual)
    .apply(logic.asa_accreditedinvestor_accreditedinvestor, ctx = asa_accreditedinvestor_accreditedinvestor)
"
`;

exports[`[MultipleCheckbox] Test 6: [fn_disable_options_and_clear_data] 1`] = `
"function(
	asa_accreditedinvestor_accreditedinvestor,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .addDeactivatedOptions([
	    "asa_yes_trust_accreditedinvestor"
	], condition = isEntity)
    .apply(logic.asa_accreditedinvestor_accreditedinvestor, ctx = asa_accreditedinvestor_accreditedinvestor)
"
`;
