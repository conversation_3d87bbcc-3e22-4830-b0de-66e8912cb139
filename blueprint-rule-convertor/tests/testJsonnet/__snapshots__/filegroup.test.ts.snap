// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[Filegroup] Test 1: [fn_add_value][fn_check_investor_types][fn_has_option_values][blueprint_investor_types][scope:Individual] 1`] = `
"function(
	filegroup1,
	entity_blueprint_investor_type,
	gr_shortcut,
	individual_blueprint_investor_type,
	investing_jointly_with_your_spouse,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";
local isLimitedLiabilityCompany = entity_blueprint_investor_type.value == "a limited liability company";
local isOptYes = investing_jointly_with_your_spouse.value == "opt_yes";

_
    .enableFiles([
		"file1",
	], condition = isIndividual && (isJointTenancy || isLimitedLiabilityCompany))
	.enableFiles([
		"file2",
	], condition = isIndividual && !((isJointTenancy || isLimitedLiabilityCompany)) && isOptYes)
    .apply(logic.filegroup1, ctx = filegroup1)
"
`;

exports[`[Filegroup] Test 2: [fn_show][fn_has_all_option_values][fn_decimal_gteq][scope:Common] 1`] = `
"function(
	filegroup4,
	asa_commitment_amount,
	parta_kyc,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isYesPartaKyc = parta_kyc.value == "yes_parta_kyc";
local isNoPartaKyc = parta_kyc.value == "no_parta_kyc";
local isNumberGreaterThanOrEqual = utils.forceNum(asa_commitment_amount.value) >= 1100000;

_
    .show(condition = (isYesPartaKyc && isNoPartaKyc) || isNumberGreaterThanOrEqual)
    .apply(logic.filegroup4, ctx = filegroup4)
"
`;

exports[`[Filegroup] Test 3: [fn_hide][scope:Entity] 1`] = `
"function(
	filegroup3,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";

_
    .hide(condition = isEntity)
    .apply(logic.filegroup3, ctx = filegroup3)
"
`;

exports[`[Filegroup] Test 4: [fn_enable][fn_str_contains][blueprint_string_constants] 1`] = `
"function(
	filegroup3,
	country_ioi,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isEntity = gr_shortcut.value == "entity";
local isStringContained = utils.stringContains(country_ioi.value, "Sweden");

_
    .activate(condition = isEntity && isStringContained)
    .apply(logic.filegroup3, ctx = filegroup3)
"
`;

exports[`[Filegroup] Test 5: [fn_add_value][fn_check_investor_types][fn_has_option_values][blueprint_investor_types] Nested If-then-else 1`] = `
"function(
	filegroup1,
	individual_blueprint_investor_type,
	investing_jointly_with_your_spouse,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isOptYes = investing_jointly_with_your_spouse.value == "opt_yes";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";

_
    .enableFiles([
		"file2",
	], condition = isOptYes && isJointTenancy)
	.enableFiles([
		"file1",
	], condition = !(isOptYes && isJointTenancy) && isOptYes)
    .apply(logic.filegroup1, ctx = filegroup1)
"
`;
