// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[Radio] Test 1: [fn_set_value][fn_str_neq][blueprint_string_constants][do_nothing][scope:Individual] 1`] = `
"function(
	attention_formalnotcie,
	asa_company_primarycontact_contactinfo,
	asa_fullname_investorname_generalinfo,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isStringNotEqual = asa_company_primarycontact_contactinfo.value != "Sweden";

_
    .fill(asa_fullname_investorname_generalinfo.value, condition = isIndividual && isStringNotEqual, disabled = false)
    .apply(logic.attention_formalnotcie, ctx = attention_formalnotcie)

function(
	organizationtype_part5_entity_qualificationstatement,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";

_
    .fill("investmentcompany_organizationtype_part5_entity_qualificationstatement_4", condition = isIndividual && isJointTenancy, disabled = true)
    .apply(logic.organizationtype_part5_entity_qualificationstatement, ctx = organizationtype_part5_entity_qualificationstatement)
"
`;

exports[`[Radio] Test 2: [fn_label][scope:Individual] 1`] = `
"function(
	asa_fullname_investorname_generalinfo,
	asa_company_primarycontact_contactinfo,
	gr_shortcut,
	organizationtype_part5_entity_qualificationstatement,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";

_
    .fill(utils.getLabel({
			"investmentcompany_organizationtype_part5_entity_qualificationstatement": "investmentcompany_organizationtype_part5_entity_qualificationstatement",
			"nonprofitorganization_organizationtype_part5_entity_qualificationstatement": "nonprofitorganization_organizationtype_part5_entity_qualificationstatement",
			"other_organizationtype_part5_entity_qualificationstatement": "other_organizationtype_part5_entity_qualificationstatement",
			"banking_organizationtype_part5_entity_qualificationstatement": "banking_organizationtype_part5_entity_qualificationstatement",
			"pensionplan_organizationtype_part5_entity_qualificationstatement": "pensionplan_organizationtype_part5_entity_qualificationstatement",
			"municipalgovernmententity_organizationtype_part5_entity_qualificationstatement": "municipalgovernmententity_organizationtype_part5_entity_qualificationstatement",
			"municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement": "municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement",
			"investmentcompany_organizationtype_part5_entity_qualificationstatement_4": "investmentcompany_organizationtype_part5_entity_qualificationstatement_4",
			"broker_organizationtype_part5_entity_qualificationstatement": "broker_organizationtype_part5_entity_qualificationstatement",
			"wealthfund_organizationtype_part5_entity_qualificationstatement": "wealthfund_organizationtype_part5_entity_qualificationstatement",
			"insurancecompany_organizationtype_part5_entity_qualificationstatement": "insurancecompany_organizationtype_part5_entity_qualificationstatement",
	    })(organizationtype_part5_entity_qualificationstatement.value, [
		"other_organizationtype_part5_entity_qualificationstatement",
	], [
		asa_company_primarycontact_contactinfo.value,
	]), condition = isIndividual, disabled = true)
    .apply(logic.asa_fullname_investorname_generalinfo, ctx = asa_fullname_investorname_generalinfo)
"
`;

exports[`[Radio] Test 3: [fn_disable_options_and_add_value][fn_str_neq][blueprint_string_constants][do_nothing][scope:Individual] 1`] = `
"function(
	organizationtype_part5_entity_qualificationstatement,
	gr_shortcut,
	individual_blueprint_investor_type,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isJointTenancy = individual_blueprint_investor_type.value == "a joint tenancy";

_
    .fill("investmentcompany_organizationtype_part5_entity_qualificationstatement_4", condition = isIndividual && isJointTenancy, disabled = true)
    .apply(logic.organizationtype_part5_entity_qualificationstatement, ctx = organizationtype_part5_entity_qualificationstatement)
"
`;
