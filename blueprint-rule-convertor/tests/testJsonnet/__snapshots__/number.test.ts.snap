// Bun Snapshot v1, https://goo.gl/fbAQLP

exports[`[Number] Test 1: [fn_set_error][fn_integer_eq][const:commitment_higher_than_100k][do_nothing] 1`] = `
"function(
	maxpercentage_suba5_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isNumberEqual = utils.forceNum(maxpercentage_suba5_part5_entity_qualificationstatement.value) == 0;

_
    .setError("The commitment amount must higher than 100,000 EUR.", condition = isIndividual && isNumberEqual)
    .apply(logic.maxpercentage_suba5_part5_entity_qualificationstatement, ctx = maxpercentage_suba5_part5_entity_qualificationstatement)
"
`;

exports[`[Number] Test 2: [fn_set_value][fn_integer_divisible][fn_divide_value][do_nothing] 1`] = `
"function(
	maxpercentage_suba5_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isNumberEqual = utils.forceNum(maxpercentage_suba5_part5_entity_qualificationstatement.value) % 3 == 0;

_
    .fill(utils.forceNum(maxpercentage_suba5_part5_entity_qualificationstatement.value) / 3, condition = isIndividual && isNumberEqual, disabled = false)
    .apply(logic.maxpercentage_suba5_part5_entity_qualificationstatement, ctx = maxpercentage_suba5_part5_entity_qualificationstatement)
"
`;

exports[`[Number] Test 3: [fn_set_value][fn_integer_divisible][fn_divide_value][do_nothing] 1`] = `
"function(
	maxpercentage_suba5_part5_entity_qualificationstatement,
	gr_shortcut,
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local isIndividual = gr_shortcut.value == "indi";
local isNumberEqual = 3 % utils.forceNum(maxpercentage_suba5_part5_entity_qualificationstatement.value) == 0;

_
    .fill(3 / utils.forceNum(maxpercentage_suba5_part5_entity_qualificationstatement.value), condition = isIndividual && isNumberEqual, disabled = false)
    .apply(logic.maxpercentage_suba5_part5_entity_qualificationstatement, ctx = maxpercentage_suba5_part5_entity_qualificationstatement)
"
`;
