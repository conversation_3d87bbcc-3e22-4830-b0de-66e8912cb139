import { expect, test, describe, beforeEach } from "bun:test";

import { getCatalaCode, intitilizeTemplateGeneratorTest } from "../utils";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";

describe("[Number]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Test 1: [fn_set_error][fn_integer_eq][const:commitment_higher_than_100k][do_nothing]", async () => {
        const catalaCode = getCatalaCode(
            `definition maxpercentage_suba5_part5_entity_qualificationstatement__fn_set_error equals
                if (fn_integer_eq of maxpercentage_suba5_part5_entity_qualificationstatement, 0) then
                    blueprint_string_constants.commitment_higher_than_100k
                else
                    do_nothing
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 2: [fn_set_value][fn_integer_divisible][fn_divide_value][do_nothing]", async () => {
        const catalaCode = getCatalaCode(
            `definition maxpercentage_suba5_part5_entity_qualificationstatement__fn_set_value equals
                if (fn_integer_divisible of maxpercentage_suba5_part5_entity_qualificationstatement, 3) then
                    fn_divide_value of maxpercentage_suba5_part5_entity_qualificationstatement, 3
                else
                    do_nothing
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 3: [fn_set_value][fn_integer_divisible][fn_divide_value][do_nothing]", async () => {
        const catalaCode = getCatalaCode(
            `definition maxpercentage_suba5_part5_entity_qualificationstatement__fn_set_value equals
                if (fn_integer_divisible of 3, maxpercentage_suba5_part5_entity_qualificationstatement) then
                    fn_divide_value of 3, maxpercentage_suba5_part5_entity_qualificationstatement
                else
                    do_nothing
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
});
