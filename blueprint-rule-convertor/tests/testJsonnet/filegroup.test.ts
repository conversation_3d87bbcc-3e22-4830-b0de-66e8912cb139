import { expect, test, describe, beforeEach } from "bun:test";

import {
    getCatalaCode,
    getScopeNameCatalaCode,
    intitilizeTemplateGeneratorTest,
} from "../utils";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";

describe("[Filegroup]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Test 1: [fn_add_value][fn_check_investor_types][fn_has_option_values][blueprint_investor_types][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition filegroup1__fn_add_value equals
                if fn_check_investor_types of 
                    [
                        blueprint_investor_types.a_joint_tenancy;
                        blueprint_investor_types.a_limited_liability_company;
                    ]
                then
                    [
                        filegroup1_fileitems.file1
                    ]
                else 
                    if fn_has_option_values of investing_jointly_with_your_spouse, 
                    [
                        investing_jointly_with_your_spouse_options.opt_yes
                    ]
                    then
                    [
                        filegroup1_fileitems.file2
                    ]
                    else []`,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 2: [fn_show][fn_has_all_option_values][fn_decimal_gteq][scope:Common]", async () => {
        const catalaCode = getCatalaCode(
            `definition filegroup4__fn_show equals
                (fn_has_all_option_values of parta_kyc, 
                    [
                        parta_kyc_options.yes_parta_kyc;
                        parta_kyc_options.no_parta_kyc
                    ])
                or (fn_decimal_gteq of asa_commitment_amount, 1100000)`,
            ScopeInvestorType.Common,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 3: [fn_hide][scope:Entity]", async () => {
        const catalaCode = getCatalaCode(
            `definition filegroup3__fn_hide equals true`,
            ScopeInvestorType.Entity,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 4: [fn_enable][fn_str_contains][blueprint_string_constants]", async () => {
        const catalaCode = getCatalaCode(
            `definition filegroup3__fn_enable equals (fn_str_contains of country_ioi, blueprint_string_constants.sweden)`,
            ScopeInvestorType.Entity,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 5: [fn_add_value][fn_check_investor_types][fn_has_option_values][blueprint_investor_types] Nested If-then-else", async () => {
        const catalaCode = getScopeNameCatalaCode(
            `scope Common_Master_SIF_I_General_Information:
                definition filegroup1__fn_add_value equals
                    if fn_has_option_values of investing_jointly_with_your_spouse, 
                        [
                            investing_jointly_with_your_spouse_options.opt_yes
                        ]
                    then 
                        if fn_check_investor_types of [blueprint_investor_types.a_joint_tenancy] 
                        then 
                            [filegroup1_fileitems.file2]
                        else
                            [filegroup1_fileitems.file1]
                    else []
                        `,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
});
