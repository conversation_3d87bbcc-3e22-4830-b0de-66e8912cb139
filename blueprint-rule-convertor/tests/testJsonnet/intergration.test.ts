import { expect, test, describe, beforeEach } from "bun:test";

import { intitilizeTemplateGeneratorTest } from "../utils";
import { formLogic } from "../data/quantum_strategic_partner/quantum_getBlueprintVersionCatalaCode.json";

describe("[Integration Test]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Form Quantumn", async () => {
        expect(await getJsonnetRules(formLogic)).toMatchSnapshot();
    });
});
