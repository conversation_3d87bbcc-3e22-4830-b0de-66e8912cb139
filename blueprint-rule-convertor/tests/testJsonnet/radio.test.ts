import { expect, test, describe, beforeEach } from "bun:test";

import { getCatalaCode, intitilizeTemplateGeneratorTest } from "../utils";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";

describe("[Radio]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Test 1: [fn_set_value][fn_str_neq][blueprint_string_constants][do_nothing][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition attention_formalnotcie__fn_set_value equals
                if (fn_str_neq of asa_company_primarycontact_contactinfo, blueprint_string_constants.sweden) then
                    asa_fullname_investorname_generalinfo
                else
                    do_nothing
                
            definition organizationtype_part5_entity_qualificationstatement__fn_disable_and_set_value equals   
                if fn_check_investor_types of 
                    [
                        blueprint_investor_types.a_joint_tenancy
                    ]
                    then
                        [
                            organizationtype_part5_entity_qualificationstatement_options.investmentcompany_organizationtype_part5_entity_qualificationstatement_4;
                        ]
                    else []`,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 2: [fn_label][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition asa_fullname_investorname_generalinfo__fn_disable_and_set_value equals
                fn_label of organizationtype_part5_entity_qualificationstatement,
                    [organizationtype_part5_entity_qualificationstatement_options.other_organizationtype_part5_entity_qualificationstatement], 
                        [asa_company_primarycontact_contactinfo]`,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 3: [fn_disable_options_and_add_value][fn_str_neq][blueprint_string_constants][do_nothing][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition organizationtype_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals   
                if fn_check_investor_types of 
                    [
                        blueprint_investor_types.a_joint_tenancy
                    ]
                    then
                        [
                            organizationtype_part5_entity_qualificationstatement_options.investmentcompany_organizationtype_part5_entity_qualificationstatement_4;
                        ]
                    else []`,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
});
