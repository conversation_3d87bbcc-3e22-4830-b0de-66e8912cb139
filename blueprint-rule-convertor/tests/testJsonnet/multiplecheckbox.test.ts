import { expect, test, describe, beforeEach } from "bun:test";

import { getCatalaCode, intitilizeTemplateGeneratorTest } from "../utils";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";

describe("[MultipleCheckbox]", () => {
    let getJsonnetRules: (catalaContent: string) => Promise<string> = (_) =>
        Promise.resolve("");

    beforeEach(() => {
        getJsonnetRules = intitilizeTemplateGeneratorTest();
    });

    test("Test 1: [fn_add_value][fn_has_option_values][blueprint_investor_types][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition blueprint__fn_mutex_options equals
                [
                    group_asa_trust_qualifiedpurchaser_options.asa_trust_qualifiedpurchaser;
                    organizationtype_part5_entity_qualificationstatement_options.nonprofitorganization_organizationtype_part5_entity_qualificationstatement;
                    organizationtype_part5_entity_qualificationstatement_options.municipalgovernmententity_organizationtype_part5_entity_qualificationstatement
                ]
                ++
                [
                    group_asa_own5m_entity_qualifiedpurchaser_options.asa_own5m_entity_qualifiedpurchaser
                ]
            definition organizationtype_part5_entity_qualificationstatement__fn_enable equals true
            definition organizationtype_part5_entity_qualificationstatement__fn_add_value equals [
                organizationtype_part5_entity_qualificationstatement_options.nonprofitorganization_organizationtype_part5_entity_qualificationstatement
            ]
            `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });

    test("Test 2: [fn_add_value][fn_has_option_values][blueprint_investor_types][scope:Individual]", async () => {
        const catalaCode = getCatalaCode(
            `definition organizationtype_part5_entity_qualificationstatement__fn_mutex_options equals
                [
                    organizationtype_part5_entity_qualificationstatement_options.investmentcompany_organizationtype_part5_entity_qualificationstatement_4
                ]
                ++
                [
                    organizationtype_part5_entity_qualificationstatement_options.nonprofitorganization_organizationtype_part5_entity_qualificationstatement;
                    organizationtype_part5_entity_qualificationstatement_options.municipalgovernmententity_organizationtype_part5_entity_qualificationstatement
                ]
                
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
    test("Test 3: [fn_disable_and_set_value][fn_has_option_values] - Multiple Data Source from pre-filled data", async () => {
        const catalaCode = getCatalaCode(
            `definition group_asa_trust_qualifiedpurchaser__fn_disable_and_set_value equals
                if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, 
                    [
                        asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor
                    ]
                then
                    [
                        group_asa_trust_qualifiedpurchaser_options.asa_trust_qualifiedpurchaser;
                        organizationtype_part5_entity_qualificationstatement_options.municipalgovernmententity_organizationtype_part5_entity_qualificationstatement;
                    ]
                else []
                
                `,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
    test("Test 4: [fn_disable_and_set_value][fn_has_option_values]", async () => {
        const catalaCode = getCatalaCode(
            `definition group_asa_trust_qualifiedpurchaser__fn_disable_and_set_value equals
                if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, 
                    [
                        asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor
                    ]
                then
                    [
                        group_asa_trust_qualifiedpurchaser_options.asa_trust_qualifiedpurchaser;
                    ]
                else []
                
                `,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
    test("Test 5: [fn_enable_options]", async () => {
        const catalaCode = getCatalaCode(
            `definition asa_accreditedinvestor_accreditedinvestor__fn_enable_options equals
                [
                    asa_accreditedinvestor_accreditedinvestor_options.asa_yes_trust_accreditedinvestor;
                    asa_accreditedinvestor_accreditedinvestor_options.asa_yes_familyoffice_accreditedinvestor;
                    asa_accreditedinvestor_accreditedinvestor_options.asa_yes_otherown5m_otherentity_accreditedinvestor
                ]
                `,
            ScopeInvestorType.Individual,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
    test("Test 6: [fn_disable_options_and_clear_data]", async () => {
        const catalaCode = getCatalaCode(
            `definition asa_accreditedinvestor_accreditedinvestor__fn_disable_options_and_clear_data equals
                [
                    asa_accreditedinvestor_accreditedinvestor_options.asa_yes_trust_accreditedinvestor;
                ]
                `,
            ScopeInvestorType.Entity,
        );
        expect(await getJsonnetRules(catalaCode)).toMatchSnapshot();
    });
});
