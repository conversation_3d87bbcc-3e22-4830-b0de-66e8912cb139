import * as fs from "fs";

import * as PantheonModel from "@/models/stargazer/";
import { GetBlueprintVersionCatalaCodeResponse } from "@/models/stargazer";
import { BlueprintVersionModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";
import { ScopeInvestorType } from "@/models/stargazer/openapi/interface";
import { BlueprintGenerator } from "@/generator";
import { renderTemplate } from "@/utils/rule/renderTemplateUtils";
import { GetFormResponse } from "@anduintransaction/pangea/models/stargazer";
import { decodeFormData } from "@/utils/formData/formDataReader";
import { BlueprintApplicationResult } from "@/models/formMapping";

const getJsonnetRulesImpl =
    (
        blueprintData: GetBlueprintVersionCatalaCodeResponse,
        annotationDataResponses: Map<
            string,
            PantheonModel.GetAnnotationDataResponse
        >,
        latestBlueprintVersionModel: BlueprintVersionModel,
    ) =>
    async (catalaContent: string): Promise<string> => {
        const modifiedBlueprintData = {
            ...blueprintData,
            formLogic: catalaContent,
        };

        const generator = await BlueprintGenerator.create(
            modifiedBlueprintData,
            annotationDataResponses,
            latestBlueprintVersionModel.baseDocuments,
        );

        const templates = generator.getFormRuleTemplates();

        const rules = templates.map((template) => renderTemplate(template));

        return rules.map((r) => r.value).join("\n");
    };

export const getCatalaCode = (
    ruleContent: string,
    scope: ScopeInvestorType = ScopeInvestorType.Common,
): string => {
    return `
    \`\`\`catala
        scope ${scope}_General_Information:
            ${ruleContent}
    \`\`\``;
};
export const getScopeNameCatalaCode = (ruleContent: string): string => {
    return `
    \`\`\`catala
        ${ruleContent}
    \`\`\``;
};

type BlueprintTestData = {
    blueprintData: GetBlueprintVersionCatalaCodeResponse;
    annotationDataResponses: Map<
        string,
        PantheonModel.GetAnnotationDataResponse
    >;
    latestBlueprintVersionModel: BlueprintVersionModel;
};

function prepareData(): BlueprintTestData {
    const blueprintData = JSON.parse(
        fs.readFileSync(
            "tests/data/quantum_strategic_partner/quantum_getBlueprintVersionCatalaCode.json",
            "utf8",
        ),
    );
    const annotationData = JSON.parse(
        fs.readFileSync(
            "tests/data/quantum_strategic_partner/quantum_getAnnotationData.json",
            "utf8",
        ),
    );
    const annotationDataResponses: Map<
        string,
        PantheonModel.GetAnnotationDataResponse
    > = new Map([["random_document_version_id.random_id_3", annotationData]]);

    const blueprintInfo = JSON.parse(
        fs.readFileSync(
            "tests/data/quantum_strategic_partner/quantum_getBlueprint.json",
            "utf8",
        ),
    );

    const latestBlueprintVersionModel: BlueprintVersionModel =
        BlueprintVersionModel.create(blueprintInfo["versionModel"]);

    return {
        blueprintData,
        annotationDataResponses,
        latestBlueprintVersionModel,
    };
}

export const intitilizeTemplateGeneratorTest = (): ((
    _: string,
) => Promise<string>) => {
    const {
        blueprintData,
        annotationDataResponses,
        latestBlueprintVersionModel,
    } = prepareData();

    let getJsonnetRules = getJsonnetRulesImpl(
        blueprintData,
        annotationDataResponses,
        latestBlueprintVersionModel,
    );

    return getJsonnetRules;
};

export const intitilizeBlueprintApplicationTest = async (
    getFormResponse: GetFormResponse,
    modifiedCatalaContent: string | undefined = undefined,
): Promise<BlueprintApplicationResult> => {
    const {
        blueprintData,
        annotationDataResponses,
        latestBlueprintVersionModel,
    } = prepareData();

    const modifiedBlueprintData = {
        ...blueprintData,
        formLogic: modifiedCatalaContent
            ? modifiedCatalaContent
            : blueprintData.formLogic,
    };
    const generator = await BlueprintGenerator.create(
        modifiedBlueprintData,
        annotationDataResponses,
        latestBlueprintVersionModel.baseDocuments,
    );

    return generator.applyForm(decodeFormData(getFormResponse.formData));
};
