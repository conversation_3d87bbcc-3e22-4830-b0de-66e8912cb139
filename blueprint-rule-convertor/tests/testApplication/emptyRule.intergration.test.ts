import { expect, test, describe, beforeAll } from "bun:test";

import { intitilizeBlueprintApplicationTest } from "../utils";

import getFormResponse from "../data/quantum_strategic_partner/quantum_getForm_emptyRule.json";
import { BlueprintApplicationResult } from "@/models/formMapping";
import { getSchema } from "@/utils/formData/formSchema";

describe("formMatching", () => {
    let blueprintApplicationResult: BlueprintApplicationResult;

    beforeAll(async () => {
        blueprintApplicationResult = await intitilizeBlueprintApplicationTest(
            getFormResponse as any,
        );
    });

    test("[Test 1]", () => {
        expect(
            blueprintApplicationResult.blueprintKeyAnalytic,
        ).toMatchSnapshot();
        expect(blueprintApplicationResult.templateAnalytic).toMatchSnapshot();
        expect(
            getSchema(blueprintApplicationResult.formData),
        ).toMatchSnapshot();
    });
});
