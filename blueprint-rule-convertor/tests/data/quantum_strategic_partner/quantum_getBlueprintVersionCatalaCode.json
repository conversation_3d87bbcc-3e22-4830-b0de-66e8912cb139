{"formLogic": "```catala\n#<CHECKLIST>\n    #This form is duplicated from Quantum Strategic Partners, LP and Quantum Blocked Strategic Partners, LP (https://portal.anduin.app/pantheon/form/form0kpd3553y9e43lqn/build?)\n\n    #scope Common_General_Information:\n        #(1) If checked \"Check this box if the Subscriber has additional contact information that is different from the Formal Notice Information.\" --> show \"Subscriber's Other Contact Information\" section\n\n        #Subscriber's Wire Transfer Instructions:\n        #(2) If Bank Address - Country = Canada --> show field 'Sort Code'\n        #(3) If Bank Address - Country = Australia --> show field 'BSB ' \n#</CHECKLIST>\n\nscope Individual_General_Information:\n    definition filegroup1__fn_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_joint_tenancy\n            ]\n        then\n            [\n                filegroup1_fileitems.file1\n            ]\n        else \n            if fn_has_option_values of investing_jointly_with_your_spouse, \n            [\n                investing_jointly_with_your_spouse_options.opt_yes\n            ]\n            then\n            [\n                filegroup1_fileitems.file2\n            ]\n            else []\n\n\n    definition investing_jointly_with_your_spouse__fn_show equals\n        fn_check_investor_types of \n            [\n                blueprint_investor_types.a_trust\n            ]\n\n    definition bluewell1__fn_show equals \n        fn_has_option_values of investing_jointly_with_your_spouse, \n            [\n                investing_jointly_with_your_spouse_options.opt_yes\n            ]\n    \n    definition gating_if_unincorporated_agency__fn_hide equals true\n\n    definition attention_formalnotcie__fn_set_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.an_individual_human_being\n            ]\n            or  fn_check_investor_types of \n            [\n                blueprint_investor_types.a_joint_tenancy\n            ]\n        then\n            asa_fullname_investorname_generalinfo\n        else\n            do_nothing\n\nscope Common_Subscriber_Permissions:\n    definition asa_company_primarycontact_contactinfo__fn_set_value equals organization_formalnotcie\n\n    definition asa_phone_primarycontact_contactinfo__fn_set_value equals phone_formalnotcie\n\n    definition asa_email_primarycontact_contactinfo__fn_set_value equals email_formalnotcie\n\n    definition asa_correspondences_primarycontact_contactinfo__fn_add_value equals\n        [\n            asa_correspondences_primarycontact_contactinfo_options.asa_capitalcall_correspondences_primarycontact_contactinfo;\n            asa_correspondences_primarycontact_contactinfo_options.asa_distribution_correspondences_primarycontact_contactinfo\n        ]\n\n    definition gating_for_another_contact__fn_add_value equals\n        if fn_has_option_values of gating_for_additional_contact_different, \n            [\n                gating_for_additional_contact_different_options.check_if_yes\n            ]\n        then\n            [\n                gating_for_another_contact_options.check_if_yes\n            ]\n        else\n            []\n\n    definition asa_company_additionalcontact_contactinfo__fn_set_value equals\n        if fn_has_option_values of gating_for_additional_contact_different, \n            [\n                gating_for_additional_contact_different_options.check_if_yes\n            ]\n        then\n            organization_ifdifferent\n        else\n            do_nothing\n\n    definition asa_phone_additionalcontact_contactinfo__fn_set_value equals\n        if fn_has_option_values of gating_for_additional_contact_different, \n            [\n                gating_for_additional_contact_different_options.check_if_yes\n            ]\n        then\n            phone_ifdifferent\n        else\n            do_nothing\n\n    definition asa_email_additionalcontact_contactinfo__fn_set_value equals\n        if fn_has_option_values of gating_for_additional_contact_different, \n            [\n                gating_for_additional_contact_different_options.check_if_yes\n            ]\n        then\n            email_ifdifferent\n        else\n            do_nothing\n\n    #<CHECKLIST>\n        #(1) Prefill information of Address, City, State, Zip/Postal Code in \"Primary Contact\" with information from \"Subscriber's Formal Notice Information\" \n        #(2) If checked \"Check this box if you want to add another contact.\" --> show repetable Secondary Contact\n        #(3) If 2nd row of repetable Secondary Contact has value --> show checkbox \"Check this box if you have more than 20 contacts with permissions.\" \n        #(4) If checked \"Check this box if the Subscriber has additional contact information that is different from the Formal Notice Information.\" (General Information) --> prefill 1st row of repetable Secondary Contact with information of Address, City, State, Zip/Postal Code from \"Subscriber's Other Contact Information\" (General Information) \n    #</CHECKLIST>\n    \nscope Common_Additional_Representations_For_Non_US_Persons:\n    definition gating_for_representation__fn_show equals\n        fn_has_option_values of indicate_if_subscriber_is_eea_or_uk, \n            [\n                indicate_if_subscriber_is_eea_or_uk_options.opt_yes\n            ]\n\n    definition bluewell2__fn_show equals\n        fn_has_option_values of indicate_if_subscriber_is_eea_or_uk, \n            [\n                indicate_if_subscriber_is_eea_or_uk_options.opt_yes\n            ]\n\n    definition orangewell1__fn_show equals\n        fn_has_option_values of checkbox_for_canadian_representation,   \n            [\n                checkbox_for_canadian_representation_options.yes\n            ]\n\n    #<CHECKLIST>\n        #(1) For question \"Are you a United States Person?\" - General Information: \n        #If check Yes --> show optional group of questions (@optional)\n        #If check No --> show required group of questions (@required)\n    #</CHECKLIST>\n\nscope Individual_Investor_Qualification_Statement_For_Individuals:\n    definition b_part2_individual_qualificationstatement__fn_disable_and_set_value equals\n        if fn_decimal_gteq of asa_commitment_amount, 1100000.0\n        then \n            [\n                b_part2_individual_qualificationstatement_options.true_b_part2_individual_qualificationstatement\n            ]\n        else \n            [\n                b_part2_individual_qualificationstatement_options.false_b_part2_individual_qualificationstatement\n            ]\n\n#<CHECKLIST>\n    #scope Entity_Investor_Qualification_Statement_For_Individuals:\n    #(1) Hide this page \n\n    #scope Individual_Investor_Qualification_Statement_For_Entities:\n    #(1) Hide this page \n#</CHECKLIST>  \n\nscope Entity_Investor_Qualification_Statement_For_Entities:\n    #Part II. Investment Company Act Matters:\n    definition a2_part2_entity_qualificationstatement__fn_add_value equals\n        if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, \n            [\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor;\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor_1\n            ]\n        then\n            [\n                a2_part2_entity_qualificationstatement_options.false_a2_part2_entity_qualificationstatement\n            ]\n        else\n            []\n    \n    definition b_part2_entity_qualificationstatement__fn_add_value equals\n        if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, \n            [\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor\n            ]\n        then\n            [\n                b_part2_entity_qualificationstatement_options.false_b_part2_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition e_part2_entity_qualificationstatement__fn_add_value equals\n        if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, \n            [\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_trust_accreditedinvestor;\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_familyoffice_accreditedinvestor;\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_otherown5m_otherentity_accreditedinvestor\n            ]\n        then\n            [\n                e_part2_entity_qualificationstatement_options.true_e_part2_entity_qualificationstatement\n            ]\n        else\n            []\n\n    #Part III. Advisers Act Matters:\n    definition a1_part3_entity_qualificationstatement__fn_disable_and_set_value equals\n        if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, \n            [\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor\n            ]\n        then\n            [\n                a1_part3_entity_qualificationstatement_options.true_a1_part3_entity_qualificationstatement\n            ]\n        else []\n\n    definition a1_part3_entity_qualificationstatement__fn_add_value equals\n        if fn_has_option_values of b_part2_entity_qualificationstatement, \n            [\n                b_part2_entity_qualificationstatement_options.true_b_part2_entity_qualificationstatement\n            ]\n        then\n            [\n                a1_part3_entity_qualificationstatement_options.true_a1_part3_entity_qualificationstatement\n            ]\n        else\n            [\n                a1_part3_entity_qualificationstatement_options.false_a1_part3_entity_qualificationstatement\n            ]\n\n\n    definition a2_part3_entity_qualificationstatement__fn_disable_and_set_value equals\n        if fn_has_option_values of asa_accreditedinvestor_accreditedinvestor, \n            [\n                asa_accreditedinvestor_accreditedinvestor_options.asa_yes_sec2a48company_accreditedinvestor_1\n            ]\n        then\n            [\n                a2_part3_entity_qualificationstatement_options.true_a2_part3_entity_qualificationstatement\n            ]\n        else\n            [\n                a2_part3_entity_qualificationstatement_options.false_a2_part3_entity_qualificationstatement\n            ]\n\n    definition b_part3_entity_qualificationstatement__fn_enable equals\n        fn_has_option_values of a1_part3_entity_qualificationstatement, \n            [\n                a1_part3_entity_qualificationstatement_options.false_a1_part3_entity_qualificationstatement\n            ]\n        and  fn_has_option_values of a2_part3_entity_qualificationstatement, \n            [\n                a2_part3_entity_qualificationstatement_options.false_a2_part3_entity_qualificationstatement\n            ]\n\n    definition c_part3_entity_qualificationstatement__fn_disable_and_clear_data equals\n        fn_has_option_values of a1_part3_entity_qualificationstatement, \n            [\n                a1_part3_entity_qualificationstatement_options.false_a1_part3_entity_qualificationstatement\n            ]\n            and  fn_has_option_values of a2_part3_entity_qualificationstatement, \n            [\n                a2_part3_entity_qualificationstatement_options.false_a2_part3_entity_qualificationstatement\n            ]\n    \n    definition d_part3_entity_qualificationstatement__fn_disable_and_clear_data equals\n        fn_has_option_values of a1_part3_entity_qualificationstatement, \n            [\n                a1_part3_entity_qualificationstatement_options.false_a1_part3_entity_qualificationstatement\n            ]\n            and  fn_has_option_values of a2_part3_entity_qualificationstatement, \n            [\n                a2_part3_entity_qualificationstatement_options.false_a2_part3_entity_qualificationstatement\n            ]\n    definition b_part3_entity_qualificationstatement__fn_add_value equals\n        if fn_decimal_gteq of asa_commitment_amount, 1100000.0\n        then \n            [\n                b_part3_entity_qualificationstatement_options.true_b_part3_entity_qualificationstatement\n            ]\n        else []\n\n    definition c_part3_entity_qualificationstatement__fn_enable equals\n        fn_has_option_values of a1_part3_entity_qualificationstatement, \n            [\n                a1_part3_entity_qualificationstatement_options.true_a1_part3_entity_qualificationstatement\n            ]\n            or  fn_has_option_values of a2_part3_entity_qualificationstatement, \n            [\n                a2_part3_entity_qualificationstatement_options.true_a2_part3_entity_qualificationstatement\n            ]\n\n    definition d_part3_entity_qualificationstatement__fn_enable equals\n        fn_has_option_values of a1_part3_entity_qualificationstatement, \n            [\n                a1_part3_entity_qualificationstatement_options.true_a1_part3_entity_qualificationstatement\n            ]\n            or  fn_has_option_values of a2_part3_entity_qualificationstatement, \n            [\n                a2_part3_entity_qualificationstatement_options.true_a2_part3_entity_qualificationstatement\n            ]\n\n    #Part IV. Qualified Purchaser Matters:\n    definition blueprint__fn_mutex_options equals\n        [\n            group_asa_trust_qualifiedpurchaser_options.asa_trust_qualifiedpurchaser\n        ]\n        ++\n        [\n            group_asa_own5m_entity_qualifiedpurchaser_options.asa_own5m_entity_qualifiedpurchaser\n        ]\n\n\n    definition represent_checkbox_for_part_ivb__fn_show equals\n        fn_has_option_values of subscriber_is_a_company_formed_april30, \n            [\n                subscriber_is_a_company_formed_april30_options.opt_true\n            ]\n\n    #Part V. Miscellaneous Matters:\n    definition a3_part5_entity_qualificationstatement__fn_disable_and_set_value equals\n        if fn_has_option_values of a1_part5_entity_qualificationstatement, \n            [\n                a1_part5_entity_qualificationstatement_options.true_a1_part5_entity_qualificationstatement\n            ]\n        then\n            [\n                a3_part5_entity_qualificationstatement_options.false_a3_part5_entity_qualificationstatement\n            ]\n        else\n            []\n    definition a4_part5_entity_qualificationstatement__fn_disable_and_set_value equals\n        if fn_has_option_values of a1_part5_entity_qualificationstatement, \n            [\n                a1_part5_entity_qualificationstatement_options.true_a1_part5_entity_qualificationstatement\n            ]\n        then\n            [\n                a4_part5_entity_qualificationstatement_options.false_a4_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition a7_part5_entity_qualificationstatement__fn_disable_and_set_value equals\n        if fn_has_option_values of a5_part5_entity_qualificationstatement, \n            [\n                a5_part5_entity_qualificationstatement_options.true_a5_part5_entity_qualificationstatement\n            ]\n            or\n            fn_has_option_values of a6_part5_entity_qualificationstatement, \n            [\n                a6_part5_entity_qualificationstatement_options.true_a6_part5_entity_qualificationstatement\n            ]\n        then\n            [\n                a7_part5_entity_qualificationstatement_options.false_a7_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition blueprint__fn_mutex_options equals\n        [\n           group_b1_part5_entity_qualificationstatement_options.b1_part5_entity_qualificationstatement\n        ]\n        ++\n        [\n            group_b2_part5_entity_qualificationstatement_options.b2_part5_entity_qualificationstatement\n        ]\n\n    definition group_c1_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_corporation\n            ]\n        then\n            [\n                group_c1_part5_entity_qualificationstatement_options.c1_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c2_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_general_partnership\n            ]\n        then\n            [\n                group_c2_part5_entity_qualificationstatement_options.c2_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c3_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_limited_partnership\n            ]\n        then\n            [\n                group_c3_part5_entity_qualificationstatement_options.c3_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c4_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_limited_liability_company\n            ]\n        then\n            [\n                group_c4_part5_entity_qualificationstatement_options.c4_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c6_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_trust_of_the_following_type\n            ]\n        then\n            [\n                group_c6_part5_entity_qualificationstatement_options.c6_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c7_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.the_following_other_form_of_entity\n            ]\n        then\n            [\n                group_c7_part5_entity_qualificationstatement_options.c7_part5_entity_qualificationstatement\n            ]\n        else\n            []\n\n    definition group_c5_part5_entity_qualificationstatement__fn_disable_options_and_add_value equals\n    if fn_has_option_values of gating_if_unincorporated_agency, \n        [\n            gating_if_unincorporated_agency_options.check_if_yes\n        ]\n    then\n    \n        [\n            group_c5_part5_entity_qualificationstatement_options.c5_part5_entity_qualificationstatement\n        ]\n    else []\n    \n    #<CHECKLIST>\n        #(1) If checked Part I(a)(10) \n            #--> prefill False and disable Part V(a)(1)\n            #--> prefill True in Part V(a)(3)\n        #(2) If checked Part V(c)(6) --> Prefill its field below with type of trust from \"The Subscriber represents that it is\" in General Information\"\n        #(3) If checked Part V(c)(7) --> Prefill its field below with type of other entity from \"The Subscriber represents that it is\" in General Information\"\n        #(4) Prefill textbox \"Specify city, state, province, country and/or other jurisdiction:\" with information in General Information\n        #PartV(e): \n        #(5) If selected country is \"United States\"--> hide text box \"State/Province\"\n        #(6) If selected country is not \"United States\" --> hide text box \"U.S. State\"\n    #</CHECKLIST>\n\n#<CHECKLIST>\n    #scope Common_Appendix_A_Definition_Of_Investment:\n    #no rules\n\n    #scope Common_Appendix_B_Definition_Of_Disqualifying_Events:\n    #no rules\n#</CHECKLIST>\n\nscope Common_Appendix_C_Form_Of_Third_Party_Accredited_Investor_Verification_Letter:\n    definition filegroup1__fn_add_value equals\n        [\n            filegroup1_fileitems.file3\n        ]\n\nscope Common_Anti_Money_Laundering_Know_Your_Customer_Questionnaire:\n    definition asa_fullname_investorname_generalinfo2__fn_set_value equals asa_fullname_investorname_generalinfo\n    \n    #Part A – Politically Exposed Persons:\n    definition bluewell3__fn_show equals\n        fn_has_option_values of parta_kyc, \n            [\n                parta_kyc_options.yes_parta_kyc\n            ]\n\n    #Part B – Simplified Due Diligence:\n    definition filegroup2__fn_add_value equals\n        if fn_has_option_values of nominee_partb_kyc, \n            [\n                nominee_partb_kyc_options.yes_nominee_partb_kyc\n            ]\n        then\n            [\n                filegroup2_fileitems.file1\n            ]\n        else []\n\n    definition bluewell5__fn_show equals \n        fn_has_option_values of nominee_partb_kyc, \n            [\n                nominee_partb_kyc_options.yes_nominee_partb_kyc\n            ]\n\n    definition filegroup2__fn_add_value equals\n        if fn_has_option_values of group_eligibleintro_partb_kyc, \n            [\n                group_eligibleintro_partb_kyc_options.eligibleintro_partb_kyc\n            ]\n        then\n            [\n                filegroup2_fileitems.file2\n            ]\n        else []\n\n    definition bluewell6__fn_show equals\n        fn_has_option_values of group_eligibleintro_partb_kyc, \n            [\n                group_eligibleintro_partb_kyc_options.eligibleintro_partb_kyc\n            ]\n\n    #<CHECKLIST>\n        #(1) If checked Yes for Part A --> disable Part B\n        #(2) If checked Yes for Part A OR checked \"None of the above categories apply to the Investor\" for Part B --> enable Part C\n        #(3) If checked \"The Investor is introduced by an Eligible Introducer...\" in Part B --> uncheck \"The Investor is not introduced by an Eligible Introducer...\" and vice versa\n        #(4) If \"Type of subscriber for the purpose of determining the applicable documentation:\" = Individual/Company/Partnership/Trust/LLC - Part C --> show corresponding paragraphs (For eg, \"For Investors who are individuals\") \n    #</CHECKLIST>\n\nscope Individual_Anti_Money_Laundering_Know_Your_Customer_Questionnaire:\n    definition asa_nationality_generalinfo__fn_show equals \n        fn_has_option_values of gating_for_nationality, \n            [\n                gating_for_nationality_options.check_if_yes\n            ]\n    \n    definition exemptedclient_partb_kyc__fn_hide equals true\n    \n    #Part C – Standard Due Diligence:\n    definition type_of_subscriber_for_the_purpose_of_de__fn_disable_and_set_value equals\n        [\n            type_of_subscriber_for_the_purpose_of_de_options.individual_natural_person\n        ]\n\n    definition filegroup3__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.individual_natural_person\n            ]\n        then\n            [\n                filegroup3_fileitems.file1;\n                filegroup3_fileitems.file2\n            ]\n        else []\n\n    definition individual_partc_kyc__fn_add_value equals \n    if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n        [\n            type_of_subscriber_for_the_purpose_of_de_options.individual_natural_person\n        ]\n    then \n        [\n            individual_partc_kyc_options.one_individual_partc_kyc;\n            individual_partc_kyc_options.two_individual_partc_kyc\n        ]\n    else []\n\n    #<CHECKLIST>\n        #(1) Hide Current Business Address & DOE & POE & Nature of Business\n        #(2) Disable Part D – Beneficial Ownership Declaration\n    #</CHECKLIST>\n\nscope Entity_Anti_Money_Laundering_Know_Your_Customer_Questionnaire:\n    definition gating_for_nationality__fn_hide equals true\n\n    definition asa_nationality_generalinfo__fn_hide equals true\n\n    #Part B – Simplified Due Diligence:\n    definition filegroup2__fn_add_value equals\n        if fn_has_option_values of exemptedclient_partb_kyc, \n            [\n                exemptedclient_partb_kyc_options.one_exemptedclient_partb_kyc\n            ]\n        then\n            [\n                filegroup2_fileitems.file3\n            ]\n        else \n            if fn_has_option_values of exemptedclient_partb_kyc, \n                [\n                    exemptedclient_partb_kyc_options.two_exemptedclient_partb_kyc\n                ]\n            then\n                [\n                    filegroup2_fileitems.file4\n                ]\n            else\n                if fn_has_option_values of exemptedclient_partb_kyc, \n                    [\n                        exemptedclient_partb_kyc_options.three_exemptedclient_partb_kyc\n                    ]\n                then\n                    [ \n                        filegroup2_fileitems.file5\n                    ]\n                else\n                    if fn_has_option_values of exemptedclient_partb_kyc, \n                        [\n                            exemptedclient_partb_kyc_options.four_exemptedclient_partb_kyc\n                        ]\n                    then\n                        [\n                            filegroup2_fileitems.file6\n                        ]\n                    else \n                        if fn_has_option_values of exemptedclient_partb_kyc, \n                            [\n                                exemptedclient_partb_kyc_options.five_exemptedclient_partb_kyc\n                            ]\n                        then\n                            [\n                                filegroup2_fileitems.file7\n                            ]\n                        else []\n\n    #Part C - Standard Due Diligence:\n    definition type_of_subscriber_for_the_purpose_of_de__fn_disable_options_and_clear_data equals \n        [\n            type_of_subscriber_for_the_purpose_of_de_options.individual_natural_person\n        ]\n\n    definition type_of_subscriber_for_the_purpose_of_de__fn_add_value equals\n        if fn_check_investor_types of \n            [\n                blueprint_investor_types.a_corporation\n            ]\n        then\n            [\n                type_of_subscriber_for_the_purpose_of_de_options.company\n            ]\n        else\n            if fn_check_investor_types of \n                [\n                    blueprint_investor_types.a_general_partnership\n                ]\n            then\n                [\n                    type_of_subscriber_for_the_purpose_of_de_options.partnership\n                ]\n            else\n                if fn_check_investor_types of \n                    [\n                        blueprint_investor_types.a_limited_partnership\n                    ]\n                then\n                    [\n                        type_of_subscriber_for_the_purpose_of_de_options.partnership\n                    ]\n                else\n                    if fn_check_investor_types of \n                        [\n                            blueprint_investor_types.a_limited_liability_company\n                        ]\n                    then\n                        [\n                            type_of_subscriber_for_the_purpose_of_de_options.limited_liability_company\n                        ]\n                    else\n                        if fn_check_investor_types of \n                            [\n                                blueprint_investor_types.a_trust_of_the_following_type\n                            ]\n                        then\n                            [\n                                type_of_subscriber_for_the_purpose_of_de_options.trust\n                            ]\n                        else\n                            []\n        \n    definition filegroup4__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.company\n            ]\n        then\n            [\n                filegroup4_fileitems.file1;\n                filegroup4_fileitems.file2;\n                filegroup4_fileitems.file3;\n                filegroup4_fileitems.file4;\n                filegroup4_fileitems.file5;\n                filegroup4_fileitems.file6;\n                filegroup4_fileitems.file7\n            ]\n        else []\n\n    definition company_partc_kyc__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.company\n            ]\n        then\n            [\n                company_partc_kyc_options.one_company_partc_kyc;\n                company_partc_kyc_options.two_company_partc_kyc;\n                company_partc_kyc_options.three_company_partc_kyc;\n                company_partc_kyc_options.five_company_partc_kyc;\n                company_partc_kyc_options.six_company_partc_kyc;\n                company_partc_kyc_options.seven_company_partc_kyc;\n                company_partc_kyc_options.eight_company_partc_kyc\n            ]\n        else\n            []\n\n    definition filegroup5__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.partnership\n            ]\n        then\n            [\n                filegroup5_fileitems.file1;\n                filegroup5_fileitems.file2;\n                filegroup5_fileitems.file3;\n                filegroup5_fileitems.file4;\n                filegroup5_fileitems.file5\n            ]\n        else []\n\n    definition partnership_partc_kyc__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.partnership\n            ]\n        then\n            [\n                partnership_partc_kyc_options.one_partnership_partc_kyc;\n                partnership_partc_kyc_options.two_partnership_partc_kyc;\n                partnership_partc_kyc_options.four_partnership_partc_kyc;\n                partnership_partc_kyc_options.five_partnership_partc_kyc;\n                partnership_partc_kyc_options.six_partnership_partc_kyc\n            ]\n        else\n            []\n\n    definition filegroup6__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.trust\n            ]\n        then\n            [\n                filegroup6_fileitems.file1;\n                filegroup6_fileitems.file2;\n                filegroup6_fileitems.file3;\n                filegroup6_fileitems.file4\n            ]\n        else []\n\n    definition trust_partc_kyc__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.trust\n            ]\n        then\n            [\n                trust_partc_kyc_options.one_trust_partc_kyc;\n                trust_partc_kyc_options.two_trust_partc_kyc;\n                trust_partc_kyc_options.four_trust_partc_kyc;\n                trust_partc_kyc_options.three_trust_partc_kyc\n            ]\n        else\n            []\n\n    definition filegroup7__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.limited_liability_company\n            ]\n        then\n            [\n                filegroup7_fileitems.file1;\n                filegroup7_fileitems.file2;\n                filegroup7_fileitems.file3;\n                filegroup7_fileitems.file4;\n                filegroup7_fileitems.file5\n            ]\n        else []\n\n    definition llc_partc_kyc__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.limited_liability_company\n            ]\n        then\n            [\n                llc_partc_kyc_options.one_llc_partc_kyc;\n                llc_partc_kyc_options.two_llc_partc_kyc;\n                llc_partc_kyc_options.three_llc_partc_kyc;\n                llc_partc_kyc_options.four_llc_partc_kyc;\n                llc_partc_kyc_options.five_llc_partc_kyc\n            ]\n        else\n            []\n\n    definition filegroup8__fn_add_value equals\n        if fn_has_option_values of type_of_subscriber_for_the_purpose_of_de, \n            [\n                type_of_subscriber_for_the_purpose_of_de_options.none_of_the_above\n            ]\n        then\n            [\n                filegroup8_fileitems.file1;\n                filegroup8_fileitems.file2;\n                filegroup8_fileitems.file3;\n                filegroup8_fileitems.file4;\n                filegroup8_fileitems.file5\n            ]\n        else []\n\n    #Part D – Beneficial Ownership Declaration:\n    definition filegroup2__fn_add_value equals\n        if fn_has_option_values of gating_for_part_d, \n            [\n                gating_for_part_d_options.check_if_yes\n            ]\n        then\n            [\n                filegroup2_fileitems.file8\n            ]\n        else []\n\n    definition bluewell4__fn_show equals \n        fn_has_option_values of gating_for_part_d, \n            [\n                gating_for_part_d_options.check_if_yes\n            ]\n    #<CHECKLIST>\n        #(1) Hide Current Residential Address & DOB & POB & \"Check this box if your nationality...\" & Occupation\n        #(2) If checked \"None of the above\" in Part C – Standard Due Diligence --> show paragraph \"Required Documents:\" \n        #(3) Enable Part D – Beneficial Ownership Declaration if Part C is enabled\n    #</CHECKLIST>\n\n#<CHECKLIST>\n    #scope Common_Privacy_Notice:\n    #no rules\n\n    #scope Common_Privacy_Notice_Supplement_For_California_Residents:\n    #no rules\n\n    #scope Common_EU_UK_Privacy_Notice:\n    #no rules\n#</CHECKLIST>\n    \nscope Individual_Signature_Page:\n    definition asa_fullname_investorname_generalinfo3__fn_set_value equals \n    asa_fullname_investorname_generalinfo\n\n    definition individualname_signaturepage__fn_set_value equals asa_fullname_investorname_generalinfo\n\n    definition individualname_aml_signaturepage__fn_set_value equals asa_fullname_investorname_generalinfo\n\n    #Individual Signatory:\n    definition subscribername_naturalperson_signaturepage_individual_qualificationstatement__fn_set_value equals asa_fullname_investorname_generalinfo\n    \n    definition lp_individual_signatory__fn_show equals\n        fn_check_investor_types of \n            [\n                blueprint_investor_types.an_individual_human_being;\n                blueprint_investor_types.a_joint_tenancy\n            ]\n\n    #Subscriber's Spouse:\n    definition lp_subscriber_spouse__fn_show equals\n        fn_check_investor_types of \n            [\n                blueprint_investor_types.a_joint_tenancy\n            ]\n            and  fn_has_option_values of investing_jointly_with_your_spouse, \n            [\n                investing_jointly_with_your_spouse_options.opt_yes\n            ]\n\n    #Alter Egos of Natural Persons Signatory:\n    definition subscribername_alteregos_signaturepage_individual_qualificationstatement__fn_set_value equals asa_fullname_investorname_generalinfo\n    \n    definition lp_alter_egos_of_natural_persons_signato__fn_show equals\n        fn_check_investor_types of \n            [\n                blueprint_investor_types.an_individual_retirement_account;\n                blueprint_investor_types.a_revocable_trust_the_sole_settlor_of_which_was;\n                blueprint_investor_types.a_self_directed_retirement_plan\n            ]\n    \nscope Entity_Signature_Page:\n    #Authorized Representative:\n    definition entityname_signaturepage__fn_set_value equals asa_fullname_investorname_generalinfo\n\n    definition subscribername_signaturepage_part5_entity_qualificationstatement__fn_set_value equals asa_fullname_investorname_generalinfo\n\n    definition entityname_aml_signaturepage__fn_set_value equals asa_fullname_investorname_generalinfo\n    \n    definition lp_authorized_representative__fn_show equals true\n    \n    \n    \n```", "blueprintInfo": {"metadata": {"blueprintScopes": [{"id": "8Gw7yC", "sectionName": "General_Information", "investorTypes": [{"Common": {}}, {"Individual": {}}, {"Entity": {}}]}, {"id": "LLSnXp", "sectionName": "Subscriber_Permissions", "investorTypes": [{"Common": {}}]}, {"id": "Y0O59Z", "sectionName": "Additional_Representations_For_Non_US_Persons", "investorTypes": [{"Common": {}}]}, {"id": "VeytrS", "sectionName": "Investor_Qualification_Statement_For_Individuals", "investorTypes": [{"Individual": {}}, {"Entity": {}}]}, {"id": "mpwQfL", "sectionName": "Investor_Qualification_Statement_For_Entities", "investorTypes": [{"Individual": {}}, {"Entity": {}}]}, {"id": "JuadO3", "sectionName": "Appendix_A_Definition_Of_Investment", "investorTypes": [{"Common": {}}]}, {"id": "zD4j67", "sectionName": "Appendix_B_Definition_Of_Disqualifying_Events", "investorTypes": [{"Common": {}}]}, {"id": "RfnZJL", "sectionName": "Appendix_C_Form_Of_Third_Party_Accredited_Investor_Verification_Letter", "investorTypes": [{"Common": {}}]}, {"id": "2ww12G", "sectionName": "Anti_Money_Laundering_Know_Your_Customer_Questionnaire", "investorTypes": [{"Common": {}}, {"Individual": {}}, {"Entity": {}}]}, {"id": "rjgejq", "sectionName": "Privacy_Notice", "investorTypes": [{"Common": {}}]}, {"id": "uJhYW8", "sectionName": "Privacy_Notice_Supplement_For_California_Residents", "investorTypes": [{"Common": {}}]}, {"id": "xjdtc1", "sectionName": "EU_UK_Privacy_Notice", "investorTypes": [{"Common": {}}]}, {"id": "MkGIec", "sectionName": "Signature_Page", "investorTypes": [{"Common": {}}, {"Individual": {}}, {"Entity": {}}]}], "investorTypes": [["individuals", ["a joint tenancy", "a trust", "a self directed retirement plan", "an individual retirement account", "a revocable trust the sole settlor of which was", "an individual human being"]], ["entities", ["a corporation", "a trust", "a trust of the following type", "a general partnership", "a limited partnership", "a limited liability company", "the following other form of entity", "an unicorporated agency or instrumentality of the government of"]]], "stringConstants": [{"id": "FjTHB9", "key": "commitment_higher_than_100k", "stringValue": "The commitment amount must higher than 100,000 EUR."}, {"id": "cTwcSK", "key": "sweden", "stringValue": "Sweden"}], "paragraphs": [{"id": "nuDl4R", "key": "bluewell1", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">Your </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>spouse</strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> will be required to </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Robot<PERSON>, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>sign</strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> this form during the signature process.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}, {"id": "ABW6VU", "key": "bluewell2", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">EEA, UK and Swiss Subscribers may be required to complete additional forms. Please contact Kirkland &amp; <PERSON> at </span><a href=\"mailto:<EMAIL>\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(43, 149, 214); line-height: 20px;\"><span><EMAIL></span></a><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> for additional information.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}, {"id": "jNFUS7", "key": "orangewell1", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">Please reach out to Kirkland &amp; <PERSON> at </span><a href=\"mailto:<EMAIL>\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(43, 149, 214); line-height: 20px;\"><span><EMAIL></span></a><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> for additional information on required supplemental documents for this jurisdiction.</span></p>", "style": {"Orange": {}}, "afterFieldOpt": null}, {"id": "f4QuGr", "key": "bluewell3", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px; font-size: 13px;\">If Yes, please proceed to </span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px; font-size: 13px;\"><strong>Part C</strong></span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px; font-size: 13px;\">.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}, {"id": "Z9ITZR", "key": "bluewell4", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">You will be asked to </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>attach </strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">a separate sheet to provide a full list of the beneficial owners during the signature process.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}, {"id": "gkZL39", "key": "bluewell5", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">You will be asked to </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>submit </strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">a letter of assurance during the signature process. Please find attached a sample</span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Robot<PERSON>, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><em> Anti-Money Laundering Attestation – Nominees letter</em></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> below.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}, {"id": "zcQrnR", "key": "bluewell6", "label": "<p><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">You will be asked to </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>submit </strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">a letter of introduction during the \"Upload AML/KYC &amp; other documents\" process. Please find attached a sample</span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><em> Eligible Introducer lette</em></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">r below.</span></p>", "style": {"Blue": {}}, "afterFieldOpt": null}], "gatingQuestions": [{"id": "Ap3DFm", "key": "investing_jointly_with_your_spouse", "label": "<p><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">Are you investing jointly with your spouse? </strong><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(219, 55, 55); line-height: 20px;\">*</strong></p>", "inputType": {"Radio": {}}, "afterFieldOpt": null, "options": [{"id": "Pgbxhp", "key": "opt_yes", "label": "Yes"}, {"id": "rpk8dZ", "key": "opt_no", "label": "No"}]}, {"id": "cShp1M", "key": "gating_if_unincorporated_agency", "label": "<p>gating if unincorporated agency</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "xLkdzU", "key": "check_if_yes", "label": "<p><strong style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Check this box</strong><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> if the Subscriber is an unincorporated agency or instrumentality of the government.</span></p>"}]}, {"id": "avuQFa", "key": "gating_for_another_contact", "label": "<p>gating for another contact</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "gInlJ9", "key": "check_if_yes", "label": "<p><strong style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Check this box if</strong><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> you want to add another contact</span></p>"}]}, {"id": "wVHCky", "key": "gating_for_additional_contact_different", "label": "<p>gating for additional contact different from formal notice information</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "wJWxFD", "key": "check_if_yes", "label": "<p><strong style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Check this box</strong><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> </span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"><strong>if</strong></span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> the Subscriber has additional contact information that is different from the Formal Notice Information.</span></p>"}]}, {"id": "9oY2uN", "key": "indicate_if_subscriber_is_eea_or_uk", "label": "<p><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">Please indicate if (a) the Subscriber is domiciled in, or has a registered office in, a member state of the European Economic Area (“<u>EEA Member State</u>”) or the United Kingdom (“<u>UK</u>”) or the decision to invest in the Partnership was made for or on behalf of the Subscriber by a person that is domiciled in, or has its registered office in, an EEA Member State (“<u>EEA Subscriber</u>”), (b) the Subscriber is domiciled in, or has a registered office in, the UK or the decision to invest in the Partnership was made for or on behalf of the Subscriber by a person that is domiciled in, or has its registered office in, the UK (“<u>UK Subscriber</u>”) or (c) the Subscriber is domiciled in, or has a registered office in, Switzerland or the decision to invest in the Partnership was made for or on behalf of the Subscriber by a person that is domiciled in, or has its registered office in, Switzerland (“<u>Swiss Subscriber</u>”) </strong><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(219, 55, 55); line-height: 20px;\">*</strong></p>", "inputType": {"Radio": {}}, "afterFieldOpt": null, "options": [{"id": "iPpg9K", "key": "opt_yes", "label": "Yes"}, {"id": "9vDKHl", "key": "opt_no", "label": "No"}]}, {"id": "LfrPaw", "key": "gating_for_representation", "label": "<p>gating for representation</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "y5AIkc", "key": "check_if_yes", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">The Subscriber represents, warrants and agrees that (i) the Subscriber has appropriately completed the EEA, Swiss and UK Supplemental IQS provided separately by the General Partner and (ii) all of the statements, answers and information in the EEA, Swiss and UK Supplemental IQS are true, complete and correct as of the date hereof, will be true, complete and correct as of the date and/or dates of the acceptance of this subscription and, as of each such date, do not and will not omit to state any material fact necessary in order to make the statements contained therein not misleading. </span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(219, 55, 55); font-size: 13px; line-height: 20px;\">*</span></p>"}]}, {"id": "gf1Cve", "key": "checkbox_for_canadian_representation", "label": "<p>checkbox for Canadian representation</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "vOSES0", "key": "yes", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">The Subscriber represents and warrants that (a) the Subscriber is an \"</span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"><strong>accredited investor</strong></span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">\" as defined in Canadian National Instrument 45-106 Prospectus Exemptions or subsection 73.3(1) of the Securities Act (Ontario), (b) the Subscriber is a permitted client, as defined in National Instrument 31-103 Registration Requirements, Exemptions and Ongoing Registrant Obligations, (c) the Subscriber has fully and truthfully completed the Supplemental Investor Qualification Statement for Canadian Subscribers provided separately by the General Partner and (d) the Subscriber has not received any general advertising materials relating to the Interests.</span></p>"}]}, {"id": "9YEcbl", "key": "represent_checkbox_for_part_ivb", "label": "<p>represent checkbox for part IV(b)</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "HuDu9k", "key": "check_if_yes", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">The Subscriber hereby represents and warrants that all consents required under the Investment Company Act to the Subscriber’s treatment as a qualified purchaser have been obtained.</span></p>"}]}, {"id": "2ttHTf", "key": "subscriber_is_a_company_formed_april30", "label": "<p><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">(b)</strong><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"> The Subscriber is a company formed on or before April 30, 1996 that relies on the exceptions provided for in Section 3(c)(1) or 3(c)(7) of the Investment Company Act to be exempt from registration as an investment company under the Investment Company Act (an “</span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\"><strong>excepted investment company</strong></span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">”). </span><span style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(219, 55, 55); line-height: 20px;\">*</span></p>", "inputType": {"Radio": {}}, "afterFieldOpt": null, "options": [{"id": "IcxhXL", "key": "opt_true", "label": "True"}, {"id": "G086Nz", "key": "opt_false", "label": "False"}]}, {"id": "mvi8OJ", "key": "type_of_subscriber_for_the_purpose_of_de", "label": "<p><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(32, 57, 77); line-height: 20px;\">Type of subscriber for the purpose of determining the applicable documentation: </strong><strong style=\"font-size: 13px; font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(253, 18, 18); line-height: 20px;\">*</strong></p>", "inputType": {"Radio": {}}, "afterFieldOpt": null, "options": [{"id": "NYbAMo", "key": "individual_natural_person", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Individual (Natural Person)</span></p>"}, {"id": "4ZMBhc", "key": "company", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Company</span></p>"}, {"id": "0okhuR", "key": "partnership", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Partnership</span></p>"}, {"id": "AR5n4g", "key": "trust", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Trust</span></p>"}, {"id": "JdBsbn", "key": "limited_liability_company", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Limited Liability Company</span></p>"}, {"id": "RmsGHK", "key": "none_of_the_above", "label": "<p><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">None of the above</span></p>"}]}, {"id": "HjE63o", "key": "gating_for_nationality", "label": "<p>gating for nationality</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "24LoHd", "key": "check_if_yes", "label": "<p><strong style=\"font-family: system-ui, -apple-system, Robot<PERSON>, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Check this box if</strong><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> your nationality is </span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"><strong>different </strong></span><span style=\"font-family: system-ui, -apple-system, <PERSON><PERSON>, <PERSON>xygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">from your place of birth.</span></p>"}]}, {"id": "JC5a6d", "key": "gating_for_part_d", "label": "<p>gating for part D</p>", "inputType": {"MultipleCheckbox": {}}, "afterFieldOpt": null, "options": [{"id": "nIuvFh", "key": "check_if_yes", "label": "<p><strong style=\"font-family: system-ui, -apple-system, Robot<PERSON>, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\">Check this box</strong><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> if you have </span><span style=\"font-family: system-ui, -apple-system, Roboto, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"><strong>more than 5</strong></span><span style=\"font-family: system-ui, -apple-system, <PERSON><PERSON>, Oxygen, Cantarell, Ubuntu, &quot;Segoe UI&quot;, sans-serif; color: rgb(50, 60, 77); font-size: 13px; line-height: 20px;\"> beneficial owners.</span></p>"}]}], "fileGroups": [{"id": "hqtfrl", "key": "filegroup1", "name": "<PERSON><PERSON><PERSON>", "helpText": "", "fileItems": [{"id": "OoSzuE", "key": "file1", "name": "The appropriate IRS tax form for the Joint Subscriber", "helpText": ""}, {"id": "A0A4JA", "key": "file2", "name": "The appropriate IRS tax form for the spouse", "helpText": ""}, {"id": "wttYFM", "key": "file3", "name": "Form of Third-party Accredited Investor Verification Letter", "helpText": ""}]}, {"id": "JqZX9m", "key": "filegroup2", "name": "Simplified Due Diligence", "helpText": "", "fileItems": [{"id": "5nqiMB", "key": "file1", "name": "Letter of assurance in relation to the underlying principals in accordance with Cayman Islands requirements", "helpText": ""}, {"id": "wR8Fjs", "key": "file2", "name": "Eligible Introducer Letter", "helpText": ""}, {"id": "70RFzb", "key": "file3", "name": "Evidence of regulation and ownership structure for majority owned subsidiaries", "helpText": ""}, {"id": "qgv72Z", "key": "file4", "name": "Evidence of listing and ownership structure for majority owned subsidiaries", "helpText": ""}, {"id": "dQpQyl", "key": "file5", "name": "Evidence of government ownership", "helpText": ""}, {"id": "NMyfkt", "key": "file6", "name": "Certificate of registration of the pension fund or equivalent", "helpText": ""}, {"id": "buychZ", "key": "file7", "name": "Structure diagram illustrating the entity’s relationship to the entity that complies with the AMLRs and written assurance regarding the entity’s compliance with the AMLRs of the Cayman Islands", "helpText": ""}, {"id": "kQgTP5", "key": "file8", "name": "A full list of the beneficial owners", "helpText": ""}]}, {"id": "n6fQAo", "key": "filegroup3", "name": "Standard Due Diligence for individuals", "helpText": "", "fileItems": [{"id": "QdcZ4W", "key": "file1", "name": "A clear certified copy of a current valid Passport, National ID Card or Driver’s License (bearing photo, signature, nationality, date and place of birth)", "helpText": ""}, {"id": "R5zSGO", "key": "file2", "name": "A current certified copy or original of Bank or Legal Reference letter or Utility Bill issued within the last 3 months that confirms the Subscriber's residential address", "helpText": ""}]}, {"id": "vLUcV2", "key": "filegroup4", "name": "Standard Due Diligence for companies", "helpText": "", "fileItems": [{"id": "ncT1cO", "key": "file1", "name": "A certified copy of Certificate of Incorporation / Establishment", "helpText": ""}, {"id": "CQGQ6Z", "key": "file2", "name": "A certified copy of Constitutional document (e.g. Memorandum and Articles of Association or equivalent)", "helpText": ""}, {"id": "hHos31", "key": "file3", "name": "A certified copy of the register of directors / senior managing officers (or equivalent)", "helpText": ""}, {"id": "6iUsuH", "key": "file4", "name": "A certified copy of the register of voting shareholders", "helpText": ""}, {"id": "LvLYX1", "key": "file5", "name": "A certified copy of a Ownership / Organisational Chart", "helpText": ""}, {"id": "qLenKf", "key": "file6", "name": "Identification information from two (2) directors or equivalent senior managing officers and any other \"Natural Person\" exercising ultimate effective control over the management of the Company", "helpText": ""}, {"id": "suXrWC", "key": "file7", "name": "Identification information for all persons with a direct or indirect interest in the Subscriber of 10% or more", "helpText": ""}]}, {"id": "VWGZuX", "key": "filegroup5", "name": "Standard Due Diligence for partnerships", "helpText": "", "fileItems": [{"id": "r3LgWp", "key": "file1", "name": "A certified copy of Certificate of Registration/Establishment (or equivalent)", "helpText": ""}, {"id": "cc9mRW", "key": "file2", "name": "A certified copy of Partnership Agreement", "helpText": ""}, {"id": "0bBoYr", "key": "file3", "name": "A certified copy of an Ownership / Organisational Chart", "helpText": ""}, {"id": "Ce16Wn", "key": "file4", "name": "Identification information for the general partner or equivalent controlling person and any other Natural Person exercising ultimate effective control over the management of the Partnership", "helpText": ""}, {"id": "twTtkZ", "key": "file5", "name": "Identification information for all persons with a direct or indirect interest in the Subscriber of 10% or more", "helpText": ""}]}, {"id": "YdObkr", "key": "filegroup6", "name": "Standard Due Diligence for trusts", "helpText": "", "fileItems": [{"id": "5nPOFc", "key": "file1", "name": "A certified copy of the Trust Deed or Declaration (or equivalent)", "helpText": ""}, {"id": "U3DlwV", "key": "file2", "name": "A certified copy of a Structure Chart of the Trust", "helpText": ""}, {"id": "KYwcR8", "key": "file3", "name": "A certified copy of Certificate of Registration (if any)", "helpText": ""}, {"id": "lDziXl", "key": "file4", "name": "Identification information for the Trustees, Settlors, Protector, Enforcer, beneficiaries (with a fixed or vested interest) or any other Natural Person exercising ultimate effective control over the management of the Trust", "helpText": ""}]}, {"id": "SFrX8P", "key": "filegroup7", "name": "Standard Due Diligence for Limited Liability Companies", "helpText": "", "fileItems": [{"id": "a1lg3P", "key": "file1", "name": "A certified copy of the Certificate of Formation (or equivalent)", "helpText": ""}, {"id": "OsZDtK", "key": "file2", "name": "A certified copy of the Operating Agreement (or equivalent)", "helpText": ""}, {"id": "Br5q5X", "key": "file3", "name": "A certified copy of a Ownership / Organisational Chart", "helpText": ""}, {"id": "6qUX9N", "key": "file4", "name": "Identification information for the managing members or any other Natural Person who exercises ultimate effective control over the management of the Company", "helpText": ""}, {"id": "qxi80R", "key": "file5", "name": "Identification information for all persons with a direct or indirect interest in the Subscriber of 10% or more", "helpText": ""}]}, {"id": "O8uRt3", "key": "filegroup8", "name": "Standard Due Diligence", "helpText": "", "fileItems": [{"id": "LfAyGY", "key": "file1", "name": "A certified copy of the Certificate of Formation (or equivalent)", "helpText": ""}, {"id": "tnGsAF", "key": "file2", "name": "A certified copy of the Operating Agreement (or equivalent)", "helpText": ""}, {"id": "cAABXK", "key": "file3", "name": "A certified copy of a Ownership / Organisational Chart", "helpText": ""}, {"id": "8Ch2Qf", "key": "file4", "name": "Identification information for the managing members or any other Natural Person who exercises ultimate effective control over the management of the Company", "helpText": ""}, {"id": "zGGhOM", "key": "file5", "name": "Identification information for all persons with a direct or indirect interest in the Subscriber of 10% or more", "helpText": ""}]}], "signatures": [{"id": "dyPL6S", "key": "gp_roman_a_bejger", "signatureType": "GpSignature", "signer": "<PERSON> <PERSON><PERSON>", "mappings": [{"mapping": "asa_commitment_amount_0", "signatureType": "Custom", "customTypeConfig": {"isCustomized": true, "description": "Accepted Amount", "isCommitmentAmount": true}, "isOptional": false}, {"mapping": "dateofdelivery_gpsignaturepage", "signatureType": "Date", "customTypeConfig": {"isCustomized": false, "description": "Date of Delivery", "isCommitmentAmount": false}, "isOptional": false}, {"mapping": "gpsignature_gpsignaturepage", "signatureType": "Signature", "isOptional": false}], "uniqueSignerEmail": false}, {"id": "iKoG7f", "key": "lp_individual_signatory", "signatureType": "LpSignature", "signer": "Individual Signatory", "mappings": [{"mapping": "date_signaturepage", "signatureType": "Date", "isOptional": false}, {"mapping": "individualsignature_signaturepage", "signatureType": "Signature", "isOptional": false}, {"mapping": "dated_signaturepage_individual_qualificationstatement", "signatureType": "Date", "isOptional": false}, {"mapping": "subscribersignature_naturalperson_signaturepage_individual_qualificationstatement", "signatureType": "Signature", "isOptional": false}, {"mapping": "signature_partg_kyc", "signatureType": "Signature", "isOptional": false}, {"mapping": "date_partg_kyc", "signatureType": "Date", "isOptional": false}, {"mapping": "date_aml_signaturepage", "signatureType": "Date", "isOptional": false}, {"mapping": "individualsignature_aml_signaturepage", "signatureType": "Signature", "isOptional": false}], "uniqueSignerEmail": false}, {"id": "LFytG1", "key": "lp_subscriber_spouse", "signatureType": "LpSignature", "signer": "Subscriber's Spouse", "mappings": [{"mapping": "spouseignature_signaturepage", "signatureType": "Signature", "isOptional": false}, {"mapping": "spousesignature_naturalperson_signaturepage_individual_qualificationstatement", "signatureType": "Signature", "isOptional": false}, {"mapping": "spousesignature_aml_signaturepage", "signatureType": "Signature", "isOptional": false}], "uniqueSignerEmail": true}, {"id": "y6acfD", "key": "lp_alter_egos_of_natural_persons_signato", "signatureType": "LpSignature", "signer": "Alter <PERSON> of Natural Persons Signatory", "mappings": [{"mapping": "date_signaturepage", "signatureType": "Date", "isOptional": false}, {"mapping": "individualsignature_signaturepage", "signatureType": "Signature", "isOptional": false}, {"mapping": "dated_signaturepage_individual_qualificationstatement", "signatureType": "Date", "isOptional": false}, {"mapping": "signature_alteregos_signaturepage_individual_qualificationstatement", "signatureType": "Signature", "isOptional": false}, {"mapping": "signatoryname_alteregos_signaturepage_individual_qualificationstatement", "signatureType": "Name", "isOptional": false}, {"mapping": "signatorytitle_alteregos_signaturepage_individual_qualificationstatement", "signatureType": "Title", "isOptional": false}, {"mapping": "signature_partg_kyc", "signatureType": "Signature", "isOptional": false}, {"mapping": "date_partg_kyc", "signatureType": "Date", "isOptional": false}, {"mapping": "date_aml_signaturepage", "signatureType": "Date", "isOptional": false}, {"mapping": "individualsignature_aml_signaturepage", "signatureType": "Signature", "isOptional": false}], "uniqueSignerEmail": true}, {"id": "qar76s", "key": "lp_authorized_representative", "signatureType": "LpSignature", "signer": "Authorized Representative", "mappings": [{"mapping": "entitysignature_signaturepage", "signatureType": "Signature", "isOptional": false}, {"mapping": "entityauthorizedname_signaturepage", "signatureType": "Name", "isOptional": false}, {"mapping": "entitytitle_signaturepage", "signatureType": "Title", "isOptional": false}, {"mapping": "dated_signaturepage_part5_entity_qualificationstatement", "signatureType": "Date", "isOptional": false}, {"mapping": "signature_signaturepage_part5_entity_qualificationstatement", "signatureType": "Signature", "isOptional": false}, {"mapping": "signatoryname_signaturepage_part5_entity_qualificationstatement", "signatureType": "Name", "isOptional": false}, {"mapping": "signatorytitle_signaturepage_part5_entity_qualificationstatement", "signatureType": "Title", "isOptional": false}, {"mapping": "signature_partg_kyc", "signatureType": "Signature", "isOptional": false}, {"mapping": "position_partg_kyc", "signatureType": "Title", "isOptional": false}, {"mapping": "contactemail_partg_kyc", "signatureType": "Custom", "isOptional": false}, {"mapping": "entityname_partg_kyc", "signatureType": "Name", "isOptional": false}, {"mapping": "date_partg_kyc", "signatureType": "Date", "isOptional": false}, {"mapping": "date_aml_signaturepage", "signatureType": "Date", "isOptional": false}, {"mapping": "signatureauthorizedrep_aml_signaturepage", "signatureType": "Signature", "isOptional": false}, {"mapping": "nameauthorizedrep_aml_signaturepage", "signatureType": "Name", "isOptional": false}, {"mapping": "title_aml_signaturepage", "signatureType": "Title", "isOptional": false}], "uniqueSignerEmail": false}]}, "catalaData": {"formStructs": ["declaration structure Asa_Correspondences_Primarycontact_Contactinfo_Struct:\n  data annualvalidation_correspondences_primarycontact_contactinfo content integer\n  data asa_quarterlycapitalstatement_correspondences_primarycontact_contactinfo content integer\n  data asa_distribution_correspondences_primarycontact_contactinfo content integer\n  data asa_capitalcall_correspondences_primarycontact_contactinfo content integer\n  data asa_annualmeeting_correspondences_primarycontact_contactinfo content integer\n  data asa_report_correspondences_primarycontact_contactinfo content integer\n  data asa_financials_correspondences_primarycontact_contactinfo content integer\n  data asa_taxreport_correspondences_primarycontact_contactinfo content integer\n  data asa_investorletter_correspondes_primarycontact_contactinfo content integer\n  data asa_general_correspondences_primarycontact_contactinfo content integer\ndeclaration asa_correspondences_primarycontact_contactinfo_options content Asa_Correspondences_Primarycontact_Contactinfo_Struct equals Asa_Correspondences_Primarycontact_Contactinfo_Struct {\n  -- annualvalidation_correspondences_primarycontact_contactinfo : 1\n  -- asa_quarterlycapitalstatement_correspondences_primarycontact_contactinfo : 2\n  -- asa_distribution_correspondences_primarycontact_contactinfo : 3\n  -- asa_capitalcall_correspondences_primarycontact_contactinfo : 4\n  -- asa_annualmeeting_correspondences_primarycontact_contactinfo : 5\n  -- asa_report_correspondences_primarycontact_contactinfo : 6\n  -- asa_financials_correspondences_primarycontact_contactinfo : 7\n  -- asa_taxreport_correspondences_primarycontact_contactinfo : 8\n  -- asa_investorletter_correspondes_primarycontact_contactinfo : 9\n  -- asa_general_correspondences_primarycontact_contactinfo : 10\n}", "declaration structure Asa_Correspondences_Additionalcontact_Contactinfo_Struct:\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo content integer\n  data asa_general_correspondences_additionalcontact_contactinfo content integer\n  data asa_report_correspondences_additionalcontact_contactinfo content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_0 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_0 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_0 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_1 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_1 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_1 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_2 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_2 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_2 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_3 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_3 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_3 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_4 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_4 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_4 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_5 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_5 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_5 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_6 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_6 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_6 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_7 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_7 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_7 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_8 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_8 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_8 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_9 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_9 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_9 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_10 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_10 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_10 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_11 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_11 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_11 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_12 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_12 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_12 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_13 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_13 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_13 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_14 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_14 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_14 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_15 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_15 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_15 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_16 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_16 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_16 content integer\n  data asa_annualmeeting_correspondences_additionalcontact_contactinfo_17 content integer\n  data annualvalidation_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_capitalcall_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_distribution_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_financials_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_taxreport_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_investorletter_correspondes_additionalcontact_contactinfo_17 content integer\n  data asa_general_correspondences_additionalcontact_contactinfo_17 content integer\n  data asa_report_correspondences_additionalcontact_contactinfo_17 content integer\ndeclaration asa_correspondences_additionalcontact_contactinfo_options content Asa_Correspondences_Additionalcontact_Contactinfo_Struct equals Asa_Correspondences_Additionalcontact_Contactinfo_Struct {\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo : 1\n  -- annualvalidation_correspondences_additionalcontact_contactinfo : 2\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo : 3\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo : 4\n  -- asa_distribution_correspondences_additionalcontact_contactinfo : 5\n  -- asa_financials_correspondences_additionalcontact_contactinfo : 6\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo : 7\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo : 8\n  -- asa_general_correspondences_additionalcontact_contactinfo : 9\n  -- asa_report_correspondences_additionalcontact_contactinfo : 10\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_0 : 11\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_0 : 12\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_0 : 13\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_0 : 14\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_0 : 15\n  -- asa_financials_correspondences_additionalcontact_contactinfo_0 : 16\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_0 : 17\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_0 : 18\n  -- asa_general_correspondences_additionalcontact_contactinfo_0 : 19\n  -- asa_report_correspondences_additionalcontact_contactinfo_0 : 20\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_1 : 21\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_1 : 22\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_1 : 23\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_1 : 24\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_1 : 25\n  -- asa_financials_correspondences_additionalcontact_contactinfo_1 : 26\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_1 : 27\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_1 : 28\n  -- asa_general_correspondences_additionalcontact_contactinfo_1 : 29\n  -- asa_report_correspondences_additionalcontact_contactinfo_1 : 30\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_2 : 31\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_2 : 32\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_2 : 33\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_2 : 34\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_2 : 35\n  -- asa_financials_correspondences_additionalcontact_contactinfo_2 : 36\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_2 : 37\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_2 : 38\n  -- asa_general_correspondences_additionalcontact_contactinfo_2 : 39\n  -- asa_report_correspondences_additionalcontact_contactinfo_2 : 40\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_3 : 41\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_3 : 42\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_3 : 43\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_3 : 44\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_3 : 45\n  -- asa_financials_correspondences_additionalcontact_contactinfo_3 : 46\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_3 : 47\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_3 : 48\n  -- asa_general_correspondences_additionalcontact_contactinfo_3 : 49\n  -- asa_report_correspondences_additionalcontact_contactinfo_3 : 50\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_4 : 51\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_4 : 52\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_4 : 53\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_4 : 54\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_4 : 55\n  -- asa_financials_correspondences_additionalcontact_contactinfo_4 : 56\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_4 : 57\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_4 : 58\n  -- asa_general_correspondences_additionalcontact_contactinfo_4 : 59\n  -- asa_report_correspondences_additionalcontact_contactinfo_4 : 60\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_5 : 61\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_5 : 62\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_5 : 63\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_5 : 64\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_5 : 65\n  -- asa_financials_correspondences_additionalcontact_contactinfo_5 : 66\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_5 : 67\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_5 : 68\n  -- asa_general_correspondences_additionalcontact_contactinfo_5 : 69\n  -- asa_report_correspondences_additionalcontact_contactinfo_5 : 70\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_6 : 71\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_6 : 72\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_6 : 73\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_6 : 74\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_6 : 75\n  -- asa_financials_correspondences_additionalcontact_contactinfo_6 : 76\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_6 : 77\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_6 : 78\n  -- asa_general_correspondences_additionalcontact_contactinfo_6 : 79\n  -- asa_report_correspondences_additionalcontact_contactinfo_6 : 80\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_7 : 81\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_7 : 82\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_7 : 83\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_7 : 84\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_7 : 85\n  -- asa_financials_correspondences_additionalcontact_contactinfo_7 : 86\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_7 : 87\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_7 : 88\n  -- asa_general_correspondences_additionalcontact_contactinfo_7 : 89\n  -- asa_report_correspondences_additionalcontact_contactinfo_7 : 90\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_8 : 91\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_8 : 92\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_8 : 93\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_8 : 94\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_8 : 95\n  -- asa_financials_correspondences_additionalcontact_contactinfo_8 : 96\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_8 : 97\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_8 : 98\n  -- asa_general_correspondences_additionalcontact_contactinfo_8 : 99\n  -- asa_report_correspondences_additionalcontact_contactinfo_8 : 100\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_9 : 101\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_9 : 102\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_9 : 103\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_9 : 104\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_9 : 105\n  -- asa_financials_correspondences_additionalcontact_contactinfo_9 : 106\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_9 : 107\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_9 : 108\n  -- asa_general_correspondences_additionalcontact_contactinfo_9 : 109\n  -- asa_report_correspondences_additionalcontact_contactinfo_9 : 110\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_10 : 111\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_10 : 112\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_10 : 113\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_10 : 114\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_10 : 115\n  -- asa_financials_correspondences_additionalcontact_contactinfo_10 : 116\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_10 : 117\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_10 : 118\n  -- asa_general_correspondences_additionalcontact_contactinfo_10 : 119\n  -- asa_report_correspondences_additionalcontact_contactinfo_10 : 120\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_11 : 121\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_11 : 122\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_11 : 123\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_11 : 124\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_11 : 125\n  -- asa_financials_correspondences_additionalcontact_contactinfo_11 : 126\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_11 : 127\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_11 : 128\n  -- asa_general_correspondences_additionalcontact_contactinfo_11 : 129\n  -- asa_report_correspondences_additionalcontact_contactinfo_11 : 130\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_12 : 131\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_12 : 132\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_12 : 133\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_12 : 134\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_12 : 135\n  -- asa_financials_correspondences_additionalcontact_contactinfo_12 : 136\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_12 : 137\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_12 : 138\n  -- asa_general_correspondences_additionalcontact_contactinfo_12 : 139\n  -- asa_report_correspondences_additionalcontact_contactinfo_12 : 140\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_13 : 141\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_13 : 142\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_13 : 143\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_13 : 144\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_13 : 145\n  -- asa_financials_correspondences_additionalcontact_contactinfo_13 : 146\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_13 : 147\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_13 : 148\n  -- asa_general_correspondences_additionalcontact_contactinfo_13 : 149\n  -- asa_report_correspondences_additionalcontact_contactinfo_13 : 150\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_14 : 151\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_14 : 152\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_14 : 153\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_14 : 154\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_14 : 155\n  -- asa_report_correspondences_additionalcontact_contactinfo_14 : 156\n  -- asa_financials_correspondences_additionalcontact_contactinfo_14 : 157\n  -- asa_general_correspondences_additionalcontact_contactinfo_14 : 158\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_14 : 159\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_14 : 160\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_15 : 161\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_15 : 162\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_15 : 163\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_15 : 164\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_15 : 165\n  -- asa_financials_correspondences_additionalcontact_contactinfo_15 : 166\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_15 : 167\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_15 : 168\n  -- asa_general_correspondences_additionalcontact_contactinfo_15 : 169\n  -- asa_report_correspondences_additionalcontact_contactinfo_15 : 170\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_16 : 171\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_16 : 172\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_16 : 173\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_16 : 174\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_16 : 175\n  -- asa_financials_correspondences_additionalcontact_contactinfo_16 : 176\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_16 : 177\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_16 : 178\n  -- asa_general_correspondences_additionalcontact_contactinfo_16 : 179\n  -- asa_report_correspondences_additionalcontact_contactinfo_16 : 180\n  -- asa_annualmeeting_correspondences_additionalcontact_contactinfo_17 : 181\n  -- annualvalidation_correspondences_additionalcontact_contactinfo_17 : 182\n  -- asa_capitalcall_correspondences_additionalcontact_contactinfo_17 : 183\n  -- asa_quarterlycapitalstatement_correspondences_additionalcontact_contactinfo_17 : 184\n  -- asa_distribution_correspondences_additionalcontact_contactinfo_17 : 185\n  -- asa_financials_correspondences_additionalcontact_contactinfo_17 : 186\n  -- asa_taxreport_correspondences_additionalcontact_contactinfo_17 : 187\n  -- asa_investorletter_correspondes_additionalcontact_contactinfo_17 : 188\n  -- asa_general_correspondences_additionalcontact_contactinfo_17 : 189\n  -- asa_report_correspondences_additionalcontact_contactinfo_17 : 190\n}", "declaration structure Group_Asa_Yes_Worth1m_Naturalperson_Accreditedinvestor_Struct:\n  data asa_yes_worth1m_naturalperson_accreditedinvestor content integer\ndeclaration group_asa_yes_worth1m_naturalperson_accreditedinvestor_options content Group_Asa_Yes_Worth1m_Naturalperson_Accreditedinvestor_Struct equals Group_Asa_Yes_Worth1m_Naturalperson_Accreditedinvestor_Struct {\n  -- asa_yes_worth1m_naturalperson_accreditedinvestor : 1\n}", "declaration structure Group_Asa_Yes_Earn200k_Naturalperson_Accreditedinvestor_Struct:\n  data asa_yes_earn200k_naturalperson_accreditedinvestor content integer\ndeclaration group_asa_yes_earn200k_naturalperson_accreditedinvestor_options content Group_Asa_Yes_Earn200k_Naturalperson_Accreditedinvestor_Struct equals Group_Asa_Yes_Earn200k_Naturalperson_Accreditedinvestor_Struct {\n  -- asa_yes_earn200k_naturalperson_accreditedinvestor : 1\n}", "declaration structure Group_Asa_Yes_Gpdirectorofthefund_Naturalperson_Accreditedinvestor_Struct:\n  data asa_yes_gpdirectorofthefund_naturalperson_accreditedinvestor content integer\ndeclaration group_asa_yes_gpdirectorofthefund_naturalperson_accreditedinvestor_options content Group_Asa_Yes_Gpdirectorofthefund_Naturalperson_Accreditedinvestor_Struct equals Group_Asa_Yes_Gpdirectorofthefund_Naturalperson_Accreditedinvestor_Struct {\n  -- asa_yes_gpdirectorofthefund_naturalperson_accreditedinvestor : 1\n}", "declaration structure Group_Asa_Yes_Procertification_Naturalperson_Accreditedinvestor_Struct:\n  data asa_yes_procertification_naturalperson_accreditedinvestor content integer\ndeclaration group_asa_yes_procertification_naturalperson_accreditedinvestor_options content Group_Asa_Yes_Procertification_Naturalperson_Accreditedinvestor_Struct equals Group_Asa_Yes_Procertification_Naturalperson_Accreditedinvestor_Struct {\n  -- asa_yes_procertification_naturalperson_accreditedinvestor : 1\n}", "declaration structure Group_Asa_Yes_Familyclient_Accreditedinvestor_Struct:\n  data asa_yes_familyclient_accreditedinvestor content integer\ndeclaration group_asa_yes_familyclient_accreditedinvestor_options content Group_Asa_Yes_Familyclient_Accreditedinvestor_Struct equals Group_Asa_Yes_Familyclient_Accreditedinvestor_Struct {\n  -- asa_yes_familyclient_accreditedinvestor : 1\n}", "declaration structure B_Part1_Individual_Qualificationstatement_Struct:\n  data false_b_part1_individual_qualificationstatement content integer\n  data true_b_part1_individual_qualificationstatement content integer\ndeclaration b_part1_individual_qualificationstatement_options content B_Part1_Individual_Qualificationstatement_Struct equals B_Part1_Individual_Qualificationstatement_Struct {\n  -- false_b_part1_individual_qualificationstatement : 1\n  -- true_b_part1_individual_qualificationstatement : 2\n}", "declaration structure A_Part2_Individual_Qualificationstatement_Struct:\n  data true_a_part2_individual_qualificationstatement content integer\n  data false_a_part2_individual_qualificationstatement content integer\ndeclaration a_part2_individual_qualificationstatement_options content A_Part2_Individual_Qualificationstatement_Struct equals A_Part2_Individual_Qualificationstatement_Struct {\n  -- true_a_part2_individual_qualificationstatement : 1\n  -- false_a_part2_individual_qualificationstatement : 2\n}", "declaration structure B_Part2_Individual_Qualificationstatement_Struct:\n  data true_b_part2_individual_qualificationstatement content integer\n  data false_b_part2_individual_qualificationstatement content integer\ndeclaration b_part2_individual_qualificationstatement_options content B_Part2_Individual_Qualificationstatement_Struct equals B_Part2_Individual_Qualificationstatement_Struct {\n  -- true_b_part2_individual_qualificationstatement : 1\n  -- false_b_part2_individual_qualificationstatement : 2\n}", "declaration structure C_Part2_Individual_Qualificationstatement_Struct:\n  data true_c_part2_individual_qualificationstatement content integer\n  data false_c_part2_individual_qualificationstatement content integer\ndeclaration c_part2_individual_qualificationstatement_options content C_Part2_Individual_Qualificationstatement_Struct equals C_Part2_Individual_Qualificationstatement_Struct {\n  -- true_c_part2_individual_qualificationstatement : 1\n  -- false_c_part2_individual_qualificationstatement : 2\n}", "declaration structure Part3_Individual_Qualificationstatement_Struct:\n  data true_part3_individual_qualificationstatement content integer\n  data false_part3_individual_qualificationstatement content integer\ndeclaration part3_individual_qualificationstatement_options content Part3_Individual_Qualificationstatement_Struct equals Part3_Individual_Qualificationstatement_Struct {\n  -- true_part3_individual_qualificationstatement : 1\n  -- false_part3_individual_qualificationstatement : 2\n}", "declaration structure Asa_Plansubjectto4975_Erisa_Struct:\n  data asa_yes_plansubjectto4975_erisa content integer\n  data asa_no_plansubjectto4975_erisa content integer\ndeclaration asa_plansubjectto4975_erisa_options content Asa_Plansubjectto4975_Erisa_Struct equals Asa_Plansubjectto4975_Erisa_Struct {\n  -- asa_yes_plansubjectto4975_erisa : 1\n  -- asa_no_plansubjectto4975_erisa : 2\n}", "declaration structure Suba_Part4_Individual_Qualificationstatement_Struct:\n  data yes_suba_part4_individual_qualificationstatement content integer\n  data no_suba_part4_individual_qualificationstatement content integer\ndeclaration suba_part4_individual_qualificationstatement_options content Suba_Part4_Individual_Qualificationstatement_Struct equals Suba_Part4_Individual_Qualificationstatement_Struct {\n  -- yes_suba_part4_individual_qualificationstatement : 1\n  -- no_suba_part4_individual_qualificationstatement : 2\n}", "declaration structure B_Part4_Individual_Qualificationstatement_Struct:\n  data true_b_part4_individual_qualificationstatement content integer\n  data false_b_part4_individual_qualificationstatement content integer\ndeclaration b_part4_individual_qualificationstatement_options content B_Part4_Individual_Qualificationstatement_Struct equals B_Part4_Individual_Qualificationstatement_Struct {\n  -- true_b_part4_individual_qualificationstatement : 1\n  -- false_b_part4_individual_qualificationstatement : 2\n}", "declaration structure C_Part4_Individual_Qualificationstatement_Struct:\n  data no_c_part4_individual_qualificationstatement content integer\n  data yes_c_part4_individual_qualificationstatement content integer\ndeclaration c_part4_individual_qualificationstatement_options content C_Part4_Individual_Qualificationstatement_Struct equals C_Part4_Individual_Qualificationstatement_Struct {\n  -- no_c_part4_individual_qualificationstatement : 1\n  -- yes_c_part4_individual_qualificationstatement : 2\n}", "declaration structure E_Part4_Individual_Qualificationstatement_Struct:\n  data one_e_part4_individual_qualificationstatement content integer\n  data two_e_part4_individual_qualificationstatement content integer\n  data three_e_part4_individual_qualificationstatement content integer\n  data four_e_part4_individual_qualificationstatement content integer\n  data five_e_part4_individual_qualificationstatement content integer\n  data six_e_part4_individual_qualificationstatement content integer\ndeclaration e_part4_individual_qualificationstatement_options content E_Part4_Individual_Qualificationstatement_Struct equals E_Part4_Individual_Qualificationstatement_Struct {\n  -- one_e_part4_individual_qualificationstatement : 1\n  -- two_e_part4_individual_qualificationstatement : 2\n  -- three_e_part4_individual_qualificationstatement : 3\n  -- four_e_part4_individual_qualificationstatement : 4\n  -- five_e_part4_individual_qualificationstatement : 5\n  -- six_e_part4_individual_qualificationstatement : 6\n}", "declaration structure Excessamount_Part4_Struct:\n  data yes_excessamount_part4 content integer\n  data no_excessamount_part4 content integer\ndeclaration excessamount_part4_options content Excessamount_Part4_Struct equals Excessamount_Part4_Struct {\n  -- yes_excessamount_part4 : 1\n  -- no_excessamount_part4 : 2\n}", "declaration structure Asa_Accreditedinvestor_Accreditedinvestor_Struct:\n  data asa_yes_bank_accreditedinvestor content integer\n  data asa_yes_broker_accreditedinvestor content integer\n  data asa_yes_regsec203_investmentadviser_accreditedinvestor content integer\n  data asa_yes_insurancecompany_accreditedinvestor content integer\n  data asa_yes_sec2a48company_accreditedinvestor content integer\n  data asa_yes_sec2a48company_accreditedinvestor_1 content integer\n  data yes_asa_smallbusinessinvestmentcompany_accreditedinvestor content integer\n  data asa_yes_rbic_accreditedinvestor content integer\n  data asa_yes_plan_accreditedinvestor content integer\n  data asa_yes_privatecompany_accreditedinvestor content integer\n  data asa_yes_trust_accreditedinvestor content integer\n  data asa_yes_familyoffice_accreditedinvestor content integer\n  data asa_yes_allequityholder_otherentity_accreditedinvestor content integer\n  data asa_yes_otherown5m_otherentity_accreditedinvestor content integer\ndeclaration asa_accreditedinvestor_accreditedinvestor_options content Asa_Accreditedinvestor_Accreditedinvestor_Struct equals Asa_Accreditedinvestor_Accreditedinvestor_Struct {\n  -- asa_yes_bank_accreditedinvestor : 1\n  -- asa_yes_broker_accreditedinvestor : 2\n  -- asa_yes_regsec203_investmentadviser_accreditedinvestor : 3\n  -- asa_yes_insurancecompany_accreditedinvestor : 4\n  -- asa_yes_sec2a48company_accreditedinvestor : 5\n  -- asa_yes_sec2a48company_accreditedinvestor_1 : 6\n  -- yes_asa_smallbusinessinvestmentcompany_accreditedinvestor : 7\n  -- asa_yes_rbic_accreditedinvestor : 8\n  -- asa_yes_plan_accreditedinvestor : 9\n  -- asa_yes_privatecompany_accreditedinvestor : 10\n  -- asa_yes_trust_accreditedinvestor : 11\n  -- asa_yes_familyoffice_accreditedinvestor : 12\n  -- asa_yes_allequityholder_otherentity_accreditedinvestor : 13\n  -- asa_yes_otherown5m_otherentity_accreditedinvestor : 14\n}", "declaration structure Group_Asa_Yes_Erisaplan_Accreditedinvestor_Struct:\n  data asa_yes_erisaplan_accreditedinvestor content integer\ndeclaration group_asa_yes_erisaplan_accreditedinvestor_options content Group_Asa_Yes_Erisaplan_Accreditedinvestor_Struct equals Group_Asa_Yes_Erisaplan_Accreditedinvestor_Struct {\n  -- asa_yes_erisaplan_accreditedinvestor : 1\n}", "declaration structure Group_Asa_Yes_Erisaplan_Accreditedinvestor_1_Struct:\n  data asa_yes_erisaplan_accreditedinvestor_1 content integer\ndeclaration group_asa_yes_erisaplan_accreditedinvestor_1_options content Group_Asa_Yes_Erisaplan_Accreditedinvestor_1_Struct equals Group_Asa_Yes_Erisaplan_Accreditedinvestor_1_Struct {\n  -- asa_yes_erisaplan_accreditedinvestor_1 : 1\n}", "declaration structure Group_Asa_Yes_Erisaplan_Accreditedinvestor_2_Struct:\n  data asa_yes_erisaplan_accreditedinvestor_2 content integer\ndeclaration group_asa_yes_erisaplan_accreditedinvestor_2_options content Group_Asa_Yes_Erisaplan_Accreditedinvestor_2_Struct equals Group_Asa_Yes_Erisaplan_Accreditedinvestor_2_Struct {\n  -- asa_yes_erisaplan_accreditedinvestor_2 : 1\n}", "declaration structure Twelve_A_Part1_Entitiesusiqs_Struct:\n  data a_twelve_a_part1_entitiesusiqs content integer\n  data b_twelve_a_part1_entitiesusiqs content integer\n  data c_twelve_a_part1_entitiesusiqs content integer\ndeclaration twelve_a_part1_entitiesusiqs_options content Twelve_A_Part1_Entitiesusiqs_Struct equals Twelve_A_Part1_Entitiesusiqs_Struct {\n  -- a_twelve_a_part1_entitiesusiqs : 1\n  -- b_twelve_a_part1_entitiesusiqs : 2\n  -- c_twelve_a_part1_entitiesusiqs : 3\n}", "declaration structure B_Part1_Entitiesusiqs_Struct:\n  data true_b_part1_entitiesusiqs content integer\n  data false_b_part1_entitiesusiqs content integer\ndeclaration b_part1_entitiesusiqs_options content B_Part1_Entitiesusiqs_Struct equals B_Part1_Entitiesusiqs_Struct {\n  -- true_b_part1_entitiesusiqs : 1\n  -- false_b_part1_entitiesusiqs : 2\n}", "declaration structure A2_Part2_Entity_Qualificationstatement_Struct:\n  data true_a2_part2_entity_qualificationstatement content integer\n  data false_a2_part2_entity_qualificationstatement content integer\ndeclaration a2_part2_entity_qualificationstatement_options content A2_Part2_Entity_Qualificationstatement_Struct equals A2_Part2_Entity_Qualificationstatement_Struct {\n  -- true_a2_part2_entity_qualificationstatement : 1\n  -- false_a2_part2_entity_qualificationstatement : 2\n}", "declaration structure B_Part2_Entity_Qualificationstatement_Struct:\n  data true_b_part2_entity_qualificationstatement content integer\n  data false_b_part2_entity_qualificationstatement content integer\ndeclaration b_part2_entity_qualificationstatement_options content B_Part2_Entity_Qualificationstatement_Struct equals B_Part2_Entity_Qualificationstatement_Struct {\n  -- true_b_part2_entity_qualificationstatement : 1\n  -- false_b_part2_entity_qualificationstatement : 2\n}", "declaration structure C_Part2_Entity_Qualificationstatement_Struct:\n  data true_c_part2_entity_qualificationstatement content integer\n  data false_c_part2_entity_qualificationstatement content integer\ndeclaration c_part2_entity_qualificationstatement_options content C_Part2_Entity_Qualificationstatement_Struct equals C_Part2_Entity_Qualificationstatement_Struct {\n  -- true_c_part2_entity_qualificationstatement : 1\n  -- false_c_part2_entity_qualificationstatement : 2\n}", "declaration structure E_Part2_Entity_Qualificationstatement_Struct:\n  data true_e_part2_entity_qualificationstatement content integer\n  data false_e_part2_entity_qualificationstatement content integer\ndeclaration e_part2_entity_qualificationstatement_options content E_Part2_Entity_Qualificationstatement_Struct equals E_Part2_Entity_Qualificationstatement_Struct {\n  -- true_e_part2_entity_qualificationstatement : 1\n  -- false_e_part2_entity_qualificationstatement : 2\n}", "declaration structure F_Part2_Entity_Qualificationstatement_Struct:\n  data true_f_part2_entity_qualificationstatement content integer\n  data false_f_part2_entity_qualificationstatement content integer\ndeclaration f_part2_entity_qualificationstatement_options content F_Part2_Entity_Qualificationstatement_Struct equals F_Part2_Entity_Qualificationstatement_Struct {\n  -- true_f_part2_entity_qualificationstatement : 1\n  -- false_f_part2_entity_qualificationstatement : 2\n}", "declaration structure G_Part2_Entity_Qualificationstatement_Struct:\n  data true_g_part2_entity_qualificationstatement content integer\n  data false_g_part2_entity_qualificationstatement content integer\ndeclaration g_part2_entity_qualificationstatement_options content G_Part2_Entity_Qualificationstatement_Struct equals G_Part2_Entity_Qualificationstatement_Struct {\n  -- true_g_part2_entity_qualificationstatement : 1\n  -- false_g_part2_entity_qualificationstatement : 2\n}", "declaration structure H_Part2_Entity_Qualificationstatement_Struct:\n  data true_h_part2_entity_qualificationstatement content integer\n  data false_h_part2_entity_qualificationstatement content integer\ndeclaration h_part2_entity_qualificationstatement_options content H_Part2_Entity_Qualificationstatement_Struct equals H_Part2_Entity_Qualificationstatement_Struct {\n  -- true_h_part2_entity_qualificationstatement : 1\n  -- false_h_part2_entity_qualificationstatement : 2\n}", "declaration structure I_Part2_Entity_Qualificationstatement_Struct:\n  data true_i_part2_entity_qualificationstatement content integer\n  data falsei_part2_entity_qualificationstatement content integer\ndeclaration i_part2_entity_qualificationstatement_options content I_Part2_Entity_Qualificationstatement_Struct equals I_Part2_Entity_Qualificationstatement_Struct {\n  -- true_i_part2_entity_qualificationstatement : 1\n  -- falsei_part2_entity_qualificationstatement : 2\n}", "declaration structure A1_Part3_Entity_Qualificationstatement_Struct:\n  data true_a1_part3_entity_qualificationstatement content integer\n  data false_a1_part3_entity_qualificationstatement content integer\ndeclaration a1_part3_entity_qualificationstatement_options content A1_Part3_Entity_Qualificationstatement_Struct equals A1_Part3_Entity_Qualificationstatement_Struct {\n  -- true_a1_part3_entity_qualificationstatement : 1\n  -- false_a1_part3_entity_qualificationstatement : 2\n}", "declaration structure A2_Part3_Entity_Qualificationstatement_Struct:\n  data true_a2_part3_entity_qualificationstatement content integer\n  data false_a2_part3_entity_qualificationstatement content integer\ndeclaration a2_part3_entity_qualificationstatement_options content A2_Part3_Entity_Qualificationstatement_Struct equals A2_Part3_Entity_Qualificationstatement_Struct {\n  -- true_a2_part3_entity_qualificationstatement : 1\n  -- false_a2_part3_entity_qualificationstatement : 2\n}", "declaration structure B_Part3_Entity_Qualificationstatement_Struct:\n  data true_b_part3_entity_qualificationstatement content integer\n  data false_b_part3_entity_qualificationstatement content integer\ndeclaration b_part3_entity_qualificationstatement_options content B_Part3_Entity_Qualificationstatement_Struct equals B_Part3_Entity_Qualificationstatement_Struct {\n  -- true_b_part3_entity_qualificationstatement : 1\n  -- false_b_part3_entity_qualificationstatement : 2\n}", "declaration structure C_Part3_Entity_Qualificationstatement_Struct:\n  data true_c_part3_entity_qualificationstatement content integer\n  data false_c_part3_entity_qualificationstatement content integer\ndeclaration c_part3_entity_qualificationstatement_options content C_Part3_Entity_Qualificationstatement_Struct equals C_Part3_Entity_Qualificationstatement_Struct {\n  -- true_c_part3_entity_qualificationstatement : 1\n  -- false_c_part3_entity_qualificationstatement : 2\n}", "declaration structure D_Part3_Entity_Qualificationstatement_Struct:\n  data true_d_part3_entity_qualificationstatement content integer\n  data false_d_part3_entity_qualificationstatement content integer\ndeclaration d_part3_entity_qualificationstatement_options content D_Part3_Entity_Qualificationstatement_Struct equals D_Part3_Entity_Qualificationstatement_Struct {\n  -- true_d_part3_entity_qualificationstatement : 1\n  -- false_d_part3_entity_qualificationstatement : 2\n}", "declaration structure Group_Asa_Inv25m_Entity_Qualifiedpurchaser_Struct:\n  data asa_inv25m_entity_qualifiedpurchaser content integer\ndeclaration group_asa_inv25m_entity_qualifiedpurchaser_options content Group_Asa_Inv25m_Entity_Qualifiedpurchaser_Struct equals Group_Asa_Inv25m_Entity_Qualifiedpurchaser_Struct {\n  -- asa_inv25m_entity_qualifiedpurchaser : 1\n}", "declaration structure Group_Asa_Trust_Qualifiedpurchaser_Struct:\n  data asa_trust_qualifiedpurchaser content integer\ndeclaration group_asa_trust_qualifiedpurchaser_options content Group_Asa_Trust_Qualifiedpurchaser_Struct equals Group_Asa_Trust_Qualifiedpurchaser_Struct {\n  -- asa_trust_qualifiedpurchaser : 1\n}", "declaration structure Group_Asa_Own5m_Entity_Qualifiedpurchaser_Struct:\n  data asa_own5m_entity_qualifiedpurchaser content integer\ndeclaration group_asa_own5m_entity_qualifiedpurchaser_options content Group_Asa_Own5m_Entity_Qualifiedpurchaser_Struct equals Group_Asa_Own5m_Entity_Qualifiedpurchaser_Struct {\n  -- asa_own5m_entity_qualifiedpurchaser : 1\n}", "declaration structure Group_Asa_Allowners_Otherentity_Qualifiedpurchaser_Struct:\n  data asa_allowners_otherentity_qualifiedpurchaser content integer\ndeclaration group_asa_allowners_otherentity_qualifiedpurchaser_options content Group_Asa_Allowners_Otherentity_Qualifiedpurchaser_Struct equals Group_Asa_Allowners_Otherentity_Qualifiedpurchaser_Struct {\n  -- asa_allowners_otherentity_qualifiedpurchaser : 1\n}", "declaration structure Group_Asa_Rule2a51_Institutionalbuyer_Qualifiedpurchaser_Struct:\n  data asa_rule2a51_institutionalbuyer_qualifiedpurchaser content integer\ndeclaration group_asa_rule2a51_institutionalbuyer_qualifiedpurchaser_options content Group_Asa_Rule2a51_Institutionalbuyer_Qualifiedpurchaser_Struct equals Group_Asa_Rule2a51_Institutionalbuyer_Qualifiedpurchaser_Struct {\n  -- asa_rule2a51_institutionalbuyer_qualifiedpurchaser : 1\n}", "declaration structure Group_Asa_Isnot_Qualifiedpurchaser_Struct:\n  data asa_isnot_qualifiedpurchaser content integer\ndeclaration group_asa_isnot_qualifiedpurchaser_options content Group_Asa_Isnot_Qualifiedpurchaser_Struct equals Group_Asa_Isnot_Qualifiedpurchaser_Struct {\n  -- asa_isnot_qualifiedpurchaser : 1\n}", "declaration structure A1_Part5_Entity_Qualificationstatement_Struct:\n  data true_a1_part5_entity_qualificationstatement content integer\n  data false_a1_part5_entity_qualificationstatement content integer\ndeclaration a1_part5_entity_qualificationstatement_options content A1_Part5_Entity_Qualificationstatement_Struct equals A1_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a1_part5_entity_qualificationstatement : 1\n  -- false_a1_part5_entity_qualificationstatement : 2\n}", "declaration structure A2_Part5_Entity_Qualificationstatement_Struct:\n  data true_a2_part5_entity_qualificationstatement content integer\n  data false_a2_part5_entity_qualificationstatement content integer\ndeclaration a2_part5_entity_qualificationstatement_options content A2_Part5_Entity_Qualificationstatement_Struct equals A2_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a2_part5_entity_qualificationstatement : 1\n  -- false_a2_part5_entity_qualificationstatement : 2\n}", "declaration structure A3_Part5_Entity_Qualificationstatement_Struct:\n  data false_a3_part5_entity_qualificationstatement content integer\n  data true_a3_part5_entity_qualificationstatement content integer\ndeclaration a3_part5_entity_qualificationstatement_options content A3_Part5_Entity_Qualificationstatement_Struct equals A3_Part5_Entity_Qualificationstatement_Struct {\n  -- false_a3_part5_entity_qualificationstatement : 1\n  -- true_a3_part5_entity_qualificationstatement : 2\n}", "declaration structure A4_Part5_Entity_Qualificationstatement_Struct:\n  data true_a4_part5_entity_qualificationstatement content integer\n  data false_a4_part5_entity_qualificationstatement content integer\ndeclaration a4_part5_entity_qualificationstatement_options content A4_Part5_Entity_Qualificationstatement_Struct equals A4_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a4_part5_entity_qualificationstatement : 1\n  -- false_a4_part5_entity_qualificationstatement : 2\n}", "declaration structure Suba4_Part5_Entity_Qualificationstatement_Struct:\n  data no_suba4_part5_entity_qualificationstatement content integer\n  data yes_suba4_part5_entity_qualificationstatement content integer\ndeclaration suba4_part5_entity_qualificationstatement_options content Suba4_Part5_Entity_Qualificationstatement_Struct equals Suba4_Part5_Entity_Qualificationstatement_Struct {\n  -- no_suba4_part5_entity_qualificationstatement : 1\n  -- yes_suba4_part5_entity_qualificationstatement : 2\n}", "declaration structure A5_Part5_Entity_Qualificationstatement_Struct:\n  data false_a5_part5_entity_qualificationstatement content integer\n  data true_a5_part5_entity_qualificationstatement content integer\ndeclaration a5_part5_entity_qualificationstatement_options content A5_Part5_Entity_Qualificationstatement_Struct equals A5_Part5_Entity_Qualificationstatement_Struct {\n  -- false_a5_part5_entity_qualificationstatement : 1\n  -- true_a5_part5_entity_qualificationstatement : 2\n}", "declaration structure Suba5_Part5_Entity_Qualificationstatement_Struct:\n  data no_suba5_part5_entity_qualificationstatement content integer\n  data yes_suba5_part5_entity_qualificationstatement content integer\ndeclaration suba5_part5_entity_qualificationstatement_options content Suba5_Part5_Entity_Qualificationstatement_Struct equals Suba5_Part5_Entity_Qualificationstatement_Struct {\n  -- no_suba5_part5_entity_qualificationstatement : 1\n  -- yes_suba5_part5_entity_qualificationstatement : 2\n}", "declaration structure A6_Part5_Entity_Qualificationstatement_Struct:\n  data true_a6_part5_entity_qualificationstatement content integer\n  data false_a6_part5_entity_qualificationstatement content integer\ndeclaration a6_part5_entity_qualificationstatement_options content A6_Part5_Entity_Qualificationstatement_Struct equals A6_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a6_part5_entity_qualificationstatement : 1\n  -- false_a6_part5_entity_qualificationstatement : 2\n}", "declaration structure Suba6_Part5_Entity_Qualificationstatement_Struct:\n  data yes_suba6_part5_entity_qualificationstatement content integer\n  data no_suba6_part5_entity_qualificationstatement content integer\ndeclaration suba6_part5_entity_qualificationstatement_options content Suba6_Part5_Entity_Qualificationstatement_Struct equals Suba6_Part5_Entity_Qualificationstatement_Struct {\n  -- yes_suba6_part5_entity_qualificationstatement : 1\n  -- no_suba6_part5_entity_qualificationstatement : 2\n}", "declaration structure A7_Part5_Entity_Qualificationstatement_Struct:\n  data true_a7_part5_entity_qualificationstatement content integer\n  data false_a7_part5_entity_qualificationstatement content integer\ndeclaration a7_part5_entity_qualificationstatement_options content A7_Part5_Entity_Qualificationstatement_Struct equals A7_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a7_part5_entity_qualificationstatement : 1\n  -- false_a7_part5_entity_qualificationstatement : 2\n}", "declaration structure A8_Part5_Entity_Qualificationstatement_Struct:\n  data true_a8_part5_entity_qualificationstatement content integer\n  data false_a8_part5_entity_qualificationstatement content integer\ndeclaration a8_part5_entity_qualificationstatement_options content A8_Part5_Entity_Qualificationstatement_Struct equals A8_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a8_part5_entity_qualificationstatement : 1\n  -- false_a8_part5_entity_qualificationstatement : 2\n}", "declaration structure A9_Part5_Entity_Qualificationstatement_Struct:\n  data true_a9_part5_entity_qualificationstatement content integer\n  data false_a9_part5_entity_qualificationstatement content integer\ndeclaration a9_part5_entity_qualificationstatement_options content A9_Part5_Entity_Qualificationstatement_Struct equals A9_Part5_Entity_Qualificationstatement_Struct {\n  -- true_a9_part5_entity_qualificationstatement : 1\n  -- false_a9_part5_entity_qualificationstatement : 2\n}", "declaration structure Suba9_Part5_Entity_Qualificationstatement_Struct:\n  data no_suba9_part5_entity_qualificationstatement content integer\n  data yes_suba9_part5_entity_qualificationstatement content integer\ndeclaration suba9_part5_entity_qualificationstatement_options content Suba9_Part5_Entity_Qualificationstatement_Struct equals Suba9_Part5_Entity_Qualificationstatement_Struct {\n  -- no_suba9_part5_entity_qualificationstatement : 1\n  -- yes_suba9_part5_entity_qualificationstatement : 2\n}", "declaration structure A10_Part5_Entity_Qualificationstatement_Struct:\n  data no_a10_part5_entity_qualificationstatement content integer\n  data yes_a10_part5_entity_qualificationstatement content integer\ndeclaration a10_part5_entity_qualificationstatement_options content A10_Part5_Entity_Qualificationstatement_Struct equals A10_Part5_Entity_Qualificationstatement_Struct {\n  -- no_a10_part5_entity_qualificationstatement : 1\n  -- yes_a10_part5_entity_qualificationstatement : 2\n}", "declaration structure Group_B1_Part5_Entity_Qualificationstatement_Struct:\n  data b1_part5_entity_qualificationstatement content integer\ndeclaration group_b1_part5_entity_qualificationstatement_options content Group_B1_Part5_Entity_Qualificationstatement_Struct equals Group_B1_Part5_Entity_Qualificationstatement_Struct {\n  -- b1_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_B2_Part5_Entity_Qualificationstatement_Struct:\n  data b2_part5_entity_qualificationstatement content integer\ndeclaration group_b2_part5_entity_qualificationstatement_options content Group_B2_Part5_Entity_Qualificationstatement_Struct equals Group_B2_Part5_Entity_Qualificationstatement_Struct {\n  -- b2_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_B3_Part5_Entity_Qualificationstatement_Struct:\n  data b3_part5_entity_qualificationstatement content integer\ndeclaration group_b3_part5_entity_qualificationstatement_options content Group_B3_Part5_Entity_Qualificationstatement_Struct equals Group_B3_Part5_Entity_Qualificationstatement_Struct {\n  -- b3_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_B4_Part5_Entity_Qualificationstatement_Struct:\n  data b4_part5_entity_qualificationstatement content integer\ndeclaration group_b4_part5_entity_qualificationstatement_options content Group_B4_Part5_Entity_Qualificationstatement_Struct equals Group_B4_Part5_Entity_Qualificationstatement_Struct {\n  -- b4_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C1_Part5_Entity_Qualificationstatement_Struct:\n  data c1_part5_entity_qualificationstatement content integer\ndeclaration group_c1_part5_entity_qualificationstatement_options content Group_C1_Part5_Entity_Qualificationstatement_Struct equals Group_C1_Part5_Entity_Qualificationstatement_Struct {\n  -- c1_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C2_Part5_Entity_Qualificationstatement_Struct:\n  data c2_part5_entity_qualificationstatement content integer\ndeclaration group_c2_part5_entity_qualificationstatement_options content Group_C2_Part5_Entity_Qualificationstatement_Struct equals Group_C2_Part5_Entity_Qualificationstatement_Struct {\n  -- c2_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C3_Part5_Entity_Qualificationstatement_Struct:\n  data c3_part5_entity_qualificationstatement content integer\ndeclaration group_c3_part5_entity_qualificationstatement_options content Group_C3_Part5_Entity_Qualificationstatement_Struct equals Group_C3_Part5_Entity_Qualificationstatement_Struct {\n  -- c3_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C4_Part5_Entity_Qualificationstatement_Struct:\n  data c4_part5_entity_qualificationstatement content integer\ndeclaration group_c4_part5_entity_qualificationstatement_options content Group_C4_Part5_Entity_Qualificationstatement_Struct equals Group_C4_Part5_Entity_Qualificationstatement_Struct {\n  -- c4_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C5_Part5_Entity_Qualificationstatement_Struct:\n  data c5_part5_entity_qualificationstatement content integer\ndeclaration group_c5_part5_entity_qualificationstatement_options content Group_C5_Part5_Entity_Qualificationstatement_Struct equals Group_C5_Part5_Entity_Qualificationstatement_Struct {\n  -- c5_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C6_Part5_Entity_Qualificationstatement_Struct:\n  data c6_part5_entity_qualificationstatement content integer\ndeclaration group_c6_part5_entity_qualificationstatement_options content Group_C6_Part5_Entity_Qualificationstatement_Struct equals Group_C6_Part5_Entity_Qualificationstatement_Struct {\n  -- c6_part5_entity_qualificationstatement : 1\n}", "declaration structure Group_C7_Part5_Entity_Qualificationstatement_Struct:\n  data c7_part5_entity_qualificationstatement content integer\ndeclaration group_c7_part5_entity_qualificationstatement_options content Group_C7_Part5_Entity_Qualificationstatement_Struct equals Group_C7_Part5_Entity_Qualificationstatement_Struct {\n  -- c7_part5_entity_qualificationstatement : 1\n}", "declaration structure Fundoffunds_Part5_Entity_Qualificationstatement_Struct:\n  data yes_fundoffunds_part5_entity_qualificationstatement content integer\n  data no_fundoffunds_part5_entity_qualificationstatement content integer\ndeclaration fundoffunds_part5_entity_qualificationstatement_options content Fundoffunds_Part5_Entity_Qualificationstatement_Struct equals Fundoffunds_Part5_Entity_Qualificationstatement_Struct {\n  -- yes_fundoffunds_part5_entity_qualificationstatement : 1\n  -- no_fundoffunds_part5_entity_qualificationstatement : 2\n}", "declaration structure Organizationtype_Part5_Entity_Qualificationstatement_Struct:\n  data broker_organizationtype_part5_entity_qualificationstatement content integer\n  data insurancecompany_organizationtype_part5_entity_qualificationstatement content integer\n  data investmentcompany_organizationtype_part5_entity_qualificationstatement content integer\n  data investmentcompany_organizationtype_part5_entity_qualificationstatement_4 content integer\n  data nonprofitorganization_organizationtype_part5_entity_qualificationstatement content integer\n  data pensionplan_organizationtype_part5_entity_qualificationstatement content integer\n  data banking_organizationtype_part5_entity_qualificationstatement content integer\n  data municipalgovernmententity_organizationtype_part5_entity_qualificationstatement content integer\n  data municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement content integer\n  data wealthfund_organizationtype_part5_entity_qualificationstatement content integer\n  data other_organizationtype_part5_entity_qualificationstatement content integer\ndeclaration organizationtype_part5_entity_qualificationstatement_options content Organizationtype_Part5_Entity_Qualificationstatement_Struct equals Organizationtype_Part5_Entity_Qualificationstatement_Struct {\n  -- broker_organizationtype_part5_entity_qualificationstatement : 1\n  -- insurancecompany_organizationtype_part5_entity_qualificationstatement : 2\n  -- investmentcompany_organizationtype_part5_entity_qualificationstatement : 3\n  -- investmentcompany_organizationtype_part5_entity_qualificationstatement_4 : 4\n  -- nonprofitorganization_organizationtype_part5_entity_qualificationstatement : 5\n  -- pensionplan_organizationtype_part5_entity_qualificationstatement : 6\n  -- banking_organizationtype_part5_entity_qualificationstatement : 7\n  -- municipalgovernmententity_organizationtype_part5_entity_qualificationstatement : 8\n  -- municipalgovernmentalpensionplan_organizationtype_part5_entity_qualificationstatement : 9\n  -- wealthfund_organizationtype_part5_entity_qualificationstatement : 10\n  -- other_organizationtype_part5_entity_qualificationstatement : 11\n}", "declaration structure Governmentplans_Part5_Entity_Qualificationstatement_Struct:\n  data yes_governmentplans_part5_entity_qualificationstatement content integer\n  data no_governmentplans_part5_entity_qualificationstatement content integer\ndeclaration governmentplans_part5_entity_qualificationstatement_options content Governmentplans_Part5_Entity_Qualificationstatement_Struct equals Governmentplans_Part5_Entity_Qualificationstatement_Struct {\n  -- yes_governmentplans_part5_entity_qualificationstatement : 1\n  -- no_governmentplans_part5_entity_qualificationstatement : 2\n}", "declaration structure Foia_Part5_Entity_Qualificationstatement_Struct:\n  data yes_foia_part5_entity_qualificationstatement content integer\n  data no_foia_part5_entity_qualificationstatement content integer\ndeclaration foia_part5_entity_qualificationstatement_options content Foia_Part5_Entity_Qualificationstatement_Struct equals Foia_Part5_Entity_Qualificationstatement_Struct {\n  -- yes_foia_part5_entity_qualificationstatement : 1\n  -- no_foia_part5_entity_qualificationstatement : 2\n}", "declaration structure Election_Qualificationstatement_Struct:\n  data yes_election_qualificationstatement content integer\n  data no_election_qualificationstatement content integer\ndeclaration election_qualificationstatement_options content Election_Qualificationstatement_Struct equals Election_Qualificationstatement_Struct {\n  -- yes_election_qualificationstatement : 1\n  -- no_election_qualificationstatement : 2\n}", "declaration structure Parta_Kyc_Struct:\n  data no_parta_kyc content integer\n  data yes_parta_kyc content integer\ndeclaration parta_kyc_options content Parta_Kyc_Struct equals Parta_Kyc_Struct {\n  -- no_parta_kyc : 1\n  -- yes_parta_kyc : 2\n}", "declaration structure Exemptedclient_Partb_Kyc_Struct:\n  data one_exemptedclient_partb_kyc content integer\n  data two_exemptedclient_partb_kyc content integer\n  data three_exemptedclient_partb_kyc content integer\n  data four_exemptedclient_partb_kyc content integer\n  data five_exemptedclient_partb_kyc content integer\ndeclaration exemptedclient_partb_kyc_options content Exemptedclient_Partb_Kyc_Struct equals Exemptedclient_Partb_Kyc_Struct {\n  -- one_exemptedclient_partb_kyc : 1\n  -- two_exemptedclient_partb_kyc : 2\n  -- three_exemptedclient_partb_kyc : 3\n  -- four_exemptedclient_partb_kyc : 4\n  -- five_exemptedclient_partb_kyc : 5\n}", "declaration structure Nominee_Partb_Kyc_Struct:\n  data no_nominee_partb_kyc content integer\n  data yes_nominee_partb_kyc content integer\ndeclaration nominee_partb_kyc_options content Nominee_Partb_Kyc_Struct equals Nominee_Partb_Kyc_Struct {\n  -- no_nominee_partb_kyc : 1\n  -- yes_nominee_partb_kyc : 2\n}", "declaration structure Group_Eligibleintro_Partb_Kyc_Struct:\n  data eligibleintro_partb_kyc content integer\ndeclaration group_eligibleintro_partb_kyc_options content Group_Eligibleintro_Partb_Kyc_Struct equals Group_Eligibleintro_Partb_Kyc_Struct {\n  -- eligibleintro_partb_kyc : 1\n}", "declaration structure Individual_Partc_Kyc_Struct:\n  data one_individual_partc_kyc content integer\n  data two_individual_partc_kyc content integer\ndeclaration individual_partc_kyc_options content Individual_Partc_Kyc_Struct equals Individual_Partc_Kyc_Struct {\n  -- one_individual_partc_kyc : 1\n  -- two_individual_partc_kyc : 2\n}", "declaration structure Company_Partc_Kyc_Struct:\n  data one_company_partc_kyc content integer\n  data two_company_partc_kyc content integer\n  data three_company_partc_kyc content integer\n  data five_company_partc_kyc content integer\n  data six_company_partc_kyc content integer\n  data seven_company_partc_kyc content integer\n  data eight_company_partc_kyc content integer\ndeclaration company_partc_kyc_options content Company_Partc_Kyc_Struct equals Company_Partc_Kyc_Struct {\n  -- one_company_partc_kyc : 1\n  -- two_company_partc_kyc : 2\n  -- three_company_partc_kyc : 3\n  -- five_company_partc_kyc : 4\n  -- six_company_partc_kyc : 5\n  -- seven_company_partc_kyc : 6\n  -- eight_company_partc_kyc : 7\n}", "declaration structure Partnership_Partc_Kyc_Struct:\n  data one_partnership_partc_kyc content integer\n  data two_partnership_partc_kyc content integer\n  data four_partnership_partc_kyc content integer\n  data five_partnership_partc_kyc content integer\n  data six_partnership_partc_kyc content integer\ndeclaration partnership_partc_kyc_options content Partnership_Partc_Kyc_Struct equals Partnership_Partc_Kyc_Struct {\n  -- one_partnership_partc_kyc : 1\n  -- two_partnership_partc_kyc : 2\n  -- four_partnership_partc_kyc : 3\n  -- five_partnership_partc_kyc : 4\n  -- six_partnership_partc_kyc : 5\n}", "declaration structure Trust_Partc_Kyc_Struct:\n  data one_trust_partc_kyc content integer\n  data two_trust_partc_kyc content integer\n  data four_trust_partc_kyc content integer\n  data three_trust_partc_kyc content integer\ndeclaration trust_partc_kyc_options content Trust_Partc_Kyc_Struct equals Trust_Partc_Kyc_Struct {\n  -- one_trust_partc_kyc : 1\n  -- two_trust_partc_kyc : 2\n  -- four_trust_partc_kyc : 3\n  -- three_trust_partc_kyc : 4\n}", "declaration structure Llc_Partc_Kyc_Struct:\n  data one_llc_partc_kyc content integer\n  data two_llc_partc_kyc content integer\n  data three_llc_partc_kyc content integer\n  data four_llc_partc_kyc content integer\n  data five_llc_partc_kyc content integer\ndeclaration llc_partc_kyc_options content Llc_Partc_Kyc_Struct equals Llc_Partc_Kyc_Struct {\n  -- one_llc_partc_kyc : 1\n  -- two_llc_partc_kyc : 2\n  -- three_llc_partc_kyc : 3\n  -- four_llc_partc_kyc : 4\n  -- five_llc_partc_kyc : 5\n}", "declaration structure Partd_Kyc_Struct:\n  data nobeneficialowner_partd_kyc content integer\n  data yesbeneficialowner_partd_kyc content integer\ndeclaration partd_kyc_options content Partd_Kyc_Struct equals Partd_Kyc_Struct {\n  -- nobeneficialowner_partd_kyc : 1\n  -- yesbeneficialowner_partd_kyc : 2\n}"], "inputDeclarations": ["  input paragraph_4 content list of integer", "  input paragraph_3 content list of integer", "  input paragraph_17 content list of integer", "  input paragraph_12 content list of integer", "  input paragraph_14 content list of integer", "  input paragraph_18 content list of integer", "  input paragraph_13 content list of integer", "  input paragraph_15 content list of integer", "  input paragraph_6 content list of integer", "  input paragraph_2 content list of integer", "  input paragraph content list of integer", "  input paragraph_1 content list of integer", "  input asa_fullname_investorname_generalinfo content integer", "  input date_signaturepage content integer", "  input asa_commitment_amount content integer", "  input fulladdress_formalnotcie content integer", "  input fulladdress_ifdifferent content integer", "  input organization_ifdifferent content integer", "  input organization_formalnotcie content integer", "  input attention_ifdifferent content integer", "  input attention_formalnotcie content integer", "  input phone_formalnotcie content integer", "  input phone_ifdifferent content integer", "  input fax_formalnotcie content integer", "  input fax_ifdifferent content integer", "  input email_formalnotcie content integer", "  input email_ifdifferent content integer", "  input individualname_signaturepage content integer", "  input individualsignature_signaturepage content integer", "  input spouseignature_signaturepage content integer", "  input entityname_signaturepage content integer", "  input entitysignature_signaturepage content integer", "  input entityauthorizedname_signaturepage content integer", "  input entitytitle_signaturepage content integer", "  input asa_lastname_primarycontact_contactinfo content integer", "  input asa_firstname_primarycontact_contactinfo content integer", "  input asa_country_primarycontact_contactinfo content integer", "  input asa_company_primarycontact_contactinfo content integer", "  input asa_phone_primarycontact_contactinfo content integer", "  input asa_email_primarycontact_contactinfo content integer", "  input asa_fax_primarycontact_contactinfo content integer", "  input asa_street_primarycontact_contactinfo content integer", "  input asa_city_primarycontact_contactinfo content integer", "  input asa_state_primarycontact_contactinfo content integer", "  input asa_zip_primarycontact_contactinfo content integer", "  input asa_correspondences_primarycontact_contactinfo content list of integer", "  input asa_lastname_additionalcontact_contactinfo content integer", "  input asa_firstname_additionalcontact_contactinfo content integer", "  input asa_country_additionalcontact_contactinfo content integer", "  input asa_company_additionalcontact_contactinfo content integer", "  input asa_fax_additionalcontact_contactinfo content integer", "  input asa_phone_additionalcontact_contactinfo content integer", "  input asa_email_additionalcontact_contactinfo content integer", "  input asa_street_additionalcontact_contactinfo content integer", "  input asa_city_additionalcontact_contactinfo content integer", "  input asa_state_additionalcontact_contactinfo content integer", "  input asa_zip_additionalcontact_contactinfo content integer", "  input asa_correspondences_additionalcontact_contactinfo content list of integer", "  input asa_lastname_additionalcontact_contactinfo_0 content integer", "  input asa_firstname_additionalcontact_contactinfo_0 content integer", "  input asa_country_additionalcontact_contactinfo_0 content integer", "  input asa_company_additionalcontact_contactinfo_0 content integer", "  input asa_phone_additionalcontact_contactinfo_0 content integer", "  input asa_email_additionalcontact_contactinfo_0 content integer", "  input asa_fax_additionalcontact_contactinfo_0 content integer", "  input asa_street_additionalcontact_contactinfo_0 content integer", "  input asa_city_additionalcontact_contactinfo_0 content integer", "  input asa_state_additionalcontact_contactinfo_0 content integer", "  input asa_zip_additionalcontact_contactinfo_0 content integer", "  input asa_lastname_additionalcontact_contactinfo_1 content integer", "  input asa_firstname_additionalcontact_contactinfo_1 content integer", "  input asa_country_additionalcontact_contactinfo_1 content integer", "  input asa_company_additionalcontact_contactinfo_1 content integer", "  input asa_phone_additionalcontact_contactinfo_1 content integer", "  input asa_email_additionalcontact_contactinfo_1 content integer", "  input asa_fax_additionalcontact_contactinfo_1 content integer", "  input asa_street_additionalcontact_contactinfo_1 content integer", "  input asa_city_additionalcontact_contactinfo_1 content integer", "  input asa_state_additionalcontact_contactinfo_1 content integer", "  input asa_zip_additionalcontact_contactinfo_1 content integer", "  input asa_lastname_additionalcontact_contactinfo_2 content integer", "  input asa_firstname_additionalcontact_contactinfo_2 content integer", "  input asa_country_additionalcontact_contactinfo_2 content integer", "  input asa_company_additionalcontact_contactinfo_2 content integer", "  input asa_phone_additionalcontact_contactinfo_2 content integer", "  input asa_email_additionalcontact_contactinfo_2 content integer", "  input asa_fax_additionalcontact_contactinfo_2 content integer", "  input asa_street_additionalcontact_contactinfo_2 content integer", "  input asa_city_additionalcontact_contactinfo_2 content integer", "  input asa_state_additionalcontact_contactinfo_2 content integer", "  input asa_zip_additionalcontact_contactinfo_2 content integer", "  input asa_lastname_additionalcontact_contactinfo_3 content integer", "  input asa_firstname_additionalcontact_contactinfo_3 content integer", "  input asa_country_additionalcontact_contactinfo_3 content integer", "  input asa_company_additionalcontact_contactinfo_3 content integer", "  input asa_phone_additionalcontact_contactinfo_3 content integer", "  input asa_email_additionalcontact_contactinfo_3 content integer", "  input asa_fax_additionalcontact_contactinfo_3 content integer", "  input asa_street_additionalcontact_contactinfo_3 content integer", "  input asa_city_additionalcontact_contactinfo_3 content integer", "  input asa_state_additionalcontact_contactinfo_3 content integer", "  input asa_zip_additionalcontact_contactinfo_3 content integer", "  input asa_lastname_additionalcontact_contactinfo_4 content integer", "  input asa_firstname_additionalcontact_contactinfo_4 content integer", "  input asa_country_additionalcontact_contactinfo_4 content integer", "  input asa_company_additionalcontact_contactinfo_4 content integer", "  input asa_phone_additionalcontact_contactinfo_4 content integer", "  input asa_email_additionalcontact_contactinfo_4 content integer", "  input asa_fax_additionalcontact_contactinfo_4 content integer", "  input asa_street_additionalcontact_contactinfo_4 content integer", "  input asa_city_additionalcontact_contactinfo_4 content integer", "  input asa_state_additionalcontact_contactinfo_4 content integer", "  input asa_zip_additionalcontact_contactinfo_4 content integer", "  input asa_lastname_additionalcontact_contactinfo_5 content integer", "  input asa_firstname_additionalcontact_contactinfo_5 content integer", "  input asa_country_additionalcontact_contactinfo_5 content integer", "  input asa_company_additionalcontact_contactinfo_5 content integer", "  input asa_phone_additionalcontact_contactinfo_5 content integer", "  input asa_email_additionalcontact_contactinfo_5 content integer", "  input asa_fax_additionalcontact_contactinfo_5 content integer", "  input asa_street_additionalcontact_contactinfo_5 content integer", "  input asa_city_additionalcontact_contactinfo_5 content integer", "  input asa_state_additionalcontact_contactinfo_5 content integer", "  input asa_zip_additionalcontact_contactinfo_5 content integer", "  input asa_lastname_additionalcontact_contactinfo_6 content integer", "  input asa_firstname_additionalcontact_contactinfo_6 content integer", "  input asa_country_additionalcontact_contactinfo_6 content integer", "  input asa_company_additionalcontact_contactinfo_6 content integer", "  input asa_phone_additionalcontact_contactinfo_6 content integer", "  input asa_email_additionalcontact_contactinfo_6 content integer", "  input asa_fax_additionalcontact_contactinfo_6 content integer", "  input asa_street_additionalcontact_contactinfo_6 content integer", "  input asa_city_additionalcontact_contactinfo_6 content integer", "  input asa_state_additionalcontact_contactinfo_6 content integer", "  input asa_zip_additionalcontact_contactinfo_6 content integer", "  input asa_lastname_additionalcontact_contactinfo_7 content integer", "  input asa_firstname_additionalcontact_contactinfo_7 content integer", "  input asa_country_additionalcontact_contactinfo_7 content integer", "  input asa_company_additionalcontact_contactinfo_7 content integer", "  input asa_phone_additionalcontact_contactinfo_7 content integer", "  input asa_email_additionalcontact_contactinfo_7 content integer", "  input asa_fax_additionalcontact_contactinfo_7 content integer", "  input asa_street_additionalcontact_contactinfo_7 content integer", "  input asa_city_additionalcontact_contactinfo_7 content integer", "  input asa_state_additionalcontact_contactinfo_7 content integer", "  input asa_zip_additionalcontact_contactinfo_7 content integer", "  input asa_lastname_additionalcontact_contactinfo_8 content integer", "  input asa_firstname_additionalcontact_contactinfo_8 content integer", "  input asa_country_additionalcontact_contactinfo_8 content integer", "  input asa_company_additionalcontact_contactinfo_8 content integer", "  input asa_phone_additionalcontact_contactinfo_8 content integer", "  input asa_email_additionalcontact_contactinfo_8 content integer", "  input asa_fax_additionalcontact_contactinfo_8 content integer", "  input asa_street_additionalcontact_contactinfo_8 content integer", "  input asa_city_additionalcontact_contactinfo_8 content integer", "  input asa_state_additionalcontact_contactinfo_8 content integer", "  input asa_zip_additionalcontact_contactinfo_8 content integer", "  input asa_lastname_additionalcontact_contactinfo_9 content integer", "  input asa_firstname_additionalcontact_contactinfo_9 content integer", "  input asa_country_additionalcontact_contactinfo_9 content integer", "  input asa_company_additionalcontact_contactinfo_9 content integer", "  input asa_phone_additionalcontact_contactinfo_9 content integer", "  input asa_email_additionalcontact_contactinfo_9 content integer", "  input asa_fax_additionalcontact_contactinfo_9 content integer", "  input asa_street_additionalcontact_contactinfo_9 content integer", "  input asa_city_additionalcontact_contactinfo_9 content integer", "  input asa_state_additionalcontact_contactinfo_9 content integer", "  input asa_zip_additionalcontact_contactinfo_9 content integer", "  input asa_lastname_additionalcontact_contactinfo_10 content integer", "  input asa_firstname_additionalcontact_contactinfo_10 content integer", "  input asa_country_additionalcontact_contactinfo_10 content integer", "  input asa_company_additionalcontact_contactinfo_10 content integer", "  input asa_phone_additionalcontact_contactinfo_10 content integer", "  input asa_email_additionalcontact_contactinfo_10 content integer", "  input asa_fax_additionalcontact_contactinfo_10 content integer", "  input asa_street_additionalcontact_contactinfo_10 content integer", "  input asa_city_additionalcontact_contactinfo_10 content integer", "  input asa_state_additionalcontact_contactinfo_10 content integer", "  input asa_zip_additionalcontact_contactinfo_10 content integer", "  input asa_lastname_additionalcontact_contactinfo_11 content integer", "  input asa_firstname_additionalcontact_contactinfo_11 content integer", "  input asa_country_additionalcontact_contactinfo_11 content integer", "  input asa_company_additionalcontact_contactinfo_11 content integer", "  input asa_phone_additionalcontact_contactinfo_11 content integer", "  input asa_email_additionalcontact_contactinfo_11 content integer", "  input asa_fax_additionalcontact_contactinfo_11 content integer", "  input asa_street_additionalcontact_contactinfo_11 content integer", "  input asa_city_additionalcontact_contactinfo_11 content integer", "  input asa_state_additionalcontact_contactinfo_11 content integer", "  input asa_zip_additionalcontact_contactinfo_11 content integer", "  input asa_lastname_additionalcontact_contactinfo_12 content integer", "  input asa_firstname_additionalcontact_contactinfo_12 content integer", "  input asa_company_additionalcontact_contactinfo_12 content integer", "  input asa_country_additionalcontact_contactinfo_12 content integer", "  input asa_phone_additionalcontact_contactinfo_12 content integer", "  input asa_email_additionalcontact_contactinfo_12 content integer", "  input asa_fax_additionalcontact_contactinfo_12 content integer", "  input asa_street_additionalcontact_contactinfo_12 content integer", "  input asa_city_additionalcontact_contactinfo_12 content integer", "  input asa_state_additionalcontact_contactinfo_12 content integer", "  input asa_zip_additionalcontact_contactinfo_12 content integer", "  input asa_lastname_additionalcontact_contactinfo_13 content integer", "  input asa_firstname_additionalcontact_contactinfo_13 content integer", "  input asa_company_additionalcontact_contactinfo_13 content integer", "  input asa_country_additionalcontact_contactinfo_13 content integer", "  input asa_phone_additionalcontact_contactinfo_13 content integer", "  input asa_email_additionalcontact_contactinfo_13 content integer", "  input asa_fax_additionalcontact_contactinfo_13 content integer", "  input asa_street_additionalcontact_contactinfo_13 content integer", "  input asa_city_additionalcontact_contactinfo_13 content integer", "  input asa_state_additionalcontact_contactinfo_13 content integer", "  input asa_zip_additionalcontact_contactinfo_13 content integer", "  input asa_lastname_additionalcontact_contactinfo_14 content integer", "  input asa_firstname_additionalcontact_contactinfo_14 content integer", "  input asa_company_additionalcontact_contactinfo_14 content integer", "  input asa_country_additionalcontact_contactinfo_14 content integer", "  input asa_phone_additionalcontact_contactinfo_14 content integer", "  input asa_email_additionalcontact_contactinfo_14 content integer", "  input asa_fax_additionalcontact_contactinfo_14 content integer", "  input asa_street_additionalcontact_contactinfo_14 content integer", "  input asa_city_additionalcontact_contactinfo_14 content integer", "  input asa_state_additionalcontact_contactinfo_14 content integer", "  input asa_zip_additionalcontact_contactinfo_14 content integer", "  input asa_lastname_additionalcontact_contactinfo_15 content integer", "  input asa_firstname_additionalcontact_contactinfo_15 content integer", "  input asa_company_additionalcontact_contactinfo_15 content integer", "  input asa_country_additionalcontact_contactinfo_15 content integer", "  input asa_phone_additionalcontact_contactinfo_15 content integer", "  input asa_email_additionalcontact_contactinfo_15 content integer", "  input asa_fax_additionalcontact_contactinfo_15 content integer", "  input asa_street_additionalcontact_contactinfo_15 content integer", "  input asa_city_additionalcontact_contactinfo_15 content integer", "  input asa_state_additionalcontact_contactinfo_15 content integer", "  input asa_zip_additionalcontact_contactinfo_15 content integer", "  input asa_lastname_additionalcontact_contactinfo_16 content integer", "  input asa_firstname_additionalcontact_contactinfo_16 content integer", "  input asa_company_additionalcontact_contactinfo_16 content integer", "  input asa_country_additionalcontact_contactinfo_16 content integer", "  input asa_phone_additionalcontact_contactinfo_16 content integer", "  input asa_email_additionalcontact_contactinfo_16 content integer", "  input asa_fax_additionalcontact_contactinfo_16 content integer", "  input asa_street_additionalcontact_contactinfo_16 content integer", "  input asa_city_additionalcontact_contactinfo_16 content integer", "  input asa_state_additionalcontact_contactinfo_16 content integer", "  input asa_zip_additionalcontact_contactinfo_16 content integer", "  input asa_lastname_additionalcontact_contactinfo_17 content integer", "  input asa_firstname_additionalcontact_contactinfo_17 content integer", "  input asa_company_additionalcontact_contactinfo_17 content integer", "  input asa_country_additionalcontact_contactinfo_17 content integer", "  input asa_phone_additionalcontact_contactinfo_17 content integer", "  input asa_email_additionalcontact_contactinfo_17 content integer", "  input asa_fax_additionalcontact_contactinfo_17 content integer", "  input asa_street_additionalcontact_contactinfo_17 content integer", "  input asa_city_additionalcontact_contactinfo_17 content integer", "  input asa_state_additionalcontact_contactinfo_17 content integer", "  input asa_zip_additionalcontact_contactinfo_17 content integer", "  input asa_fullname_investorname_generalinfo_0 content integer", "  input asa_commitment_amount_0 content integer", "  input dateofdelivery_gpsignaturepage content integer", "  input gpsignature_gpsignaturepage content integer", "  input subscribername_individual_qualificationstatement content integer", "  input group_asa_yes_worth1m_naturalperson_accreditedinvestor content list of integer", "  input group_asa_yes_earn200k_naturalperson_accreditedinvestor content list of integer", "  input group_asa_yes_gpdirectorofthefund_naturalperson_accreditedinvestor content list of integer", "  input group_asa_yes_procertification_naturalperson_accreditedinvestor content list of integer", "  input group_asa_yes_familyclient_accreditedinvestor content list of integer", "  input b_part1_individual_qualificationstatement content list of integer", "  input a_part2_individual_qualificationstatement content list of integer", "  input b_part2_individual_qualificationstatement content list of integer", "  input c_part2_individual_qualificationstatement content list of integer", "  input part3_individual_qualificationstatement content list of integer", "  input asa_plansubjectto4975_erisa content list of integer", "  input suba_part4_individual_qualificationstatement content list of integer", "  input b_part4_individual_qualificationstatement content list of integer", "  input c_part4_individual_qualificationstatement content list of integer", "  input e_part4_individual_qualificationstatement content list of integer", "  input jointtenancy_e_part4_individual_qualificationstatement content integer", "  input individualname_trust_e_part4_individual_qualificationstatement content integer", "  input individualname_ira_e_part4_individual_qualificationstatement content integer", "  input individualname_selfretirementplan_e_part4_individual_qualificationstatement content integer", "  input explain_noneoftheabove_e_part4_individual_qualificationstatement content integer", "  input naturalperson_citizen_country content integer", "  input country_f_part4_individual_qualificationstatement content integer", "  input state_f_part4_individual_qualificationstatement content integer", "  input city_f_part4_individual_qualificationstatement content integer", "  input jurisdiction_g_part4_individual_qualificationstatement content integer", "  input country_g_part4_individual_qualificationstatement content integer", "  input state_g_part4_individual_qualificationstatement content integer", "  input city_g_part4_individual_qualificationstatement content integer", "  input excessamount_part4 content list of integer", "  input dated_signaturepage_individual_qualificationstatement content integer", "  input subscribername_naturalperson_signaturepage_individual_qualificationstatement content integer", "  input subscribersignature_naturalperson_signaturepage_individual_qualificationstatement content integer", "  input spousesignature_naturalperson_signaturepage_individual_qualificationstatement content integer", "  input subscribername_alteregos_signaturepage_individual_qualificationstatement content integer", "  input signature_alteregos_signaturepage_individual_qualificationstatement content integer", "  input signatoryname_alteregos_signaturepage_individual_qualificationstatement content integer", "  input signatorytitle_alteregos_signaturepage_individual_qualificationstatement content integer", "  input asa_bankname_wireinstructionsfrom_wireinfo content integer", "  input bankstreet_wireinstructionsfrom_wireinfo content integer", "  input bankcity_wireinstructionsfrom_wireinfo content integer", "  input state_name_wireinstructionsfrom_wireinfo content integer", "  input state_number_wireinstructionsfrom_wireinfo content integer", "  input bankcountry_number_wireinstructionsfrom_wireinfo content integer", "  input asa_aba_wireinstructionsfrom_wireinfo content integer", "  input asa_swift_wireinstructionsfrom_wireinfo content integer", "  input softcode_swift_wireinstructionsfrom_wireinfo content integer", "  input asa_accountname_wireinstructionsfrom_wireinfo content integer", "  input accountnumberiban_wireinstructionsfrom_wireinfo content integer", "  input asa_reference_wireinstructionsfrom_wireinfo content integer", "  input fbo_name_wireinstructionsfrom_wireinfo content integer", "  input fbo_number_wireinstructionsfrom_wireinfo content integer", "  input asa_intermediarybankname_wireinstructionsfrom_wireinfo content integer", "  input intermediarybankstreetaddress_wireinstructionsfrom_wireinfo content integer", "  input intermediarybankcity_brokerageaccount_wireinfo content integer", "  input bankstate_intermediarybank_wireinstructionsfrom_wireinfo content integer", "  input bankzip_name_intermediarybank_wireinstructionsfrom_wireinfo content integer", "  input bankcountry_number_intermediarybank_wireinstructionsfrom_wireinfo content integer", "  input asa_intermediaryaba_wireinstructionsfrom_wireinfo content integer", "  input asa_intermediaryswift_wireinstructionsfrom_wireinfo content integer", "  input asa_intermediaryaccnumber_brokerageaccount_wireinfo content integer", "  input subscribername_entity_qualificationstatement content integer", "  input asa_accreditedinvestor_accreditedinvestor content list of integer", "  input group_asa_yes_erisaplan_accreditedinvestor content list of integer", "  input group_asa_yes_erisaplan_accreditedinvestor_1 content list of integer", "  input group_asa_yes_erisaplan_accreditedinvestor_2 content list of integer", "  input twelve_a_part1_entitiesusiqs content list of integer", "  input b_part1_entitiesusiqs content list of integer", "  input a2_part2_entity_qualificationstatement content list of integer", "  input b_part2_entity_qualificationstatement content list of integer", "  input c_part2_entity_qualificationstatement content list of integer", "  input beneficialowner_d_part2_entity_qualificationstatement content integer", "  input e_part2_entity_qualificationstatement content list of integer", "  input f_part2_entity_qualificationstatement content list of integer", "  input g_part2_entity_qualificationstatement content list of integer", "  input h_part2_entity_qualificationstatement content list of integer", "  input i_part2_entity_qualificationstatement content list of integer", "  input beneficialowner_j_part2_entity_qualificationstatement content integer", "  input a1_part3_entity_qualificationstatement content list of integer", "  input a2_part3_entity_qualificationstatement content list of integer", "  input b_part3_entity_qualificationstatement content list of integer", "  input c_part3_entity_qualificationstatement content list of integer", "  input d_part3_entity_qualificationstatement content list of integer", "  input group_asa_inv25m_entity_qualifiedpurchaser content list of integer", "  input group_asa_trust_qualifiedpurchaser content list of integer", "  input group_asa_own5m_entity_qualifiedpurchaser content list of integer", "  input group_asa_allowners_otherentity_qualifiedpurchaser content list of integer", "  input group_asa_rule2a51_institutionalbuyer_qualifiedpurchaser content list of integer", "  input group_asa_isnot_qualifiedpurchaser content list of integer", "  input a1_part5_entity_qualificationstatement content list of integer", "  input a2_part5_entity_qualificationstatement content list of integer", "  input a3_part5_entity_qualificationstatement content list of integer", "  input a4_part5_entity_qualificationstatement content list of integer", "  input suba4_part5_entity_qualificationstatement content list of integer", "  input a5_part5_entity_qualificationstatement content list of integer", "  input suba5_part5_entity_qualificationstatement content list of integer", "  input maxpercentage_suba5_part5_entity_qualificationstatement content integer", "  input a6_part5_entity_qualificationstatement content list of integer", "  input suba6_part5_entity_qualificationstatement content list of integer", "  input a7_part5_entity_qualificationstatement content list of integer", "  input maxpercentage_a7_part5_entity_qualificationstatement content integer", "  input a8_part5_entity_qualificationstatement content list of integer", "  input a9_part5_entity_qualificationstatement content list of integer", "  input suba9_part5_entity_qualificationstatement content list of integer", "  input a10_part5_entity_qualificationstatement content list of integer", "  input group_b1_part5_entity_qualificationstatement content list of integer", "  input group_b2_part5_entity_qualificationstatement content list of integer", "  input group_b3_part5_entity_qualificationstatement content list of integer", "  input group_b4_part5_entity_qualificationstatement content list of integer", "  input group_c1_part5_entity_qualificationstatement content list of integer", "  input group_c2_part5_entity_qualificationstatement content list of integer", "  input group_c3_part5_entity_qualificationstatement content list of integer", "  input group_c4_part5_entity_qualificationstatement content list of integer", "  input group_c5_part5_entity_qualificationstatement content list of integer", "  input agency_c5_part5_entity_qualificationstatement content integer", "  input group_c6_part5_entity_qualificationstatement content list of integer", "  input trusttype_c6_part5_entity_qualificationstatement content integer", "  input group_c7_part5_entity_qualificationstatement content list of integer", "  input entityform_c7_part5_entity_qualificationstatement content integer", "  input jurisdiction_d_part5_entity_qualificationstatement content integer", "  input country_e_part5_individual_qualificationstatement content integer", "  input state_e_part5_individual_qualificationstatement content integer", "  input city_e_part5_individual_qualificationstatement content integer", "  input fundoffunds_part5_entity_qualificationstatement content list of integer", "  input organizationtype_part5_entity_qualificationstatement content list of integer", "  input specify_other_organizationtype_part5_entity_qualificationstatement content integer", "  input governmentplans_part5_entity_qualificationstatement content list of integer", "  input foia_part5_entity_qualificationstatement content list of integer", "  input election_qualificationstatement content list of integer", "  input dated_signaturepage_part5_entity_qualificationstatement content integer", "  input subscribername_signaturepage_part5_entity_qualificationstatement content integer", "  input signature_signaturepage_part5_entity_qualificationstatement content integer", "  input signatoryname_signaturepage_part5_entity_qualificationstatement content integer", "  input signatorytitle_signaturepage_part5_entity_qualificationstatement content integer", "  input asa_bankname_wireinstructionsfrom_wireinfo_0 content integer", "  input bankstreet_wireinstructionsfrom_wireinfo_0 content integer", "  input bankcity_wireinstructionsfrom_wireinfo_0 content integer", "  input bankstate_wireinstructionsfrom_wireinfo_0 content integer", "  input bankzip_wireinstructionsfrom_wireinfo_0 content integer", "  input bankcountry_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_aba_wireinstructionsfrom_wireinfo_0 content integer", "  input swiftchips_wireinstructionsfrom_wireinfo_0 content integer", "  input sortcode_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_accountname_wireinstructionsfrom_wireinfo_0 content integer", "  input accountnumberiban_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_reference_wireinstructionsfrom_wireinfo_0 content integer", "  input fbo_name_wireinstructionsfrom_wireinfo_0 content integer", "  input fbo_number_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_intermediarybankname_wireinstructionsfrom_wireinfo_0 content integer", "  input intermediarybankstreet_wireinstructionsfrom_wireinfo_0 content integer", "  input intermediarycity_wireinstructionsfrom_wireinfo_0 content integer", "  input intermediarystate_brokerageaccount_wireinfo_0 content integer", "  input zip_intermediarybank_wireinstructionsfrom_wireinfo_0 content integer", "  input bankcountry_intermediarybank_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_intermediaryaba_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_intermediaryswift_wireinstructionsfrom_wireinfo_0 content integer", "  input asa_intermediaryaccnumber_brokerageaccount_wireinfo_0 content integer", "  input asa_fullname_investorname_generalinfo2 content integer", "  input city_kyc content integer", "  input state_kyc content integer", "  input zip_kyc content integer", "  input country_kyc content integer", "  input numberandstreet_kyc content integer", "  input placeofbirth_kyc content integer", "  input dob_kyc content integer", "  input asa_nationality_generalinfo content integer", "  input occupation_kyc content integer", "  input sourceoffunds_kyc content integer", "  input parta_kyc content list of integer", "  input exemptedclient_partb_kyc content list of integer", "  input jurisdiction_one_exemptedclient_partb_kyc content integer", "  input jurisdiction_three_exemptedclient_partb_kyc content integer", "  input jurisdiction_four_exemptedclient_partb_kyc content integer", "  input nominee_partb_kyc content list of integer", "  input jurisdiction_nominee_partb_kyc content integer", "  input group_eligibleintro_partb_kyc content list of integer", "  input individual_partc_kyc content list of integer", "  input company_partc_kyc content list of integer", "  input partnership_partc_kyc content list of integer", "  input trust_partc_kyc content list of integer", "  input llc_partc_kyc content list of integer", "  input partd_kyc content list of integer", "  input name_partd_kyc content integer", "  input address_partd_kyc content integer", "  input dob_partd_kyc content integer", "  input placeofbirth_partd_kyc content integer", "  input nationality_partd_kyc content integer", "  input name_partd_kyc_0 content integer", "  input address_partd_kyc_0 content integer", "  input dob_partd_kyc_0 content integer", "  input placeofbirth_partd_kyc_0 content integer", "  input nationality_partd_kyc_0 content integer", "  input name_partd_kyc_1 content integer", "  input address_partd_kyc_1 content integer", "  input dob_partd_kyc_1 content integer", "  input placeofbirth_partd_kyc_1 content integer", "  input nationality_partd_kyc_1 content integer", "  input name_partd_kyc_2 content integer", "  input address_partd_kyc_2 content integer", "  input dob_partd_kyc_2 content integer", "  input placeofbirth_partd_kyc_2 content integer", "  input nationality_partd_kyc_2 content integer", "  input name_partd_kyc_3 content integer", "  input address_partd_kyc_3 content integer", "  input dob_partd_kyc_3 content integer", "  input placeofbirth_partd_kyc_3 content integer", "  input nationality_partd_kyc_3 content integer", "  input signature_partg_kyc content integer", "  input asa_fullname_investorname_generalinfo3 content integer", "  input position_partg_kyc content integer", "  input contactemail_partg_kyc content integer", "  input entityname_partg_kyc content integer", "  input date_partg_kyc content integer", "  input date_aml_signaturepage content integer", "  input individualname_aml_signaturepage content integer", "  input individualsignature_aml_signaturepage content integer", "  input spousesignature_aml_signaturepage content integer", "  input entityname_aml_signaturepage content integer", "  input signatureauthorizedrep_aml_signaturepage content integer", "  input nameauthorizedrep_aml_signaturepage content integer", "  input title_aml_signaturepage content integer"], "investorTypes": "declaration structure Blueprint_Investor_Types_Struct:\n  data a_trust content integer\n  data a_revocable_trust_the_sole_settlor_of_which_was content integer\n  data a_self_directed_retirement_plan content integer\n  data a_trust_of_the_following_type content integer\n  data a_limited_partnership content integer\n  data an_individual_retirement_account content integer\n  data a_limited_liability_company content integer\n  data an_individual_human_being content integer\n  data an_unicorporated_agency_or_instrumentality_of_the_government_of content integer\n  data a_general_partnership content integer\n  data a_joint_tenancy content integer\n  data a_corporation content integer\n  data the_following_other_form_of_entity content integer\ndeclaration blueprint_investor_types content Blueprint_Investor_Types_Struct equals Blueprint_Investor_Types_Struct\n{\n  -- a_corporation : 2\n  -- a_joint_tenancy : 1\n  -- a_limited_liability_company : 8\n  -- a_limited_partnership : 12\n  -- an_individual_human_being : 9\n  -- an_unicorporated_agency_or_instrumentality_of_the_government_of : 11\n  -- the_following_other_form_of_entity : 10\n  -- a_revocable_trust_the_sole_settlor_of_which_was : 13\n  -- a_trust : 3\n  -- an_individual_retirement_account : 6\n  -- a_self_directed_retirement_plan : 5\n  -- a_trust_of_the_following_type : 4\n  -- a_general_partnership : 7\n}\n\ndeclaration individual_types content list of integer equals [\n  blueprint_investor_types.a_trust;\n  blueprint_investor_types.a_joint_tenancy;\n  blueprint_investor_types.a_self_directed_retirement_plan;\n  blueprint_investor_types.an_individual_human_being;\n  blueprint_investor_types.an_individual_retirement_account;\n  blueprint_investor_types.a_revocable_trust_the_sole_settlor_of_which_was\n]\n\ndeclaration entity_types content list of integer equals [\n  blueprint_investor_types.the_following_other_form_of_entity;\n  blueprint_investor_types.a_trust_of_the_following_type;\n  blueprint_investor_types.a_general_partnership;\n  blueprint_investor_types.a_corporation;\n  blueprint_investor_types.an_unicorporated_agency_or_instrumentality_of_the_government_of;\n  blueprint_investor_types.a_limited_liability_company;\n  blueprint_investor_types.a_trust;\n  blueprint_investor_types.a_limited_partnership\n]\n", "stringConstantStruct": "declaration structure Blueprint_String_Constants_Struct:\n  data commitment_higher_than_100k content integer\n  data sweden content integer\ndeclaration blueprint_string_constants content Blueprint_String_Constants_Struct equals Blueprint_String_Constants_Struct\n{\n  -- commitment_higher_than_100k : 1\n  -- sweden : 2\n}\n", "gatingQuestionStruct": ["declaration structure Investing_Jointly_With_Your_Spouse_Struct:\n  data opt_yes content integer\n  data opt_no content integer\ndeclaration investing_jointly_with_your_spouse_options content Investing_Jointly_With_Your_Spouse_Struct equals Investing_Jointly_With_Your_Spouse_Struct {\n  -- opt_yes : 1\n  -- opt_no : 2\n}", "declaration structure Gating_If_Unincorporated_Agency_Struct:\n  data check_if_yes content integer\ndeclaration gating_if_unincorporated_agency_options content Gating_If_Unincorporated_Agency_Struct equals Gating_If_Unincorporated_Agency_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Gating_For_Another_Contact_Struct:\n  data check_if_yes content integer\ndeclaration gating_for_another_contact_options content Gating_For_Another_Contact_Struct equals Gating_For_Another_Contact_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Gating_For_Additional_Contact_Different_Struct:\n  data check_if_yes content integer\ndeclaration gating_for_additional_contact_different_options content Gating_For_Additional_Contact_Different_Struct equals Gating_For_Additional_Contact_Different_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Indicate_If_Subscriber_Is_Eea_Or_Uk_Struct:\n  data opt_yes content integer\n  data opt_no content integer\ndeclaration indicate_if_subscriber_is_eea_or_uk_options content Indicate_If_Subscriber_Is_Eea_Or_Uk_Struct equals Indicate_If_Subscriber_Is_Eea_Or_Uk_Struct {\n  -- opt_yes : 1\n  -- opt_no : 2\n}", "declaration structure Gating_For_Representation_Struct:\n  data check_if_yes content integer\ndeclaration gating_for_representation_options content Gating_For_Representation_Struct equals Gating_For_Representation_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Checkbox_For_Canadian_Representation_Struct:\n  data yes content integer\ndeclaration checkbox_for_canadian_representation_options content Checkbox_For_Canadian_Representation_Struct equals Checkbox_For_Canadian_Representation_Struct {\n  -- yes : 1\n}", "declaration structure Represent_Checkbox_For_Part_Ivb_Struct:\n  data check_if_yes content integer\ndeclaration represent_checkbox_for_part_ivb_options content Represent_Checkbox_For_Part_Ivb_Struct equals Represent_Checkbox_For_Part_Ivb_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Subscriber_Is_A_Company_Formed_April30_Struct:\n  data opt_true content integer\n  data opt_false content integer\ndeclaration subscriber_is_a_company_formed_april30_options content Subscriber_Is_A_Company_Formed_April30_Struct equals Subscriber_Is_A_Company_Formed_April30_Struct {\n  -- opt_true : 1\n  -- opt_false : 2\n}", "declaration structure Type_Of_Subscriber_For_The_Purpose_Of_De_Struct:\n  data individual_natural_person content integer\n  data company content integer\n  data partnership content integer\n  data trust content integer\n  data limited_liability_company content integer\n  data none_of_the_above content integer\ndeclaration type_of_subscriber_for_the_purpose_of_de_options content Type_Of_Subscriber_For_The_Purpose_Of_De_Struct equals Type_Of_Subscriber_For_The_Purpose_Of_De_Struct {\n  -- individual_natural_person : 1\n  -- company : 2\n  -- partnership : 3\n  -- trust : 4\n  -- limited_liability_company : 5\n  -- none_of_the_above : 6\n}", "declaration structure Gating_For_Nationality_Struct:\n  data check_if_yes content integer\ndeclaration gating_for_nationality_options content Gating_For_Nationality_Struct equals Gating_For_Nationality_Struct {\n  -- check_if_yes : 1\n}", "declaration structure Gating_For_Part_D_Struct:\n  data check_if_yes content integer\ndeclaration gating_for_part_d_options content Gating_For_Part_D_Struct equals Gating_For_Part_D_Struct {\n  -- check_if_yes : 1\n}"], "gatingQuestionInputDeclarations": ["  input investing_jointly_with_your_spouse content list of integer", "  input gating_if_unincorporated_agency content list of integer", "  input gating_for_another_contact content list of integer", "  input gating_for_additional_contact_different content list of integer", "  input indicate_if_subscriber_is_eea_or_uk content list of integer", "  input gating_for_representation content list of integer", "  input checkbox_for_canadian_representation content list of integer", "  input represent_checkbox_for_part_ivb content list of integer", "  input subscriber_is_a_company_formed_april30 content list of integer", "  input type_of_subscriber_for_the_purpose_of_de content list of integer", "  input gating_for_nationality content list of integer", "  input gating_for_part_d content list of integer"], "fileGroupStruct": ["declaration structure Filegroup1_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\ndeclaration filegroup1_fileitems content Filegroup1_Struct equals Filegroup1_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n}", "declaration structure Filegroup2_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\n  data file5 content integer\n  data file6 content integer\n  data file7 content integer\n  data file8 content integer\ndeclaration filegroup2_fileitems content Filegroup2_Struct equals Filegroup2_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n  -- file5 : 5\n  -- file6 : 6\n  -- file7 : 7\n  -- file8 : 8\n}", "declaration structure Filegroup3_Struct:\n  data file1 content integer\n  data file2 content integer\ndeclaration filegroup3_fileitems content Filegroup3_Struct equals Filegroup3_Struct {\n  -- file1 : 1\n  -- file2 : 2\n}", "declaration structure Filegroup4_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\n  data file5 content integer\n  data file6 content integer\n  data file7 content integer\ndeclaration filegroup4_fileitems content Filegroup4_Struct equals Filegroup4_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n  -- file5 : 5\n  -- file6 : 6\n  -- file7 : 7\n}", "declaration structure Filegroup5_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\n  data file5 content integer\ndeclaration filegroup5_fileitems content Filegroup5_Struct equals Filegroup5_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n  -- file5 : 5\n}", "declaration structure Filegroup6_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\ndeclaration filegroup6_fileitems content Filegroup6_Struct equals Filegroup6_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n}", "declaration structure Filegroup7_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\n  data file5 content integer\ndeclaration filegroup7_fileitems content Filegroup7_Struct equals Filegroup7_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n  -- file5 : 5\n}", "declaration structure Filegroup8_Struct:\n  data file1 content integer\n  data file2 content integer\n  data file3 content integer\n  data file4 content integer\n  data file5 content integer\ndeclaration filegroup8_fileitems content Filegroup8_Struct equals Filegroup8_Struct {\n  -- file1 : 1\n  -- file2 : 2\n  -- file3 : 3\n  -- file4 : 4\n  -- file5 : 5\n}"]}}}