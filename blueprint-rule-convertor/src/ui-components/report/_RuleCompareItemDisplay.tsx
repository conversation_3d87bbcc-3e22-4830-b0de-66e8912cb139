import React, { useState } from "react";
import { RuleCompareItem } from "@/models/formMapping";
import { FormRule } from "@/models/stargazer/";
import { RuleId } from "@/models/matchingId/ruleMappingId";
import { toComparedRule } from "@/generator/formApplication/mappingAnalysis";
import { DiffEditor } from "@monaco-editor/react";

// Design tokens
const colors = {
    neutral: {
        bg: "#ffffff",
        border: "#e9ecef",
        text: "#495057",
        icon: "#6c757d",
        muted: "#6c757d",
    },
    diff: {
        header: "#f1f3f4",
        border: "#dee2e6",
        formLabel: "#dc3545",
        blueprintLabel: "#28a745",
    },
};

const spacing = {
    xs: "4px",
    sm: "8px",
    md: "12px",
    lg: "16px",
    xl: "20px",
    xxl: "24px",
};

const typography = {
    xs: "10px",
    sm: "11px",
    md: "12px",
    base: "14px",
    lg: "16px",
    xl: "18px",
};

const borderRadius = {
    sm: "4px",
    md: "6px",
    lg: "8px",
};

const shadows = {
    sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
    md: "0 2px 8px rgba(0, 0, 0, 0.1)",
    lg: "0 4px 16px rgba(0, 0, 0, 0.1)",
};

const ItemContainer = ({
    item,
    children,
}: {
    item: RuleCompareItem;
    children: React.ReactNode;
}) => {
    return (
        <li
            style={{
                border: "1px solid #e9ecef",
                padding: "20px",
                backgroundColor: "white",
                marginBottom: "12px",
                fontSize: typography.base,
                transition: "all 0.2s ease",
                cursor: "default",
                boxShadow: shadows.sm,
                margin: 0,
            }}
        >
            {children}
        </li>
    );
};

const DiffContainer = ({ children }: { children: React.ReactNode }) => (
    <div
        style={{
            marginTop: spacing.md,
            border: `1px solid ${colors.diff.border}`,
            borderRadius: borderRadius.sm,
            backgroundColor: colors.neutral.bg,
            boxShadow: shadows.md,
        }}
    >
        {children}
    </div>
);

const getCopyContent = (formRule: FormRule) => {
    return `**Rule Name**
    ${formRule.name}
    
**Rule Description**
    ${formRule.description}
    
**Rule Content**
\`\`\`${formRule.value}\`\`\`
`;
};

const CopyButton = ({
    onClick,
    title,
}: {
    onClick: () => void;
    title: string;
}) => (
    <button
        onClick={onClick}
        title={title}
        style={{
            padding: `${spacing.xs} ${spacing.sm}`,
            fontSize: typography.xs,
            backgroundColor: colors.neutral.bg,
            border: `1px solid ${colors.neutral.border}`,
            borderRadius: borderRadius.sm,
            cursor: "pointer",
            color: colors.neutral.muted,
            transition: "all 0.2s ease",
            fontWeight: "500",
            boxShadow: shadows.sm,
            marginLeft: spacing.xs,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            minWidth: "24px",
            height: "24px",
        }}
        onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colors.neutral.border;
            e.currentTarget.style.transform = "translateY(-1px)";
            e.currentTarget.style.boxShadow = shadows.md;
        }}
        onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = colors.neutral.bg;
            e.currentTarget.style.transform = "translateY(0)";
            e.currentTarget.style.boxShadow = shadows.sm;
        }}
    >
        Copy
    </button>
);

const DiffHeader = ({
    formRule,
    blueprintRule,
    onCopyForm,
    onCopyBlueprint,
}: {
    formRule?: FormRule;
    blueprintRule?: FormRule;
    onCopyForm: () => void;
    onCopyBlueprint: () => void;
}) => (
    <div
        style={{
            display: "flex",
            backgroundColor: colors.diff.header,
            borderBottom: `1px solid ${colors.diff.border}`,
            fontSize: typography.md,
            fontWeight: "600",
            padding: spacing.sm + " " + spacing.xl,
        }}
    >
        <div
            style={{
                width: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                borderRight: `1px solid ${colors.diff.border}`,
                paddingRight: spacing.sm,
            }}
        >
            {formRule && <span>{formRule?.name || "N/A"}</span>}
        </div>
        <div
            style={{
                width: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                paddingLeft: spacing.sm,
            }}
        >
            {blueprintRule && <span>{blueprintRule?.name || "N/A"}</span>}
            {blueprintRule && (
                <CopyButton
                    onClick={onCopyBlueprint}
                    title="Copy blueprint rule content"
                />
            )}
        </div>
    </div>
);

const CopyFeedback = ({ message }: { message: string }) => (
    <div
        style={{
            position: "fixed",
            top: spacing.xl,
            right: spacing.xl,
            backgroundColor: "#d1f2eb",
            color: "#0e5132",
            padding: `${spacing.sm} ${spacing.md}`,
            borderRadius: borderRadius.md,
            boxShadow: shadows.lg,
            fontSize: typography.sm,
            fontWeight: "500",
            zIndex: 2000,
            border: `1px solid "#27ae60"`,
            animation: "slideIn 0.3s ease-out",
        }}
    >
        ✅ {message}
        <style>
            {`
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `}
        </style>
    </div>
);

interface RuleCompareItemDisplayProps {
    item: RuleCompareItem;
    formRuleMap: Map<string, FormRule>;
    blueprintRuleMap: Map<string, FormRule>;
    itemIndex: number;
}

export const RuleCompareItemDisplay: React.FC<RuleCompareItemDisplayProps> = ({
    item,
    formRuleMap,
    blueprintRuleMap,
    itemIndex,
}) => {
    const [copyFeedback, setCopyFeedback] = useState<string | null>(null);
    const getFormRule = (ruleId: RuleId): FormRule | undefined => {
        return formRuleMap.get(ruleId.id);
    };

    const getBlueprintRule = (ruleId: RuleId): FormRule | undefined => {
        return blueprintRuleMap.get(ruleId.id);
    };

    const formRule = getFormRule(item.ruleId);
    const blueprintRule = getBlueprintRule(item.ruleId);

    // Copy functions
    const copyToClipboard = async (text: string, label: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopyFeedback(`${label} copied to clipboard!`);
            setTimeout(() => setCopyFeedback(null), 2000);
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);
            setCopyFeedback(`${label} copied to clipboard!`);
            setTimeout(() => setCopyFeedback(null), 2000);
        }
    };

    const handleCopyFormRule = () => {
        if (formRule) {
            const content = getCopyContent(formRule);
            copyToClipboard(content, "Form rule");
        }
    };

    const handleCopyBlueprintRule = () => {
        if (blueprintRule) {
            const content = getCopyContent(blueprintRule);
            copyToClipboard(content, "Blueprint rule");
        }
    };

    const renderDiffView = (item: RuleCompareItem) => {
        const normalizedFormRule = formRule
            ? item.type === "ExistingFormRule"
                ? toComparedRule(formRule.value)
                : formRule.value
            : "";
        const normalizedBlueprintRule = blueprintRule
            ? item.type === "ExistingFormRule"
                ? toComparedRule(blueprintRule.value)
                : blueprintRule.value
            : "";

        return (
            <DiffContainer>
                <DiffHeader
                    formRule={formRule}
                    blueprintRule={blueprintRule}
                    onCopyForm={handleCopyFormRule}
                    onCopyBlueprint={handleCopyBlueprintRule}
                />
                <div
                    style={{
                        height: "400px",
                        borderRadius: `0 0 ${borderRadius.sm} ${borderRadius.sm}`,
                        overflow: "hidden",
                    }}
                >
                    <DiffEditor
                        height="400px"
                        language="lua"
                        original={normalizedFormRule}
                        modified={normalizedBlueprintRule}
                        options={{
                            readOnly: true,
                            renderSideBySide: true,
                            enableSplitViewResizing: true,
                            renderOverviewRuler: true,
                            scrollBeyondLastLine: false,
                            minimap: { enabled: false },
                            wordWrap: "off",
                            fontSize: 12,
                            lineNumbers: "on",
                            folding: true,
                            selectOnLineNumbers: true,
                            automaticLayout: true,
                            scrollbar: {
                                vertical: "auto",
                                horizontal: "auto",
                                verticalScrollbarSize: 10,
                                horizontalScrollbarSize: 10,
                            },
                            diffWordWrap: "off",
                            ignoreTrimWhitespace: false,
                            renderIndicators: true,
                            originalEditable: false,
                        }}
                        theme="vs-light"
                    />
                </div>
            </DiffContainer>
        );
    };
    const ruleId = RuleId.fromTagString(
        blueprintRule?.description || formRule?.description || "",
    );

    return (
        <>
            {copyFeedback && <CopyFeedback message={copyFeedback} />}
            <ItemContainer item={item}>
                <div
                    style={{
                        display: "flex",
                        gap: "8px",
                    }}
                >
                    <h4
                        style={{
                            fontSize: "16px",
                            marginBottom: "10px",
                            fontWeight: "600",
                            minWidth: "fit-content",
                        }}
                    >
                        Rule Signature {itemIndex}:
                    </h4>

                    <p
                        style={{
                            fontSize: "16px",
                            color: "#2c3e50",
                        }}
                    >
                        {ruleId?.buildTag()}
                    </p>
                </div>
                {renderDiffView(item)}
            </ItemContainer>
        </>
    );
};

export default RuleCompareItemDisplay;
