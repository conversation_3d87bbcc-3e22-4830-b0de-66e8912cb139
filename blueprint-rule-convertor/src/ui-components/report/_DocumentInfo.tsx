import React from "react";
import { BlueprintApplicationReport } from "@/models/formMapping";

interface DocumentInfoProps {
    blueprintApplicationResult: BlueprintApplicationReport;
}

export const DocumentInfo: React.FC<DocumentInfoProps> = ({
    blueprintApplicationResult,
}) => {
    if (!blueprintApplicationResult) {
        return null;
    }

    return (
        <div style={{ flex: "1", minWidth: "300px" }}>
            <h3
                style={{
                    fontSize: "18px",
                    color: "#34495e",
                    marginBottom: "15px",
                }}
            >
                Document Information
            </h3>
            <p style={{ margin: "8px 0", fontSize: "16px" }}>
                <strong>Blueprint Name:</strong>{" "}
                <a
                    href={blueprintApplicationResult.blueprintLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                        color: "#3498db",
                        textDecoration: "none",
                    }}
                >
                    {blueprintApplicationResult.blueprintName}
                </a>{" "}
                (Version {blueprintApplicationResult.blueprintVersionCount}:{" "}
                {blueprintApplicationResult.blueprintVersionName})
            </p>
            <p style={{ margin: "8px 0", fontSize: "16px" }}>
                <strong>Form Name:</strong>{" "}
                <a
                    href={blueprintApplicationResult.formLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                        color: "#3498db",
                        textDecoration: "none",
                    }}
                >
                    {blueprintApplicationResult.formName}
                </a>{" "}
                (Version {blueprintApplicationResult.formVersionCount}:{" "}
                {blueprintApplicationResult.formVersionName})
            </p>
        </div>
    );
};

export default DocumentInfo;
