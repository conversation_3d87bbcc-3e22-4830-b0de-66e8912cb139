import React from "react";
import { Box, Container, Typography, <PERSON> } from "@mui/material";

const Header = () => (
    <Box
        sx={{
            width: "100%",
            bgcolor: "primary.main",
            color: "white",
            position: "fixed",
            top: 0,
            zIndex: 1100,
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
    >
        <Container maxWidth="lg">
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    height: 72,
                    gap: 4,
                }}
            >
                <Typography
                    variant="h5"
                    component="div"
                    sx={{
                        flexGrow: 0,
                        fontWeight: 600,
                        letterSpacing: "0.5px",
                        "&:hover": {
                            transform: "scale(1.02)",
                            transition: "transform 0.2s ease-in-out",
                        },
                    }}
                >
                    Anduin
                </Typography>
                <Box sx={{ display: "flex", gap: 3 }}>
                    <Link
                        href="/"
                        sx={{
                            color: "white",
                            textDecoration: "none",
                            fontSize: "1.1rem",
                            padding: "6px 12px",
                            borderRadius: "4px",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                                bgcolor: "rgba(255,255,255,0.1)",
                            },
                        }}
                    >
                        Home
                    </Link>
                    <Link
                        href="/reports"
                        sx={{
                            color: "white",
                            textDecoration: "none",
                            fontSize: "1.1rem",
                            padding: "6px 12px",
                            borderRadius: "4px",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                                bgcolor: "rgba(255,255,255,0.1)",
                            },
                        }}
                    >
                        Reports
                    </Link>
                </Box>
            </Box>
        </Container>
    </Box>
);

export default Header;
