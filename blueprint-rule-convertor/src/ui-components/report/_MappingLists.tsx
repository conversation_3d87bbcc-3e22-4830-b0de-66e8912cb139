import React from "react";
import { MappingList } from "./_MappingList";

interface MappingListsProps {
    oneMappings: string[];
    manyMappings: string[];
    missingMappings: string[];
}

export const MappingLists: React.FC<MappingListsProps> = ({
    oneMappings,
    manyMappings,
    missingMappings,
}) => {
    if (!oneMappings || !manyMappings || !missingMappings) {
        return null;
    }
    return (
        <div
            style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "20px",
                marginTop: "10px",
            }}
        >
            <MappingList
                mappings={oneMappings}
                title="Existing Mappings (One-to-One or One-to-Many)"
                backgroundColor="#f1f8e9"
                borderColor="#c5e1a5"
                titleColor="#33691e"
                icon="✓"
                borderBottomColor="#dcedc8"
            />

            <MappingList
                mappings={manyMappings}
                title="Existing Mappings (Many-to-One or Many-to-Many)"
                backgroundColor="#fff8e1"
                borderColor="#ffe082"
                titleColor="#ff8f00"
                icon="⚠️"
                borderBottomColor="#ffecb3"
            />

            <MappingList
                mappings={missingMappings}
                title="Missing Mappings"
                backgroundColor="#ffebee"
                borderColor="#ffcdd2"
                titleColor="#c62828"
                icon="❌"
                borderBottomColor="#ef9a9a"
            />
        </div>
    );
};
export default MappingLists;
