import { BlueprintLine } from "@/models/formMapping";
import React, { useState } from "react";
import Tag from "./common/_Tag";
import Modal from "./common/_Modal";
import { CodeDisplay } from "./common/_CodeDisplay";
import * as designTokens from "@/styles/designTokens";

const { colors, spacing, borderRadius } = designTokens;

interface LineKeyData {
    allKeys: string[];
    missingMappingKeys: string[];
    multipleMappingKeys: string[];
}

interface LineItemPopupProps {
    number: number;
    lineKey: LineKeyData;
    successConvertedBlueprintLines: number[];
    blueprintRuleContents: Map<number, BlueprintLine>;
    targets: string[];
}

// Key list component for displaying arrays of keys
const KeyList: React.FC<{
    keys: string[];
    variant: "neutral" | "error" | "warning";
    title: string;
}> = ({ keys, variant, title }) => {
    if (keys.length === 0) return null;

    const variantStyles = {
        neutral: { bg: colors.neutral.light, text: colors.neutral.dark },
        error: { bg: colors.error.bg, text: colors.error.text },
        warning: { bg: colors.warning.bg, text: colors.warning.text },
    };

    const style = variantStyles[variant];

    return (
        <div style={{ marginBottom: spacing.md }}>
            <strong
                style={{
                    color: style.text,
                    marginBottom: spacing.xs,
                    display: "block",
                }}
            >
                {title}:
            </strong>
            <div
                style={{
                    background: style.bg,
                    padding: spacing.sm,
                    borderRadius: borderRadius.sm,
                    marginTop: "5px",
                }}
            >
                {keys.map((key, index) => (
                    <div
                        key={index}
                        title={key}
                        style={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            marginBottom: index < keys.length - 1 ? "2px" : "0",
                        }}
                    >
                        {key}
                    </div>
                ))}
            </div>
        </div>
    );
};

export const LineItemPopup: React.FC<LineItemPopupProps> = ({
    number,
    lineKey,
    successConvertedBlueprintLines,
    blueprintRuleContents,
    targets,
}) => {
    const [showPopup, setShowPopup] = useState(false);

    if (!successConvertedBlueprintLines) {
        return null;
    }

    const isSuccess = successConvertedBlueprintLines.includes(number);
    const ruleContent = blueprintRuleContents.get(number);

    const handleClick = () => {
        setShowPopup(true);
    };

    const handleClosePopup = () => {
        setShowPopup(false);
    };

    const lineTarget =
        targets.filter((key) => lineKey.allKeys.includes(key))[0] ?? "";

    return (
        <>
            <li style={{ listStyle: "none" }}>
                <Tag
                    variant={isSuccess ? "success" : "error"}
                    clickable
                    onClick={handleClick}
                    style={{
                        padding: "5px 10px",
                        fontSize: "14px",
                        fontWeight: "500",
                    }}
                >
                    Line {number}
                </Tag>
            </li>

            <Modal
                isOpen={showPopup}
                onClose={handleClosePopup}
                title={`Line ${number}`}
                maxWidth="1000px"
                minWidth="800px"
            >
                {/* Missing Mappings Section */}
                <KeyList
                    keys={lineKey.missingMappingKeys}
                    variant="error"
                    title="Missing Mappings"
                />

                {/* Multiple Mappings Section */}
                <KeyList
                    keys={lineKey.multipleMappingKeys}
                    variant="warning"
                    title="Multiple Mappings"
                />

                {/* Rule Content Section */}
                {ruleContent && (
                    <CodeDisplay
                        content={lineTarget + ruleContent.content}
                        title="Rule Content:"
                        maxHeight="200px"
                    />
                )}
            </Modal>
        </>
    );
};

export default LineItemPopup;
