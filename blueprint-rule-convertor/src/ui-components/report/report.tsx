import $ from "lodash";

import {
    BlueprintApplicationReport,
    BlueprintLine,
    RuleCompareItem,
} from "@/models/formMapping";
import { DocumentInfo } from "./_DocumentInfo";
import { FormRuleStatistics } from "./_FormRuleStatistics";
import { BlueprintInsights } from "./_BlueprintInsights";
import { calculateSuccessRate } from "@/utils/application/report";
import RuleAnalysisSection from "./_RuleAnalysisSection";
import FormRuleUpdates from "./_FormRuleUpdates";

interface ReportProps {
    blueprintApplicationResult: BlueprintApplicationReport;
}

const Report = ({ blueprintApplicationResult }: ReportProps) => {
    if (!blueprintApplicationResult) {
        return null;
    }
    // Calculate statistics
    const successParsedRulesRatio =
        1 -
        blueprintApplicationResult.parserAnalytic.missingRuleLines.length /
            blueprintApplicationResult.parserAnalytic.expectedRuleLines.length;
    const successParsedRulesPercent = (successParsedRulesRatio * 100).toFixed(
        2,
    );

    const successConvertedBlueprintLines = $.uniq(
        blueprintApplicationResult.templateAnalytic.flatMap(
            ({ templateLineKeys }) =>
                templateLineKeys
                    .filter(
                        ([_, lineKey]) =>
                            lineKey.missingMappingKeys.length == 0 &&
                            lineKey.multipleMappingKeys.length == 0,
                    )
                    .map(([lineNum, _]) => lineNum),
        ),
    );
    const successConvertedBlueprintLinesRatio =
        successConvertedBlueprintLines.length /
        blueprintApplicationResult.parserAnalytic.expectedRuleLines.length;

    const mappedBlueprintKeysRatio =
        1 -
        blueprintApplicationResult.blueprintKeyAnalytic.missingKeys.length /
            blueprintApplicationResult.blueprintKeyAnalytic.allKeys.length;
    const mappedBlueprintKeysPercent = (mappedBlueprintKeysRatio * 100).toFixed(
        2,
    );

    const blueprintRuleContents = new Map(
        (blueprintApplicationResult?.blueprintLines || []).map(
            (line) => [line.lineNum, line] as [number, BlueprintLine],
        ),
    );

    const [
        successRuleGeneratorPercent,
        totalTemplates,
        reviewedRenderedTemplates,
        consistentRuleRatio,
        modifiedRuleCount,
        addingRuleCount,
        removingRuleCount,
        totalCompareItems,
    ] = calculateSuccessRate(blueprintApplicationResult);

    return (
        <div
            style={{
                maxWidth: "100%",
                margin: "0 auto",
                padding: "20px",
                fontFamily: "Arial, sans-serif",
                backgroundColor: "#f5f7fa",
                minHeight: "100vh",
            }}
        >
            <h1
                style={{
                    textAlign: "center",
                    color: "#2c3e50",
                    marginBottom: "30px",
                    fontSize: "32px",
                    borderBottom: "2px solid #3498db",
                    paddingBottom: "10px",
                }}
            >
                Blueprint Rule Generator Report
            </h1>

            {/* Summary Section */}
            <section
                style={{
                    backgroundColor: "white",
                    borderRadius: "8px",
                    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                    padding: "20px",
                    marginBottom: "30px",
                }}
            >
                <h2
                    style={{
                        color: "#2c3e50",
                        borderBottom: "1px solid #eee",
                        paddingBottom: "10px",
                        fontSize: "24px",
                    }}
                >
                    Summary
                </h2>

                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "20px",
                        marginTop: "20px",
                    }}
                >
                    <DocumentInfo
                        blueprintApplicationResult={blueprintApplicationResult}
                    />
                    <FormRuleStatistics
                        totalTemplates={totalTemplates}
                        successRuleGeneratorPercent={
                            successRuleGeneratorPercent
                        }
                        consistentRuleRatio={consistentRuleRatio}
                        modifiedRuleCount={modifiedRuleCount}
                        addingRuleCount={addingRuleCount}
                        removingRuleCount={removingRuleCount}
                        totalCompareItems={totalCompareItems}
                    />
                </div>
            </section>

            <BlueprintInsights
                blueprintApplicationResult={blueprintApplicationResult}
                blueprintRuleContents={blueprintRuleContents}
                successParsedRulesRatio={successParsedRulesRatio}
                successParsedRulesPercent={successParsedRulesPercent}
                successConvertedBlueprintLines={successConvertedBlueprintLines}
                successConvertedBlueprintLinesRatio={
                    successConvertedBlueprintLinesRatio
                }
                mappedBlueprintKeysRatio={mappedBlueprintKeysRatio}
                mappedBlueprintKeysPercent={mappedBlueprintKeysPercent}
            />

            {/* Rule Analysis Section */}
            <RuleAnalysisSection
                templateAnalytic={blueprintApplicationResult.templateAnalytic}
                successConvertedBlueprintLines={successConvertedBlueprintLines}
                reviewedRenderedTemplates={reviewedRenderedTemplates.map(
                    (template) => template.name,
                )}
                blueprintRuleContents={blueprintRuleContents}
            />

            {/* Form Rule Updates Section */}
            {blueprintApplicationResult.ruleCompareItems && (
                <FormRuleUpdates
                    ruleCompareItems={blueprintApplicationResult.ruleCompareItems
                        .map((item) =>
                            RuleCompareItem.fromRuleCompareItem(item),
                        )
                        .filter((v) => v != undefined)}
                    formRules={blueprintApplicationResult.formRules || []}
                    blueprintRules={
                        blueprintApplicationResult.blueprintRules || []
                    }
                />
            )}
        </div>
    );
};

export default Report;
