import React, { useState } from "react";
import {
    BlueprintApplicationReport,
    BlueprintLine,
} from "@/models/formMapping";
import CollapsibleSection from "./common/_CollapsibleSection";
import Modal from "./common/_Modal";
import Tag from "./common/_Tag";
import CodeDisplay from "./common/_CodeDisplay";
import BlueprintAnalysis from "./_BlueprintAnalysis";

interface BlueprintInsightsProps {
    blueprintApplicationResult: BlueprintApplicationReport;
    blueprintRuleContents: Map<number, BlueprintLine>;

    successParsedRulesRatio: number;
    successParsedRulesPercent: string;
    successConvertedBlueprintLines: number[];
    successConvertedBlueprintLinesRatio: number;
    mappedBlueprintKeysRatio: number;
    mappedBlueprintKeysPercent: string;
}

export const BlueprintInsights: React.FC<BlueprintInsightsProps> = ({
    blueprintApplicationResult,
    blueprintRuleContents,
    successParsedRulesRatio,
    successParsedRulesPercent,
    successConvertedBlueprintLines,
    successConvertedBlueprintLinesRatio,
    mappedBlueprintKeysRatio,
    mappedBlueprintKeysPercent,
}) => {
    const [showPopup, setShowPopup] = useState(false);
    const [selectedLine, setSelectedLine] = useState<number | null>(null);
    const [popupContent, setPopupContent] = useState<BlueprintLine | null>(
        null,
    );

    if (
        !blueprintApplicationResult ||
        !blueprintApplicationResult.parserAnalytic ||
        !blueprintApplicationResult.parserAnalytic.missingRuleLines
    ) {
        return null;
    }

    const handleLineClick = (line: number) => {
        const content = blueprintRuleContents.get(line);
        if (content) {
            setSelectedLine(line);
            setPopupContent(content);
            setShowPopup(true);
        }
    };

    const handleClosePopup = () => {
        setShowPopup(false);
        setSelectedLine(null);
        setPopupContent(null);
    };

    const missingRuleLines =
        blueprintApplicationResult.parserAnalytic.missingRuleLines;
    const commentRuleLines = (blueprintApplicationResult?.blueprintLines || [])
        .filter((line) => line.isComment)
        .map((line) => line.lineNum);

    return (
        <>
            <CollapsibleSection
                title="Blueprint Insights"
                style={{ marginBottom: "32px" }}
            >
                <div style={{ margin: "12px 0" }}>
                    <BlueprintAnalysis
                        successParsedRulesRatio={successParsedRulesRatio}
                        successParsedRulesPercent={successParsedRulesPercent}
                        successConvertedBlueprintLines={
                            successConvertedBlueprintLines
                        }
                        successConvertedBlueprintLinesRatio={
                            successConvertedBlueprintLinesRatio
                        }
                        mappedBlueprintKeysRatio={mappedBlueprintKeysRatio}
                        mappedBlueprintKeysPercent={mappedBlueprintKeysPercent}
                        blueprintApplicationResult={blueprintApplicationResult}
                    />

                    <h3
                        style={{
                            fontSize: "18px",
                            color: "#34495e",
                            marginBottom: "15px",
                            marginTop: "32px",
                        }}
                    >
                        Failed Parsed Rule Lines:
                    </h3>
                    <div
                        style={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: "8px",
                            margin: "15px 24px",
                        }}
                    >
                        {missingRuleLines.length > 0
                            ? missingRuleLines.map((line, idx) => (
                                  <Tag
                                      key={idx}
                                      variant="error"
                                      clickable
                                      onClick={() => handleLineClick(line)}
                                  >
                                      Line {line}
                                  </Tag>
                              ))
                            : "(empty)"}
                    </div>
                </div>

                <div style={{ margin: "24px 0" }}>
                    <h3
                        style={{
                            fontSize: "18px",
                            color: "#34495e",
                            marginBottom: "15px",
                            marginTop: "32px",
                        }}
                    >
                        Comments:
                    </h3>
                    <div
                        style={{
                            margin: "15px 24px",
                            backgroundColor: "#f8f9fa",
                            border: "1px solid #e9ecef",
                            borderRadius: "6px",
                            overflow: "hidden",
                        }}
                    >
                        {commentRuleLines.length > 0 ? (
                            <div
                                style={{
                                    fontFamily:
                                        'Monaco, Consolas, "Courier New", monospace',
                                    fontSize: "13px",
                                    lineHeight: "1.5",
                                }}
                            >
                                {commentRuleLines.map((lineNum, idx) => {
                                    const blueprintLine =
                                        blueprintRuleContents.get(lineNum);
                                    return (
                                        <div
                                            key={idx}
                                            style={{
                                                display: "flex",
                                                borderBottom:
                                                    idx <
                                                    commentRuleLines.length - 1
                                                        ? "1px solid #e9ecef"
                                                        : "none",
                                                cursor: "pointer",
                                                transition:
                                                    "background-color 0.2s ease",
                                            }}
                                            onClick={() =>
                                                handleLineClick(lineNum)
                                            }
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.backgroundColor =
                                                    "#e9ecef";
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.backgroundColor =
                                                    "transparent";
                                            }}
                                        >
                                            <div
                                                style={{
                                                    minWidth: "60px",
                                                    padding: "8px 12px",
                                                    backgroundColor: "#e9ecef",
                                                    color: "#6c757d",
                                                    textAlign: "right",
                                                    fontWeight: "600",
                                                    borderRight:
                                                        "1px solid #dee2e6",
                                                }}
                                            >
                                                {lineNum}
                                            </div>
                                            <div
                                                style={{
                                                    flex: 1,
                                                    padding: "8px 12px",
                                                    color: "#2c3e50",
                                                    whiteSpace: "pre-wrap",
                                                    wordBreak: "break-word",
                                                }}
                                            >
                                                {blueprintLine?.content ||
                                                    `// Comment on line ${lineNum}`}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div
                                style={{
                                    padding: "20px",
                                    textAlign: "center",
                                    color: "#6c757d",
                                    fontStyle: "italic",
                                }}
                            >
                                No comments found
                            </div>
                        )}
                    </div>
                </div>
            </CollapsibleSection>

            <Modal
                isOpen={showPopup}
                onClose={handleClosePopup}
                title={`Line ${selectedLine}`}
                maxWidth="800px"
                minWidth="600px"
            >
                <CodeDisplay
                    content={popupContent?.content || ""}
                    title="Blueprint Content"
                />
            </Modal>
        </>
    );
};

export default BlueprintInsights;
