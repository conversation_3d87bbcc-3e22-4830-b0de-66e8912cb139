import React, { useState } from "react";
import {
    Tooltip,
    Modal,
    Box,
    Typography,
    IconButton,
    Paper,
    Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { LineItemPopup } from "./_LineItemPopup";
import { MappingLists } from "./_MappingLists";
import { BlueprintLine, TemplateAnalytic } from "@/models/formMapping";
import { CombineMappingId } from "@/models/matchingId/blueprintMappingKey";

import Tag from "./common/_Tag";

interface RuleAnalysisSectionProps {
    templateAnalytic: TemplateAnalytic[];
    successConvertedBlueprintLines: any;
    reviewedRenderedTemplates: string[];
    blueprintRuleContents: Map<number, BlueprintLine>;
}

export const RuleAnalysisSection: React.FC<RuleAnalysisSectionProps> = ({
    templateAnalytic,
    successConvertedBlueprintLines,
    reviewedRenderedTemplates,
    blueprintRuleContents,
}) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showOnlyReviewed, setShowOnlyReviewed] = useState(true);
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedRule, setSelectedRule] = useState<any>(null);
    const [mappingModalOpen, setMappingModalOpen] = useState(false);
    const [selectedMapping, setSelectedMapping] = useState<any>(null);

    const handleRuleClick = (rule: any) => {
        setSelectedRule(rule);
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setModalOpen(false);
        setSelectedRule(null);
    };

    const handleMappingClick = (mappingId: any, fieldNames: string[]) => {
        setSelectedMapping({ mappingId, fieldNames });
        setMappingModalOpen(true);
    };

    const handleCloseMappingModal = () => {
        setMappingModalOpen(false);
        setSelectedMapping(null);
    };

    if (!templateAnalytic || templateAnalytic.length === 0) {
        return null;
    }

    // Filter logic: show only reviewed rules by default
    const filteredTemplateAnalytic = showOnlyReviewed
        ? templateAnalytic.filter(({ templateContent }) =>
              reviewedRenderedTemplates.includes(templateContent.name),
          )
        : templateAnalytic;

    return (
        <section
            style={{
                padding: "0 0 20px 0",
                backgroundColor: "white",
                borderRadius: "8px",
                boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                marginBottom: "20px",
            }}
        >
            <div
                style={{
                    color: "#2c3e50",
                    paddingTop: "20px",
                    fontSize: "24px",
                    cursor: "pointer",
                    backgroundColor: "white",
                    position: "sticky",
                    top: "64px",
                    zIndex: 10,
                }}
            >
                <div>
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "12px",

                            borderBottom: isExpanded
                                ? "2px solid #3498db"
                                : "none",
                            paddingBottom: isExpanded ? "10px" : "0",
                            margin: isExpanded
                                ? "0 20px 20px 20px"
                                : "0 20px 0 20px",
                        }}
                        onClick={() => setIsExpanded(!isExpanded)}
                    >
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                width: "24px",
                                height: "24px",
                                borderRadius: "4px",
                            }}
                        >
                            <span
                                style={{
                                    fontSize: "18px",
                                    transition: "all 0.3s ease",
                                    transform: isExpanded
                                        ? "rotate(90deg)"
                                        : "rotate(0deg)",
                                    fontWeight: "bold",
                                }}
                            >
                                ▶
                            </span>
                        </div>
                        <span>Blueprint Application Analysis</span>
                        <Tooltip
                            title="Detailed analysis of each generated rule template. Each template has form rule format and is generated from a group of blueprint rule line numbers with the same target."
                            placement="right"
                        >
                            <span
                                style={{
                                    display: "inline-block",
                                    width: "16px",
                                    height: "16px",
                                    backgroundColor: "#3498db",
                                    color: "white",
                                    borderRadius: "50%",
                                    textAlign: "center",
                                    lineHeight: "16px",
                                    fontSize: "12px",
                                    cursor: "pointer",
                                }}
                                onClick={(e) => e.stopPropagation()}
                            >
                                ?
                            </span>
                        </Tooltip>
                    </div>
                    {isExpanded && (
                        <div>
                            {/* Filter UI Component */}
                            <div
                                style={{
                                    backgroundColor: "#f8f9fa",
                                    borderRadius: "8px",
                                    border: "1px solid #e9ecef",
                                    padding: "16px 20px",
                                    margin: "0 20px 20px 20px",
                                    display: "flex",
                                    gap: "12px",
                                    position: "sticky",
                                    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                                    alignItems: "center",
                                }}
                            >
                                <span
                                    style={{
                                        fontSize: "18px",
                                        fontWeight: "600",
                                        color: "#2c3e50",
                                    }}
                                >
                                    Filter:
                                </span>
                                <div style={{ display: "flex", gap: "12px" }}>
                                    <button
                                        style={{
                                            padding: "8px 16px",
                                            borderRadius: "6px",
                                            border: "1px solid #e9ecef",
                                            backgroundColor: showOnlyReviewed
                                                ? "#3498db"
                                                : "#ffffff",
                                            color: showOnlyReviewed
                                                ? "#ffffff"
                                                : "#2c3e50",
                                            fontSize: "13px",
                                            fontWeight: "500",
                                            cursor: "pointer",
                                            transition: "all 0.2s ease",
                                        }}
                                        onClick={() =>
                                            setShowOnlyReviewed(true)
                                        }
                                        onMouseEnter={(e) => {
                                            if (!showOnlyReviewed) {
                                                e.currentTarget.style.backgroundColor =
                                                    "#f8f9fa";
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (!showOnlyReviewed) {
                                                e.currentTarget.style.backgroundColor =
                                                    "#ffffff";
                                            }
                                        }}
                                    >
                                        To-Reviewed Targets (
                                        {reviewedRenderedTemplates.length})
                                    </button>
                                    <button
                                        style={{
                                            padding: "8px 16px",
                                            borderRadius: "6px",
                                            border: "1px solid #e9ecef",
                                            backgroundColor: !showOnlyReviewed
                                                ? "#3498db"
                                                : "#ffffff",
                                            color: !showOnlyReviewed
                                                ? "#ffffff"
                                                : "#2c3e50",
                                            fontSize: "13px",
                                            fontWeight: "500",
                                            cursor: "pointer",
                                            transition: "all 0.2s ease",
                                        }}
                                        onClick={() =>
                                            setShowOnlyReviewed(false)
                                        }
                                        onMouseEnter={(e) => {
                                            if (showOnlyReviewed) {
                                                e.currentTarget.style.backgroundColor =
                                                    "#f8f9fa";
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (showOnlyReviewed) {
                                                e.currentTarget.style.backgroundColor =
                                                    "#ffffff";
                                            }
                                        }}
                                    >
                                        All Targets ({templateAnalytic.length})
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {isExpanded && (
                <div
                    style={{
                        animation: "slideDown 0.3s ease-out",
                        overflow: "hidden",
                    }}
                >
                    <div
                        style={{
                            padding: "0 24px",
                        }}
                    >
                        {filteredTemplateAnalytic.map(
                            (
                                {
                                    singleMappingKeys,
                                    missingMappingKeys,
                                    multipleMappingKeys,
                                    templateLineKeys,
                                    templateContent,
                                    renderedRules,
                                    templateId,
                                    combinedMappingInputs,
                                },
                                index,
                            ) => {
                                const oneMappings = singleMappingKeys;
                                const manyMappings = multipleMappingKeys;
                                const missingMappings = missingMappingKeys;
                                const totalLines = templateLineKeys.length;

                                return (
                                    <section
                                        key={index}
                                        style={{
                                            border: "1px solid #e9ecef",
                                            padding: "20px",
                                            backgroundColor: "white",
                                            marginBottom: "12px",
                                        }}
                                    >
                                        <div
                                            style={{
                                                display: "flex",
                                                borderBottom: "1px solid #eee",
                                                paddingBottom: "10px",
                                                color: "#2c3e50",
                                                fontSize: "20px",
                                                fontWeight: "600",
                                            }}
                                        >
                                            {index + 1}. Target{" "}
                                            {templateId.templateKeys
                                                .map((x) => `@${x}`)
                                                .join(", ")}
                                        </div>
                                        <div style={{ margin: "0 32px" }}>
                                            <div
                                                style={{
                                                    marginTop: "15px",
                                                    gap: "8px",
                                                }}
                                            >
                                                <h4
                                                    style={{
                                                        fontSize: "16px",
                                                        color: "#34495e",
                                                        fontWeight: "550",
                                                    }}
                                                >
                                                    Blueprint Rules (
                                                    {totalLines}):
                                                </h4>

                                                <ul
                                                    style={{
                                                        display: "flex",
                                                        flexWrap: "wrap",
                                                        gap: "10px",
                                                        listStyle: "none",
                                                        margin: "10px 24px 15px 24px",
                                                        padding: 0,
                                                    }}
                                                >
                                                    {templateLineKeys.map(
                                                        (
                                                            [number, lineKey]: [
                                                                any,
                                                                any,
                                                            ],
                                                            idx: number,
                                                        ) => (
                                                            <LineItemPopup
                                                                key={idx}
                                                                number={number}
                                                                lineKey={
                                                                    lineKey
                                                                }
                                                                successConvertedBlueprintLines={
                                                                    successConvertedBlueprintLines
                                                                }
                                                                blueprintRuleContents={
                                                                    blueprintRuleContents
                                                                }
                                                                targets={
                                                                    templateId.templateKeys
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </ul>
                                            </div>

                                            <div
                                                style={{
                                                    marginTop: "25px",
                                                    marginBottom: "25px",
                                                }}
                                            >
                                                <h4
                                                    style={{
                                                        fontSize: "16px",
                                                        color: "#34495e",
                                                        marginBottom: "10px",
                                                        fontWeight: "550",
                                                    }}
                                                >
                                                    Generated Form Rules (
                                                    {renderedRules.length}):
                                                </h4>
                                                <div
                                                    style={{
                                                        display: "flex",
                                                        flexDirection: "column",
                                                        gap: "8px",
                                                    }}
                                                >
                                                    {renderedRules.map(
                                                        (
                                                            rule: any,
                                                            ruleIndex: number,
                                                        ) => (
                                                            <Tooltip
                                                                title="Click to view rule content"
                                                                arrow
                                                                placement="top"
                                                            >
                                                                <div
                                                                    key={
                                                                        ruleIndex
                                                                    }
                                                                    style={{
                                                                        padding:
                                                                            "12px 16px",
                                                                        margin: "0 24px",
                                                                        backgroundColor:
                                                                            "#f8f9fa",
                                                                        borderRadius:
                                                                            "6px",
                                                                        fontSize:
                                                                            "14px",
                                                                        border: "1px solid #e9ecef",
                                                                        color: "#2c3e50",
                                                                        cursor: "pointer",
                                                                        transition:
                                                                            "all 0.2s ease",
                                                                        display:
                                                                            "flex",
                                                                        justifyContent:
                                                                            "space-between",
                                                                    }}
                                                                    onClick={() =>
                                                                        handleRuleClick(
                                                                            rule,
                                                                        )
                                                                    }
                                                                    onMouseEnter={(
                                                                        e,
                                                                    ) => {
                                                                        e.currentTarget.style.backgroundColor =
                                                                            "#e9ecef";
                                                                        e.currentTarget.style.borderColor =
                                                                            "#007bff";
                                                                    }}
                                                                    onMouseLeave={(
                                                                        e,
                                                                    ) => {
                                                                        e.currentTarget.style.backgroundColor =
                                                                            "#f8f9fa";
                                                                        e.currentTarget.style.borderColor =
                                                                            "#e9ecef";
                                                                    }}
                                                                >
                                                                    <div>
                                                                        <p
                                                                            style={{
                                                                                fontWeight:
                                                                                    "600",
                                                                                margin: "0 0 8px 0",
                                                                            }}
                                                                        >
                                                                            {
                                                                                rule.name
                                                                            }
                                                                        </p>

                                                                        <p
                                                                            style={{
                                                                                fontWeight:
                                                                                    "500",
                                                                                margin: "0",
                                                                                color: "#6c757d",
                                                                            }}
                                                                        >
                                                                            {
                                                                                rule.description
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                    <p
                                                                        style={{
                                                                            fontSize:
                                                                                "12px",
                                                                            color: "#007bff",
                                                                            margin: "8px 0 0 0",
                                                                            fontStyle:
                                                                                "italic",
                                                                        }}
                                                                    >
                                                                        View
                                                                        rule
                                                                        content
                                                                    </p>
                                                                </div>
                                                            </Tooltip>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                            {combinedMappingInputs &&
                                                combinedMappingInputs.length >
                                                    0 && (
                                                    <div
                                                        style={{
                                                            marginTop: "15px",
                                                            gap: "8px",
                                                        }}
                                                    >
                                                        <h4
                                                            style={{
                                                                fontSize:
                                                                    "16px",
                                                                color: "#34495e",
                                                                fontWeight:
                                                                    "550",
                                                                marginBottom:
                                                                    "12px",
                                                            }}
                                                        >
                                                            Combined Field Names
                                                            (
                                                            {
                                                                combinedMappingInputs.length
                                                            }
                                                            ):
                                                        </h4>

                                                        <div
                                                            style={{
                                                                display: "flex",
                                                                flexWrap:
                                                                    "wrap",
                                                                gap: "8px",
                                                                margin: "0 12px 24px 24px",
                                                            }}
                                                        >
                                                            {combinedMappingInputs.map(
                                                                ([
                                                                    _,
                                                                    combineField,
                                                                ]) => {
                                                                    const fieldNames =
                                                                        [
                                                                            combineField.fieldName,
                                                                            ...combineField.otherFieldNames,
                                                                        ].map(
                                                                            (
                                                                                field,
                                                                            ) =>
                                                                                field.id,
                                                                        );
                                                                    return fieldNames.map(
                                                                        (
                                                                            fieldName,
                                                                            _,
                                                                        ) => (
                                                                            <Tag
                                                                                variant={
                                                                                    "grey"
                                                                                }
                                                                                clickable
                                                                                onClick={() =>
                                                                                    handleMappingClick(
                                                                                        combineField.mappingId,
                                                                                        fieldNames,
                                                                                    )
                                                                                }
                                                                                style={{
                                                                                    padding:
                                                                                        "5px 10px",
                                                                                    fontSize:
                                                                                        "14px",
                                                                                    fontWeight:
                                                                                        "500",
                                                                                }}
                                                                            >
                                                                                {
                                                                                    fieldName
                                                                                }
                                                                            </Tag>
                                                                        ),
                                                                    );
                                                                },
                                                            )}
                                                        </div>
                                                    </div>
                                                )}

                                            <h4
                                                style={{
                                                    fontSize: "16px",
                                                    color: "#34495e",
                                                    fontWeight: "550",
                                                }}
                                            >
                                                Mapping Validation:
                                            </h4>

                                            <div
                                                style={{
                                                    margin: "0 24px",
                                                }}
                                            >
                                                <MappingLists
                                                    oneMappings={oneMappings}
                                                    manyMappings={manyMappings}
                                                    missingMappings={
                                                        missingMappings
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </section>
                                );
                            },
                        )}
                    </div>
                </div>
            )}
            <style>
                {`
                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-10px);
                            max-height: 0;
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                            max-height: 1000px;
                        }
                    }
                `}
            </style>

            {/* Rule Content Modal */}
            <Modal
                open={modalOpen}
                onClose={handleCloseModal}
                aria-labelledby="rule-content-modal-title"
                aria-describedby="rule-content-modal-description"
            >
                <Box
                    sx={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: { xs: "90%", sm: "80%", md: "70%", lg: "60%" },
                        maxWidth: "800px",
                        maxHeight: "80vh",
                        bgcolor: "background.paper",
                        borderRadius: 2,
                        boxShadow: 24,
                        overflow: "hidden",
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    {/* Modal Header */}
                    <Box
                        sx={{
                            p: 3,
                            borderBottom: "1px solid #e0e0e0",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            backgroundColor: "#f8f9fa",
                        }}
                    >
                        <Box sx={{ flex: 1, mr: 2 }}>
                            <Typography
                                id="rule-content-modal-title"
                                variant="h6"
                                component="h2"
                                sx={{
                                    fontWeight: 600,
                                    color: "#2c3e50",
                                    mb: 1,
                                }}
                            >
                                {selectedRule?.name || "Rule Content"}
                            </Typography>
                            <Typography
                                variant="body2"
                                sx={{
                                    color: "#6c757d",
                                    fontStyle: "italic",
                                }}
                            >
                                {selectedRule?.description ||
                                    "No description available"}
                            </Typography>
                        </Box>
                        <IconButton
                            onClick={handleCloseModal}
                            sx={{
                                color: "#6c757d",
                                "&:hover": {
                                    backgroundColor: "#e9ecef",
                                },
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Box>

                    {/* Modal Content */}
                    <Box
                        sx={{
                            flex: 1,
                            overflow: "auto",
                            p: 3,
                        }}
                    >
                        <Paper
                            elevation={0}
                            sx={{
                                p: 2,
                                backgroundColor: "#f8f9fa",
                                border: "1px solid #e9ecef",
                                borderRadius: 1,
                            }}
                        >
                            <Typography
                                variant="body2"
                                sx={{
                                    fontFamily:
                                        'Monaco, Consolas, "Courier New", monospace',
                                    fontSize: "13px",
                                    lineHeight: 1.5,
                                    whiteSpace: "pre-wrap",
                                    wordBreak: "break-word",
                                    color: "#2c3e50",
                                }}
                            >
                                {selectedRule?.value ||
                                    "No rule content available"}
                            </Typography>
                        </Paper>
                    </Box>
                </Box>
            </Modal>

            {/* Mapping Details Modal */}
            <Modal
                open={mappingModalOpen}
                onClose={handleCloseMappingModal}
                aria-labelledby="mapping-modal-title"
                aria-describedby="mapping-modal-description"
            >
                <Box
                    sx={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: { xs: "90%", sm: "80%", md: "60%" },
                        maxWidth: "600px",
                        bgcolor: "background.paper",
                        borderRadius: 2,
                        boxShadow: 24,
                        overflow: "hidden",
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    {/* Modal Header */}
                    <Box
                        sx={{
                            p: 3,
                            borderBottom: "1px solid #e0e0e0",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            backgroundColor: "#f8f9fa",
                        }}
                    >
                        <Typography
                            id="mapping-modal-title"
                            variant="h6"
                            component="h2"
                            sx={{
                                fontWeight: 600,
                                color: "#2c3e50",
                            }}
                        >
                            Mapping Details
                        </Typography>
                        <IconButton
                            onClick={handleCloseMappingModal}
                            sx={{
                                color: "#6c757d",
                                "&:hover": {
                                    backgroundColor: "#e9ecef",
                                },
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Box>

                    {/* Modal Content */}
                    <Box sx={{ p: 3 }}>
                        {selectedMapping && (
                            <>
                                <Box sx={{ mb: 3 }}>
                                    <Typography
                                        variant="subtitle1"
                                        sx={{
                                            fontWeight: 600,
                                            color: "#2c3e50",
                                            mb: 1,
                                        }}
                                    >
                                        Mapping:
                                    </Typography>
                                    <Paper
                                        elevation={0}
                                        sx={{
                                            p: 2,
                                            backgroundColor: "#f8f9fa",
                                            border: "1px solid #e9ecef",
                                            borderRadius: 1,
                                        }}
                                    >
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                fontFamily:
                                                    'Monaco, Consolas, "Courier New", monospace',
                                                fontSize: "13px",
                                                color: "#2c3e50",
                                            }}
                                        >
                                            {selectedMapping.mappingId
                                                ?.mapping ||
                                                "No mapping available"}
                                        </Typography>
                                    </Paper>
                                </Box>

                                <Box>
                                    <Typography
                                        variant="subtitle1"
                                        sx={{
                                            fontWeight: 600,
                                            color: "#2c3e50",
                                            mb: 2,
                                        }}
                                    >
                                        Combined by following fields:
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexWrap: "wrap",
                                            gap: 1,
                                        }}
                                    >
                                        {selectedMapping.mappingId?.elements?.map(
                                            (element: any, index: number) => (
                                                <Chip
                                                    key={index}
                                                    label={element.id}
                                                    variant="filled"
                                                    size="small"
                                                    sx={{
                                                        backgroundColor:
                                                            "#e3f2fd",
                                                        color: "#1976d2",
                                                    }}
                                                />
                                            ),
                                        ) || (
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    color: "#6c757d",
                                                    fontStyle: "italic",
                                                }}
                                            >
                                                No elements available
                                            </Typography>
                                        )}
                                    </Box>
                                </Box>
                            </>
                        )}
                    </Box>
                </Box>
            </Modal>
        </section>
    );
};

export default RuleAnalysisSection;
