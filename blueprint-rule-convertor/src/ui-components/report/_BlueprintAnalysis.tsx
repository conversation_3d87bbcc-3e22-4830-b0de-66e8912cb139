import React from "react";
import { BlueprintApplicationReport } from "@/models/formMapping";
import { ProgressBar } from "./_ProgressBar";

interface BlueprintAnalysisProps {
    successParsedRulesRatio: number;
    successParsedRulesPercent: string;
    successConvertedBlueprintLines: number[];
    successConvertedBlueprintLinesRatio: number;
    mappedBlueprintKeysRatio: number;
    mappedBlueprintKeysPercent: string;
    blueprintApplicationResult: BlueprintApplicationReport;
}

export const BlueprintAnalysis: React.FC<BlueprintAnalysisProps> = ({
    successParsedRulesRatio,
    successParsedRulesPercent,
    successConvertedBlueprintLines,
    successConvertedBlueprintLinesRatio,
    mappedBlueprintKeysRatio,
    mappedBlueprintKeysPercent,
    blueprintApplicationResult,
}) => {
    if (
        !blueprintApplicationResult ||
        !blueprintApplicationResult.parserAnalytic ||
        !blueprintApplicationResult.parserAnalytic.expectedRuleLines ||
        !blueprintApplicationResult.parserAnalytic.missingRuleLines ||
        !blueprintApplicationResult.blueprintKeyAnalytic ||
        !blueprintApplicationResult.blueprintKeyAnalytic.allKeys ||
        !blueprintApplicationResult.blueprintKeyAnalytic.missingKeys
    ) {
        return null;
    }

    return (
        <div style={{ flex: "1", minWidth: "300px" }}>
            <h3
                style={{
                    fontSize: "18px",
                    color: "#34495e",
                    marginBottom: "15px",
                }}
            >
                Blueprint Analysis
            </h3>
            <ProgressBar
                label="Success Parsed Rules"
                tooltip="The total number of structured Blueprint rules successfully parsed."
                ratio={successParsedRulesRatio}
                percent={successParsedRulesPercent}
                current={
                    blueprintApplicationResult.parserAnalytic.expectedRuleLines
                        .length -
                    blueprintApplicationResult.parserAnalytic.missingRuleLines
                        .length
                }
                total={
                    blueprintApplicationResult.parserAnalytic.expectedRuleLines
                        .length
                }
            />
            <ProgressBar
                label="Success Converted Rules"
                tooltip="The total number of structured Blueprint rules successfully converted to form rules. These rules include one-to-one and one-to-many mappings and were safely matched to form fields without missing mappings."
                ratio={successConvertedBlueprintLinesRatio}
                percent={(successConvertedBlueprintLinesRatio * 100).toFixed(2)}
                current={successConvertedBlueprintLines.length}
                total={
                    blueprintApplicationResult.parserAnalytic.expectedRuleLines
                        .length
                }
            />
            <ProgressBar
                label="Mapped Blueprint Keys"
                tooltip="The total mappings which are used by Blueprint rules and these mappings are mapped to form fields."
                ratio={mappedBlueprintKeysRatio}
                percent={mappedBlueprintKeysPercent}
                current={
                    blueprintApplicationResult.blueprintKeyAnalytic.allKeys
                        .length -
                    blueprintApplicationResult.blueprintKeyAnalytic.missingKeys
                        .length
                }
                total={
                    blueprintApplicationResult.blueprintKeyAnalytic.allKeys
                        .length
                }
            />
        </div>
    );
};
export default BlueprintAnalysis;
