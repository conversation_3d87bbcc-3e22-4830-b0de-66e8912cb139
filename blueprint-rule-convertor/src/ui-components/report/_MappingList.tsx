import React from "react";

interface MappingListProps {
    mappings: string[];
    title: string;
    backgroundColor: string;
    borderColor: string;
    titleColor: string;
    icon: string;
    borderBottomColor: string;
}

export const MappingList: React.FC<MappingListProps> = ({
    mappings,
    title,
    backgroundColor,
    borderColor,
    titleColor,
    icon,
    borderBottomColor,
}) => {
    if (!mappings || mappings.length === 0) {
        return null;
    }

    return (
        <div
            style={{
                padding: "15px",
                backgroundColor,
                borderRadius: "6px",
                border: `1px solid ${borderColor}`,
            }}
        >
            <h3
                style={{
                    fontSize: "18px",
                    color: titleColor,
                    marginBottom: "10px",
                }}
            >
                {title}
            </h3>
            <ul
                style={{
                    listStyle: "none",
                    padding: 0,
                    margin: 0,
                }}
            >
                {mappings.map((mapping, idx) => (
                    <li
                        key={idx}
                        style={{
                            padding: "8px 0",
                            borderBottom: `1px solid ${borderBottomColor}`,
                            fontSize: "14px",
                            display: "flex",
                        }}
                        title={mapping}
                    >
                        <span style={{ marginRight: "8px" }}>{icon}</span>
                        <span
                            style={{
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                display: "inline-block",
                                maxWidth: "calc(100% - 24px)",
                            }}
                        >
                            {mapping}
                        </span>
                    </li>
                ))}
            </ul>
        </div>
    );
};
export default MappingList;
