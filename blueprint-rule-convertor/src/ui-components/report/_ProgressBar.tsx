import React from "react";
import { Tooltip } from "@mui/material";

interface ProgressBarProps {
    label: string;
    tooltip: string;
    ratio: number;
    percent: string;
    current: number;
    total: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
    label,
    tooltip,
    ratio,
    percent,
    current,
    total,
}) => {
    return (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                margin: "8px 0",
            }}
        >
            <div
                style={{
                    width: "250px",
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    gap: "5px",
                }}
            >
                <strong>{label}:</strong>
                <Tooltip title={tooltip}>
                    <span
                        style={{
                            display: "inline-block",
                            width: "16px",
                            height: "16px",
                            backgroundColor: "#3498db",
                            color: "white",
                            borderRadius: "50%",
                            textAlign: "center",
                            lineHeight: "16px",
                            fontSize: "12px",
                            cursor: "pointer",
                        }}
                    >
                        ?
                    </span>
                </Tooltip>
            </div>
            <div
                style={{
                    width: "200px",
                    height: "20px",
                    backgroundColor: "#e0e0e0",
                    borderRadius: "10px",
                    overflow: "hidden",
                }}
            >
                <div
                    style={{
                        width: `${percent}%`,
                        height: "100%",
                        backgroundColor:
                            ratio > 0.7
                                ? "#4caf50"
                                : ratio > 0.4
                                  ? "#ff9800"
                                  : "#f44336",
                        transition: "width 1s ease-in-out",
                    }}
                ></div>
            </div>
            <div style={{ marginLeft: "10px", fontSize: "16px" }}>
                {current} / {total} ({percent}%)
            </div>
        </div>
    );
};

export default ProgressBar;
