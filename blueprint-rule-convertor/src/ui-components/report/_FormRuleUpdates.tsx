import React, { useState, useMemo, useEffect } from "react";
import { ExistingFormRule, RuleCompareItem } from "@/models/formMapping";
import { FormRule } from "@/models/stargazer/";
import RuleCompareItemDisplay from "./_RuleCompareItemDisplay";
import { RuleId, CombineRuleId } from "@/models/matchingId/ruleMappingId";

interface FormRuleUpdatesProps {
    ruleCompareItems: RuleCompareItem[];
    formRules: FormRule[];
    blueprintRules: FormRule[];
}

type RuleTypeFilter = "all" | "combine_only";
type FilterType =
    | "adding"
    | "removing"
    | "existing_modified"
    | "existing_unchanged";

interface FilterState {
    adding: boolean;
    removing: boolean;
    existing_modified: boolean;
    existing_unchanged: boolean;
}

export const FormRuleUpdates: React.FC<FormRuleUpdatesProps> = ({
    ruleCompareItems,
    formRules,
    blueprintRules,
}) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [filters, setFilters] = useState<FilterState>({
        adding: true,
        removing: true,
        existing_modified: true,
        existing_unchanged: false,
    });
    const [ruleTypeFilter, setRuleTypeFilter] = useState<RuleTypeFilter>("all");
    const [displayCount, setDisplayCount] = useState(20);

    // Reset display count when filters change
    useEffect(() => {
        setDisplayCount(20);
    }, [filters, ruleTypeFilter]);

    // Create maps from rule arrays for efficient lookup
    const formRuleMap = useMemo(() => {
        const map = new Map<string, FormRule>();
        formRules.forEach((rule) => {
            // Extract rule ID from description if it contains blueprint information
            const ruleId = RuleId.fromTagString(rule.description);
            if (ruleId) {
                map.set(ruleId.id, rule);
            }
        });
        return map;
    }, [formRules]);

    const blueprintRuleMap = useMemo(() => {
        const map = new Map<string, FormRule>();
        blueprintRules.forEach((rule) => {
            // Extract rule ID from description if it contains blueprint information
            const ruleId = RuleId.fromTagString(rule.description);
            if (ruleId) {
                map.set(ruleId.id, rule);
            }
        });
        return map;
    }, [blueprintRules]);

    if (!ruleCompareItems || ruleCompareItems.length === 0) {
        return null;
    }

    // Helper function to check if a rule is a combine rule
    const isCombineRule = (ruleId: RuleId): boolean => {
        return ruleId.type === "CombineRuleId";
    };

    // Filter logic based on selected filters
    const filteredItems = ruleCompareItems.filter((item) => {
        // First apply the status filter
        let statusMatch = false;
        if (item.type === "AddingFormRule") {
            statusMatch = filters.adding;
        } else if (item.type === "RemovingFormRule") {
            statusMatch = filters.removing;
        } else if (item.type === "ExistingFormRule") {
            const existingRule = item as ExistingFormRule;
            statusMatch = existingRule.isModfied
                ? filters.existing_modified
                : filters.existing_unchanged;
        }

        if (!statusMatch) {
            return false;
        }

        // Then apply the rule type filter
        if (ruleTypeFilter === "combine_only") {
            return isCombineRule(item.ruleId);
        }

        return true; // "all" filter shows everything that passed status filter
    });

    // Count items by type and rule type
    const counts = ruleCompareItems.reduce(
        (acc, item) => {
            const isCombine = isCombineRule(item.ruleId);

            if (item.type === "AddingFormRule") {
                acc.adding++;
                if (isCombine) acc.adding_combine++;
            } else if (item.type === "RemovingFormRule") {
                acc.removing++;
                if (isCombine) acc.removing_combine++;
            } else if (item.type === "ExistingFormRule") {
                const existingRule = item as ExistingFormRule;
                if (existingRule.isModfied) {
                    acc.existing_modified++;
                    if (isCombine) acc.existing_modified_combine++;
                } else {
                    acc.existing_unchanged++;
                    if (isCombine) acc.existing_unchanged_combine++;
                }
            }
            return acc;
        },
        {
            adding: 0,
            removing: 0,
            existing_modified: 0,
            existing_unchanged: 0,
            adding_combine: 0,
            removing_combine: 0,
            existing_modified_combine: 0,
            existing_unchanged_combine: 0,
            total_combine: 0,
        },
    );

    // Calculate total combine rules
    counts.total_combine =
        counts.adding_combine +
        counts.removing_combine +
        counts.existing_modified_combine +
        counts.existing_unchanged_combine;

    const toggleFilter = (filterType: FilterType) => {
        setFilters((prev) => ({
            ...prev,
            [filterType]: !prev[filterType],
        }));
    };
    return (
        <section
            style={{
                padding: "0 20px 20px 20px",
                backgroundColor: "white",
                borderRadius: "8px",
                boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                marginBottom: "12px",
                marginTop: "32px",
            }}
        >
            <div
                style={{
                    color: "#2c3e50",
                    paddingTop: "20px",
                    fontSize: "24px",
                    cursor: "pointer",
                    position: "sticky",
                    top: "64px",
                    backgroundColor: "white",
                    zIndex: 10,
                }}
            >
                <div>
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "12px",
                            borderBottom: isExpanded
                                ? "2px solid #3498db"
                                : "none",
                            paddingBottom: isExpanded ? "10px" : "0",
                            margin: isExpanded ? "0 20px 20px 0" : "0 20px 0 0",
                        }}
                        onClick={() => setIsExpanded(!isExpanded)}
                    >
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                width: "24px",
                                height: "24px",
                                borderRadius: "4px",
                            }}
                        >
                            <span
                                style={{
                                    fontSize: "18px",
                                    transition: "all 0.3s ease",
                                    transform: isExpanded
                                        ? "rotate(90deg)"
                                        : "rotate(0deg)",
                                    fontWeight: "bold",
                                }}
                            >
                                ▶
                            </span>
                        </div>
                        <span>Rule Mistmatches</span>
                    </div>
                    {isExpanded && (
                        <div>
                            {/* Filter UI Component */}
                            <div
                                style={{
                                    backgroundColor: "#ffffff",
                                    borderRadius: "12px",
                                    border: "2px solid #e9ecef",
                                    padding: "20px",
                                    marginBottom: "24px",
                                    position: "sticky",
                                    boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
                                }}
                            >
                                {/* Header with Results Count */}
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        marginBottom: "12px",
                                        gap: "8px",
                                    }}
                                >
                                    <h3
                                        style={{
                                            fontSize: "16px",
                                            fontWeight: "600",
                                            color: "#2c3e50",
                                            margin: "0",
                                        }}
                                    >
                                        Filter Rules
                                    </h3>
                                    <div
                                        style={{
                                            color: "#2c3e50",
                                            borderRadius: "20px",
                                            fontSize: "14px",
                                        }}
                                    >
                                        ({filteredItems.length} of{" "}
                                        {ruleCompareItems.length} rules shown)
                                    </div>
                                </div>

                                {/* Filter Controls */}
                                <div
                                    style={{
                                        display: "flex",
                                        justifyContent: "space-between",

                                        gap: "24px",
                                    }}
                                >
                                    {/* Rule Status Filters */}
                                    <div>
                                        <div
                                            style={{
                                                display: "flex",
                                                gap: "8px",
                                                fontSize: "13px",
                                            }}
                                        >
                                            <button
                                                style={{
                                                    borderRadius: "8px",
                                                    width: "150px",
                                                    border: filters.adding
                                                        ? "2px solid #28a745"
                                                        : "2px solid #e9ecef",
                                                    backgroundColor:
                                                        filters.adding
                                                            ? "#28a745"
                                                            : "#ffffff",
                                                    color: filters.adding
                                                        ? "#ffffff"
                                                        : "#28a745",
                                                    boxShadow: filters.adding
                                                        ? "0 2px 8px rgba(40, 167, 69, 0.3)"
                                                        : "none",
                                                }}
                                                onClick={() =>
                                                    toggleFilter("adding")
                                                }
                                            >
                                                + Missing ({counts.adding})
                                            </button>
                                            <button
                                                style={{
                                                    borderRadius: "8px",
                                                    width: "150px",
                                                    border: filters.removing
                                                        ? "2px solid #dc3545"
                                                        : "2px solid #e9ecef",
                                                    backgroundColor:
                                                        filters.removing
                                                            ? "#dc3545"
                                                            : "#ffffff",
                                                    color: filters.removing
                                                        ? "#ffffff"
                                                        : "#dc3545",
                                                    fontSize: "13px",
                                                    cursor: "pointer",
                                                    transition: "all 0.2s ease",
                                                    textAlign: "center",
                                                    boxShadow: filters.removing
                                                        ? "0 2px 8px rgba(220, 53, 69, 0.3)"
                                                        : "none",
                                                }}
                                                onClick={() =>
                                                    toggleFilter("removing")
                                                }
                                            >
                                                − Redundant ({counts.removing})
                                            </button>
                                            <button
                                                style={{
                                                    borderRadius: "8px",
                                                    width: "150px",
                                                    border: filters.existing_modified
                                                        ? "2px solid #ffc107"
                                                        : "2px solid #e9ecef",
                                                    backgroundColor:
                                                        filters.existing_modified
                                                            ? "#ffc107"
                                                            : "#ffffff",
                                                    color: filters.existing_modified
                                                        ? "#ffffff"
                                                        : "#ffc107",
                                                    fontSize: "13px",
                                                    cursor: "pointer",
                                                    transition: "all 0.2s ease",
                                                    textAlign: "center",
                                                    boxShadow:
                                                        filters.existing_modified
                                                            ? "0 2px 8px rgba(255, 193, 7, 0.3)"
                                                            : "none",
                                                }}
                                                onClick={() =>
                                                    toggleFilter(
                                                        "existing_modified",
                                                    )
                                                }
                                            >
                                                ~ Modified (
                                                {counts.existing_modified})
                                            </button>
                                            <button
                                                style={{
                                                    padding: "10px 14px",
                                                    borderRadius: "8px",
                                                    border: filters.existing_unchanged
                                                        ? "2px solid #6c757d"
                                                        : "2px solid #e9ecef",
                                                    backgroundColor:
                                                        filters.existing_unchanged
                                                            ? "#6c757d"
                                                            : "#ffffff",
                                                    color: filters.existing_unchanged
                                                        ? "#ffffff"
                                                        : "#6c757d",
                                                    fontSize: "13px",
                                                    cursor: "pointer",
                                                    transition: "all 0.2s ease",
                                                    textAlign: "center",
                                                    boxShadow:
                                                        filters.existing_unchanged
                                                            ? "0 2px 8px rgba(108, 117, 125, 0.3)"
                                                            : "none",
                                                }}
                                                onClick={() =>
                                                    toggleFilter(
                                                        "existing_unchanged",
                                                    )
                                                }
                                            >
                                                = Consistent (
                                                {counts.existing_unchanged})
                                            </button>
                                        </div>
                                    </div>

                                    {/* Rule Type Filter */}
                                    <div
                                        style={{
                                            minWidth: "200px",
                                        }}
                                    >
                                        <label
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "12px",
                                                padding: "12px 16px",
                                                borderRadius: "8px",
                                                backgroundColor: "#ffffff",
                                                cursor: "pointer",
                                                transition: "all 0.2s ease",
                                                fontSize: "14px",
                                                fontWeight: "500",
                                                color: "#495057",
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.borderColor =
                                                    "#007bff";
                                                e.currentTarget.style.backgroundColor =
                                                    "#f8f9fa";
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.borderColor =
                                                    "#e9ecef";
                                                e.currentTarget.style.backgroundColor =
                                                    "#ffffff";
                                            }}
                                        >
                                            <input
                                                type="checkbox"
                                                checked={
                                                    ruleTypeFilter ===
                                                    "combine_only"
                                                }
                                                onChange={(e) =>
                                                    setRuleTypeFilter(
                                                        e.target.checked
                                                            ? "combine_only"
                                                            : "all",
                                                    )
                                                }
                                                style={{
                                                    width: "18px",
                                                    height: "18px",
                                                    cursor: "pointer",
                                                    accentColor: "#007bff",
                                                }}
                                            />
                                            <span>
                                                Show Combine Rules Only (
                                                {counts.total_combine})
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {isExpanded && (
                <div
                    style={{
                        marginTop: "20px",
                        animation: "slideDown 0.3s ease-out",
                        overflow: "hidden",
                    }}
                >
                    <div
                        style={{
                            marginTop: "10px",
                        }}
                    >
                        {filteredItems.length === 0 ? (
                            <div
                                style={{
                                    textAlign: "center",
                                    padding: "40px",
                                    color: "#6c757d",
                                    fontSize: "16px",
                                }}
                            >
                                No items match the selected filters.
                            </div>
                        ) : (
                            <>
                                <ul
                                    style={{
                                        listStyle: "none",
                                        padding: "0",
                                        margin: "0",
                                        display: "flex",
                                        flexDirection: "column",
                                        gap: "8px",
                                    }}
                                >
                                    {filteredItems
                                        .slice(0, displayCount)
                                        .map((item, index) => (
                                            <RuleCompareItemDisplay
                                                key={index}
                                                item={item}
                                                formRuleMap={formRuleMap}
                                                blueprintRuleMap={
                                                    blueprintRuleMap
                                                }
                                                itemIndex={index + 1}
                                            />
                                        ))}
                                </ul>

                                {/* Load More Button */}
                                {filteredItems.length > displayCount && (
                                    <div
                                        style={{
                                            textAlign: "center",
                                            marginTop: "24px",
                                            padding: "20px",
                                        }}
                                    >
                                        <button
                                            onClick={() =>
                                                setDisplayCount(
                                                    (prev) => prev + 20,
                                                )
                                            }
                                            style={{
                                                padding: "12px 24px",
                                                backgroundColor: "#007bff",
                                                color: "#ffffff",
                                                border: "none",
                                                borderRadius: "8px",
                                                fontSize: "14px",
                                                fontWeight: "600",
                                                cursor: "pointer",
                                                transition: "all 0.2s ease",
                                                boxShadow:
                                                    "0 2px 8px rgba(0, 123, 255, 0.3)",
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.backgroundColor =
                                                    "#0056b3";
                                                e.currentTarget.style.transform =
                                                    "translateY(-1px)";
                                                e.currentTarget.style.boxShadow =
                                                    "0 4px 12px rgba(0, 123, 255, 0.4)";
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.backgroundColor =
                                                    "#007bff";
                                                e.currentTarget.style.transform =
                                                    "translateY(0)";
                                                e.currentTarget.style.boxShadow =
                                                    "0 2px 8px rgba(0, 123, 255, 0.3)";
                                            }}
                                        >
                                            Load More (
                                            {Math.min(
                                                20,
                                                filteredItems.length -
                                                    displayCount,
                                            )}{" "}
                                            more items)
                                        </button>
                                        <div
                                            style={{
                                                marginTop: "8px",
                                                fontSize: "12px",
                                                color: "#6c757d",
                                            }}
                                        >
                                            Showing {displayCount} of{" "}
                                            {filteredItems.length} items
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            )}
            <style>
                {`
                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-10px);
                            max-height: 0;
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                            max-height: 1000px;
                        }
                    }
                `}
            </style>
        </section>
    );
};

export default FormRuleUpdates;
