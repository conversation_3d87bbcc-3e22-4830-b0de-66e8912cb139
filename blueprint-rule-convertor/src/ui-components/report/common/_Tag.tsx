import React from "react";
import {
    colors,
    spacing,
    borderRadius,
    transitions,
    typography,
} from "@/styles/designTokens";

type TagVariant = "success" | "error" | "warning" | "info" | "neutral" | "grey";

interface TagProps {
    children: React.ReactNode;
    variant?: TagVariant;
    onClick?: () => void;
    clickable?: boolean;
    title?: string;
    style?: React.CSSProperties;
    className?: string;
}

const getVariantStyles = (variant: TagVariant) => {
    const variants = {
        success: {
            bg: colors.success.bg,
            color: colors.success.text,
            border: colors.success.border,
        },
        error: {
            bg: colors.error.bg,
            color: colors.error.text,
            border: colors.error.border,
        },
        warning: {
            bg: colors.warning.bg,
            color: colors.warning.text,
            border: colors.warning.border,
        },
        info: {
            bg: colors.info.bg,
            color: colors.info.text,
            border: colors.info.border,
        },
        neutral: {
            bg: colors.neutral.light,
            color: colors.neutral.text,
            border: colors.neutral.border,
        },
        grey: {
            bg: colors.grey.bg,
            color: colors.grey.text,
            border: colors.grey.border,
        },
    };
    return variants[variant];
};

export const Tag: React.FC<TagProps> = ({
    children,
    variant = "neutral",
    onClick,
    clickable = false,
    title,
    style,
    className,
}) => {
    const variantStyles = getVariantStyles(variant);
    const isClickable = clickable || !!onClick;

    return (
        <div
            onClick={onClick}
            title={title}
            className={className}
            style={{
                display: "inline-flex",
                alignItems: "center",
                padding: `${spacing.xs} ${spacing.md}`,
                backgroundColor: variantStyles.bg,
                border: `1px solid ${variantStyles.border}`,
                borderRadius: borderRadius.xl,
                fontSize: typography.base,
                color: variantStyles.color,
                cursor: isClickable ? "pointer" : "default",
                transition: isClickable ? transitions.fast : "none",
                fontWeight: "500",
                ...style,
            }}
            onMouseEnter={(e) => {
                if (isClickable) {
                    e.currentTarget.style.backgroundColor = colors.grey.light;
                    e.currentTarget.style.transform = "translateY(-1px)";
                    e.currentTarget.style.boxShadow =
                        "0 2px 4px rgba(0,0,0,0.1)";
                }
            }}
            onMouseLeave={(e) => {
                if (isClickable) {
                    e.currentTarget.style.backgroundColor = variantStyles.bg;
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow = "none";
                }
            }}
        >
            {children}
        </div>
    );
};

export default Tag;
