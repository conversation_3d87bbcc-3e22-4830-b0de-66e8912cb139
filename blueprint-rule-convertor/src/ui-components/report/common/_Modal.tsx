import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import {
    colors,
    spacing,
    borderRadius,
    shadows,
    zIndex,
} from "@/styles/designTokens";

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    children: React.ReactNode;
    maxWidth?: string;
    minWidth?: string;
    maxHeight?: string;
}

const Modal: React.FC<ModalProps> = ({
    isOpen,
    onClose,
    title,
    children,
    maxWidth = "600px",
    minWidth = "400px",
    maxHeight = "80vh",
}) => {
    const modalRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!isOpen) return;

        const handleClickOutside = (event: MouseEvent) => {
            if (
                modalRef.current &&
                !modalRef.current.contains(event.target as Node)
            ) {
                onClose();
            }
        };

        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Escape") {
                onClose();
            }
        };

        // Add event listeners after a small delay to prevent immediate closing
        const timer = setTimeout(() => {
            document.addEventListener("mousedown", handleClickOutside);
            document.addEventListener("keydown", handleKeyDown);
        }, 100);

        // Focus the modal for keyboard accessibility
        if (modalRef.current) {
            modalRef.current.focus();
        }

        return () => {
            clearTimeout(timer);
            document.removeEventListener("mousedown", handleClickOutside);
            document.removeEventListener("keydown", handleKeyDown);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    const ModalContent = () => (
        <>
            {/* Overlay Background */}
            <div
                style={{
                    position: "fixed",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: colors.popup.overlay,
                    zIndex: zIndex.overlay,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: spacing.lg,
                    animation: "fadeIn 0.2s ease-out",
                }}
                onClick={onClose}
            />

            {/* Centered Modal */}
            <div
                ref={modalRef}
                role="dialog"
                aria-modal="true"
                aria-labelledby={title ? "modal-title" : undefined}
                tabIndex={-1}
                style={{
                    position: "fixed",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    backgroundColor: colors.popup.bg,
                    padding: spacing.xl,
                    borderRadius: borderRadius.lg,
                    boxShadow: shadows.xl,
                    zIndex: zIndex.modal,
                    maxWidth,
                    minWidth,
                    fontSize: "14px",
                    maxHeight,
                    overflowY: "auto",
                    border: `1px solid ${colors.popup.border}`,
                    outline: "none",
                    animation: "slideIn 0.3s ease-out",
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* CSS Animations */}
                <style>
                    {`
                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }
                        
                        @keyframes slideIn {
                            from {
                                opacity: 0;
                                transform: translate(-50%, -50%) scale(0.9);
                            }
                            to {
                                opacity: 1;
                                transform: translate(-50%, -50%) scale(1);
                            }
                        }
                    `}
                </style>

                {/* Close button */}
                <button
                    onClick={onClose}
                    style={{
                        position: "absolute",
                        right: spacing.sm,
                        top: spacing.sm,
                        border: "none",
                        background: "none",
                        fontSize: spacing.xl,
                        cursor: "pointer",
                        color: colors.neutral.muted,
                        padding: "0",
                        width: spacing.xxl,
                        height: spacing.xxl,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                            colors.neutral.light;
                        e.currentTarget.style.borderRadius = borderRadius.sm;
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                    }}
                >
                    ×
                </button>

                {/* Modal Content */}
                <div>
                    {title && (
                        <strong
                            id="modal-title"
                            style={{
                                color: colors.neutral.dark,
                                marginBottom: spacing.md,
                                display: "block",
                                fontSize: "16px",
                            }}
                        >
                            {title}
                        </strong>
                    )}
                    {children}
                </div>
            </div>
        </>
    );

    return createPortal(<ModalContent />, document.body);
};

export default Modal;
