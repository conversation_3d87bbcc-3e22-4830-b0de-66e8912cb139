import React, { useState } from "react";
import {
    colors,
    spacing,
    typography,
    borderRadius,
    shadows,
    transitions,
    zIndex,
} from "@/styles/designTokens";

interface CollapsibleSectionProps {
    title: string;
    children: React.ReactNode;
    defaultExpanded?: boolean;
    className?: string;
    style?: React.CSSProperties;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
    title,
    children,
    defaultExpanded = false,
    className,
    style,
}) => {
    const [isExpanded, setIsExpanded] = useState(defaultExpanded);

    return (
        <section
            className={className}
            style={{
                padding: "0 20px 20px 20px",
                backgroundColor: colors.neutral.bg,
                borderRadius: borderRadius.lg,
                boxShadow: shadows.md,
                marginBottom: spacing.md,
                marginTop: spacing.xxxl,
                ...style,
            }}
        >
            {/* Toggle Header */}
            <div
                style={{
                    color: colors.neutral.dark,
                    paddingTop: spacing.xl,
                    fontSize: typography.xxxl,
                    cursor: "pointer",
                    position: "sticky",
                    top: "64px",
                    backgroundColor: colors.neutral.bg,
                    zIndex: zIndex.sticky,
                }}
            >
                <div>
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            gap: spacing.md,
                            borderBottom: isExpanded
                                ? `2px solid ${colors.primary.main}`
                                : "none",
                            paddingBottom: isExpanded ? spacing.md : "0",
                            margin: isExpanded
                                ? `0 ${spacing.xl} ${spacing.xl} 0`
                                : `0 ${spacing.xl} 0 0`,
                        }}
                        onClick={() => setIsExpanded(!isExpanded)}
                    >
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                width: spacing.xxl,
                                height: spacing.xxl,
                                borderRadius: borderRadius.sm,
                            }}
                        >
                            <span
                                style={{
                                    fontSize: typography.xl,
                                    transition: transitions.medium,
                                    transform: isExpanded
                                        ? "rotate(90deg)"
                                        : "rotate(0deg)",
                                    fontWeight: "bold",
                                }}
                            >
                                ▶
                            </span>
                        </div>
                        <span>{title}</span>
                    </div>
                </div>
            </div>

            {/* Collapsible Content */}
            {isExpanded && (
                <div
                    style={{
                        marginTop: spacing.xl,
                        animation: "slideDown 0.3s ease-out",
                        overflow: "hidden",
                    }}
                >
                    {children}
                </div>
            )}

            {/* CSS Animations */}
            <style>
                {`
                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-10px);
                            max-height: 0;
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                            max-height: 1000px;
                        }
                    }
                `}
            </style>
        </section>
    );
};

export default CollapsibleSection;
