import React from "react";
import {
    colors,
    spacing,
    borderRadius,
    shadows,
    transitions,
    typography,
} from "@/styles/designTokens";

type ButtonVariant =
    | "primary"
    | "secondary"
    | "success"
    | "error"
    | "warning"
    | "info"
    | "neutral";
type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    variant?: ButtonVariant;
    size?: ButtonSize;
    disabled?: boolean;
    active?: boolean;
    title?: string;
    style?: React.CSSProperties;
    className?: string;
}

const getVariantStyles = (variant: ButtonVariant, active: boolean) => {
    const variants = {
        primary: {
            bg: active ? colors.primary.main : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.primary.main,
            border: colors.primary.main,
        },
        secondary: {
            bg: active ? colors.neutral.muted : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.neutral.muted,
            border: colors.neutral.muted,
        },
        success: {
            bg: active ? colors.success.main : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.success.main,
            border: colors.success.main,
        },
        error: {
            bg: active ? colors.error.main : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.error.main,
            border: colors.error.main,
        },
        warning: {
            bg: active ? colors.warning.main : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.warning.main,
            border: colors.warning.main,
        },
        info: {
            bg: active ? colors.info.main : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.info.main,
            border: colors.info.main,
        },
        neutral: {
            bg: active ? colors.neutral.muted : colors.neutral.bg,
            color: active ? colors.neutral.bg : colors.neutral.text,
            border: colors.neutral.border,
        },
    };
    return variants[variant];
};

const getSizeStyles = (size: ButtonSize) => {
    const sizes = {
        sm: {
            padding: `${spacing.xs} ${spacing.sm}`,
            fontSize: typography.sm,
        },
        md: {
            padding: `${spacing.sm} ${spacing.md}`,
            fontSize: typography.base,
        },
        lg: {
            padding: `${spacing.md} ${spacing.lg}`,
            fontSize: typography.lg,
        },
    };
    return sizes[size];
};

export const Button: React.FC<ButtonProps> = ({
    children,
    onClick,
    variant = "neutral",
    size = "md",
    disabled = false,
    active = false,
    title,
    style,
    className,
}) => {
    const variantStyles = getVariantStyles(variant, active);
    const sizeStyles = getSizeStyles(size);

    return (
        <button
            onClick={onClick}
            disabled={disabled}
            title={title}
            className={className}
            style={{
                ...sizeStyles,
                backgroundColor: variantStyles.bg,
                color: variantStyles.color,
                border: `1px solid ${variantStyles.border}`,
                borderRadius: borderRadius.md,
                cursor: disabled ? "not-allowed" : "pointer",
                transition: transitions.fast,
                fontWeight: "500",
                boxShadow: shadows.sm,
                opacity: disabled ? 0.6 : 1,
                ...style,
            }}
            onMouseEnter={(e) => {
                if (!disabled) {
                    e.currentTarget.style.transform = "translateY(-1px)";
                    e.currentTarget.style.boxShadow = shadows.md;
                }
            }}
            onMouseLeave={(e) => {
                if (!disabled) {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow = shadows.sm;
                }
            }}
        >
            {children}
        </button>
    );
};

export default Button;
