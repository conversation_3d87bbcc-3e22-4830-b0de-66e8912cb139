import React from "react";
import {
    colors,
    spacing,
    borderRadius,
    typography,
} from "@/styles/designTokens";

interface CodeDisplayProps {
    content: string;
    maxHeight?: string;
    title?: string;
    language?: string;
    style?: React.CSSProperties;
    className?: string;
}

export const CodeDisplay: React.FC<CodeDisplayProps> = ({
    content,
    maxHeight = "300px",
    title,
    language = "text",
    style,
    className,
}) => {
    return (
        <div className={className} style={style}>
            {title && (
                <div
                    style={{
                        marginBottom: spacing.xs,
                        fontSize: typography.base,
                        fontWeight: "600",
                        color: colors.neutral.dark,
                    }}
                >
                    {title}
                </div>
            )}
            <div
                style={{
                    background: colors.neutral.light,
                    padding: spacing.md,
                    borderRadius: borderRadius.sm,
                    fontFamily: "monospace",
                    fontSize: typography.md,
                    maxHeight,
                    overflowY: "auto",
                    border: `1px solid ${colors.neutral.border}`,
                    whiteSpace: "pre-wrap",
                    wordBreak: "break-word",
                    lineHeight: "1.4",
                    color: colors.neutral.text,
                }}
            >
                {content}
            </div>
        </div>
    );
};

export default CodeDisplay;
