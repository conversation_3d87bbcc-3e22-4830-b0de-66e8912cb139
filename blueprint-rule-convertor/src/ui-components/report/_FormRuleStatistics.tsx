import React from "react";

interface FormRuleStatisticsProps {
    totalTemplates: number;
    successRuleGeneratorPercent: string;
    consistentRuleRatio: string;
    modifiedRuleCount: number;
    addingRuleCount: number;
    removingRuleCount: number;
    totalCompareItems: number;
}

export const FormRuleStatistics: React.FC<FormRuleStatisticsProps> = ({
    totalTemplates,
    successRuleGeneratorPercent,
    consistentRuleRatio,
    modifiedRuleCount,
    addingRuleCount,
    removingRuleCount,
    totalCompareItems,
}) => {
    return (
        <div style={{ flex: "1", minWidth: "300px" }}>
            <h3
                style={{
                    fontSize: "18px",
                    color: "#34495e",
                    marginBottom: "15px",
                }}
            >
                Application Statistics
            </h3>
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    margin: "8px 0",
                }}
            >
                <div
                    style={{
                        width: "250px",
                        fontSize: "16px",
                        display: "flex",
                        alignItems: "center",
                        gap: "5px",
                    }}
                >
                    <strong>Success Convert Rate:</strong>
                </div>
                <p>
                    {totalTemplates >= 0
                        ? `${successRuleGeneratorPercent}% of ${totalTemplates} target(s)`
                        : `N/A`}
                </p>
            </div>
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    margin: "8px 0",
                }}
            >
                <div
                    style={{
                        width: "250px",
                        fontSize: "16px",
                        display: "flex",
                        alignItems: "center",
                        gap: "5px",
                    }}
                >
                    <strong>Consistent Rate:</strong>
                </div>
                <p>
                    {totalCompareItems == 0
                        ? "N/A"
                        : `${consistentRuleRatio}% (${addingRuleCount} Missing, ${removingRuleCount} Redundant, ${modifiedRuleCount} Modified and ${totalCompareItems - addingRuleCount - removingRuleCount - modifiedRuleCount} Consistent)`}
                </p>
            </div>
            <div style={{ margin: "8px 0", fontSize: "16px" }}></div>
        </div>
    );
};

export default FormRuleStatistics;
