import $ from "lodash";

import { FormRule } from "@/models/stargazer";

import { FormRuleTemplate } from "@/models";
import { MappingCategoryData } from "@/generator/formApplication/benchmark";
import { BlueprintMappingKey } from "@/models/matchingId/blueprintMappingKey";
import {
    RuleTargetId,
    TemplateMappingId,
} from "@/models/matchingId/ruleMappingId";
import { FieldName, OptionFieldName } from "@/models/matchingId/formElementId";
import { renderTemplate } from "@/utils/rule/renderTemplateUtils";
import { findOptionKey } from "@/utils/application/blueprintMappingKey";

export class TemplateManager {
    templateIdMap: Map<TemplateMappingId, FormRuleTemplate>;

    constructor(templates: FormRuleTemplate[]) {
        const templateIdMap: Map<TemplateMappingId, FormRuleTemplate> =
            new Map();

        templates.forEach((t) => {
            const templateId = TemplateMappingId.fromTagString(
                t.rule.description,
            );
            if (templateId) {
                templateIdMap.set(templateId, t);
            }
        });
        this.templateIdMap = templateIdMap;
    }

    renderRules(
        inputFieldNameMap: Map<string, string>,
        targetFieldNameMap: Map<string, string[]>,
        mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>,
        investorFieldNameMap: Map<string, string>,
    ): [
        Map<TemplateMappingId, FormRule[]>,
        Map<TemplateMappingId, string[]>,
        Map<TemplateMappingId, Record<string, any>>,
    ] {
        const renderedItems: [
            TemplateMappingId,
            [FormRule[], string[], Record<string, any>],
        ][] = Array.from(this.templateIdMap.entries()).map(
            ([templateId, template]): [
                TemplateMappingId,
                [FormRule[], string[], Record<string, any>],
            ] => {
                const targetKeys =
                    template.blueprintRuleKeys.getAllTargetKeys();
                const inputKeys = $.difference(
                    template.blueprintRuleKeys.getAllInputKeys(),
                    targetKeys,
                );
                const optionKeys =
                    template.blueprintRuleKeys.getAllOptionKeys();
                const missingReplacementKeys: string[] = [];
                const inputReplacements: Map<string, string> = new Map([
                    ...inputKeys
                        .map((key): [string, string] | undefined => {
                            if (investorFieldNameMap.has(key)) {
                                return [key, key];
                            }
                            const fieldName = inputFieldNameMap.get(key);

                            if (fieldName) {
                                return [key, fieldName];
                            }

                            console.error(
                                `Template ${templateId.buildTag()}: Cannot find replacement for field ${key}`,
                            );

                            missingReplacementKeys.push(key);

                            return undefined;
                        })
                        .filter((v) => v != undefined),
                ]);

                const optionReplacements: Map<string, string> = new Map([
                    ...optionKeys
                        .map(
                            (
                                optionMappingKey,
                            ): [string, string] | undefined => {
                                if (
                                    investorFieldNameMap.has(optionMappingKey)
                                ) {
                                    const op =
                                        OptionFieldName.fromString(
                                            optionMappingKey,
                                        );
                                    if (!op) {
                                        missingReplacementKeys.push(
                                            optionMappingKey,
                                        );
                                        return undefined;
                                    }
                                    return [optionMappingKey, op.optionName];
                                }
                                const fieldOption =
                                    OptionFieldName.fromString(
                                        optionMappingKey,
                                    );

                                if (!fieldOption) {
                                    console.error(
                                        `Template ${templateId.buildTag()}: Cannot find replacement for option ${optionMappingKey}`,
                                    );

                                    missingReplacementKeys.push(
                                        optionMappingKey,
                                    );
                                    return undefined;
                                }
                                const parentName = fieldOption.parentName;
                                const parentFieldName =
                                    inputFieldNameMap.get(parentName);
                                if (!parentFieldName) {
                                    missingReplacementKeys.push(
                                        optionMappingKey,
                                    );
                                    return undefined;
                                }

                                const optionKey = findOptionKey(
                                    mappingToFieldNamesMap,
                                    template.blueprintRuleKeys.getAllFieldKeys(),
                                    optionMappingKey,
                                );

                                if (!optionKey) {
                                    missingReplacementKeys.push(
                                        optionMappingKey,
                                    );
                                    return undefined;
                                }

                                return [optionMappingKey, optionKey];
                            },
                        )
                        .filter((v) => v != undefined),
                ]);

                const targetReplacements: Map<string, string[]> = new Map(
                    Array.from(targetFieldNameMap).filter(([mapping]) =>
                        targetKeys.map((key) => key).includes(mapping),
                    ),
                );

                const maxTargetLength = $.max(
                    [...targetReplacements.values()].map(
                        (items) => items.length,
                    ),
                );

                if (!maxTargetLength || maxTargetLength == 0) {
                    console.error(
                        `Template ${templateId.buildTag()}: Cannot find field names maped to targets ${targetKeys}`,
                    );
                    targetKeys.forEach((key) => {
                        missingReplacementKeys.push(key);
                    });
                }

                const flattenTargetReplacements: Map<string, string>[] =
                    Array.from({ length: maxTargetLength || 0 }, (_, index) => {
                        const replacementMap = new Map<string, string>();
                        Array.from(targetReplacements).forEach(
                            ([targetKey, fieldNames]) => {
                                replacementMap.set(
                                    targetKey,
                                    fieldNames[index] || fieldNames[0],
                                );
                            },
                        );
                        return replacementMap;
                    });

                const rules = flattenTargetReplacements.map(
                    (targetMap, index) => {
                        const values = new Map([
                            ...targetMap,
                            ...inputReplacements,
                            ...optionReplacements,
                        ]);

                        const rule = renderTemplate(template, values);
                        const targetId = new RuleTargetId([
                            ...targetMap.values(),
                        ]);

                        return {
                            ...rule,
                            name: `${rule.name} (Target Set ${index + 1})`,
                            description:
                                `${targetId.buildTag()} ${rule.description}`.replaceAll(
                                    "] [",
                                    "][",
                                ),
                        };
                    },
                );

                const replacements = {
                    flattenTargetReplacements: [
                        ...flattenTargetReplacements.map((x) => [
                            ...x.entries(),
                        ]),
                    ],
                    targetReplacements: [...targetReplacements.entries()],
                    inputReplacements: [...inputReplacements.entries()],
                    optionReplacements: [...optionReplacements.entries()],
                };

                return [
                    templateId,
                    [rules, missingReplacementKeys, replacements],
                ];
            },
        );

        const rules = new Map(
            renderedItems.map(
                ([templateId, [rules]]): [TemplateMappingId, FormRule[]] => [
                    templateId,
                    rules,
                ],
            ),
        );
        const missingReplacementKeys: Map<TemplateMappingId, string[]> =
            new Map(
                renderedItems
                    .map(
                        ([templateId, [_, missingKeys]]):
                            | [TemplateMappingId, string[]]
                            | undefined =>
                            missingKeys.length > 0
                                ? [templateId, missingKeys]
                                : undefined,
                    )
                    .filter((v) => v != undefined),
            );
        const replacements: Map<
            TemplateMappingId,
            Record<string, any>
        > = new Map(
            renderedItems.map(([templateId, [_, __, replacements]]) => [
                templateId,
                replacements,
            ]),
        );

        return [rules, missingReplacementKeys, replacements];
    }

    getMultipleTargetTemplates(): [TemplateMappingId, string[]][] {
        return Array.from(this.templateIdMap)
            .filter(
                ([_, template]) =>
                    template.blueprintRuleKeys.getAllTargetKeys.length > 1,
            )
            .map(([k, v]) => [k, v.blueprintRuleKeys.getAllTargetKeys()]);
    }
    getSingleTargetTemplates(): [TemplateMappingId, string][] {
        return Array.from(this.templateIdMap)
            .map(([k, v]): [TemplateMappingId, string] | undefined =>
                v.blueprintRuleKeys.getAllTargetKeys().length == 1
                    ? [k, v.blueprintRuleKeys.getAllTargetKeys()[0]]
                    : undefined,
            )
            .filter((v) => v != undefined);
    }
    getAllTargetKeys(): string[] {
        return $.uniq(
            Array.from(this.templateIdMap.values()).flatMap((template) =>
                template.blueprintRuleKeys.getAllTargetKeys(),
            ),
        );
    }

    getAllInputKeys(): string[] {
        return $.uniq(
            Array.from(this.templateIdMap.values()).flatMap((template) =>
                template.blueprintRuleKeys.getAllInputKeys(),
            ),
        );
    }

    getAllInputExcludeTargetKeys(): string[] {
        return $.uniq(
            Array.from(this.templateIdMap.values()).flatMap((template) =>
                template.blueprintRuleKeys.getInputsExludeTargetKeys(),
            ),
        );
    }

    getAllOptionKeys(): string[] {
        return $.uniq(
            Array.from(this.templateIdMap.values()).flatMap((template) =>
                template.blueprintRuleKeys.getAllOptionKeys(),
            ),
        );
    }

    getAllKeys(): string[] {
        return $.uniq([
            ...this.getAllTargetKeys(),
            ...this.getAllInputKeys(),
            ...this.getAllOptionKeys(),
        ]);
    }

    getCategoryData(): MappingCategoryData {
        return {
            targetCount: {
                multipleTargetTemplates: this.getMultipleTargetTemplates().map(
                    ([t, _]) => t.id,
                ),
                singleTargetTemplates: this.getSingleTargetTemplates().map(
                    ([t, _]) => t.id,
                ),
            },
            targetType: {
                targetKeys: this.getAllTargetKeys(),
                inputKeys: this.getAllInputKeys(),
                optionKeys: this.getAllOptionKeys(),
            },
        };
    }
}
