import { renderCombineRule } from "@/utils/rule/renderTemplateContent";
import { FormDataImpl } from "./formData";
import { CombineMappingId } from "./matchingId/blueprintMappingKey";
import { FieldName, ParentFieldName } from "./matchingId/formElementId";
import { FormRule } from "./stargazer";
import { TemplateFormApplicationAnalysisImpl } from "./templateFormApplicationAnalysis";

export type CombineMappingField = {
    mappingId: CombineMappingId;
    fieldName: FieldName;
    otherFieldNames: FieldName[];
};

export class CombineMappingManager {
    private _missingCombineMappingFieldsCache: Map<
        string,
        CombineMappingField
    > | null = null;
    private _existingCombineMappingFieldsCache: Map<
        string,
        CombineMappingField
    > | null = null;

    constructor(
        private templateApplication: TemplateFormApplicationAnalysisImpl,
        private formDataImpl: FormDataImpl,
    ) {}

    getMissingCombineMappingFields(): Map<string, CombineMappingField> {
        // Return cached result if available
        if (this._missingCombineMappingFieldsCache !== null) {
            return this._missingCombineMappingFieldsCache;
        }

        // Compute and cache the result
        this._missingCombineMappingFieldsCache = new Map(
            this.templateApplication
                .getCombineMappingMissingKeys()
                .map((id): [string, CombineMappingField] | undefined => {
                    const mappingId = CombineMappingId.fromKeyString(id);

                    if (!mappingId) {
                        console.error(
                            `Cannot find combine mapping id for ${id}`,
                        );
                        return undefined;
                    }

                    return [
                        mappingId.id,
                        {
                            mappingId,
                            fieldName: new ParentFieldName(
                                this.formDataImpl.getUniqueName(
                                    mappingId.getRenderedName(),
                                ),
                            ),
                            otherFieldNames: [],
                        },
                    ];
                })
                .filter((v) => v != undefined),
        );

        return this._missingCombineMappingFieldsCache;
    }

    getAddingCombineMappingFieldByKey(
        mappingKey: string,
    ): CombineMappingField | undefined {
        return this.getMissingCombineMappingFields().get(mappingKey);
    }

    getExistingCombineMappingFields(): Map<string, CombineMappingField> {
        // Return cached result if available
        if (this._existingCombineMappingFieldsCache !== null) {
            return this._existingCombineMappingFieldsCache;
        }

        // Compute and cache the result
        this._existingCombineMappingFieldsCache = new Map(
            this.templateApplication
                .getCombineMappingExistingKeys()
                .map((mappingId): [string, CombineMappingField] | undefined => {
                    const fieldNames = this.formDataImpl
                        .getAnalysis()
                        .getMappingToFieldNamesMap()
                        .get(mappingId);

                    if (!fieldNames || fieldNames.length == 0) {
                        console.error(
                            `Cannot find field name for combine mapping id : ${mappingId.id}`,
                        );
                        return undefined;
                    }

                    if (fieldNames.length >= 2) {
                        console.error(
                            `Found more than 2 field names for combine mapping id : ${mappingId.id}. Field names: ${fieldNames}`,
                        );
                    }

                    return [
                        mappingId.id,
                        {
                            mappingId: mappingId,
                            fieldName: fieldNames[0],
                            otherFieldNames: fieldNames.slice(1),
                        },
                    ];
                })
                .filter((v) => v != undefined),
        );

        return this._existingCombineMappingFieldsCache;
    }

    getCombineRules(): FormRule[] {
        return [...this.getAllCombineMappingFields()]
            .map(([_, combineField]): FormRule | undefined => {
                const value = renderCombineRule(
                    combineField.mappingId,
                    combineField.fieldName.id,
                );

                return {
                    value,
                    name: `[Combine Value] combine values of ${combineField.mappingId.mapping}`,
                    defaultNamespace: "main",
                    description: `${combineField.mappingId.buildTag()}`,
                    templateId: "",
                } as FormRule;
            })
            .filter((v) => v != undefined);
    }

    getAllCombineMappingFields(): Map<string, CombineMappingField> {
        return new Map([
            ...this.getExistingCombineMappingFields(),
            ...this.getMissingCombineMappingFields(),
        ]);
    }

    /**
     * Clears all cached values. Call this method when the underlying data changes
     * to ensure fresh computations on the next method calls.
     */
    clearCache(): void {
        this._missingCombineMappingFieldsCache = null;
        this._existingCombineMappingFieldsCache = null;
    }
}
