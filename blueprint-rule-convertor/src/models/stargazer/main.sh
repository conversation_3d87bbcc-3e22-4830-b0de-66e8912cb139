#!/bin/bash

# $1 => Path to the Stargazer folder

# Variable storing all needed path 
stargazer_path=$1
proto_folder_path="./src/models/stargazer/proto"
openapi_folder_path="./src/models/stargazer/openapi"
openapi_document_file_path="document.yaml"


echo "Directory of Stargazer: ${stargazer_path}"

mkdir "${proto_folder_path}"
mkdir "${openapi_folder_path}"

source_protobuf_dirs=()

# Find all directories named "protobuf" under the stargazer path
for dir in $(find "$stargazer_path" -type d -name "protobuf"); do
  source_protobuf_dirs+=("$dir")
done

# Find all directories named "scalaPBUnpackProto.dest" under the stargazer path
for dir in $(find "$stargazer_path" -type d -name "scalaPBUnpackProto.dest"); do
  source_protobuf_dirs+=("$dir")
done

echo "Number of found 'protobuf' directories: ${#source_protobuf_dirs[@]}"

proto_source_file_name=(
  "date_time.proto"
  "email_address.proto"
  "form_models.proto"
  "form_version_models.proto"
  "system_tag.proto"
  "blueprint_model.proto"
  "blueprint_version.proto"
)

# Loop through the stargazer_path to find path of all needed protobuf files
for file_name in "${proto_source_file_name[@]}"; do
  for path in $(find "$stargazer_path" -type f -name "$file_name"); do
      proto_source_file_paths+=("$path")
  done
done

protobuf_input_param=""

# Loop through the source_protobuf_dirs array and concatenate the paths
for dir in "${source_protobuf_dirs[@]}"; do
  # Build Inpur parameter for npx protoc by concatenating the strings
  protobuf_input_param+=" --proto_path $dir"
done

for file_name in "${proto_source_file_name[@]}"; do
  proto_source_file_paths=()
  
  for path in $(find "$stargazer_path" -type f -name "$file_name"); do
      bun run protoc --ts_out "$proto_folder_path" $protobuf_input_param --descriptor_set_out=./out/protoc_descriptor.bin --include_imports "$path"
      proto_source_file_paths+=("$path")
  done

  echo "Generated Typescript models from protobuf file: ${file_name} with ${#proto_source_file_paths[@]} file path(s) found"
done

bun run openapi-typescript ${openapi_folder_path}/document.yaml --output ${openapi_folder_path}/interface.ts --enum=true --enum-values --make-paths-enum

echo "Generated Typescript models from OpenAPI schema"

echo "Models are generated. Script execution complete."