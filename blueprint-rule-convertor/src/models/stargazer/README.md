# Stargazer Model Generation

This project provides tools for generating data models for the Stargazer project. It uses two primary methods:

### 1. TypeScript Model Generation from Protobuf

This script automates the generation of TypeScript models from Protobuf (`.proto`) files within the Stargazer project.

The script will:

- Locate all relevant Protobuf files within the Stargazer project.
- Use the `protoc` compiler and the appropriate plugins to generate TypeScript model files.
- The generated TypeScript model files will be placed in a designated output directory (defined within the script).

### 2. Model Generation from OpenAPI YAML

This project also generates models based on the OpenAPI YAML specification. This specification defines models based on the Scala code within the Stargazer project.

### Purpose

This method generates models based on the defined schemas in the OpenAPI YAML file. This file provides a representation of the data models used by the Stargazer project, as defined in its Scala code.

The process for generating models from the OpenAPI YAML file involve following steps:

1.  Generate OpenAPI YAML files.
2.  Executing a script that utilizes the OpenAPI specification to generate model definitions.

### Usage

To generate TypeScript models:

1.  Execute the `main.sh` script, providing the path to your Stargazer project root directory as a command-line argument:

    ```bash
    sh main.sh [path_to_stargazer]
    ```
    - `main.sh`: The shell script responsible for finding and processing Protobuf files.
    - `[path_to_stargazer]`: Replace this with the actual path to the root directory of your Stargazer project.
