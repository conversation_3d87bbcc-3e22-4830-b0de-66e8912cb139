import * as Interface from "@/models/stargazer/openapi/interface";

// Object Type
export type AfterFieldInfo = Interface.components["schemas"]["AfterFieldInfo"];
export type AnnotationDocumentData =
    Interface.components["schemas"]["AnnotationDocumentData"];
export type AnnotationDocumentId =
    Interface.components["schemas"]["AnnotationDocumentId"];
export type AnnotationDocumentVersionId =
    Interface.components["schemas"]["AnnotationDocumentVersionId"];
export type Area2D = Interface.components["schemas"]["Area2D"];
export type AssociatedLink = Interface.components["schemas"]["AssociatedLink"];
export type BlueprintId = Interface.components["schemas"]["BlueprintId"];
export type BlueprintInfo = Interface.components["schemas"]["BlueprintInfo"];
export type BlueprintMetaData =
    Interface.components["schemas"]["BlueprintMetadata"];
export type BlueprintScope = Interface.components["schemas"]["BlueprintScope"];
export type BlueprintSignature =
    Interface.components["schemas"]["BlueprintSignature"];
export type BlueprintVersionId =
    Interface.components["schemas"]["BlueprintVersionId"];
export type CatalaData = Interface.components["schemas"]["CatalaData"];
export type Custom = Interface.components["schemas"]["Custom"];
export type CustomTypeConfig =
    Interface.components["schemas"]["CustomTypeConfig"];
export type Derived = Interface.components["schemas"]["Derived"];
export type ExtractedField = Interface.components["schemas"]["ExtractedField"];
export type ExtractedPdf = Interface.components["schemas"]["ExtractedPdf"];
export type Field = Interface.components["schemas"]["Field"];
export type FieldPosition = Interface.components["schemas"]["FieldPosition"];
export type FileGroup = Interface.components["schemas"]["FileGroup"];
export type FileItem = Interface.components["schemas"]["FileItem"];
export type Form = Interface.components["schemas"]["Form"];
export type FormCheckUpToDateData =
    Interface.components["schemas"]["FormCheckUpToDateData"];
export type FormData = Interface.components["schemas"]["FormData"];
export type FormId = Interface.components["schemas"]["FormId"];
export type FormLibrary = Interface.components["schemas"]["FormLibrary"];
export type FormRule = Interface.components["schemas"]["FormRule"];
export type FormSchema = Interface.components["schemas"]["FormSchema"];
export type FormVersionId = Interface.components["schemas"]["FormVersionId"];
export type GatingQuestion = Interface.components["schemas"]["GatingQuestion"];
export type GatingQuestionOption =
    Interface.components["schemas"]["GatingQuestionOption"];
export type GetAnnotationDataParams =
    Interface.components["schemas"]["GetAnnotationDataParams"];
export type GetAnnotationDataResponse =
    Interface.components["schemas"]["GetAnnotationDataResponse"];
export type GetBlueprintVersionCatalaCodeParams =
    Interface.components["schemas"]["GetBlueprintVersionCatalaCodeParams"];
export type GetBlueprintVersionCatalaCodeResponse =
    Interface.components["schemas"]["GetBlueprintVersionCatalaCodeResponse"];
export type GetFormParams = Interface.components["schemas"]["GetFormParams"];
export type GetFormResponse =
    Interface.components["schemas"]["GetFormResponse"];
export type GetRandomParams =
    Interface.components["schemas"]["GetRandomParams"];
export type MapAssociatedLink =
    Interface.components["schemas"]["Map_AssociatedLink"];
export type MapFileId = Interface.components["schemas"]["Map_FileId"];
export type MapFileIdExtractedPdf =
    Interface.components["schemas"]["Map_FileId_ExtractedPdf"];
export type MapFileIdSeqString =
    Interface.components["schemas"]["Map_FileId_Seq_String"];
export type MapFormNamespaceFormSchema =
    Interface.components["schemas"]["Map_FormNamespace_FormSchema"];
export type MapPdfObjectIdPdfObject =
    Interface.components["schemas"]["Map_PdfObjectId_PdfObject"];
export type MapSetString = Interface.components["schemas"]["Map_Set_String"];
export type MapUIKeyString =
    Interface.components["schemas"]["Map_UIKey_String"];
export type MapUserIdUserInfo =
    Interface.components["schemas"]["Map_UserId_UserInfo"];
export type MapWidget = Interface.components["schemas"]["Map_Widget"];
export type ParagraphMessage =
    Interface.components["schemas"]["ParagraphMessage"];
export type PdfCheckboxField =
    Interface.components["schemas"]["PdfCheckboxField"];
export type PdfColor = Interface.components["schemas"]["PdfColor"];
export type PdfEmbeddedFileField =
    Interface.components["schemas"]["PdfEmbeddedFileField"];
export type PdfGroupField = Interface.components["schemas"]["PdfGroupField"];
export type PdfMultipleCheckboxesField =
    Interface.components["schemas"]["PdfMultipleCheckboxesField"];
export type PdfObjects = Interface.components["schemas"]["PdfObjects"];
export type PdfPageField = Interface.components["schemas"]["PdfPageField"];
export type PdfParagraphField =
    Interface.components["schemas"]["PdfParagraphField"];
export type PdfRadioField = Interface.components["schemas"]["PdfRadioField"];
export type PdfRadioGroupField =
    Interface.components["schemas"]["PdfRadioGroupField"];
export type PdfRepeatableField =
    Interface.components["schemas"]["PdfRepeatableField"];
export type PdfSignatureField =
    Interface.components["schemas"]["PdfSignatureField"];
export type PdfTextBlock = Interface.components["schemas"]["PdfTextBlock"];
export type PdfTextField = Interface.components["schemas"]["PdfTextField"];
export type PdfTextLabel = Interface.components["schemas"]["PdfTextLabel"];
export type Position2D = Interface.components["schemas"]["Position2D"];
export type SignatureMapping =
    Interface.components["schemas"]["SignatureMapping"];
export type StringConstant = Interface.components["schemas"]["StringConstant"];
export type UserId = Interface.components["schemas"]["UserId"];
export type UserInfo = Interface.components["schemas"]["UserInfo"];
export type ArraySchema = Interface.components["schemas"]["array"];
export type BooleanSchema = Interface.components["schemas"]["boolean"];
export type CustomSchema = Interface.components["schemas"]["custom"];
export type EnumSchema = Interface.components["schemas"]["enum"];
export type IntegerSchema = Interface.components["schemas"]["integer"];
export type NullSchema = Interface.components["schemas"]["null"];
export type NumberSchema = Interface.components["schemas"]["number"];
export type ObjSchema = Interface.components["schemas"]["obj"];
export type Obj1Schema = Interface.components["schemas"]["obj1"];
export type SetSchema = Interface.components["schemas"]["set"];
export type StringSchema = Interface.components["schemas"]["string"];

// Union Type
export type Format = Interface.components["schemas"]["Format"];
export type GaiaLogicVersion =
    Interface.components["schemas"]["GaiaLogicVersion"];
export type GaiaLogicVersionV2 = Interface.GaiaLogicVersion.V2;
export type PdfObject = Interface.components["schemas"]["PdfObject"];
export type Schema = Interface.components["schemas"]["Schema"];
export type TextContent = Interface.components["schemas"]["TextContent"];

// Enum

export type AfterFieldType = typeof Interface.AfterFieldType;
export type FormUIInputType = typeof Interface.FormUIInputType;
export type ParagraphStyle = typeof Interface.ParagraphStyle;
export type ScopeInvestorType = typeof Interface.ScopeInvestorType;
export type SignatureBlockType = typeof Interface.SignatureBlockType;
export type SignatureType = typeof Interface.SignatureType;
export type TextAlignment = typeof Interface.TextAlignment;
export type TickStyle = typeof Interface.TickStyle;
export type WidgetType = typeof Interface.WidgetType;
export type ArrayGetName = typeof Interface.ArrayGetName;
export type BooleanGetName = typeof Interface.BooleanGetName;
export type EnumGetName = typeof Interface.EnumGetName;
export type IntegerGetName = typeof Interface.IntegerGetName;
export type NullGetName = typeof Interface.NullGetName;
export type NumberGetName = typeof Interface.NumberGetName;
export type ObjGetName = typeof Interface.ObjGetName;
export type SetGetName = typeof Interface.SetGetName;
export type StringGetName = typeof Interface.StringGetName;
export type ApiPaths = typeof Interface.ApiPaths;

export const SchemaTypes = {
    Array: Interface.ArrayGetName.array,
    Boolean: Interface.BooleanGetName.boolean,
    Enum: Interface.EnumGetName.enum,
    Integer: Interface.IntegerGetName.integer,
    Null: Interface.NullGetName.null,
    Number: Interface.NumberGetName.number,
    Obj: Interface.ObjGetName.obj,
    Set: Interface.SetGetName.set,
    String: Interface.StringGetName.string,
} as const;
