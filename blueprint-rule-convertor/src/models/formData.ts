import $ from "lodash";
import * as PantheonModel from "@/models/stargazer/";
import { FormElementIdUISchema } from "@/models";
import { getAllFieldNames, getUniqueName } from "@/utils/formData/formSchema";
import {
    AnnotationMappingsCategoryData,
    BlueprintMetaMappingsCategoryData,
    getTemplateApplicationToFormAnalysis,
} from "../generator/formApplication/mappingAnalysis";
import { getMappingRelationship } from "../generator/formApplication/cardinalityAnalysis";
import { MappingRelationship } from "@/models/formMapping";
import { FormRule } from "@anduintransaction/pangea/models/stargazer";
import { TemplateManager } from "@/models/templateManager";
import { TemplateFormApplicationAnalysis } from "@/models/templateFormApplicationAnalysis";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import { getTitleDict } from "@/utils/formData/schema";
import { FormAnalysisImpl } from "./formAnalysis";

export class FormDataImpl {
    private readonly _formData: PantheonModel.FormData;

    private readonly _uiSchema: FormElementIdUISchema;
    private readonly _schema: PantheonModel.ObjSchema;
    private readonly _formRules: PantheonModel.FormRule[];

    private readonly _annotationMappings: AnnotationMappingsCategoryData;
    private readonly _blueprintMappings: BlueprintMetaMappingsCategoryData;

    // caching
    private _allFieldNames: string[] | undefined;
    private _mappingRelationship: MappingRelationship | undefined;
    private _formAnalysis: FormAnalysisImpl | undefined;

    constructor(
        formData: PantheonModel.FormData,
        pdfDocuments: PDFDocument[],
        blueprintMetaData: PantheonModel.BlueprintMetaData,
    ) {
        this._formData = formData;

        this._uiSchema = formData.form.namespaceFormSchemaMap.main
            .uiSchema as FormElementIdUISchema;

        this._schema = formData.form.namespaceFormSchemaMap.main.schema;

        this._formRules = formData.form.rules || [];

        this._annotationMappings = new AnnotationMappingsCategoryData(
            pdfDocuments,
        );
        this._blueprintMappings = new BlueprintMetaMappingsCategoryData(
            blueprintMetaData,
        );
    }

    getAllFormRules(): PantheonModel.FormRule[] {
        return this._formRules;
    }

    getAllFieldNames(): string[] {
        if (!this._allFieldNames) {
            const names = getAllFieldNames(this._schema);

            this._allFieldNames = names;

            return names;
        }

        return this._allFieldNames;
    }

    getUniqueName(name: string): string {
        return getUniqueName(name, this.getAllFieldNames());
    }

    getMappingRelationship(): MappingRelationship {
        if (!this._mappingRelationship) {
            const mappingRelationship = getMappingRelationship(
                this._uiSchema,
                getTitleDict(this._schema),
            );

            this._mappingRelationship = mappingRelationship;

            return mappingRelationship;
        }

        return this._mappingRelationship;
    }

    getAnalysis(): FormAnalysisImpl {
        if (!this._formAnalysis) {
            const mappingRelationship = this.getMappingRelationship();
            const formAnalysis = new FormAnalysisImpl({
                mappingRelationship,
                allFieldNames: this.getAllFieldNames(),
                allMappings: {
                    annotations: this._annotationMappings.getAllMappingKeys(),
                    blueprintElements:
                        this._blueprintMappings.getAllMappingKeys(),
                },
            });

            this._formAnalysis = formAnalysis;

            return formAnalysis;
        }

        return this._formAnalysis;
    }

    getTemplateApplicationStatus(
        templateManager: TemplateManager,
    ): TemplateFormApplicationAnalysis {
        const analysis = getTemplateApplicationToFormAnalysis(
            templateManager,
            this.getAnalysis(),
            this._annotationMappings.getAllMappingKeys(),
            this._blueprintMappings.getAllMappingKeys(),
        );

        return analysis;
    }

    combineData(
        name: string,
        schema: PantheonModel.Schema | undefined,
        uiSchema: FormElementIdUISchema | undefined,
        rules: FormRule[] | undefined,
    ): PantheonModel.FormData {
        return {
            ...this._formData,
            form: {
                ...this._formData.form,
                namespaceFormSchemaMap: {
                    main: {
                        schema: {
                            fields: [
                                ...(this._schema.fields || []),
                                ...(schema
                                    ? [
                                          {
                                              name,
                                              tpe: schema,
                                          },
                                      ]
                                    : []),
                            ],
                            title: "main",
                            getName: PantheonModel.SchemaTypes.Obj,
                        },
                        uiSchema: {
                            ...this._uiSchema,
                            ...(uiSchema ? uiSchema : {}),
                        },
                    },
                },
                rules: [...(this._formData.form.rules || []), ...(rules || [])],
            },
        };
    }
}
