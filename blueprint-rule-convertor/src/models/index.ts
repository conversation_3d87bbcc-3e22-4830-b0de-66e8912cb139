import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";

import * as PantheonModel from "@/models/stargazer/index";
import {
    CombineMappingId,
    FakeCheckboxTargetMappingId,
} from "./matchingId/blueprintMappingKey";
import { BlueprintRuleKeyManager } from "@/models/blueprintRuleKeyManager";
import * as SchemaUtils from "@/utils/formData/schema";

// ---- Form Field ----

export type FormElementIdUIs = { [key: string]: any };

export type FormElementIdUISchema = { [key: string]: FormElementIdUIs };

export class FormElementIdSchema {
    constructor(
        public name: string,
        public schema: PantheonModel.Schema,
        public uiSchema: FormElementIdUISchema,
    ) {}

    rename(newName: string): FormElementIdSchema {
        const oldName = this.name;

        this.name = newName;

        this.uiSchema = Object.fromEntries(
            Object.entries(this.uiSchema).map(([key, value]) => {
                if (key === oldName) {
                    return [newName, value];
                }
                return [key, value];
            }),
        );

        return this;
    }

    appendTitle(title: string): FormElementIdSchema {
        this.schema = SchemaUtils.appendTitle(this.schema)(title);

        return this;
    }
}

export type BlueprintInvestorType = {
    ui: FormElementIdSchema;
    catalaValueMap: Map<string, string>;
};

export type BlueprintMetaFieldGroups = {
    investorTypeFields: Map<string, BlueprintInvestorType>;
    stringConstantFields: Map<string, string>;
    paragraphFields: Map<string, FormElementIdSchema>;
    gatingQuestionFields: Map<string, FormElementIdSchema>;
    fileGroupFields: Map<string, FormElementIdSchema>;
    signatureFields: Map<string, FormElementIdSchema>;
};

// ---- CONTEXT ----

export interface LogicContext {
    processBlueprintInfo: BlueprintMetaFieldGroups;
    processPdfDocuments: PDFDocument[];
    baseConditionNames: Map<string, string>;
}

type FieldNameMap = Map<string, string>;
export class FormContext {
    public renderedMappingNames: string[] = [];

    constructor(
        public generatedKeyToFieldNameMaps: {
            combinedMappings: FieldNameMap;
            fakeCheckboxTargetMappings: Map<
                FakeCheckboxTargetMappingId,
                string
            >;
            blueprintMappings: FieldNameMap;
        },
        public uploadedPdfMap: PantheonModel.MapFileIdExtractedPdf = {},
        public documentIdToNameMap: Map<string, string> = new Map(),
        public allExistingNames: string[] = [],
        public generatedKeyToCombinedMappingId: Map<
            string,
            CombineMappingId
        > = new Map(),
    ) {}

    getAllGeneratedMappings(): FieldNameMap {
        return new Map([
            ...this.generatedKeyToFieldNameMaps.combinedMappings,
            ...this.generatedKeyToFieldNameMaps.blueprintMappings,
        ]);
    }

    addExistingNames(names: string[]): this {
        this.allExistingNames = [...this.allExistingNames, ...names];
        return this;
    }
    addRenderedMappingNames(names: string[]): this {
        this.renderedMappingNames = [...this.renderedMappingNames, ...names];
        return this;
    }

    static DefaultValue = new FormContext({
        combinedMappings: new Map(),
        fakeCheckboxTargetMappings: new Map(),
        blueprintMappings: new Map(),
    });
}

export class FormRuleExpression {
    constructor(
        public statement: string,
        public inputs: string[] = [],
        public optionKeys: string[] = [],
        public expressionToConditionNameMap: Map<string, string> = new Map(),
    ) {}
}

export type FormRuleTemplate = {
    rule: PantheonModel.FormRule;
    blueprintRuleKeys: BlueprintRuleKeyManager;
};
