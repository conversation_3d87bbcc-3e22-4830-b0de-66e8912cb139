import $ from "lodash";

import { BlueprintMappingKey } from "@/models/matchingId/blueprintMappingKey";
import { MappingRelationship } from "@/models/formMapping";
import { MappingCategoryData } from "../generator/formApplication/benchmark";
import { FieldName, ParentFieldName } from "./matchingId/formElementId";
import {
    BlueprintMappingKeyCategories,
    getMappingTypeCategories,
} from "@/generator/formApplication/mappingAnalysis";

export type MappingCardinalityCategories = {
    oneOneMappings: string[];
    oneManyMappings: string[];
    manyOneMappings: string[];
    manyManyMappings: string[];
};

export type FormAnalysis = {
    mappingRelationship: MappingRelationship;
    allFieldNames: string[];
    allMappings: {
        annotations: string[];
        blueprintElements: string[];
    };
};

export class FormAnalysisImpl {
    constructor(public formAnalysis: FormAnalysis) {}

    getOneMappingItems(): [BlueprintMappingKey, FieldName][] {
        return this.formAnalysis.mappingRelationship.pdfMappingCategories
            .oneMappingItems;
    }
    getManyMappingItems(): [BlueprintMappingKey, FieldName[]][] {
        return this.formAnalysis.mappingRelationship.pdfMappingCategories
            .manyMappingItems;
    }

    getFieldNamesByMapping(mapping: BlueprintMappingKey): FieldName[] {
        return (
            this.formAnalysis.mappingRelationship.mappingToFieldNamesMap.get(
                mapping,
            ) || []
        );
    }

    getAllFieldMappings(): BlueprintMappingKey[] {
        return [...this.getMappingToFieldNamesMap().keys()];
    }

    getMappingToFieldNamesMap(): Map<BlueprintMappingKey, FieldName[]> {
        return this.formAnalysis.mappingRelationship.mappingToFieldNamesMap;
    }

    getMappingToParentFieldNamesMap(): Map<
        BlueprintMappingKey,
        ParentFieldName[]
    > {
        return new Map(
            Array.from(this.getMappingToFieldNamesMap())
                .map(
                    ([mappingKey, fieldElements]): [
                        BlueprintMappingKey,
                        ParentFieldName[],
                    ] => [
                        mappingKey,
                        fieldElements.filter(
                            (e) => e instanceof ParentFieldName,
                        ),
                    ],
                )
                .filter(([_, fieldElements]) => fieldElements.length > 0),
        );
    }

    getCategorizedExistingItems(): BlueprintMappingKeyCategories {
        return getMappingTypeCategories(this.getAllFieldMappings());
    }

    getAllExistingMappings(): BlueprintMappingKey[] {
        return Object.values(this.getCategorizedExistingItems()).flat();
    }

    getMissingAnnotationMappings(): string[] {
        const allAnnotationMappings = this.formAnalysis.allMappings.annotations;

        return $.difference(
            allAnnotationMappings,
            this.getAllFieldMappings().map((key) => key.id),
        );
    }

    getMissingBlueprintMappings(): string[] {
        const allBlueprintMappings =
            this.formAnalysis.allMappings.blueprintElements;

        return $.difference(
            allBlueprintMappings,
            this.getAllFieldMappings().map((key) => key.id),
        );
    }

    getAllMissingMappings(): string[] {
        return [
            ...this.getMissingAnnotationMappings(),
            ...this.getMissingBlueprintMappings(),
        ];
    }

    getMappingCardinalityCategories(): MappingCardinalityCategories {
        const { mappingRelationship } = this.formAnalysis;
        const { cardinalityCategories } = mappingRelationship;

        return {
            oneOneMappings: cardinalityCategories.oneOneItems.map(
                ([_, m]) => m.id,
            ),
            oneManyMappings: cardinalityCategories.oneManyItems.flatMap(
                ([_, ms]) => ms.map((m) => m.id),
            ),
            manyOneMappings: cardinalityCategories.manyOneItems.map(
                ([_, m]) => m.id,
            ),
            manyManyMappings: cardinalityCategories.manyManyItems.flatMap(
                ([_, ms]) => ms.map((m) => m.id),
            ),
        };
    }

    getCategoryData(): MappingCategoryData {
        const { mappingRelationship } = this.formAnalysis;
        const { pdfMappingCategories } = mappingRelationship;

        const existingItems = this.getCategorizedExistingItems();
        return {
            mappingRelationship: {
                cardinalityCategories: this.getMappingCardinalityCategories(),
                pdfMappingCategories: {
                    oneMappingItems: [
                        ...pdfMappingCategories.oneMappingItems.map(
                            ([m, _]) => m.id,
                        ),
                    ],
                    manyMappingItems: [
                        ...pdfMappingCategories.manyMappingItems.map(
                            ([m, _]) => m.id,
                        ),
                    ],
                },
            },
            fields: {
                allMappings: {
                    existingItems: {
                        pdfMappings: existingItems.pdfMappings.map((m) => m.id),
                        blueprintMetaDataMappings:
                            existingItems.blueprintMetaDataMappings.map(
                                (m) => m.id,
                            ),
                        combinedMappingIds:
                            existingItems.combinedMappingIds.map((m) => m.id),
                        fakeCheckboxTargetMappingIds:
                            existingItems.fakeCheckboxTargetMappingIds.map(
                                (m) => m.id,
                            ),
                    },
                    missingItems: {
                        annotations: this.getMissingAnnotationMappings(),
                        blueprintElements: this.getMissingBlueprintMappings(),
                    },
                },
            },
        };
    }
}
