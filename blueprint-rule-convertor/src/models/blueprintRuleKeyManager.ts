import $ from "lodash";

import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import { MixcalcLogicAPI } from "@/models/mixcalcAPI";
import { FakeCheckboxTargetMappingId } from "@/models/matchingId/blueprintMappingKey";
import { sortStrings } from "@/utils/application/matchingIdConverter";

export class BlueprintRuleLineKey {
    constructor(
        public inputKeys: string[],
        public optionKeys: string[],
        public targetKeys: string[],
    ) {}

    private _allKeys?: string[];
    private _inputExceptTargetKeys?: string[];

    getAllKeys(): string[] {
        if (!this._allKeys) {
            this._allKeys = $.uniq([
                ...this.inputKeys,
                ...this.optionKeys,
                ...this.targetKeys,
            ]);
        }
        return this._allKeys;
    }

    getInputExceptTargetKeys(): string[] {
        if (!this._inputExceptTargetKeys) {
            this._inputExceptTargetKeys = $.difference(
                this.inputKeys,
                this.targetKeys,
            );
        }
        return this._inputExceptTargetKeys;
    }
}

export const getBlueprintRuleLineKey = (
    effectMixcalcLogics: [DirectBlueprintEffect, MixcalcLogicAPI][],
): [number, BlueprintRuleLineKey][] => {
    return effectMixcalcLogics.reduce(
        (acc, [effect, logic]): [number, BlueprintRuleLineKey][] => {
            const lineIndex = acc.findIndex(
                ([line]) => line === effect.parentRule.startLineNumber,
            );

            const targetKeys = $.uniq(
                effect.getTargets().map((target) => target.key),
            );
            const inputKeys = $.uniq([
                ...logic.toFormRule().inputs,
                ...targetKeys,
            ]);
            const optionKeys = $.uniq(logic.toFormRule().optionKeys);

            if (lineIndex == -1) {
                acc.push([
                    effect.parentRule.startLineNumber,
                    new BlueprintRuleLineKey(inputKeys, optionKeys, targetKeys),
                ]);

                return acc;
            }

            acc[lineIndex][1].inputKeys = $.uniq([
                ...acc[lineIndex][1].inputKeys,
                ...inputKeys,
            ]);
            acc[lineIndex][1].optionKeys = $.uniq([
                ...acc[lineIndex][1].optionKeys,
                ...optionKeys,
            ]);
            acc[lineIndex][1].targetKeys = $.uniq([
                ...acc[lineIndex][1].targetKeys,
                ...targetKeys,
            ]);
            return acc;
        },
        [] as [number, BlueprintRuleLineKey][],
    );
};

export class BlueprintRuleKeyManager {
    constructor(
        public lineToKeysMap: [number, BlueprintRuleLineKey][],
        public isFakeCheckboxRule: boolean = false,
    ) {}

    private cache: {
        allKeys?: string[];
        allFieldKeys?: string[];
        allInputKeys?: string[];
        allOptionKeys?: string[];
        allTargetKeys?: string[];
        inputExcludeTargetKeys?: string[];
        fakeCheckboxField?: string;
    } = {};

    getAllKeys(): string[] {
        if (!this.cache.allKeys) {
            this.cache.allKeys = $.uniq([
                ...this.getAllInputKeys(),
                ...this.getAllTargetKeys(),
                ...this.getAllOptionKeys(),
            ]);
        }
        return this.cache.allKeys;
    }

    getInputsExludeTargetKeys(): string[] {
        if (!this.cache.inputExcludeTargetKeys) {
            this.cache.inputExcludeTargetKeys = $.uniq(
                this.lineToKeysMap.flatMap(([_, lineKey]): string[] =>
                    lineKey.getInputExceptTargetKeys(),
                ),
            );
        }
        return this.cache.inputExcludeTargetKeys;
    }

    getAllFieldKeys(): string[] {
        if (!this.cache.allFieldKeys) {
            const fakeCheckboxField = this.getFakeCheckboxField();

            this.cache.allFieldKeys = $.uniq([
                ...this.getAllTargetKeys(),
                ...sortStrings(this.getAllInputKeys()),
                ...(fakeCheckboxField ? [fakeCheckboxField] : []),
            ]);
        }
        return this.cache.allFieldKeys;
    }

    getAllInputKeys(): string[] {
        if (!this.cache.allInputKeys) {
            this.cache.allInputKeys = $.uniq(
                this.lineToKeysMap.reduce(
                    (acc, [_, { inputKeys }]): string[] => [
                        ...acc,
                        ...inputKeys,
                    ],
                    [] as string[],
                ),
            );
        }
        return this.cache.allInputKeys;
    }

    getAllOptionKeys(): string[] {
        if (!this.cache.allOptionKeys) {
            this.cache.allOptionKeys = $.uniq(
                this.lineToKeysMap.reduce(
                    (acc, [_, { optionKeys }]): string[] => [
                        ...acc,
                        ...optionKeys,
                    ],
                    [] as string[],
                ),
            );
        }
        return this.cache.allOptionKeys;
    }

    getAllTargetKeys(): string[] {
        if (!this.cache.allTargetKeys) {
            this.cache.allTargetKeys = $.uniq(
                this.lineToKeysMap.reduce(
                    (acc, [_, { targetKeys }]): string[] => [
                        ...acc,
                        ...targetKeys,
                    ],
                    [] as string[],
                ),
            );
        }
        return this.cache.allTargetKeys;
    }

    getFakeCheckboxField(): string | undefined {
        if (!this.isFakeCheckboxRule) {
            return undefined;
        }

        if (!this.cache.fakeCheckboxField) {
            this.cache.fakeCheckboxField = new FakeCheckboxTargetMappingId(
                this.getAllTargetKeys(),
            ).getRenderedName();
        }
        return this.cache.fakeCheckboxField;
    }
}
