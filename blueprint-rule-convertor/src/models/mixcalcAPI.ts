import { FormRuleExpression } from "@/models";
import { mapToJson } from "@/utils/common";

export abstract class MixcalcLogicAPI {
    abstract key: string;

    constructor(public conditionExpression: FormRuleExpression) {}

    abstract toFormRule(): FormRuleExpression;

    conditionVarName?: string;

    static getDisabledParam(disabled: boolean): FormRuleExpression {
        if (!disabled) {
            return new FormRuleExpression(`disabled = false`);
        }

        return new FormRuleExpression(`disabled = true`);
    }
}

export const MixcalcLogicApiKeys = {
    Unit: "Unit",
    Show: "show",
    Hide: "hide",
    Deactivate: "deactivate",
    Activate: "activate",
    SetError: "setError",
    SetWarning: "setWarning",
    Fill: "fill",
    FillAllOptions: "fillAllOptions",
    IncludeOptions: "includeOptions",
    DeactivateOptions: "deactivateOptions",
    ActivateOptions: "activateOptions",
    AddDeactivatedOptions: "addDeactivatedOptions",
    AddActivatedOptions: "addActivatedOptions",
    ExcludeOptions: "excludeOptions",
    ExcludeOptionGroups: "excludeOptionGroups",
    ExcludeKeyOptionGroups: "excludeKeyOptionGroups",
    ExcludeOtherOptions: "excludeOtherOptions",
    ExcludeEachOptions: "excludeEachOptions",
    EnableFiles: "enableFiles",
    SetEnabledFiles: "setEnabledFiles",
    EnableAllFiles: "enableAllFiles",
    SetSigningTypes: "setSigningTypes",
    AutoCorrect: "autoCorrect",
    UnsafeResetValue: "unsafeResetValue",
    UnsafeDeselectOptions: "unsafeDeselectOptions",
};

const getConditionParam = (expression: string) => `condition = ${expression}`;

const DUMMY_CONDITION = new FormRuleExpression("dummy");

export class UnitAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.Unit;

    constructor() {
        super(DUMMY_CONDITION);
    }

    toFormRule(): FormRuleExpression {
        return new FormRuleExpression(`unit`);
    }
}

export class ShowAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.Show;

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );

        return new FormRuleExpression(
            `${this.key}(${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class HideAPI extends ShowAPI {
    key = MixcalcLogicApiKeys.Hide;
}

export class DeactivateAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.Deactivate;

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );

        return new FormRuleExpression(
            `${this.key}(${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class ActivateAPI extends DeactivateAPI {
    key = MixcalcLogicApiKeys.Activate;
}

export class SetErrorAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.SetError;

    constructor(
        public conditionExpression: FormRuleExpression,
        public message: FormRuleExpression,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );

        return new FormRuleExpression(
            `${this.key}(${this.message.statement}, ${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class SetWarningAPI extends SetErrorAPI {
    key = MixcalcLogicApiKeys.SetWarning;
}

export class FillAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.Fill;
    constructor(
        public conditionExpression: FormRuleExpression,
        public value: FormRuleExpression,
        public disabled: boolean = false,
        public orElse: "clear" | "keep" = "clear",
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;
        const valueParam = this.value.statement;

        return new FormRuleExpression(
            `${this.key}(${valueParam}, ${conditionParam}, ${disabledParam})`,
            [...this.value.inputs, ...this.conditionExpression.inputs],
            [...this.value.optionKeys, ...this.conditionExpression.optionKeys],
        );
    }
}

export class FillAllOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.FillAllOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public disabled: boolean = false,
        public orElse: "clear" | "keep" = "clear",
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;

        return new FormRuleExpression(
            `${this.key}(${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class DeactivateOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.DeactivateOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public options: string[],
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const optionsParam = JSON.stringify(this.options, null, 4);

        return new FormRuleExpression(
            `${this.key}(${optionsParam}, ${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class ActivateOptionsAPI extends DeactivateOptionsAPI {
    key = MixcalcLogicApiKeys.ActivateOptions;
}

export class AddDeactivatedOptionsAPI extends DeactivateOptionsAPI {
    key = MixcalcLogicApiKeys.AddDeactivatedOptions;
}

export class AddActivatedOptionsAPI extends DeactivateOptionsAPI {
    key = MixcalcLogicApiKeys.AddActivatedOptions;
}

export class ExcludeOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.ExcludeOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public options: string[],
        public disabled: boolean = false,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;
        const optionsParam = JSON.stringify(this.options);

        return new FormRuleExpression(
            `${this.key}(${optionsParam}, ${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class ExcludeOptionGroupsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.ExcludeOptionGroups;
    constructor(
        public conditionExpression: FormRuleExpression,
        public optionGroups: string[][],
        public disabled: boolean = false,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;
        const optionGroupsParam = this.optionGroups
            .map((group) => JSON.stringify(group, null, 4))
            .join(",\n");

        return new FormRuleExpression(
            `${this.key}(${optionGroupsParam}, ${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}
export class ExcludeKeyOptionGroupsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.ExcludeKeyOptionGroups;
    constructor(
        public conditionExpression: FormRuleExpression,
        public optionGroups: Map<string, string[]>[],
        public disabled: boolean = false,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;
        const optionGroupsParam = this.optionGroups
            .map((group) => {
                return mapToJson(group);
            })
            .join(", ");

        return new FormRuleExpression(
            `${MixcalcLogicApiKeys.ExcludeOptionGroups}(${optionGroupsParam}, ${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class ExcludeOtherOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.ExcludeOtherOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public options: string[],
        public disabled: boolean = false,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;
        const optionsParam = JSON.stringify(this.options);

        return new FormRuleExpression(
            `${this.key}(${optionsParam}, ${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class ExcludeEachOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.ExcludeEachOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public disabled: boolean = false,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const disabledParam = MixcalcLogicAPI.getDisabledParam(
            this.disabled,
        ).statement;

        return new FormRuleExpression(
            `${this.key}(${conditionParam}, ${disabledParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class SetEnableFilesAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.SetEnabledFiles;
    constructor(
        public conditionExpression: FormRuleExpression,
        public value: FormRuleExpression,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const filesParam = this.value.statement;

        return new FormRuleExpression(
            `${this.key}(${filesParam}, ${conditionParam})`,
            [...this.value.inputs, ...this.conditionExpression.inputs],
            [...this.value.optionKeys, ...this.conditionExpression.optionKeys],
        );
    }
}
export class EnableFilesAPI extends SetEnableFilesAPI {
    key = MixcalcLogicApiKeys.EnableFiles;
}

export class EnableAllFilesAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.EnableAllFiles;

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );

        return new FormRuleExpression(`${this.key}(${conditionParam})`);
    }
}

export class SetSigningTypesAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.SetSigningTypes;
    constructor(
        public conditionExpression: FormRuleExpression,
        public singingTypes: string[],
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const singingTypesParam = JSON.stringify(this.singingTypes);

        return new FormRuleExpression(
            `${this.key}(${singingTypesParam}, ${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class AutoCorrectAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.AutoCorrect;
    constructor(
        public conditionExpression: FormRuleExpression,
        public getValueFunctionName: string,
        public trimEnabledVarName: string | undefined = undefined,
    ) {
        super(conditionExpression);
    }

    toFormRule(): FormRuleExpression {
        return new FormRuleExpression(
            `${this.key}(utils.${this.getValueFunctionName})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class UnsafeResetValueAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.UnsafeResetValue;

    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );

        return new FormRuleExpression(
            `${this.key}(${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}

export class UnsafeDeselectOptionsAPI extends MixcalcLogicAPI {
    key = MixcalcLogicApiKeys.UnsafeDeselectOptions;
    constructor(
        public conditionExpression: FormRuleExpression,
        public options: string[],
    ) {
        super(conditionExpression);
    }
    toFormRule(): FormRuleExpression {
        const conditionParam = getConditionParam(
            this.conditionExpression.statement,
        );
        const optionsParam = JSON.stringify(this.options);

        return new FormRuleExpression(
            `${this.key}(${optionsParam}, ${conditionParam})`,
            this.conditionExpression.inputs,
            this.conditionExpression.optionKeys,
        );
    }
}
