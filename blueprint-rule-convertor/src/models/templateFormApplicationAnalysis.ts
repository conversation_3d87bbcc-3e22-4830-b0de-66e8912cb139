import $ from "lodash";

import { MappingCategoryData } from "@/generator/formApplication/benchmark";
import {
    BlueprintFieldMappingKey,
    BlueprintOptionMappingKey,
    CombineMappingId,
    FakeCheckboxTargetMappingId,
    FieldBlueprintMetadataMapping,
    FieldPdfMapping,
    BlueprintMappingKey,
    OptionBlueprintMetadataMapping,
    OptionPdfMapping,
} from "@/models/matchingId/blueprintMappingKey";
import { MappingCardinalityCategories } from "./formAnalysis";

export type KeyAnalysis<T extends BlueprintMappingKey> = {
    existingKeys: {
        oneOneKeys: T[];
        oneManyKeys: T[];
    };
    missingKeys: string[];
};
export type TemplateFormApplicationAnalysis = {
    inputs: {
        annotations: KeyAnalysis<FieldPdfMapping>;
        blueprint: KeyAnalysis<FieldBlueprintMetadataMapping>;
        others: string[];
    };
    options: {
        annotations: KeyAnalysis<OptionPdfMapping>;
        blueprint: KeyAnalysis<OptionBlueprintMetadataMapping>;
        others: string[];
    };
    targets: {
        annotations: KeyAnalysis<FieldPdfMapping>;
        blueprint: KeyAnalysis<FieldBlueprintMetadataMapping>;
        others: string[];
    };
    combines: KeyAnalysis<CombineMappingId>;
    fakeCheckboxes: KeyAnalysis<FakeCheckboxTargetMappingId>;
};

export class TemplateFormApplicationAnalysisImpl {
    constructor(
        public templateApplicationToFormAnalysis: TemplateFormApplicationAnalysis,
        public formMappingCardinalityCategories: MappingCardinalityCategories,
    ) {}

    getTargetOneMappingItems(): BlueprintFieldMappingKey[] {
        const { targets } = this.templateApplicationToFormAnalysis;

        return [
            ...targets.annotations.existingKeys.oneOneKeys,
            ...targets.blueprint.existingKeys.oneOneKeys,
        ];
    }
    getInputOneMappingItems(): BlueprintFieldMappingKey[] {
        const { inputs } = this.templateApplicationToFormAnalysis;

        return [
            ...inputs.annotations.existingKeys.oneOneKeys,
            ...inputs.blueprint.existingKeys.oneOneKeys,
        ];
    }
    getOptionOneMappingItems(): BlueprintOptionMappingKey[] {
        const { options } = this.templateApplicationToFormAnalysis;

        return [
            ...options.annotations.existingKeys.oneOneKeys,
            ...options.blueprint.existingKeys.oneOneKeys,
        ];
    }

    getAllOneMappingItems(): BlueprintMappingKey[] {
        return [
            ...this.getTargetOneMappingItems(),
            ...this.getInputOneMappingItems(),
            ...this.getOptionOneMappingItems(),
        ];
    }

    getTargetManyMappingItems(): BlueprintFieldMappingKey[] {
        const { targets } = this.templateApplicationToFormAnalysis;

        return [
            ...targets.annotations.existingKeys.oneManyKeys,
            ...targets.blueprint.existingKeys.oneManyKeys,
        ];
    }
    getInputManyMappingItems(): BlueprintFieldMappingKey[] {
        const { inputs } = this.templateApplicationToFormAnalysis;

        return [
            ...inputs.annotations.existingKeys.oneManyKeys,
            ...inputs.blueprint.existingKeys.oneManyKeys,
        ];
    }
    getOptionManyMappingItems(): BlueprintOptionMappingKey[] {
        const { options } = this.templateApplicationToFormAnalysis;

        return [
            ...options.annotations.existingKeys.oneManyKeys,
            ...options.blueprint.existingKeys.oneManyKeys,
        ];
    }

    getAllManyMappingItems(): BlueprintMappingKey[] {
        return [
            ...this.getTargetManyMappingItems(),
            ...this.getInputManyMappingItems(),
            ...this.getOptionManyMappingItems(),
        ];
    }
    getAllMappingItems(): BlueprintMappingKey[] {
        return [
            ...this.getAllOneMappingItems(),
            ...this.getAllManyMappingItems(),
        ];
    }

    getTemplateMappingCardinalityCategories(): MappingCardinalityCategories {
        const allMappingKeys = this.getAllMappingItems().map((item) => item.id);
        const {
            oneOneMappings,
            oneManyMappings,
            manyOneMappings,
            manyManyMappings,
        } = this.formMappingCardinalityCategories;

        return {
            oneOneMappings: $.intersection(allMappingKeys, oneOneMappings),
            oneManyMappings: $.intersection(allMappingKeys, oneManyMappings),
            manyOneMappings: $.intersection(allMappingKeys, manyOneMappings),
            manyManyMappings: $.intersection(allMappingKeys, manyManyMappings),
        };
    }

    getPdfAnnotationMissingKeys(): string[] {
        const { inputs, options, targets } =
            this.templateApplicationToFormAnalysis;
        return [
            ...inputs.annotations.missingKeys,
            ...options.annotations.missingKeys,
            ...targets.annotations.missingKeys,
        ];
    }
    getBlueprintMetaElementMissingKeys(): string[] {
        const { inputs, options, targets } =
            this.templateApplicationToFormAnalysis;
        return [
            ...inputs.blueprint.missingKeys,
            ...options.blueprint.missingKeys,
            ...targets.blueprint.missingKeys,
        ];
    }
    getOptionMissingKeys(): string[] {
        const { options } = this.templateApplicationToFormAnalysis;
        return [
            ...options.annotations.missingKeys,
            ...options.blueprint.missingKeys,
        ];
    }
    getCombineMappingMissingKeys(): string[] {
        const { combines } = this.templateApplicationToFormAnalysis;
        return combines.missingKeys;
    }
    getCombineMappingExistingKeys(): CombineMappingId[] {
        const { combines } = this.templateApplicationToFormAnalysis;
        return [
            ...combines.existingKeys.oneOneKeys,
            ...combines.existingKeys.oneManyKeys,
        ];
    }
    getAllCombineMappingIds(): string[] {
        const { combines } = this.templateApplicationToFormAnalysis;
        return [
            ...combines.existingKeys.oneOneKeys.map((k) => k.id),
            ...combines.existingKeys.oneManyKeys.map((k) => k.id),
            ...combines.missingKeys,
        ];
    }
    getFakeCheckboxMappingMissingKeys(): string[] {
        const { fakeCheckboxes } = this.templateApplicationToFormAnalysis;
        return fakeCheckboxes.missingKeys;
    }
    getAllFakeCheckboxMappingKeys(): string[] {
        const { fakeCheckboxes } = this.templateApplicationToFormAnalysis;
        return [
            ...fakeCheckboxes.missingKeys,
            ...fakeCheckboxes.existingKeys.oneOneKeys.map((k) =>
                k.getRenderedName(),
            ),
            ...fakeCheckboxes.existingKeys.oneManyKeys.map((k) =>
                k.getRenderedName(),
            ),
        ];
    }

    getAllMissingBlueprintKeys(): string[] {
        return $.uniq([
            ...this.getPdfAnnotationMissingKeys(),
            ...this.getBlueprintMetaElementMissingKeys(),
        ]);
    }

    getOtherMissingKeys(): string[] {
        return $.uniq([
            ...this.getCombineMappingMissingKeys()
                .map((key) => CombineMappingId.fromKeyString(key))
                .filter((v) => v != undefined)
                .map((key) => key.getRenderedName()),
            ...this.getFakeCheckboxMappingMissingKeys()
                .map((key) => FakeCheckboxTargetMappingId.fromKeyString(key))
                .filter((v) => v != undefined)
                .map((key) => key.getRenderedName()),
        ]);
    }

    getAllMissingFieldKeys(): string[] {
        const { inputs, targets, combines, fakeCheckboxes } =
            this.templateApplicationToFormAnalysis;

        return [
            ...inputs.annotations.missingKeys,
            ...targets.annotations.missingKeys,
            ...inputs.blueprint.missingKeys,
            ...targets.blueprint.missingKeys,
            ...combines.missingKeys,
            ...fakeCheckboxes.missingKeys,
        ];
    }
    getAllMissingOptionKeys(): string[] {
        const { options } = this.templateApplicationToFormAnalysis;

        return [
            ...options.annotations.missingKeys,
            ...options.blueprint.missingKeys,
        ];
    }
    getMissingMappingKeys(): string[] {
        return [
            ...this.getPdfAnnotationMissingKeys(),
            ...this.getBlueprintMetaElementMissingKeys(),
        ];
    }

    getAllMultipleMappingKeys(): string[] {
        const cardinalityCategories =
            this.getTemplateMappingCardinalityCategories();
        const reviewedMappingKeys = [
            ...cardinalityCategories.manyManyMappings,
            ...cardinalityCategories.oneManyMappings,
        ];
        return reviewedMappingKeys;
    }

    getAllSingleMappingKeys(): string[] {
        const cardinalityCategories =
            this.getTemplateMappingCardinalityCategories();
        const reviewedMappingKeys = [
            ...cardinalityCategories.oneOneMappings,
            ...cardinalityCategories.manyOneMappings,
        ];
        return reviewedMappingKeys;
    }

    getCategoryData(): MappingCategoryData {
        const { inputs, options, targets } =
            this.templateApplicationToFormAnalysis;

        const { annotations: inputAnnotations, blueprint: inputBlueprint } =
            inputs;
        const { annotations: optionAnnotations, blueprint: optionBlueprint } =
            options;
        const { annotations: targetAnnotations, blueprint: targetBlueprint } =
            targets;
        const { combines, fakeCheckboxes } =
            this.templateApplicationToFormAnalysis;

        return {
            inputs: {
                annotations: {
                    existingKeys: {
                        oneOneKeys:
                            inputAnnotations.existingKeys.oneOneKeys.map(
                                (v) => v.id,
                            ),
                        oneManyKeys:
                            inputAnnotations.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: inputAnnotations.missingKeys,
                },
                blueprint: {
                    existingKeys: {
                        oneOneKeys: inputBlueprint.existingKeys.oneOneKeys.map(
                            (v) => v.id,
                        ),
                        oneManyKeys:
                            inputBlueprint.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: inputBlueprint.missingKeys,
                },
            },
            options: {
                annotations: {
                    existingKeys: {
                        oneOneKeys:
                            optionAnnotations.existingKeys.oneOneKeys.map(
                                (v) => v.id,
                            ),
                        oneManyKeys:
                            optionAnnotations.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: optionAnnotations.missingKeys,
                },
                blueprint: {
                    existingKeys: {
                        oneOneKeys: optionBlueprint.existingKeys.oneOneKeys.map(
                            (v) => v.id,
                        ),
                        oneManyKeys:
                            optionBlueprint.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: optionBlueprint.missingKeys,
                },
            },
            targets: {
                annotations: {
                    existingKeys: {
                        oneOneKeys:
                            targetAnnotations.existingKeys.oneOneKeys.map(
                                (v) => v.id,
                            ),
                        oneManyKeys:
                            targetAnnotations.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: targetAnnotations.missingKeys,
                },
                blueprint: {
                    existingKeys: {
                        oneOneKeys: targetBlueprint.existingKeys.oneOneKeys.map(
                            (v) => v.id,
                        ),
                        oneManyKeys:
                            targetBlueprint.existingKeys.oneManyKeys.map(
                                (v) => v.id,
                            ),
                    },
                    missingKeys: targetBlueprint.missingKeys,
                },
            },
            combines: {
                existingKeys: {
                    oneOneKeys: combines.existingKeys.oneOneKeys.map(
                        (v) => v.id,
                    ),
                    oneManyKeys: combines.existingKeys.oneManyKeys.map(
                        (v) => v.id,
                    ),
                },
                missingKeys: combines.missingKeys,
            },
            fakeCheckboxes: {
                existingKeys: {
                    oneOneKeys: fakeCheckboxes.existingKeys.oneOneKeys.map(
                        (v) => v.id,
                    ),
                    oneManyKeys: fakeCheckboxes.existingKeys.oneManyKeys.map(
                        (v) => v.id,
                    ),
                },
                missingKeys: fakeCheckboxes.missingKeys,
            },
        };
    }
}
