import * as PantheonModel from "@/models/stargazer/";

import {
    CardinalityCategories,
    PdfMappingCategories,
} from "@/generator/formApplication/cardinalityAnalysis";
import { BlueprintMappingKey } from "@/models/matchingId/blueprintMappingKey";
import { RuleId, TemplateMappingId } from "@/models/matchingId/ruleMappingId";
import { FieldName } from "@/models/matchingId/formElementId";
import { BlueprintAnalytic } from "@anduintransaction/pangea/models/intermediateRepresentation";
import { FormRule } from "@/models/stargazer/";
import { MappingCardinalityCategories } from "./formAnalysis";
import { CombineMappingField } from "./combineMappingManager";

export abstract class RuleCompareItem {
    abstract type: string;
    constructor(public ruleId: RuleId) {}

    static fromRuleCompareItem(item: any): RuleCompareItem | undefined {
        try {
            if (item.type == "ExistingFormRule") {
                return new ExistingFormRule(
                    item.ruleId,
                    item.formRuleName,
                    item.blueprintRuleName,
                    item.isModfied,
                );
            } else if (item.type == "AddingFormRule") {
                return new AddingFormRule(item.ruleId, item.blueprintRuleName);
            } else if (item.type == "RemovingFormRule") {
                return new RemovingFormRule(item.ruleId, item.formRuleName);
            } else {
                throw Error("Unknown RuleCompareItem type: " + item.type);
            }
        } catch (e) {
            console.error(e);
            return undefined;
        }
    }
}

export class ExistingFormRule extends RuleCompareItem {
    type = "ExistingFormRule";
    constructor(
        public ruleId: RuleId,
        public formRuleName: string,
        public blueprintRuleName: string,
        public isModfied: boolean,
    ) {
        super(ruleId);
    }
}
export class AddingFormRule extends RuleCompareItem {
    type = "AddingFormRule";
    constructor(
        public ruleId: RuleId,
        public blueprintRuleName: string,
    ) {
        super(ruleId);
    }
}
export class RemovingFormRule extends RuleCompareItem {
    type = "RemovingFormRule";
    constructor(
        public ruleId: RuleId,
        public formRuleName: string,
    ) {
        super(ruleId);
    }
}

export class RuleMatchingAnalysis {
    constructor(public ruleCompareItems: RuleCompareItem[]) {}

    getExistingIds(): RuleId[] {
        return this.ruleCompareItems
            .filter((item) => item instanceof ExistingFormRule)
            .map((item) => (item as ExistingFormRule).ruleId);
    }
    getModifiedExistingIds(): RuleId[] {
        return this.ruleCompareItems
            .filter(
                (item) => item instanceof ExistingFormRule && item.isModfied,
            )
            .map((item) => (item as ExistingFormRule).ruleId);
    }
    getAddingIds(): RuleId[] {
        return this.ruleCompareItems
            .filter((item) => item instanceof AddingFormRule)
            .map((item) => (item as AddingFormRule).ruleId);
    }
    getRemovingIds(): RuleId[] {
        return this.ruleCompareItems
            .filter((item) => item instanceof RemovingFormRule)
            .map((item) => (item as RemovingFormRule).ruleId);
    }
    getAddingRuleNames(): string[] {
        return this.ruleCompareItems
            .filter((item) => item instanceof AddingFormRule)
            .map((item) => (item as AddingFormRule).blueprintRuleName);
    }
    getMatchingRatio(): string {
        return (
            (this.ruleCompareItems.filter(
                (item) => item instanceof ExistingFormRule && !item.isModfied,
            ).length /
                this.ruleCompareItems.length) *
            100
        ).toFixed(2);
    }
}

export type InverseFieldMapping = {
    fieldNameToMappingsMap: Map<FieldName, BlueprintMappingKey[]>;
    mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>;
};

export type BlueprintKeyAnalytic = {
    allKeys: string[];
    missingKeys: string[];
    otherMissingKeys: string[];
    existingKeys: MappingCardinalityCategories;
};

export type BlueprintApplicationResult = {
    formData: PantheonModel.FormData;
    blueprintKeyAnalytic: BlueprintKeyAnalytic;
    templateAnalytic: TemplateAnalytic[];
    ruleCompareItems: RuleCompareItem[];
    formRules: FormRule[];
    blueprintRules: FormRule[];
};

export type TemplateAnalytic = {
    templateId: TemplateMappingId;
    templateContent: FormRule;
    templateLineKeys: [
        number,
        {
            allKeys: string[];
            multipleMappingKeys: string[];
            missingMappingKeys: string[];
        },
    ][];
    combinedMappingInputs?: [string, CombineMappingField][];
    renderedRules: PantheonModel.FormRule[];
    singleMappingKeys: string[];
    multipleMappingKeys: string[];
    missingMappingKeys: string[];
};

export type BlueprintApplicationReport = {
    blueprintName: string;
    blueprintLink: string;
    blueprintVersionName: string;
    blueprintVersionCount: string;

    formName: string;
    formLink: string;
    formVersionName: string;
    formVersionCount: string;

    blueprintKeyAnalytic: BlueprintKeyAnalytic;
    parserAnalytic: BlueprintAnalytic;
    templateAnalytic: TemplateAnalytic[];
    ruleCompareItems?: RuleCompareItem[];
    formRules?: FormRule[];
    blueprintRules?: FormRule[];
    blueprintLines?: BlueprintLine[];
};

export type BlueprintLine = {
    lineNum: number;
    targets: string[];
    content: string;
    isComment: boolean;
};

export type MappingRelationship = {
    cardinalityCategories: CardinalityCategories;
    pdfMappingCategories: PdfMappingCategories;

    mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>;
    fieldNameToMappingsMap: Map<FieldName, BlueprintMappingKey[]>;
};
