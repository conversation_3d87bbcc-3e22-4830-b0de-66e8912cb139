/**
 * Base class for matching identifiers used for PDF Mapping, Rule ID
 * Provides functionality to compare mapping ids based on their string values.
 * The 'equals' method checks if two mapping keys have the same string representation.
 */

export abstract class MatchingId {
    abstract type: string;

    constructor(public id: string) {}

    equals(matchingId: MatchingId) {
        return (
            matchingId.id === this.id && matchingId instanceof this.constructor
        );
    }
}
