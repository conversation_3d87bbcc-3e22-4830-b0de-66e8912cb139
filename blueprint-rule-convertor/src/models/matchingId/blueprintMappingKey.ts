import { MatchingId } from "./index";

import {
    extractId,
    sortStrings,
} from "@/utils/application/matchingIdConverter";
import { OptionFieldName, ParentFieldName } from "./formElementId";

export abstract class BlueprintMapping<PERSON>ey extends MatchingId {}

export abstract class PdfMapping extends BlueprintMappingKey {}

export class FieldPdfMapping extends PdfMapping {
    type = "FieldPdfMapping";
}
export class OptionPdfMapping extends PdfMapping {
    type = "OptionPdfMapping";

    constructor(
        public parentKey: string,
        public childKey: string,
    ) {
        const option = new OptionFieldName(parentKey, childKey);
        super(option.id);
    }
}

export abstract class BlueprintMetadataMapping extends BlueprintMappingKey {}

export class FieldBlueprintMetadataMapping extends BlueprintMetadataMapping {
    type = "FieldBlueprintMetadataMapping";
}
export class OptionBlueprintMetadataMapping extends BlueprintMetadataMapping {
    type = "OptionBlueprintMetadataMapping";

    constructor(
        public parentKey: string,
        public childKey: string,
    ) {
        const option = new OptionFieldName(parent<PERSON><PERSON>, child<PERSON>ey);
        super(option.id);
    }
}

export class CombineMappingId extends BlueprintMappingKey {
    type = "CombineMappingId";

    constructor(
        public mapping: string,
        public elements: ParentFieldName[],
    ) {
        const fieldNames = elements.map((ele) => ele.fieldName);
        const sortedElements = sortStrings(fieldNames);
        const key = `${mapping}__${sortedElements.join("$")}`;

        super(key);
    }

    static fromKeyString(extractedId: string): CombineMappingId | undefined {
        if (!extractedId) return undefined;

        const [mapping, elementStr] = extractedId.split("__");
        if (!mapping || !elementStr) return undefined;

        const elements = elementStr.split("$");
        // Convert string array to ParentFieldName array
        const formElements = elements.map(
            (fieldName) => new ParentFieldName(fieldName),
        );

        return new CombineMappingId(mapping, formElements);
    }

    static fromTagString(str: string): CombineMappingId | undefined {
        const extractedId = extractId(str, "combineMappingId");

        if (!extractedId) return undefined;

        return CombineMappingId.fromKeyString(extractedId);
    }

    getRenderedName(): string {
        return `combine_${this.mapping}`;
    }

    buildTag(): string {
        return `[combineMappingId:${this.id}]`;
    }
}

export class FakeCheckboxTargetMappingId extends BlueprintMappingKey {
    type = "FakeCheckboxTargetMappingId";

    constructor(public targetKeys: string[]) {
        const sortedNames = sortStrings(targetKeys);
        const key = targetKeys.length === 0 ? "empty" : sortedNames.join("$");

        super(key);
    }
    static fromKeyString(
        extractedId: string,
    ): FakeCheckboxTargetMappingId | undefined {
        if (!extractedId) return undefined;
        if (extractedId === "empty") return undefined;

        const elements = extractedId.split("$");

        return new FakeCheckboxTargetMappingId(elements);
    }

    getRenderedName(): string {
        const lastIndex = this.targetKeys.length - 1;
        const lastTargetKey = this.targetKeys[lastIndex];

        if (lastTargetKey === "empty") return "fakeCheckbox_empty";

        return `fakeCheckbox_${lastTargetKey}`;
    }

    static fromTagString(str: string): FakeCheckboxTargetMappingId | undefined {
        const extractedId = extractId(str, "fakeCheckboxTarget");

        if (!extractedId) return undefined;

        return FakeCheckboxTargetMappingId.fromKeyString(extractedId);
    }
    buildTag(): string {
        return `[fakeCheckboxTarget:${this.id}]`;
    }
}

export type BlueprintFieldMappingKey =
    | FieldPdfMapping
    | FieldBlueprintMetadataMapping;
export type BlueprintOptionMappingKey =
    | OptionPdfMapping
    | OptionBlueprintMetadataMapping;

export type BlueprintAnnotationMappingKey = FieldPdfMapping | OptionPdfMapping;
export type BlueprintMetaElementMappingKey =
    | FieldBlueprintMetadataMapping
    | OptionBlueprintMetadataMapping;
