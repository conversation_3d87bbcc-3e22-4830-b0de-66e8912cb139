import { MatchingId } from "./index";

import {
    extractId,
    sortStrings,
} from "../../utils/application/matchingIdConverter";
import { CombineMappingId } from "./blueprintMappingKey";

export abstract class RuleMappingId extends MatchingId {}

export class TemplateMappingId extends RuleMappingId {
    type = "TemplateMappingId";

    constructor(public templateKeys: string[]) {
        const sortedElements = sortStrings(templateKeys);
        const key =
            templateKeys.length === 0 ? "empty" : sortedElements.join("$");

        super(key);
    }

    static fromKeyString(extractedId: string): TemplateMappingId | undefined {
        if (!extractedId) return undefined;
        if (extractedId == "empty") return undefined;

        const elements = extractedId.split("$");

        return new TemplateMappingId(elements);
    }
    static fromTagString(str: string): TemplateMappingId | undefined {
        const extractedId = extractId(str, "blueprintTemplateId");

        if (!extractedId) return undefined;

        return TemplateMappingId.fromKeyString(extractedId);
    }

    buildTag(): string {
        return `[blueprintTemplateId:${this.id}]`;
    }
}

export class RuleTargetId extends RuleMappingId {
    type = "RuleTargetId";

    constructor(public targetNames: string[]) {
        const sortedElements = sortStrings(targetNames);
        const key =
            targetNames.length === 0 ? "empty" : sortedElements.join("$");

        super(key);
    }

    static fromKeyString(extractedId: string): RuleTargetId | undefined {
        if (!extractedId) return undefined;
        if (extractedId == "empty") return undefined;

        const elements = extractedId.split("$");

        return new RuleTargetId(elements);
    }
    static fromTagString(str: string): RuleTargetId | undefined {
        const extractedId = extractId(str, "blueprintTarget");

        if (!extractedId) return undefined;

        return RuleTargetId.fromKeyString(extractedId);
    }

    buildTag(): string {
        return `[blueprintTarget:${this.id}]`;
    }
}

export abstract class RuleId extends MatchingId {
    static fromTagString(str: string): RuleId | undefined {
        const blueprintRuleId = BlueprintRuleId.fromTagString(str);

        if (blueprintRuleId) return blueprintRuleId;

        const combineRuleId = CombineRuleId.fromTagString(str);

        if (combineRuleId) return combineRuleId;

        return undefined;
    }
    abstract buildTag(): string;
}

export class BlueprintRuleId extends RuleId {
    type = "BlueprintRuleId";

    constructor(
        public templateMappingId: TemplateMappingId,
        public ruleTargetId: RuleTargetId,
    ) {
        const key = `${templateMappingId.id}__${ruleTargetId.id}`;
        super(key);
    }

    static fromKeyString(extractedId: string): BlueprintRuleId | undefined {
        if (!extractedId) return undefined;

        const [targetId, templateId] = extractedId.split("__");

        const templateMappingId = TemplateMappingId.fromKeyString(templateId);
        const ruleTargetId = RuleTargetId.fromKeyString(targetId);

        if (!templateMappingId || !ruleTargetId) return undefined;

        return new BlueprintRuleId(templateMappingId, ruleTargetId);
    }
    static fromTagString(str: string): BlueprintRuleId | undefined {
        const templateMappingId = TemplateMappingId.fromTagString(str);
        const ruleTargetId = RuleTargetId.fromTagString(str);

        if (!templateMappingId || !ruleTargetId) return undefined;

        return new BlueprintRuleId(templateMappingId, ruleTargetId);
    }

    buildTag(): string {
        return `${this.templateMappingId.buildTag()}${this.ruleTargetId.buildTag()}`;
    }
}

export class CombineRuleId extends RuleId {
    type = "CombineRuleId";

    constructor(public combineMappingId: CombineMappingId) {
        super(combineMappingId.id);
    }

    static fromTagString(str: string): CombineRuleId | undefined {
        const combineMappingId = CombineMappingId.fromTagString(str);

        if (!combineMappingId) return undefined;

        return new CombineRuleId(combineMappingId);
    }

    buildTag(): string {
        return this.combineMappingId.buildTag();
    }
}
