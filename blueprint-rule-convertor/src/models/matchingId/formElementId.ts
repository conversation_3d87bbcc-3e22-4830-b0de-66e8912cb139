import { MatchingId } from "./index";

export abstract class FieldN<PERSON> extends MatchingId {}

export class ParentFieldName extends FieldName {
    type = "ParentFieldName";

    constructor(public fieldName: string) {
        super(fieldName);
    }
}

export class OptionFieldName extends FieldName {
    type = "OptionFieldName";

    constructor(
        public parentName: string,
        public optionName: string,
    ) {
        super(`${parentName}/${optionName}`);
    }

    static fromString(str: string): OptionFieldName | undefined {
        const firstSlashIndex = str.indexOf("/");
        const fieldName = str.slice(0, firstSlashIndex);
        const optionName = str.slice(firstSlashIndex + 1);

        if (!fieldName || !optionName || !firstSlashIndex) {
            return undefined;
        }

        return new OptionFieldName(fieldName, optionName);
    }
}
