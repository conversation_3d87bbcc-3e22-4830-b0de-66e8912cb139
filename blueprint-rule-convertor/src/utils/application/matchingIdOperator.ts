import { MatchingId } from "@/models/matchingId";

export const uniq = <T extends MatchingId>(elements: T[]): T[] =>
    elements.reduce((acc, value): T[] => {
        const isExisted = Array.from(acc).find((item) => {
            return value == item || value.equals(item);
        });
        if (!isExisted) {
            acc.push(value);
        }

        return acc;
    }, [] as T[]);

export const include = <T extends MatchingId>(
    elements: T[],
    element: T,
): boolean => elements.some((item) => item === element || item.equals(element));

export const pull = <T extends MatchingId>(
    elements: T[],
    ...deletedElements: T[]
): T[] => {
    for (const value of deletedElements) {
        let fromIndex = 0;
        while (
            (fromIndex = elements.findIndex(
                (item) => item === value || item.equals(value),
            )) > -1
        ) {
            elements.splice(fromIndex, 1);
        }
    }
    return elements;
};
export const intersection = <T extends MatchingId>(
    listElement1: T[],
    listElement2: T[],
): T[] => {
    return listElement1.filter((element1) =>
        listElement2.some(
            (element2) => element1 === element2 || element1.equals(element2),
        ),
    );
};
export const filterIncludingIdsFromKeys =
    <T extends MatchingId>(
        type: new (...args: any[]) => T,
        allMatchingIds: MatchingId[],
    ) =>
    (keys: string[]): T[] => {
        return allMatchingIds.filter(
            (mapping) => mapping instanceof type && keys.includes(mapping.id),
        ) as T[];
    };

export const filterExcludingIdsFromKeys =
    <T extends MatchingId>(
        type: new (...args: any[]) => T,
        allMatchingIds: T[],
    ) =>
    (keys: string[]): string[] => {
        const existingMappings = filterIncludingIdsFromKeys(
            type,
            allMatchingIds,
        )(keys);
        const existingKeys = existingMappings.map((mapping) => mapping.id);
        return keys.filter((key) => !existingKeys.includes(key));
    };
