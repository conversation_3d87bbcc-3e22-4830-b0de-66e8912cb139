import $ from "lodash";
import { FormElementIdUIs } from "@/models";
import { FormRule } from "@/models/stargazer";

import {
    CombineMappingId,
    FakeCheckboxTargetMappingId,
    FieldBlueprintMetadataMapping,
    FieldPdfMapping,
    BlueprintMappingKey,
    OptionBlueprintMetadataMapping,
    OptionPdfMapping,
} from "@/models/matchingId/blueprintMappingKey";
import {
    FieldName,
    OptionFieldName,
    ParentFieldName,
} from "@/models/matchingId/formElementId";
import { RuleId } from "@/models/matchingId/ruleMappingId";

export const getMappingKeys = (
    fieldName: string,
    value: FormElementIdUIs,
    title: string | undefined,
): [FieldName, BlueprintMappingKey][] => {
    const result: [FieldName, BlueprintMappingKey][] = [];

    const fieldElement = new ParentFieldName(fieldName);

    const fieldPdfMappingKeys = (
        $.get(value, ["ui:pdfMapping"], []) as string[]
    ).filter((mapping) => !!mapping);
    fieldPdfMappingKeys.forEach((key) => {
        result.push([fieldElement, new FieldPdfMapping(key)]);
    });

    const blueprintMappingKey: string | undefined = $.get(
        value,
        ["ui:blueprintMetadataMapping"],
        undefined,
    );
    if (blueprintMappingKey) {
        result.push([
            fieldElement,
            new FieldBlueprintMetadataMapping(blueprintMappingKey),
        ]);
    }

    const options: [string, FormElementIdUIs][] = $.get(
        value,
        ["ui:multipleOption", "options"],
        [],
    );
    const signatureItems: FormElementIdUIs[] = $.get(
        value,
        ["ui:signature"],
        [],
    );

    const fileItems: [string, FormElementIdUIs][] = Object.entries(
        $.get(value, ["ui:supportingFileGroup", "files"], {}),
    );

    const combinedMappingId: CombineMappingId | undefined = title
        ? CombineMappingId.fromTagString(title)
        : undefined;
    const fakeCheckboxTargetId: FakeCheckboxTargetMappingId | undefined = title
        ? FakeCheckboxTargetMappingId.fromTagString(title)
        : undefined;

    if (combinedMappingId) {
        result.push([fieldElement, combinedMappingId]);
    }
    if (fakeCheckboxTargetId) {
        result.push([fieldElement, fakeCheckboxTargetId]);
    }

    // fileitems mappings
    fileItems.forEach(([optionName, optionData]) => {
        const optionFieldElement = new OptionFieldName(fieldName, optionName);

        const optionBlueprintMappingKey: string | undefined = $.get(
            optionData,
            ["blueprintMetadataMapping"],
            undefined,
        );

        if (blueprintMappingKey && optionBlueprintMappingKey) {
            result.push([
                optionFieldElement,
                new OptionBlueprintMetadataMapping(
                    blueprintMappingKey,
                    optionBlueprintMappingKey,
                ),
            ]);
        }
    });

    signatureItems.forEach((item) => {
        if (!item.mapping) return;
        const optionFieldElement = new OptionFieldName(fieldName, item.mapping);

        result.push([optionFieldElement, new FieldPdfMapping(item.mapping)]);
    });
    // option mappings

    options.forEach(([optionName, optionData]) => {
        const optionFieldElement = new OptionFieldName(fieldName, optionName);
        const optionPdfMappingKeys: string[] = $.get(
            optionData,
            ["pdfMappings"],
            [],
        );

        const optionBlueprintMappingKey: string | undefined = $.get(
            optionData,
            ["blueprintMetadataMapping"],
            undefined,
        );

        optionPdfMappingKeys.forEach((childKey) => {
            fieldPdfMappingKeys.forEach((parentKey) => {
                result.push([
                    optionFieldElement,
                    new OptionPdfMapping(parentKey, childKey),
                ]);
            });
        });

        if (blueprintMappingKey && optionBlueprintMappingKey) {
            result.push([
                optionFieldElement,
                new OptionBlueprintMetadataMapping(
                    blueprintMappingKey,
                    optionBlueprintMappingKey,
                ),
            ]);
        }
    });

    return result;
};

export const getRuleSignatureToFormRuleMap = (
    rules: FormRule[],
): Map<string, FormRule> =>
    new Map(
        rules.reduce(
            (acc, rule): [string, FormRule][] => {
                const ruleId = RuleId.fromTagString(rule.description);
                if (!ruleId) {
                    return acc;
                }
                return [...acc, [ruleId.buildTag(), rule]];
            },
            [] as [string, FormRule][],
        ),
    );

export const getMappingReplacementValue = (formField: FieldName): string => {
    if (formField instanceof ParentFieldName) {
        return formField.fieldName;
    }

    if (formField instanceof OptionFieldName) {
        return formField.optionName;
    }
    throw new Error("Input must be an instance of FieldName");
};

export const InvestorFieldNames = [
    "individual_blueprint_investor_type",
    "entity_blueprint_investor_type",
];

export const ConstantFieldNames = ["gr_shortcut", ...InvestorFieldNames];

export const findOptionKey = (
    mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>,
    possibleParentMappingKeys: string[],
    optionMappingKey: string,
): string | undefined => {
    const possibleParentNames = Array.from(mappingToFieldNamesMap.entries())
        .filter(([mappingKey]) =>
            possibleParentMappingKeys.includes(mappingKey.id),
        )
        .flatMap(([_, names]) => names.map((name) => name.id));
    const optionNames =
        Array.from(mappingToFieldNamesMap.entries()).filter(
            ([mappingKey]) => mappingKey.id == optionMappingKey,
        )?.[0]?.[1] || [];

    const relevantOptionFields = optionNames
        .filter((field) => field instanceof OptionFieldName)
        .filter((field) => {
            const parentName = field.parentName;
            return possibleParentNames.includes(parentName);
        });

    if (relevantOptionFields.length > 0) {
        return relevantOptionFields[0].optionName;
    }

    return undefined;
};
