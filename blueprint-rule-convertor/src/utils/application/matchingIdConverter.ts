// Common sorting function for string arrays
export const sortStrings = (strings: string[]) => {
    return strings.sort((a, b) => {
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();
        if (aLower < bLower) return -1;
        if (aLower > bLower) return 1;
        return 0;
    });
};

// Common extraction function for IDs
export const extractId = (str: string, pattern: string): string | undefined => {
    const regex = new RegExp(`\\[${pattern}:(.*?)\\]`, "g");
    const extractedValues = [];
    let match;

    while ((match = regex.exec(str)) !== null) {
        extractedValues.push(match[1]);
    }

    if (extractedValues.length === 0) {
        return undefined;
    }

    if (extractedValues.length > 1) {
        console.warn(`Found 2 ${pattern} IDs from string: `, str);
    }

    return extractedValues.join(",");
};
