import {
    BlueprintApplicationReport,
    RuleCompareItem,
    RuleMatchingAnalysis,
} from "@/models/formMapping";
import { FormRule } from "@/models/stargazer";

export function calculateSuccessRate(
    blueprintApplicationResult: BlueprintApplicationReport,
): [string, number, FormRule[], string, number, number, number, number] {
    const totalTemplates = blueprintApplicationResult.templateAnalytic.length;

    const reviewedRenderedTemplates =
        blueprintApplicationResult.templateAnalytic.flatMap(
            ({ templateContent, multipleMappingKeys, missingMappingKeys }) => {
                if (
                    missingMappingKeys.length === 0 &&
                    multipleMappingKeys.length === 0
                ) {
                    return [];
                }
                return [templateContent];
            },
        );

    const successRuleGeneratorRatio =
        1 - reviewedRenderedTemplates.length / totalTemplates;

    const ruleMatchingAnalysis = new RuleMatchingAnalysis(
        (blueprintApplicationResult.ruleCompareItems || [])
            .map((item) => RuleCompareItem.fromRuleCompareItem(item))
            .filter((v) => v != undefined),
    );
    const modifiedRuleCount =
        ruleMatchingAnalysis.getModifiedExistingIds().length;
    const addingRuleCount = ruleMatchingAnalysis.getAddingIds().length;
    const removingRuleCount = ruleMatchingAnalysis.getRemovingIds().length;
    const consistentRuleRatio = ruleMatchingAnalysis.getMatchingRatio();
    const totalCompareItems = ruleMatchingAnalysis.ruleCompareItems.length;

    const successRate = (successRuleGeneratorRatio * 100).toFixed(2);
    return [
        successRate,
        totalTemplates,
        reviewedRenderedTemplates,
        consistentRuleRatio,
        modifiedRuleCount,
        addingRuleCount,
        removingRuleCount,
        totalCompareItems,
    ];
}
