import { promisify } from "util";
import { exec } from "child_process";
import fs from "fs";

export const execPromise = promisify(exec);

/**
 * Utility functions for API responses
 */

/**
 * Generates an HTML error message with formatted stdout and stderr
 * @param stdout Standard output from command execution
 * @param stderr Standard error from command execution
 * @returns HTML formatted error message
 */
export function generateHtmlErrorMessage(error: unknown): string {
    const { stdout = "", stderr = "" } = error as {
        stdout?: string;
        stderr?: string;
    };
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Blueprint Rule Generator Error</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { background: #f5f5f5; padding: 15px; border-radius: 5px; white-space: pre-wrap; margin-bottom: 20px; }
        .error { background: #fff0f0; padding: 15px; border-radius: 5px; white-space: pre-wrap; color: #d32f2f; }
        h2 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Blueprint Rule Generator Error</h1>
        <h2>Error Details</h2>
        <div class="error">${stderr.replace(/\n/g, "<br>")}</div>
        <h2>Log Output</h2>
        <div class="log">${stdout.replace(/\n/g, "<br>")}</div>
    </div>
</body>
</html>`;
}

/**
 * Generates an HTML error message for general errors
 * @param error Error object or string
 * @returns HTML formatted error message
 */
export function generateGeneralErrorMessage(error: unknown): string {
    const errorMessage = error instanceof Error ? error.message : String(error);

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Blueprint Rule Generator Fail</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .error { background: #fff0f0; padding: 15px; border-radius: 5px; white-space: pre-wrap; color: #d32f2f; }
        h2 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Blueprint Rule Generator Fail:</h1>
        <div class="error">${errorMessage}</div>
    </div>
</body>
</html>`;
}

export const getTemplateFilePath = (fileName: string): string =>
    `src/script/template/${fileName}.json`;
