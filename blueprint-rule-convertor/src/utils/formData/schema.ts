import * as PantheonModel from "@/models/stargazer/";

import { FormElementIdSchema, FormElementIdUIs } from "@/models";
import { toTitleCaseAlphanumeric } from "../common";
import { WidgetType } from "@/models/stargazer/openapi/interface";

export const getErrorFormElementIdSchema = (
    fieldId: string,
    errorMessage: string,
): FormElementIdSchema => {
    console.error(errorMessage);
    const title = `Error schema generate ${fieldId}`;

    const schema = {
        title: title,
        getName: PantheonModel.SchemaTypes.Null,
    } as PantheonModel.NullSchema;

    const uiSchema = {
        "ui:formattedText": title,
        "ui:required": "This text is required",
        "ui:marginBottom": "12px",
        "ui:widget": WidgetType.Paragraph,
    } as FormElementIdUIs;

    return new FormElementIdSchema(toTitleCaseAlphanumeric(title), schema, {
        [toTitleCaseAlphanumeric(title)]: uiSchema,
    });
};

export const getTitle = (schema: PantheonModel.Schema): string | undefined => {
    switch (schema.getName) {
        case "string":
            return (schema as PantheonModel.StringSchema).title;
        case "obj":
            return (schema as PantheonModel.ObjSchema).title;
        case "array":
            return (schema as PantheonModel.ArraySchema).title;
        case "set":
            return (schema as PantheonModel.SetSchema).title;
        case "null":
            return (schema as PantheonModel.NullSchema).title;
        case "boolean":
            return (schema as PantheonModel.BooleanSchema).title;
        case "integer":
            return (schema as PantheonModel.IntegerSchema).title;
        case "number":
            return (schema as PantheonModel.NumberSchema).title;
        case "enum":
            return undefined;
        default:
            console.error(
                `Unknown schema type while accessing "title": ${schema.getName}`,
            );
    }
};
export const appendTitle =
    (schema: PantheonModel.Schema) =>
    (title: string): PantheonModel.Schema => {
        switch (schema.getName) {
            case "string": {
                const fieldSchema = schema as PantheonModel.StringSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "obj": {
                const fieldSchema = schema as PantheonModel.ObjSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "array": {
                const fieldSchema = schema as PantheonModel.ArraySchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "set": {
                const fieldSchema = schema as PantheonModel.SetSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "null": {
                const fieldSchema = schema as PantheonModel.NullSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "boolean": {
                const fieldSchema = schema as PantheonModel.BooleanSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "integer": {
                const fieldSchema = schema as PantheonModel.IntegerSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "number": {
                const fieldSchema = schema as PantheonModel.NumberSchema;
                fieldSchema.title = `${fieldSchema.title} ${title}`;
                break;
            }
            case "enum":
                appendTitle((schema as PantheonModel.EnumSchema).componentType)(
                    title,
                );
                break;
            default:
                break;
        }

        return schema;
    };

export const getTitleDict = (
    schema: PantheonModel.Schema,
): Map<string, string> => {
    let dict: Map<string, string> = new Map();

    const processSchema = (schema: PantheonModel.Schema) => {
        switch (schema.getName) {
            case "obj":
                (schema as PantheonModel.ObjSchema).fields?.forEach((field) => {
                    const title = getTitle(field.tpe);
                    if (title) dict.set(field.name, title);
                    processSchema(field.tpe);
                });
                break;

            case "array":
                processSchema(
                    (schema as PantheonModel.ArraySchema).componentType,
                );
                break;
            case "set":
                processSchema(
                    (schema as PantheonModel.SetSchema).componentType,
                );
                break;
            case "enum":
                processSchema(
                    (schema as PantheonModel.EnumSchema).componentType,
                );
                break;

            default:
                break;
        }
    };

    processSchema(schema);

    return dict;
};
