import $ from "lodash";

import * as PantheonModel from "@/models/stargazer";

export const getAllFieldNames = (schema: PantheonModel.Schema): string[] => {
    switch (schema.getName) {
        case "obj":
            const fields = schema.fields || [];
            const fieldSchemas: PantheonModel.Schema[] = fields.map(
                (f) => f.tpe,
            );

            return [
                ...fields.map((f) => f.name),
                ...fieldSchemas.flatMap(getAllFieldNames),
            ];
        default:
            return [];
    }
};

function splitString(input: string): [string, number | undefined] {
    const numericPartMatch = input.match(/(\d+)$/); // Find trailing numbers
    if (numericPartMatch) {
        const numericPart = parseInt(numericPartMatch[0], 10);
        const nonNumericPart = input.slice(
            0,
            input.length - numericPartMatch[0].length,
        );
        return [nonNumericPart, numericPart];
    } else {
        return [input, undefined];
    }
}

export const getUniqueName = (
    name: string,
    existingNames: string[],
): string => {
    if (!existingNames.includes(name)) {
        return name;
    }

    const [baseName, _] = splitString(name);

    let index = 1;
    let nameWithIndex = `${baseName}${index}`;

    while (existingNames.includes(nameWithIndex)) {
        index++;
        nameWithIndex = `${baseName}${index}`;
    }

    return nameWithIndex;
};

export const getUiSchema = (encodedFormData: object) => {
    const uiSchemaPath = ["form", "namespaceFormSchemaMap", "main", "uiSchema"];

    return $.get(encodedFormData, uiSchemaPath);
};
export const getSchema = (encodedFormData: object) => {
    const schemaPath = ["form", "namespaceFormSchemaMap", "main", "schema"];

    return $.get(encodedFormData, schemaPath);
};
export const getRules = (encodedFormData: object) => {
    const rulePath = ["form", "rules"];

    return $.get(encodedFormData, rulePath);
};
export const getUploadedPdf = (encodedFormData: object) => {
    const uploadedPdfPath = ["uploadedPdf"];

    return $.get(encodedFormData, uploadedPdfPath);
};
