import * as PantheonModel from "@/models/stargazer";
import { getTitle } from "./schema";
import { GaiaLogicVersion } from "@/models/stargazer/openapi/interface";

const getDefault = (schema: PantheonModel.Schema): any | undefined => {
    switch (schema.getName) {
        case "string":
            return (schema as PantheonModel.StringSchema).default;
        case "obj":
            return (schema as PantheonModel.ObjSchema).default;
        case "array":
            return (schema as PantheonModel.ArraySchema).default;
        case "set":
            return (schema as PantheonModel.SetSchema).default;
        case "null":
            return (schema as PantheonModel.NullSchema).default;
        case "boolean":
            return (schema as PantheonModel.BooleanSchema).default;
        case "integer":
            return (schema as PantheonModel.IntegerSchema).default;
        case "number":
            return (schema as PantheonModel.NumberSchema).default;
        default:
            return undefined;
    }
};

const getDescription = (schema: PantheonModel.Schema): string | undefined => {
    switch (schema.getName) {
        case "string":
            return (schema as PantheonModel.StringSchema).description;
        case "obj":
            return (schema as PantheonModel.ObjSchema).description;
        case "array":
            return (schema as PantheonModel.ArraySchema).description;
        case "set":
            return (schema as PantheonModel.SetSchema).description;
        case "null":
            return (schema as PantheonModel.NullSchema).description;
        case "boolean":
            return (schema as PantheonModel.BooleanSchema).description;
        case "integer":
            return (schema as PantheonModel.IntegerSchema).description;
        case "number":
            return (schema as PantheonModel.NumberSchema).description;
        default:
            return undefined;
    }
};

const getSchemaType = (schema: PantheonModel.Schema): string => {
    switch (schema.getName) {
        case "obj":
            return "object";

        default:
            return schema.getName;
    }
};

const fromSchema = (schema: PantheonModel.Schema): [string, any][] => {
    const defaultValue = getDefault(schema);
    const description = getDescription(schema);
    const title = getTitle(schema);

    const result: [string, any][] = [];

    result.push(["type", getSchemaType(schema)]);

    if (defaultValue !== undefined) {
        result.push(["default", defaultValue]);
    }

    if (description !== undefined) {
        result.push(["description", description]);
    }

    if (title !== undefined) {
        result.push(["title", title]);
    }

    return result;
};

const encodeNull = (_: PantheonModel.NullSchema): [string, any][] => {
    return [];
};

const encodeBoolean = (_: PantheonModel.BooleanSchema): [string, any][] => {
    return [];
};

const encodeInteger = (_: PantheonModel.IntegerSchema): [string, any][] => {
    return [];
};

const encodeNumber = (_: PantheonModel.NumberSchema): [string, any][] => {
    return [];
};

const encodeString = (schema: PantheonModel.StringSchema): [string, any][] => {
    return schema.format ? [["format", schema.format.customName]] : [];
};

const encodeSet = (schema: PantheonModel.SetSchema): [string, any][] => {
    return [
        ["items", encodeSchema(schema.componentType)],
        ["uniqueItems", true],
    ];
};

const encodeArray = (schema: PantheonModel.ArraySchema): [string, any][] => {
    return [["items", encodeSchema(schema.componentType)]];
};

const encodeEnum = (schema: PantheonModel.EnumSchema): [string, any][] => {
    return [
        ...fromSchema(schema.componentType),
        ["type", schema.componentType.getName],
        ["enum", schema.values],
    ];
};

const encodeObject = (schema: PantheonModel.ObjSchema): [string, any][] => {
    const propFields = schema.fields?.map((field) => {
        const options = [
            ["name", field.name],
            ...encodeSchemaOptions(field.tpe),
        ];
        return Object.fromEntries(options);
    });

    return propFields ? [["properties", propFields]] : [];
};

const encodeSchemaOptions = (schema: PantheonModel.Schema): [string, any][] => {
    const schemaOptions = fromSchema(schema);

    let specificOptions: [string, any][] = [];
    switch (schema.getName) {
        case "string":
            specificOptions = encodeString(
                schema as PantheonModel.StringSchema,
            );
            break;
        case "obj":
            specificOptions = encodeObject(schema as PantheonModel.ObjSchema);
            break;
        case "array":
            specificOptions = encodeArray(schema as PantheonModel.ArraySchema);
            break;
        case "set":
            specificOptions = encodeSet(schema as PantheonModel.SetSchema);
            break;
        case "enum":
            specificOptions = encodeEnum(schema as PantheonModel.EnumSchema);
            break;
        case "null":
            specificOptions = encodeNull(schema as PantheonModel.NullSchema);
            break;
        case "boolean":
            specificOptions = encodeBoolean(
                schema as PantheonModel.BooleanSchema,
            );
            break;
        case "integer":
            specificOptions = encodeInteger(
                schema as PantheonModel.IntegerSchema,
            );
            break;
        case "number":
            specificOptions = encodeNumber(
                schema as PantheonModel.NumberSchema,
            );
            break;

        default:
            console.error(
                `Unknown schema type while encoding: ${schema.getName}`,
            );
    }

    return [...schemaOptions, ...specificOptions];
};

export const encodeSchema = (
    schema: PantheonModel.Schema,
): Record<string, any> => {
    return Object.fromEntries(encodeSchemaOptions(schema));
};

const encodeWithExtra = <T extends Record<string, any>>(
    obj: T,
    extra: [string, any][],
): Record<string, any> => {
    const entries: [string, any][] = Object.entries(obj);
    return Object.fromEntries([...entries, ...extra]);
};

const encodeFormSchema = (
    formSchema: PantheonModel.FormSchema,
): Record<string, any> => {
    const fieldKeys = Object.keys(formSchema.uiSchema);

    const duplicates = fieldKeys.filter(
        (item, index) => fieldKeys.indexOf(item) !== index,
    );

    if (duplicates.length > 0) {
        console.error("Duplicate form field found:", duplicates);
    }

    return encodeWithExtra(formSchema, [
        ["schema", encodeSchema(formSchema.schema)],
    ]);
};

const encodeFormSchemaMap = (
    formSchemaMap: PantheonModel.MapFormNamespaceFormSchema,
): Record<string, any> => {
    return Object.fromEntries(
        Object.entries(formSchemaMap).map(([key, value]) => {
            return [key, encodeFormSchema(value)];
        }),
    );
};
const encodeGaiaLogicVersion = (
    gaiaLogicVersion: PantheonModel.GaiaLogicVersion,
): number => {
    if (gaiaLogicVersion == GaiaLogicVersion.V1) {
        return 100
    }

    if (gaiaLogicVersion == GaiaLogicVersion.V2) {
        return 200
    }
    console.error(`Unhandled value of gaiaLogicVersion: ${gaiaLogicVersion}`)

    return 200
};

const encodeForm = (form: PantheonModel.Form): Record<string, any> => {
    return encodeWithExtra(form, [
        [
            "namespaceFormSchemaMap",
            encodeFormSchemaMap(form.namespaceFormSchemaMap),
        ],
        [
            "gaiaLogicVersion",
            encodeGaiaLogicVersion(form.gaiaLogicVersion),
        ],
    ]);
};

const encodeUploadedPdf = (
    uploadedPdf: PantheonModel.MapFileIdExtractedPdf,
): [string, any][] => {
    return Object.entries(uploadedPdf);
};
const encodeUploadedDocx = (
    uploadedDocx: PantheonModel.MapFileIdSeqString,
): [string, any][] => {
    return Object.entries(uploadedDocx);
};
const encodeEmbeddedPdf = (
    embeddedPdf: PantheonModel.MapFileId,
): [string, any][] => {
    return Object.entries(embeddedPdf);
};
const encodeAssociatedLinks = (
    associatedLinks: PantheonModel.MapAssociatedLink,
): [string, any][] => {
    return Object.entries(associatedLinks);
};

export const encodeFormData = (
    formData: PantheonModel.FormData,
): Record<string, any> => {
    return encodeWithExtra(formData, [
        ["form", encodeForm(formData.form)],
        ["uploadedPdf", encodeUploadedPdf(formData.uploadedPdf)],
        ["uploadedDocx", encodeUploadedDocx(formData.uploadedDocx)],
        ["embeddedPdf", encodeEmbeddedPdf(formData.embeddedPdf)],
        ["associatedLinks", encodeAssociatedLinks(formData.associatedLinks)],
    ]);
};
