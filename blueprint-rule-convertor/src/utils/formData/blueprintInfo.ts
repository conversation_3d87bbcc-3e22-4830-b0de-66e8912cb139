import * as PantheonModel from "@/models/stargazer/";
import * as OpenApiInterface from "@/models/stargazer/openapi/interface";
import { getAvailableInvestorType } from "@anduintransaction/pangea/components/blueprintDataReader";

import {
    BlueprintInvestorType,
    BlueprintMetaFieldGroups,
    FormElementIdSchema,
    FormElementIdUIs,
} from "@/models";
import * as FormUiGenerator from "@/generator/schema/metaElement";
import {
    BlueprintSignature,
    FileGroup,
    GatingQuestion,
    ParagraphMessage,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/metaElement";

const handleBlueprintInvestorTypes = (
    rawInvestorTypes: any,
): Map<string, BlueprintInvestorType> => {
    const investorTypes = getAvailableInvestorType(rawInvestorTypes);

    const investorTypeGroups = Array.from(investorTypes.keys());

    return new Map(
        investorTypeGroups.map((groupName) => {
            const schema = {
                componentType: {
                    title: `[Blueprint] Investor Types - ${groupName}`,
                    getName: OpenApiInterface.StringGetName.string,
                } as PantheonModel.StringSchema,
                values: investorTypes.get(groupName),
                getName: PantheonModel.SchemaTypes.Enum,
            } as PantheonModel.EnumSchema;

            const optionWidgets = investorTypes
                .get(groupName)
                ?.map((option) => {
                    return [
                        option,
                        {
                            pdfMapping: "",
                            formattedText: `<p>${option}</p>`,
                        },
                    ];
                });
            const widget = {
                "ui:formattedText": groupName,
                "ui:multipleOption": {
                    options: optionWidgets,
                },
                "ui:required": "This field is required",
                "ui:widget": OpenApiInterface.WidgetType.Radio,
            } as FormElementIdUIs;

            const valueMap = new Map<string, string>(
                investorTypes.get(groupName)?.map((option) => {
                    const value = sanitizeString(option);
                    return [value, option];
                }),
            );

            const alias = `${groupName.toLowerCase()}_blueprint_investor_type`;

            return [
                groupName,
                {
                    ui: new FormElementIdSchema(alias, schema, {
                        [alias]: widget,
                    }),
                    catalaValueMap: valueMap,
                } as BlueprintInvestorType,
            ];
        }),
    );
};

const handleBlueprintStringConstants = (
    stringConstants: PantheonModel.StringConstant[],
): Map<string, string> => {
    return new Map(
        stringConstants.map((value) => [value.key, value.stringValue]),
    );
};

function sanitizeString(input: string): string {
    if (!input) {
        return "";
    }

    return input
        .replace(/ /g, "_") // Replace spaces with underscores
        .replace(/[^a-zA-Z0-9_]/g, ""); // Filter out non-alphanumeric characters and underscores
}

const handleBlueprintParagraphs = (
    paragraphs: PantheonModel.ParagraphMessage[],
): Map<string, FormElementIdSchema> => {
    const fields: [string, FormElementIdSchema][] = paragraphs.map(
        (paragraph) => {
            const schema = FormUiGenerator.fromParagraphMessage(
                new ParagraphMessage(paragraph),
            );

            return [paragraph.key, schema];
        },
    );

    return new Map(fields);
};

const handleBlueprintGatingQuestions = (
    gatingQuestions: PantheonModel.GatingQuestion[],
): Map<string, FormElementIdSchema> => {
    const fields: [string, FormElementIdSchema][] = gatingQuestions.map(
        (gatingQuestion) => {
            const schema = FormUiGenerator.fromGatingQuestion(
                new GatingQuestion(gatingQuestion),
            );

            return [gatingQuestion.key, schema];
        },
    );
    return new Map(fields);
};

const handleBlueprintFileGroups = (
    fileGroups: PantheonModel.FileGroup[],
): Map<string, FormElementIdSchema> => {
    const fields: [string, FormElementIdSchema][] = fileGroups.map(
        (fileGroup) => {
            const schema = FormUiGenerator.fromFileGroup(
                new FileGroup(fileGroup),
            );

            return [fileGroup.key, schema];
        },
    );
    return new Map(fields);
};

const handleBlueprintSignatures = (
    signatures: PantheonModel.BlueprintSignature[],
): Map<string, FormElementIdSchema> => {
    const fields: [string, FormElementIdSchema][] = signatures.map(
        (signature) => {
            const schema = FormUiGenerator.fromBlueprintSignature(
                new BlueprintSignature(signature),
            );

            return [signature.key, schema];
        },
    );
    return new Map(fields);
};

// Process Blueprint Metadata to Form Component
export const processBlueprintInfo = (
    getBlueprintVersionCatalaCodeResponse: PantheonModel.GetBlueprintVersionCatalaCodeResponse,
): BlueprintMetaFieldGroups => {
    const blueprintInfo = getBlueprintVersionCatalaCodeResponse.blueprintInfo;

    const {
        investorTypes,
        stringConstants,
        paragraphs,
        gatingQuestions,
        fileGroups,
        signatures,
    } = blueprintInfo.metadata;

    const investorTypeFields = handleBlueprintInvestorTypes(investorTypes);
    const stringConstantFields = handleBlueprintStringConstants(
        stringConstants || [],
    );
    const paragraphFields = handleBlueprintParagraphs(paragraphs || []);
    const gatingQuestionFields = handleBlueprintGatingQuestions(
        gatingQuestions || [],
    );
    const fileGroupFields = handleBlueprintFileGroups(fileGroups || []);
    const signatureFields = handleBlueprintSignatures(signatures || []);

    const processBlueprintInfo = {
        investorTypeFields,
        stringConstantFields,
        paragraphFields,
        gatingQuestionFields,
        fileGroupFields,
        signatureFields,
    };

    return processBlueprintInfo;
};
