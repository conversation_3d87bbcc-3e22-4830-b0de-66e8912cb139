import * as PantheonModel from "@/models/stargazer";
import { GaiaLogicVersion } from "@/models/stargazer/openapi/interface";

export const decodeFormData = (value: any): PantheonModel.FormData => {
    return {
        ...decodeWithoutExtra(value, [
            "form",
            "uploadedPdf",
            "uploadedDocx",
            "embeddedPdf",
            "associatedLinks",
        ]),
        form: decodeForm(value.form),
        uploadedPdf: decodeUploadedPdf(value.uploadedPdf || []),
        uploadedDocx: decodeUploadedDocx(value.uploadedDocx || []),
        embeddedPdf: decodeEmbeddedPdf(value.embeddedPdf || []),
        associatedLinks: decodeAssociatedLinks(value.associatedLinks || []),
    } as PantheonModel.FormData;
};

const decodeWithoutExtra = <T>(
    obj: Record<string, any>,
    extraKeys: string[],
): T => {
    const result: Record<string, any> = {};

    Object.entries(obj).forEach(([key, value]) => {
        if (!extraKeys.includes(key)) {
            result[key] = value;
        }
    });

    return result as T;
};

const decodeAssociatedLinks = (
    associatedLinks: [string, any][],
): PantheonModel.MapAssociatedLink => {
    return Object.fromEntries(
        associatedLinks,
    ) as PantheonModel.MapAssociatedLink;
};

const decodeEmbeddedPdf = (
    embeddedPdf: [string, any][],
): PantheonModel.MapFileId => {
    return Object.fromEntries(embeddedPdf) as PantheonModel.MapFileId;
};

const decodeUploadedDocx = (
    uploadedDocx: [string, any][],
): PantheonModel.MapFileIdSeqString => {
    return Object.fromEntries(uploadedDocx) as PantheonModel.MapFileIdSeqString;
};

const decodeUploadedPdf = (
    uploadedPdf: [string, any][],
): PantheonModel.MapFileIdExtractedPdf => {
    return Object.fromEntries(
        uploadedPdf,
    ) as PantheonModel.MapFileIdExtractedPdf;
};

const decodeForm = (form: Record<string, any>): PantheonModel.Form => {
    return {
        ...decodeWithoutExtra(form, ["namespaceFormSchemaMap", "gaiaLogicVersion"]),
        namespaceFormSchemaMap: decodeFormSchemaMap(
            form.namespaceFormSchemaMap,
        ),
        gaiaLogicVersion: decodeGaiaLogicVersion(
            form.gaiaLogicVersion,
        ),
    } as PantheonModel.Form;
};

const decodeFormSchemaMap = (
    formSchemaMap: Record<string, any>,
): PantheonModel.MapFormNamespaceFormSchema => {
    return Object.fromEntries(
        Object.entries(formSchemaMap).map(([key, value]) => {
            return [key, decodeFormSchema(value)];
        }),
    ) as PantheonModel.MapFormNamespaceFormSchema;
};

const decodeGaiaLogicVersion = (
    gaiaLogicVersion: any,
): PantheonModel.GaiaLogicVersion => {
    if (gaiaLogicVersion == 100) {
        return GaiaLogicVersion.V1
    }
    if (gaiaLogicVersion == 200) {
        return GaiaLogicVersion.V2
    }

    console.error(`Cannot decode value of gaiaLogicVersion: $gaiaLogicVersion`)

    return GaiaLogicVersion.V2
};

const decodeFormSchema = (
    formSchema: Record<string, any>,
): PantheonModel.FormSchema => {
    return {
        ...decodeWithoutExtra(formSchema, ["schema"]),
        schema: decodeSchema(formSchema.schema),
    } as PantheonModel.FormSchema;
};

const decodeSchema = (schema: Record<string, any>): PantheonModel.Schema => {
    const schemaType = schema.type;

    switch (schemaType) {
        case "string":
            if (schema.enum) {
                return decodeEnumSchema(schema);
            }
            return decodeStringSchema(schema);
        case "object":
            return decodeObjectSchema(schema);
        case "array":
            return decodeArraySchema(schema);
        case "set":
            return decodeSetSchema(schema);
        case "enum":
            return decodeEnumSchema(schema);
        case "null":
            return decodeNullSchema(schema);
        case "boolean":
            return decodeBooleanSchema(schema);
        case "integer":
            return decodeIntegerSchema(schema);
        case "number":
            return decodeNumberSchema(schema);
        default:
            console.error(`Unknown schema type while decoding: ${schemaType}`);
            throw new Error(`Unknown schema type: ${schemaType}`);
    }
};

const decodeCommonSchemaProperties = (schema: Record<string, any>) => {
    return {
        default: schema.default,
        description: schema.description,
        title: schema.title,
    };
};

const decodeStringSchema = (
    schema: Record<string, any>,
): PantheonModel.StringSchema => {
    return {
        getName: "string",
        ...decodeCommonSchemaProperties(schema),
        format: schema.format ? { customName: schema.format } : undefined,
    } as PantheonModel.StringSchema;
};

const decodeObjectSchema = (
    schema: Record<string, any>,
): PantheonModel.ObjSchema => {
    return {
        getName: "obj",
        ...decodeCommonSchemaProperties(schema),
        fields: schema.properties
            ? schema.properties.map((prop: any) => ({
                  name: prop.name,
                  tpe: decodeSchema(prop),
              }))
            : undefined,
    } as PantheonModel.ObjSchema;
};

const decodeArraySchema = (
    schema: Record<string, any>,
): PantheonModel.ArraySchema => {
    let componentType: PantheonModel.Schema;

    if (schema.items.properties) {
        componentType = {
            ...decodeCommonSchemaProperties(schema.items),
            fields: schema.items.properties.map(
                (item: Record<string, any>) => ({
                    name: item.name,
                    tpe: decodeSchema(item),
                }),
            ),
            getName: PantheonModel.SchemaTypes.Obj,
        } as PantheonModel.ObjSchema;
    } else {
        componentType = {
            componentType: {
                getName: PantheonModel.SchemaTypes.String,
                ...decodeCommonSchemaProperties(schema.items),
            } as PantheonModel.Schema,
            values: schema.items.enum || [],
            getName: PantheonModel.SchemaTypes.Enum,
        } as PantheonModel.EnumSchema;
    }

    return {
        getName: "array",
        ...decodeCommonSchemaProperties(schema),
        componentType,
    } as PantheonModel.ArraySchema;
};

const decodeSetSchema = (
    schema: Record<string, any>,
): PantheonModel.SetSchema => {
    return {
        getName: "set",
        ...decodeCommonSchemaProperties(schema),
        componentType: {
            componentType: {
                ...decodeCommonSchemaProperties(schema.items),
                getName: PantheonModel.SchemaTypes.String,
            } as PantheonModel.Schema,
            values: schema.items.enum || [],
            getName: PantheonModel.SchemaTypes.Enum,
        } as PantheonModel.EnumSchema,
    } as PantheonModel.SetSchema;
};

const decodeEnumSchema = (
    schema: Record<string, any>,
): PantheonModel.EnumSchema => {
    return {
        getName: "enum",
        componentType: decodeSchema({
            type: schema.type,
            ...decodeCommonSchemaProperties(schema),
        }),
        values: schema.enum,
    } as PantheonModel.EnumSchema;
};

const decodeNullSchema = (
    schema: Record<string, any>,
): PantheonModel.NullSchema => {
    return {
        getName: "null",
        ...decodeCommonSchemaProperties(schema),
    } as PantheonModel.NullSchema;
};

const decodeBooleanSchema = (
    schema: Record<string, any>,
): PantheonModel.BooleanSchema => {
    return {
        getName: "boolean",
        ...decodeCommonSchemaProperties(schema),
    } as PantheonModel.BooleanSchema;
};

const decodeIntegerSchema = (
    schema: Record<string, any>,
): PantheonModel.IntegerSchema => {
    return {
        getName: "integer",
        ...decodeCommonSchemaProperties(schema),
    } as PantheonModel.IntegerSchema;
};

const decodeNumberSchema = (
    schema: Record<string, any>,
): PantheonModel.NumberSchema => {
    return {
        getName: "number",
        ...decodeCommonSchemaProperties(schema),
    } as PantheonModel.NumberSchema;
};
