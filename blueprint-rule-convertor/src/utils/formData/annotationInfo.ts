import $ from "lodash";
import * as PantheonModel from "@/models/stargazer/";

import { BaseDocument } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import { PdfFieldImpl } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";

export const processPdfDocuments = (
    annotationDataResponses: Map<
        string,
        PantheonModel.GetAnnotationDataResponse
    >,
    baseDocuments: BaseDocument[],
): PDFDocument[] => {
    const pdfDocuments = baseDocuments.map((baseDocument) => {
        const response = annotationDataResponses.get(
            baseDocument.documentVersionId,
        );

        if (!response) {
            throw new Error(
                "Cannot find annotation data of fileID: " +
                    baseDocument.documentVersionId,
            );
        }
        return new PDFDocument(
            $.get(baseDocument, ["keyOpt"]),
            baseDocument.documentVersionId,
            response.annotationData,
        );
    });

    return pdfDocuments;
};

export const findPdfFieldByName =
    (pdfDocuments: PDFDocument[]) =>
    (fieldName: string): PdfFieldImpl | undefined =>
        pdfDocuments
            .map((document): PdfFieldImpl | undefined =>
                document.pdfFields.get(fieldName),
            )
            .filter((v) => v != undefined)[0];
