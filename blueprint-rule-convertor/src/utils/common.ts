const COMMON_TOKENS = ["a", "an", "the", "asa"];

export const toTitleCaseAlphanumeric = (str: string): string => {
    // Remove non-alphanumeric characters and convert to lowercase
    const alphanumeric = str.replace(/[^a-zA-Z0-9]/g, " ").toLowerCase();

    // Convert to title case (capitalize first letter of each word)
    return alphanumeric
        .split(" ")
        .filter((word) => word.length > 0 && !COMMON_TOKENS.includes(word))
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join("");
};

export const toSnakeCase = (str: string): string => {
    return str
        .replace(/[^a-zA-Z0-9]+/g, "_") // Replace non-alphanumeric characters with underscores
        .toLowerCase() // Convert to lowercase
        .replace(/(^_|_$)/g, ""); // Remove leading and trailing underscores
};

export function enumToString<T>(enumValue: T): string {
    return String(enumValue);
}

export const intersect = (ops1: string[], ops2: string[]) =>
    ops2.filter((op) => ops1.indexOf(op) > -1);

export function tryParseEnumFromStringValue<T>(
    enm: { [s: string]: T },
    value: string,
): T | undefined {
    return (Object.values(enm) as unknown as string[]).includes(value)
        ? (value as unknown as T)
        : undefined;
}

export function mapToJson(map: Map<string, string[]>): string {
    const obj: { [key: string]: string[] } = {};
    map.forEach((value, key) => {
        obj[key] = value;
    });
    return JSON.stringify(obj, null, 4);
}
