import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import {
    BlueprintElement,
    BlueprintPdfElement,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import {
    BlueprintMetaElement,
    BlueprintSignature,
    FileGroup,
    GatingQuestion,
    ParagraphMessage,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/metaElement";

import {
    PdfEmbeddedFileFieldImpl,
    PdfFieldImpl,
    PdfMultipleCheckboxesFieldImpl,
    PdfParagraphFieldImpl,
    PdfRadioGroupFieldImpl,
    PdfSignatureFieldImpl,
    PdfTextFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";
import {
    PdfGroupFieldImpl,
    PdfPageFieldImpl,
    PdfRepeatableFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfGroup";
import {
    FormUIInputType,
    WidgetType,
} from "@/models/stargazer/openapi/interface";
import { tryParseEnumFromStringValue } from "./common";

export const getInvisibleWidgets = [
    WidgetType.File,
    WidgetType.FileGroup,
    WidgetType.Signature,
];

export const getSingleValueWidgets = [
    WidgetType.TextBox,
    WidgetType.TextArea,
    WidgetType.Email,
    WidgetType.Date,
    WidgetType.Phone,
    WidgetType.CustomFormat,
    WidgetType.Country,
    WidgetType.State,
    WidgetType.Radio,
    WidgetType.Dropdown,
    WidgetType.Integer,
    WidgetType.Float,
    WidgetType.Money,
    WidgetType.Percentage,
    WidgetType.Year,
];

export const getMultipleValueWidgets = [
    WidgetType.FileGroup,
    WidgetType.MultipleCheckbox,
    WidgetType.MultipleSuggest,
];

export const getMultipleSelectionWidgets = [
    WidgetType.MultipleCheckbox,
    WidgetType.MultipleSuggest,
];

export const getSingleSelectionWidgets = [
    WidgetType.Radio,
    WidgetType.Dropdown,
    WidgetType.Country,
    WidgetType.State,
];

export const getOptionWidgets = [
    ...getMultipleSelectionWidgets,
    ...getSingleSelectionWidgets,
];

export const getStringValueWidgets = [
    WidgetType.TextBox,
    WidgetType.TextArea,
    WidgetType.Email,
    WidgetType.Date,
    WidgetType.Phone,
    WidgetType.CustomFormat,
    WidgetType.Country,
    WidgetType.State,
    WidgetType.Radio,
    WidgetType.Dropdown,
];

export const getNumberValueWidgets = [
    WidgetType.Integer,
    WidgetType.Float,
    WidgetType.Money,
    WidgetType.Percentage,
    WidgetType.Year,
];

export const getArrayValueWidgets = [
    WidgetType.FileGroup,
    WidgetType.Repeatable,
    WidgetType.MultipleCheckbox,
    WidgetType.MultipleSuggest,
];

export const BLUEPRINT_INVESTOR_TYPE_WIDGET = WidgetType.Radio;

export const fromBlueprintElement = (
    component: BlueprintElement,
): WidgetType => {
    if (component instanceof BlueprintPdfElement) {
        return fromPdfField(component.pdfField);
    }

    if (component instanceof PDFDocument) {
        return WidgetType.File;
    }

    if (component instanceof BlueprintMetaElement) {
        return fromBlueprintMetaElement(component);
    }

    throw Error(
        `Unsupported BlueprintElement component type: ${component.constructor.name}`,
    );
};

export const fromBlueprintMetaElement = (
    component: BlueprintMetaElement,
): WidgetType => {
    if (component instanceof ParagraphMessage) {
        return WidgetType.Paragraph;
    }

    if (component instanceof GatingQuestion) {
        switch (component.inputType) {
            case FormUIInputType.Dropdown:
                return WidgetType.Dropdown;
            case FormUIInputType.MultipleCheckbox:
                return WidgetType.MultipleCheckbox;
            case FormUIInputType.MultipleSuggest:
                return WidgetType.MultipleSuggest;
            case FormUIInputType.Radio:
                return WidgetType.Radio;
            default:
                console.error("Unknow Input Type of key: " + component.key);
        }
    }

    if (component instanceof FileGroup) {
        return WidgetType.FileGroup;
    }

    if (component instanceof BlueprintSignature) {
        return WidgetType.Signature;
    }

    throw Error(
        `Unsupported BlueprintMetaElement component type: ${component.constructor.name}`,
    );
};

export const fromPdfField = (component: PdfFieldImpl): WidgetType => {
    if (component instanceof PdfEmbeddedFileFieldImpl) {
        return WidgetType.Pdf;
    }

    if (component instanceof PdfGroupFieldImpl) {
        return WidgetType.Group;
    }

    if (component instanceof PdfMultipleCheckboxesFieldImpl) {
        return WidgetType.MultipleCheckbox;
    }

    if (component instanceof PdfPageFieldImpl) {
        return WidgetType.Page;
    }

    if (component instanceof PdfParagraphFieldImpl) {
        return WidgetType.Paragraph;
    }

    if (component instanceof PdfRadioGroupFieldImpl) {
        return WidgetType.Radio;
    }

    if (component instanceof PdfRepeatableFieldImpl) {
        return WidgetType.Repeatable;
    }

    if (component instanceof PdfSignatureFieldImpl) {
        return WidgetType.Signature;
    }

    if (component instanceof PdfTextFieldImpl) {
        return WidgetType.TextBox;
    }

    throw Error(
        `Unsupported PdfFieldImpl component type: ${component.constructor.name}`,
    );
};

export const decodeValue = (value: any): WidgetType => {
    if (typeof value != "string") {
        console.error(`Cannot decode WidgetType for value: ${value}`);
    }

    const parsedValue = tryParseEnumFromStringValue(WidgetType, value);

    if (parsedValue) {
        return parsedValue;
    }

    return WidgetType.Null;
};
