import * as PantheonModel from "@/models/stargazer/";

import $ from "lodash";

import {
    Blueprint,
    StructuredRule,
} from "@anduintransaction/pangea/models/intermediateRepresentation";
import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
/**
 * Extracts all effects from parsed blueprint
 */
export const extractAllEffects = (
    parsedBlueprint: Blueprint,
): DirectBlueprintEffect[] => {
    return parsedBlueprint.scopes.flatMap((scope) =>
        scope.allRules.flatMap((rule) => {
            if (rule instanceof StructuredRule) {
                rule.originalEffects.map((effect) => {
                    effect.parentRule = rule;
                });

                return rule.originalEffects;
            }

            return [];
        }),
    );
};

export const createDocumentIdToNameMap = (
    annotationDocumentIdMap: Map<string, string>,
    uploadedPdfMap: PantheonModel.MapFileIdExtractedPdf,
): Map<string, string> => {
    const documentIdToNameMap = new Map<string, string>();

    for (const [fileId, documentId] of annotationDocumentIdMap.entries()) {
        const fileName = $.get(uploadedPdfMap, [fileId, "name"], null);

        fileName && documentIdToNameMap.set(documentId, fileName);
    }

    return documentIdToNameMap;
};
