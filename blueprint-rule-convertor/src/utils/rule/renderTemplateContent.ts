import * as WidgetTypeUtils from "@/utils/widgetType";

import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import {
    ExternalMutualExclusiveOptions,
    RelativeFormRuleEffect,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/blueprintRuleEffects";
import { FormRuleTemplate, LogicContext } from "@/models";
import { MixcalcLogicAPI } from "@/models/mixcalcAPI";
import { fromDirectBlueprintEffect } from "@/generator/ruleEffects";
import { FormRule } from "@/models/stargazer";
import { WidgetType } from "@/models/stargazer/openapi/interface";
import { TemplateMappingId } from "@/models/matchingId/ruleMappingId";
import { CombineMappingId } from "@/models/matchingId/blueprintMappingKey";
import {
    combineDirrectBlueprintEffects,
    indentTabs,
    KeyRuleContent,
    renderConditionVariables,
    renderDescription,
    renderLogicHeader,
    renderMixcalcLogics,
    renderWidgetTags,
} from "./renderTemplateUtils";
import {
    BlueprintRuleKeyManager,
    getBlueprintRuleLineKey,
} from "@/models/blueprintRuleKeyManager";

export const renderFormRule =
    (context: LogicContext) =>
    (
        targetKey: string,
        widget: WidgetType,
        effects: DirectBlueprintEffect[],
    ): FormRuleTemplate => {
        const effectMixcalcLogics: [
            DirectBlueprintEffect[],
            MixcalcLogicAPI,
        ][] = combineDirrectBlueprintEffects(effects).map(
            ([effects, combineEffect]) => [
                effects,
                fromDirectBlueprintEffect(context)(combineEffect),
            ],
        );
        const blueprintRuleKeys = new BlueprintRuleKeyManager(
            getBlueprintRuleLineKey(
                effects.map((e) => [e, fromDirectBlueprintEffect(context)(e)]),
            ),
        );

        const mixcalcLogics = effectMixcalcLogics.map(([_, logic]) => logic);
        const inputsKey = blueprintRuleKeys.getAllFieldKeys();
        const templateId = new TemplateMappingId([targetKey]);
        const parentRules = effects.map((effect) => effect.parentRule);

        const content = `${renderLogicHeader(inputsKey)}

${renderConditionVariables(context)}

_
    ${indentTabs(renderMixcalcLogics(mixcalcLogics))}
    .apply(logic.{{${targetKey}}}, ctx = {{${targetKey}}})
`;

        return {
            rule: {
                value: content,
                name: `[Blueprint][Single Target] ${renderDescription(parentRules)}`,
                defaultNamespace: "main",
                description: `${templateId.buildTag()}${renderWidgetTags([widget])}`,
                templateId: "",
            } as FormRule,
            blueprintRuleKeys,
        };
    };

/**
 * Gets rule content for a specific key
 */
export const getKeyRuleContent =
    (context: LogicContext) =>
    (
        fakeCheckboxKey: string,
        effects: RelativeFormRuleEffect[],
    ): KeyRuleContent => {
        const effectMixcalcLogics: [
            DirectBlueprintEffect[],
            MixcalcLogicAPI,
        ][] = combineDirrectBlueprintEffects(effects).map(
            ([effects, combineEffect]) => [
                effects,
                fromDirectBlueprintEffect(context)(combineEffect),
            ],
        );
        const blueprintRuleKeys = new BlueprintRuleKeyManager(
            getBlueprintRuleLineKey(
                effects.map((e) => [e, fromDirectBlueprintEffect(context)(e)]),
            ),
        );
        const mixcalcLogics = effectMixcalcLogics.map(([_, logic]) => logic);
        const widgetTypes = effects
            .flatMap((effect) => effect.getTargets())
            .map(WidgetTypeUtils.fromBlueprintElement);

        const parentRules = effects.map((effect) => effect.parentRule);
        const content = `_
    ${indentTabs(renderMixcalcLogics(mixcalcLogics))}
    .applyKey("${fakeCheckboxKey}"),
    `;

        return {
            content,
            blueprintRuleKeys,
            widgetTypes,
            parentRules,
        };
    };

/**
 * Gets rule content for fake checkbox
 */
export const getFakeCheckboxRuleContent =
    (context: LogicContext) =>
    (
        parentEffects: ExternalMutualExclusiveOptions[],
        keyRuleContents: KeyRuleContent[],
    ): FormRuleTemplate => {
        const parentRules = [
            ...parentEffects.map((effect) => effect.parentRule),
            ...keyRuleContents.flatMap((content) => content.parentRules),
        ];
        const widgetTypes = keyRuleContents.flatMap(
            (value) => value.widgetTypes,
        );

        const parentEffectLogics: [DirectBlueprintEffect[], MixcalcLogicAPI][] =
            combineDirrectBlueprintEffects(parentEffects).map(
                ([effects, combineEffect]) => [
                    effects,
                    fromDirectBlueprintEffect(context)(combineEffect),
                ],
            );

        const blueprintRuleKeys = new BlueprintRuleKeyManager(
            [
                ...getBlueprintRuleLineKey(
                    parentEffects.map((e) => [
                        e,
                        fromDirectBlueprintEffect(context)(e),
                    ]),
                ),
                ...keyRuleContents.flatMap(
                    (content) => content.blueprintRuleKeys.lineToKeysMap,
                ),
            ],
            true,
        );
        const parentLogics = parentEffectLogics.map(([_, logic]) => logic);
        const fakeCheckboxField = blueprintRuleKeys.getFakeCheckboxField();

        if (!fakeCheckboxField) {
            console.error(`Cannot get FakeCheckbox field name`);
        }

        // Collect all unique input keys
        const inputsKey = blueprintRuleKeys.getAllFieldKeys();

        // Generate key definitions for checkbox
        const keyDefinitions = blueprintRuleKeys
            .getAllTargetKeys()
            .map(
                (key) =>
                    `\t.addKey("{{${key}}}", {{${key}}}, logic.{{${key}}})`,
            )
            .join("\n");

        // Generate content for child rules
        const childRuleContent = keyRuleContents
            .map((value) => value.content)
            .join("\n");

        const content = `${renderLogicHeader(inputsKey)}

local checkbox = _.fakeCheckbox
${keyDefinitions}
    .init(
        logic.{{${fakeCheckboxField}}}, {{${fakeCheckboxField}}}
    );

${renderConditionVariables(context)}

_
    ${indentTabs(renderMixcalcLogics(parentLogics))}
    .applyFakeCheckbox(checkbox, [
        ${indentTabs(childRuleContent, 2)}
    ])
`;

        const templateId = new TemplateMappingId(
            blueprintRuleKeys.getAllTargetKeys(),
        );

        return {
            rule: {
                value: content,
                name: `[Blueprint][Multiple Targets] ${renderDescription(parentRules)}`,
                defaultNamespace: "main",
                description: `${templateId.buildTag()}${renderWidgetTags(widgetTypes)}`,
                templateId: "",
            } as FormRule,
            blueprintRuleKeys,
        };
    };

export const renderCombineRule = (
    combineMappingId: CombineMappingId,
    combineFieldName: string,
): string | undefined => {
    const combinedFields = combineMappingId.elements.map(
        (ele) => ele.fieldName,
    );

    return `function(
${combinedFields.map((name) => `\t${name},`).join("\n")}
)

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;

local value = error "TODO: Combine following values " + std.toString([
${combinedFields.map((name) => `\t${name}.value,`).join("\n")}
]);

logic.${combineFieldName}.value(value)
`;
};
