import $ from "lodash";

import {
    ComparisonType,
    StringComparisonType,
} from "@anduintransaction/pangea/models/intermediateRepresentation/condition/conditionEnums";
import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import { FormRuleExpression, FormRuleTemplate, LogicContext } from "@/models";
import { MixcalcLogicAPI, UnitAPI } from "@/models/mixcalcAPI";
import { FormRule } from "@/models/stargazer";
import { WidgetType } from "@/models/stargazer/openapi/interface";
import { fromBaseBlueprintComponent } from "@/generator/blueprint";
import { StructuredRule } from "@anduintransaction/pangea/models/intermediateRepresentation";
import { OptionFieldName } from "@/models/matchingId/formElementId";
import { getUniqueName } from "../formData/formSchema";
import { BlueprintRuleKeyManager } from "../../models/blueprintRuleKeyManager";
import { ControlDisplay } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/commonAction";
import { BlueprintControlDisplayFnAction } from "@anduintransaction/pangea/models/handler/blueprintActionEnums";
import { ControlDisplayAction } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/actionEnums";
import { ORCondition } from "@anduintransaction/pangea/models/intermediateRepresentation/condition";
import { RelativeFormRuleEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/blueprintRuleEffects";

export type KeyRuleContent = {
    content: string;
    blueprintRuleKeys: BlueprintRuleKeyManager;
    widgetTypes: WidgetType[];
    parentRules: StructuredRule[];
};

// Mapping objects for enum conversions
export const STRING_COMPARISON_TYPE_MAP: Record<StringComparisonType, string> =
    {
        [StringComparisonType.Equal]: "Equal",
        [StringComparisonType.NotEqual]: "NotEqual",
    };

export const COMPARISON_TYPE_MAP: Record<ComparisonType, string> = {
    [ComparisonType.Equal]: "Equal",
    [ComparisonType.NotEqual]: "NotEqual",
    [ComparisonType.GreaterThan]: "GreaterThan",
    [ComparisonType.GreaterThanOrEqual]: "GreaterThanOrEqual",
    [ComparisonType.LessThan]: "LessThan",
    [ComparisonType.LessThanOrEqual]: "LessThanOrEqual",
};

/**
 * Renders the function header with input keys
 */
export const renderLogicHeader = (inputKeys: string[]): string => {
    return `${renderFunctionStatement(inputKeys)}

local _ = import 'mixcalc';
local utils = import 'utils';
local logic = afs.logic.main;`;
};

/**
 * Renders the function statement with input keys
 */
export const renderFunctionStatement = (inputKeys: string[]): string => {
    if (inputKeys.length === 0) return "function()";

    return [
        `function(`,
        ...inputKeys.map((input) => `\t{{${input}}},`),
        `)`,
    ].join("\n");
};

/**
 * Renders mixcalc logic statements
 */

export const combineDirrectBlueprintEffects = (
    effects: DirectBlueprintEffect[],
): [DirectBlueprintEffect[], DirectBlueprintEffect][] => {
    let combinedEffects = effects.map(
        (e): [DirectBlueprintEffect[], DirectBlueprintEffect] => [[e], e],
    );

    const controlDisplayEffects = effects
        .filter((e) => e instanceof RelativeFormRuleEffect)
        .map((e) => e.formRuleEffect)
        .filter((e) => e instanceof ControlDisplay);
    const hideEffects: ControlDisplay[] = controlDisplayEffects.filter(
        (e) => e.action == ControlDisplayAction.Hide,
    );

    if (hideEffects.length > 1) {
        const combinedCondition = hideEffects
            .map((e) => e.triggerCondition)
            .reduce((acc, condition) => {
                return new ORCondition(acc, condition);
            });
        const combineEffect = new ControlDisplay(
            hideEffects[0].target,
            combinedCondition,
            combinedCondition,
            hideEffects[0].action,
            BlueprintControlDisplayFnAction.Hide,
        );
        combinedEffects = [
            [
                effects.filter(
                    (e) =>
                        e instanceof RelativeFormRuleEffect &&
                        e.formRuleEffect instanceof ControlDisplay &&
                        hideEffects.includes(e.formRuleEffect),
                ),
                new RelativeFormRuleEffect(combineEffect),
            ],
            ...combinedEffects.filter(
                ([_, e]) =>
                    !(e instanceof RelativeFormRuleEffect) ||
                    !(e.formRuleEffect instanceof ControlDisplay) ||
                    !hideEffects.includes(e.formRuleEffect),
            ),
        ];
    }

    const disableEffects: ControlDisplay[] = controlDisplayEffects.filter(
        (e) => e.action == ControlDisplayAction.Disable,
    );
    if (disableEffects.length > 1) {
        const combinedCondition = disableEffects
            .map((e) => e.triggerCondition)
            .reduce((acc, condition) => {
                return new ORCondition(acc, condition);
            });
        const combineEffect = new ControlDisplay(
            disableEffects[0].target,
            combinedCondition,
            combinedCondition,
            disableEffects[0].action,
            BlueprintControlDisplayFnAction.DisableClear,
        );
        combineEffect.parentRule = disableEffects[0].parentRule;
        combinedEffects = [
            [
                effects.filter(
                    (e) =>
                        e instanceof RelativeFormRuleEffect &&
                        e.formRuleEffect instanceof ControlDisplay &&
                        disableEffects.includes(e.formRuleEffect),
                ),
                new RelativeFormRuleEffect(combineEffect),
            ],
            ...combinedEffects.filter(
                ([_, e]) =>
                    !(e instanceof RelativeFormRuleEffect) ||
                    !(e.formRuleEffect instanceof ControlDisplay) ||
                    !disableEffects.includes(e.formRuleEffect),
            ),
        ];
    }

    return combinedEffects;
};

export const renderMixcalcLogics = (
    mixcalcLogics: MixcalcLogicAPI[],
): string => {
    const rules = mixcalcLogics
        .filter((logic) => !(logic instanceof UnitAPI))
        .map((logic) => logic.toFormRule().statement);

    return rules.length === 0
        ? ".unit"
        : rules.map((rule) => `.${rule}`).join("\n");
};

/**
 * Formats tags for display
 */
export const renderTags = (parentRules: StructuredRule[]): string => {
    const tags = $.uniq(parentRules.map((parentRule) => parentRule.ruleName));

    return tags.map((tag) => `[effect:${tag}]`).join("");
};
/**
 * Formats tags for display
 */
export const renderDescription = (parentRules: StructuredRule[]): string =>
    $.uniq(parentRules.map((parentRule) => parentRule.ruleDescription)).join(
        ", ",
    );

/**
 * Formats tags for display
 */
export const renderWidgetTags = (widgetTypes: WidgetType[]): string =>
    $.uniq(widgetTypes)
        .map((widgetType) => `[widgetType:${widgetType}]`)
        .join("");

export const renderConditionVariables = (context: LogicContext): string =>
    Array.from(context.baseConditionNames.entries())
        .map(([expr, name]) => `local ${name} = ${expr};`)
        .join("\n");

/**
 * Optimizes expressions using cached condition names
 */
export const optimizeExpression =
    (context: LogicContext) =>
    (expr: string): string =>
        context.baseConditionNames.get(expr) || expr;

/**
 * Gets string comparison type name
 */
export function getStringComparisonTypeName(
    type: StringComparisonType,
): string {
    const result = STRING_COMPARISON_TYPE_MAP[type];
    if (!result) {
        return getErrorFormRuleExpression("Unknown StringComparisonType type")
            .statement;
    }
    return result;
}

/**
 * Gets comparison type name
 */
export function getComparisonTypeName(type: ComparisonType): string {
    const result = COMPARISON_TYPE_MAP[type];
    if (!result) {
        return getErrorFormRuleExpression("Unknown ComparisonType type")
            .statement;
    }
    return result;
}

/**
 * Extracts and deduplicates condition names from effects
 */
export const getConditionNames =
    (context: LogicContext) =>
    (effects: DirectBlueprintEffect[]): Map<string, string> => {
        return effects.reduce((acc, effect) => {
            const component = fromBaseBlueprintComponent(context)(
                effect.triggerCondition,
            );

            for (const [
                expr,
                name,
            ] of component.expressionToConditionNameMap.entries()) {
                if (!acc.has(expr)) {
                    const existingNames = new Set(acc.values());

                    let uniqueName = getUniqueName(name, [...existingNames]);

                    acc.set(expr, uniqueName);
                }
            }

            return acc;
        }, new Map<string, string>());
    };

/**
 * Indents each line of text with the specified number of tabs
 * @param content The text content to indent
 * @param num The number of tab characters to add (default: 1)
 */
export const indentTabs = (content: string, num: number = 1): string =>
    content.replaceAll("\n", "\n" + "\t".repeat(num));

export const getErrorFormRuleExpression = (
    message: string,
): FormRuleExpression => {
    console.error(message);
    return new FormRuleExpression(`(function() error "${message}")()`);
};

export const renderTemplate = (
    formRuleTemplate: FormRuleTemplate,
    values: Map<string, string> = new Map(),
): FormRule => {
    const text = formRuleTemplate.rule.value;
    const replacements = new Map([
        ...formRuleTemplate.blueprintRuleKeys
            .getAllFieldKeys()
            .map((key): [string, string] => [key, key]),
        ...formRuleTemplate.blueprintRuleKeys
            .getAllOptionKeys()
            .map((optionId): [string, string] | undefined => {
                const optionField = OptionFieldName.fromString(optionId);
                if (!optionField) {
                    console.error(
                        `Cannot find option field for id: ${optionId}`,
                    );
                    return undefined;
                }
                return [optionField.id, optionField.optionName];
            })
            .filter((v) => v != undefined),
        ...values,
    ]);
    const renderedText = text.replace(/{{([^}]+)}}/g, (match, key) => {
        if (!replacements.get(key)) {
            console.error(`Cannot find replacement for key: ${key}`);
            return key;
        }

        return replacements.get(key);
    });
    return { ...formRuleTemplate.rule, value: renderedText };
};
