import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataSource } from "@/generator/blueprint/dataSource";
import {
    DataSource,
    ElementOptions,
    MultipleDataSources,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import { OptionFieldName } from "@/models/matchingId/formElementId";
import {
    BlueprintControlValueFnAction,
    BlueprintSelectionFieldFnAction,
} from "@anduintransaction/pangea/models/handler/blueprintActionEnums";
import { SetValue } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/commonAction";
import { AddSelectionToValue } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/selectionFieldAction";

export const getSingleDataSourceExpression =
    (context: LogicContext) =>
    (dataToFill: DataSource): FormRuleExpression | undefined => {
        const dataSource = fromDataSource(context)(dataToFill);

        let overwritedDataSource = dataSource;

        if (dataToFill instanceof ElementOptions) {
            if (dataToFill.options.length == 1) {
                overwritedDataSource = {
                    ...dataSource,
                    statement: `"{{${new OptionFieldName(dataToFill.element.key, dataToFill.options[0]).id}}}"`,
                };
            } else if (dataToFill.options.length == 0) {
                return undefined;
            } else {
                overwritedDataSource = {
                    ...dataSource,
                    statement: `${dataSource.statement}[0]`,
                };
            }
        }

        if (dataToFill instanceof MultipleDataSources) {
            if (dataToFill.dataSources.length == 1) {
                overwritedDataSource = fromDataSource(context)(
                    dataToFill.dataSources[0],
                );
            } else if (dataToFill.dataSources.length == 0) {
                return undefined;
            } else {
                overwritedDataSource = {
                    ...dataSource,
                    statement: `${dataSource.statement}[0]`,
                };
            }
        }

        return overwritedDataSource;
    };

export const isDisableAndFillRule = (
    effect: SetValue | AddSelectionToValue,
): boolean => {
    return (
        effect.blueprintAction ==
            BlueprintControlValueFnAction.DisableSetValue ||
        effect.blueprintAction ==
            BlueprintSelectionFieldFnAction.DisableOptionsAndAddValue
    );
};
