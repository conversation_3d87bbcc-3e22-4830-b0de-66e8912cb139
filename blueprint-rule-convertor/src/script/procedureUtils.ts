import * as fs from "fs";
import $ from "lodash";

import * as PantheonModel from "@/models/stargazer";
import { BlueprintVersionModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";
import { FormModel } from "@/models/stargazer/proto/anduin/forms/model/form_models";
import { InstantMessage } from "@/models/stargazer/proto/date_time";

import {
    EnvironmentParameters,
    combineTemplateFormData,
    StartEditFormParam,
    convertInstantMessageToDatetime,
    getGeneratedOutput,
    BLUEPRINT_APPLICATION_REPORT_PREFIX,
    BLUEPRINT_TEMPLATE_FORM_PREFIX,
    BLUEPRINT_PARSER_REPORT_PREFIX,
    BLUEPRINT_APPLICATION_FORM_PREFIX,
    getOutputPath,
} from "./components/utils";
import {
    makeBlueprintRequest,
    makeCatalaCodeRequest,
    makeAnnotationDataRequest,
    makeFormRequest,
    makeSaveFormRequest,
    makeStartEditFormRequest,
    makeReleaseFormLockRequest,
} from "./components/makeRequest";
import { BlueprintModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_model";
import { BlueprintGenerator } from "@/generator";
import { encodeFormData } from "@/utils/formData/formDataWriter";
import { decodeFormData } from "@/utils/formData/formDataReader";
import { BlueprintApplicationReport } from "@/models/formMapping";
import {
    FormVersionMetadataModel,
    FormVersionModel,
} from "@/models/stargazer/proto/anduin/forms/model/form_version_models";
import { BlueprintApplicationReportMetaData } from "./components/parameters";
import { calculateSuccessRate } from "@/utils/application/report";
import path from "path";

// Step 1: Fetch blueprint data
export async function fetchBlueprintData(
    params: EnvironmentParameters,
    blueprintId: string,
) {
    const blueprintResponse = await makeBlueprintRequest(params, blueprintId);
    const blueprintModel = BlueprintModel.create(
        blueprintResponse.blueprintModel,
    );
    const blueprintVersionId = blueprintResponse.blueprintModel.latestVersionId;

    const latestBlueprintResponse = await makeBlueprintRequest(
        params,
        blueprintId,
        blueprintVersionId,
    );

    const latestBlueprintVersionModel = BlueprintVersionModel.create(
        latestBlueprintResponse.versionModel,
    );

    // Run the catala code request in parallel
    const blueprintCatalaCode = await makeCatalaCodeRequest(
        blueprintResponse.blueprintModel,
        params,
    );

    return {
        blueprintModel,
        latestBlueprintVersionModel,
        blueprintVersionId,
        blueprintCatalaCode,
    };
}

export async function fetchBlueprintVersionData(
    params: EnvironmentParameters,
    blueprintId: string,
    blueprintVersionId: string,
) {
    const blueprintResponse = await makeBlueprintRequest(
        params,
        blueprintId,
        blueprintVersionId,
    );

    const blueprintModel = BlueprintModel.create(
        blueprintResponse.blueprintModel,
    );

    const blueprintVersionModel =
        blueprintResponse.versionModel as BlueprintVersionModel;

    // Run the catala code request in parallel
    const blueprintCatalaCode = await makeCatalaCodeRequest(
        blueprintVersionId,
        params,
    );
    return {
        blueprintModel,
        blueprintVersionModel,
        blueprintCatalaCode,
    };
}
export async function fetchFormData(
    params: EnvironmentParameters,
    formId: string,
) {
    const formResponse: PantheonModel.GetFormResponse = await makeFormRequest(
        formId,
        params,
    );

    const formModel = FormModel.create(formResponse.formModel as any);

    const latestFormResponse: PantheonModel.GetFormResponse =
        await makeFormRequest(formId, params, formModel.latestVersionId);

    const latestFormModel = FormModel.create(
        latestFormResponse.formModel as any,
    );

    if (
        !latestFormResponse.versionOpt ||
        !$.get(latestFormResponse, ["versionOpt", "versionNumber"])
    ) {
        throw Error(
            `No valid version found at form version ${formModel.latestVersionId}`,
        );
    }

    const latestFormVersionModel = FormVersionModel.create(
        latestFormResponse.versionOpt as any,
    );
    const formVersionMetadataModel = FormVersionMetadataModel.fromJson(
        latestFormResponse.metadata,
    );
    return {
        encodedFormData: latestFormResponse.formData,
        formModel: latestFormModel,
        latestFormVersionModel,
        formVersionMetadataModel,
        draftVersionId: formResponse.draftVersionId,
        latestDraftCreatedTime: InstantMessage.create(
            formModel.draftVersion?.createdAt,
        ),
    };
}

// Step 2: Process annotation documents
export async function fetchAnnotationDocuments(
    blueprintVersionModel: BlueprintVersionModel,
    params: EnvironmentParameters,
) {
    // Create an array of promises for all annotation data requests
    const annotationDataPromises = blueprintVersionModel.baseDocuments.map(
        async (baseDocument) => {
            const response = await makeAnnotationDataRequest(
                baseDocument,
                params,
            );

            return [baseDocument.documentVersionId, response] as [
                string,
                PantheonModel.GetAnnotationDataResponse,
            ];
        },
    );

    // Wait for all annotation data requests to complete
    const annotationResults = await Promise.all(annotationDataPromises);

    // Combine the results
    return new Map(annotationResults);
}

// Step 3: Generate form and write cache files
export async function generateTemplateForm(
    generator: BlueprintGenerator,
    blueprintVersionId: string,
): Promise<object> {
    const templateFormData = generator.generateBlueprintTemplateForm();

    const formData = combineTemplateFormData(encodeFormData(templateFormData));

    const cacheTemplateFormFilePath = getGeneratedOutput(
        BLUEPRINT_TEMPLATE_FORM_PREFIX,
        blueprintVersionId,
    );
    const cacheParserFilePath = getGeneratedOutput(
        BLUEPRINT_PARSER_REPORT_PREFIX,
        blueprintVersionId,
    );

    fs.writeFileSync(
        cacheTemplateFormFilePath,
        JSON.stringify(formData, null, 4),
    );
    fs.writeFileSync(
        cacheParserFilePath,
        JSON.stringify(generator.getParseAnalytic(), null, 4),
    );

    console.log("Generated Template Form locate at cache file:");
    console.log(cacheTemplateFormFilePath);
    console.log(cacheParserFilePath);

    return formData;
}

// Extract version parameter from URL
// Example usage:
// extractVersionFromUrl('https://portal.anduin.app/pantheon/form/for94xp48epvgvk4qwr5/versions?version=for94xp48epvgvk4qwr5.fved98gl58')
// returns 'for94xp48epvgvk4qwr5.fved98gl58'
export const extractVersionFromUrl = (url: string): string | null => {
    try {
        const urlObj = new URL(url);
        return urlObj.searchParams.get("version");
    } catch (error) {
        console.error("Invalid URL:", url);
        return null;
    }
};
export async function mirgrateAllBlueprintApplicationReportMetaData(): Promise<void> {
    try {
        const dataDir = path.join(process.cwd(), "out", "generatedData");
        const reports: BlueprintApplicationReportMetaData[] = [];

        // Read all files in the directory
        const files = fs.readdirSync(dataDir);

        // Filter for blueprint application report files
        const reportFiles = files.filter(
            (file) =>
                file.startsWith(BLUEPRINT_APPLICATION_REPORT_PREFIX) &&
                file.endsWith(".json"),
        );

        // Function to convert filename to reportLink format
        const getReportLinkFromFileName = (fileName: string): string => {
            // Remove prefix and extension
            const withoutPrefix = fileName
                .replace(`${BLUEPRINT_APPLICATION_REPORT_PREFIX}__`, "")
                .replace(".json", "");

            // Split by double underscore
            const [formPart, blueprintPart] = withoutPrefix.split("__");

            // Final path construction
            return `/blueprintApplicationReport/${formPart}--${blueprintPart}`;
        };

        // Process each report file
        for (const file of reportFiles) {
            const filePath = path.join(dataDir, file);
            const fileContent = fs.readFileSync(filePath, "utf-8");
            const reportData = JSON.parse(fileContent);
            const [successRate, totalTemplates] =
                calculateSuccessRate(reportData);
            const versionName = extractVersionFromUrl(reportData.formLink);
            if (!versionName) {
                throw Error(
                    `Cannot extract version from ${reportData.formLink}`,
                );
            }

            const getFormResponse: PantheonModel.GetFormResponse = JSON.parse(
                fs.readFileSync(getOutputPath(versionName, "getForm"), "utf-8"),
            );
            const formModel = FormModel.create(
                getFormResponse.formModel as any,
            );

            reports.push({
                blueprintName: reportData.blueprintName,
                blueprintLink: reportData.blueprintLink,
                blueprintVersionName: reportData.blueprintVersionName,
                blueprintVersionCount: reportData.blueprintVersionCount,
                formName: reportData.formName,
                formLink: reportData.formLink,
                formVersionName: reportData.formVersionName,
                formVersionCount: reportData.formVersionCount,
                reportLink: getReportLinkFromFileName(file),
                successRate: `${successRate}% of ${totalTemplates}`,
                formUpdatedAt: formModel.lastEditedAt
                    ? new Date(
                          convertInstantMessageToDatetime(
                              formModel.lastEditedAt,
                          ),
                      )
                    : undefined,
            } as BlueprintApplicationReportMetaData);
        }

        // Sort reports by formUpdatedAt in descending order
        reports.sort((a, b) =>
            b.formUpdatedAt && a.formUpdatedAt
                ? b.formUpdatedAt.getTime() - a.formUpdatedAt.getTime()
                : 0,
        );

        // Generate CSV content
        const headers = [
            "Blueprint Name",
            "Blueprint Link",
            "Blueprint Version",
            "Blueprint Version Count",
            "Form Name",
            "Form Link",
            "Form Version",
            "Form Version Count",
            "Report Link",
            "Success Rate",
            "Created At",
        ];

        const csvRows = [
            headers.join(","),
            ...reports.map((report) =>
                [
                    `"${report.blueprintName}"`,
                    `"${report.blueprintLink}"`,
                    `"${report.blueprintVersionName}"`,
                    report.blueprintVersionCount,
                    `"${report.formName}"`,
                    `"${report.formLink}"`,
                    `"${report.formVersionName}"`,
                    report.formVersionCount,
                    `"${report.reportLink}"`,
                    `"${report.successRate}"`,
                    `"${report.formUpdatedAt?.toISOString() || new Date(0).toISOString()}"`,
                ].join(","),
            ),
        ];

        // Write to CSV file
        const csvContent = csvRows.join("\n");
        const csvFilePath = path.join(dataDir, "__reports.csv");
        fs.writeFileSync(csvFilePath, csvContent);

        console.log(
            `Successfully wrote ${reports.length} reports to ${csvFilePath}`,
        );
    } catch (error) {
        console.error("Error processing reports:", error);
        process.exit(1);
    }
}

export async function getBlueprintApplicationForm(
    params: EnvironmentParameters,
    blueprintModel: BlueprintModel,
    blueprintVersionModel: BlueprintVersionModel,
    encodedFormData: object,
    formModel: FormModel,
    formVersionModel: FormVersionModel,
    generator: BlueprintGenerator,
    addMissingField: boolean,
    addMissingRule: boolean,
) {
    const applicationResult = generator.applyForm(
        decodeFormData(encodedFormData),
        addMissingField,
        addMissingRule,
    );

    const report: BlueprintApplicationReport = {
        blueprintName: blueprintModel.name,
        blueprintLink: `https://${params.hostname}/pantheon/blueprint/${blueprintModel.id}?version=${blueprintModel.latestVersionId}`,
        blueprintVersionName: blueprintVersionModel.name,
        blueprintVersionCount: blueprintVersionModel.versionIndex.toString(),

        formName: formModel.name,
        formLink: `https://${params.hostname}/pantheon/form/${formModel.formId}/versions?version=${formVersionModel.formVersionId}`,
        formVersionName: formVersionModel.name,
        formVersionCount: formVersionModel.versionNumber.toString(),

        blueprintKeyAnalytic: applicationResult.blueprintKeyAnalytic,
        parserAnalytic: generator.getParseAnalytic(),
        templateAnalytic: applicationResult.templateAnalytic,
        ruleCompareItems: applicationResult.ruleCompareItems,
        formRules: applicationResult.formRules,
        blueprintRules: applicationResult.blueprintRules,
        blueprintLines: generator.getAllBlueprintLines(),
    };

    const response = { formData: applicationResult.formData, report };

    const cacheFormFilePath = getGeneratedOutput(
        BLUEPRINT_APPLICATION_FORM_PREFIX,
        `${formVersionModel.formVersionId}__${blueprintVersionModel.id}`,
    );
    const cacheReportFilePath = getGeneratedOutput(
        BLUEPRINT_APPLICATION_REPORT_PREFIX,
        `${formVersionModel.formVersionId}__${blueprintVersionModel.id}`,
    );

    fs.writeFileSync(
        cacheFormFilePath,
        JSON.stringify(applicationResult.formData, null, 4),
    );
    fs.writeFileSync(cacheReportFilePath, JSON.stringify(report, null, 4));

    await mirgrateAllBlueprintApplicationReportMetaData();

    console.log("Generated Blueprint Application Result locate at cache file:");
    console.log(cacheFormFilePath);
    console.log(cacheReportFilePath);

    return response;
}

export function getSavedFormData(
    formData: PantheonModel.FormData,
    formId: string,
    latestFormVersionModel: FormVersionModel,
    blueprintModel: BlueprintModel,
    sessionID: string,
) {
    const savedForm = {
        formData: encodeFormData(formData),
        formId: formId,
        formTypeOpt: null,
        name: `Based on version ${latestFormVersionModel.versionNumber.toString()}: Generate rule from blueprint ${blueprintModel.name}`,
        parentVersionIdOpt: null,
        sessionId: sessionID,
    };

    return savedForm;
}

export async function updateForm(
    params: EnvironmentParameters,
    formId: string,
    formModel: FormModel,
    latestDraftCreatedTime: InstantMessage,
    savedForm: object,
    sessionID: string,
): Promise<string> {
    const startEditFormBody: StartEditFormParam = {
        formId,
        checkData: {
            latestVersionIdOpt: formModel.latestVersionId,
            latestDraftCreatedOpt: convertInstantMessageToDatetime(
                latestDraftCreatedTime,
            ),
        },
        sessionID: sessionID,
    };

    // Start edit session
    await makeStartEditFormRequest(startEditFormBody, params, formId);

    try {
        const result = await makeSaveFormRequest(savedForm, params, formId);

        await makeReleaseFormLockRequest(formId, sessionID, params);

        return $.get(result, ["result", "r", "lastSavedAt"]);
    } catch (error) {
        await makeReleaseFormLockRequest(formId, sessionID, params);

        throw Error(`Unable to save form: ${error}`);
    }
}
