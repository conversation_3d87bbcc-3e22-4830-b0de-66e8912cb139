import {
    ProcessBlueprintFormParameters,
    ProcessBlueprintFormResponse,
} from "./components/parameters";
import { parseCommandLineArgs } from "./components/requestUtils";
import { processParams } from "./processBlueprintFormProcedure";

function validateAndConvertParams(params: {
    [key: string]: string;
}): ProcessBlueprintFormParameters {
    if (!params.hostname || !params.bearerToken || !params.formId) {
        throw new Error(
            "Missing required parameters: hostname, bearerToken, or formId",
        );
    }

    return {
        hostname: params.hostname,
        bearerToken: params.bearerToken,
        formId: params.formId,
        addMissingRule: false,
        addMissingField: false,
    } as ProcessBlueprintFormParameters;
}

function executeScript(
    processFunction: (
        params: ProcessBlueprintFormParameters,
    ) => Promise<ProcessBlueprintFormResponse>,
): void {
    const rawParams = parseCommandLineArgs();

    try {
        const typedParams = validateAndConvertParams(rawParams);
        console.log("ProcessBlueprintFormParameters:");
        console.log(typedParams);

        processFunction(typedParams).catch((error) => {
            console.error(`Error in script execution: ${error}`);
            process.exit(1);
        });
    } catch (error) {
        console.error(error);
        process.exit(1);
    }
}

console.log("Execute procedure of processing Blueprint Form");
console.log(executeScript(processParams));
