{"form": {"namespaceFormSchemaMap": {"main": {"schema": {"type": "object", "properties": [{"name": "gr_logic_support", "type": "object", "title": "Logic Support", "properties": [{"name": "gr_logic_support_header", "type": "null", "title": "Header"}, {"name": "gr_is_natural_person", "type": "string", "title": "Is user Natural Person?", "enum": ["yes", "no"]}, {"name": "gr_is_joint", "type": "string", "title": "Is user Joint?", "enum": ["yes", "no"]}, {"name": "gr_is_ira", "type": "string", "title": "Is user IRA?", "enum": ["yes", "no"]}, {"name": "gr_is_llc", "type": "string", "title": "Is user LLC?", "enum": ["yes", "no"]}, {"name": "gr_is_trust", "type": "string", "title": "Is user Trust?", "enum": ["yes", "no"]}, {"name": "gr_is_indi_trust", "type": "string", "title": "Is user Individual Trust?", "enum": ["yes", "no"]}, {"name": "gr_is_entity_trust", "type": "string", "title": "Is user Entity Trust?", "enum": ["yes", "no"]}, {"name": "gr_is_indi_other", "type": "string", "title": "Is user Individual Other?", "enum": ["yes", "no"]}, {"name": "gr_is_entity_other", "type": "string", "title": "Is user Entity Other?", "enum": ["yes", "no"]}, {"name": "gr_shortcut", "type": "string", "title": "Shortcut of Investor Type (Individual vs Entity)", "enum": ["indi", "entity"]}, {"name": "gr_groups", "type": "string", "title": "Groups of investor defined by checklist", "enum": ["g1", "g2", "g3", "g4"]}, {"name": "gr_merged_individual_name", "type": "string", "title": "Merged Individual Name"}, {"name": "gr_investment_entity_fs_setup", "type": "string", "title": "Investment Entity FS Setup"}, {"name": "gr_legal_form_of_entity", "type": "string", "title": "Legal Form Of Entity"}]}]}, "uiSchema": {"gr_is_llc": {"ui:formattedText": "Is user LLC?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_logic_support": {"ui:invisible": true, "ui:widget": "Page"}, "gr_merged_individual_name": {"ui:formattedText": "Merged Individual Name", "ui:widget": "TextBox"}, "gr_groups": {"ui:formattedText": "Groups of investor defined by checklist (Indi & Joint, Other Indi, Trust, Other Entites, etc.)", "ui:multipleOption": {"options": [["g1", {"formattedText": "<p>g1</p>", "blueprintMetadataMapping": ""}], ["g2", {"formattedText": "<p>g2</p>", "blueprintMetadataMapping": ""}], ["g3", {"formattedText": "<p>g3</p>", "blueprintMetadataMapping": ""}], ["g4", {"formattedText": "<p>g4</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "gr_is_entity_other": {"ui:formattedText": "Is user Entity Other?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_logic_support_header": {"ui:formattedText": "Header", "ui:heading": "LOGIC SUPPORT", "ui:widget": "Header"}, "gr_is_trust": {"ui:formattedText": "Is user Trust?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_is_indi_other": {"ui:formattedText": "Is user Indi Other?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_is_natural_person": {"ui:formattedText": "Is user Natural Person?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_investment_entity_fs_setup": {"ui:formattedText": "Investment Entity FS Setup", "ui:pdfMapping": [], "ui:signature": [], "ui:widget": "TextBox"}, "gr_is_ira": {"ui:formattedText": "Is user IRA?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_is_entity_trust": {"ui:formattedText": "Is user Entity Trust?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_is_indi_trust": {"ui:formattedText": "Is user Indi Trust?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "gr_legal_form_of_entity": {"ui:enabledComment": true, "ui:formattedText": "<p>Legal Form Of Entity</p>", "ui:marginBottom": "12px", "ui:required": "This field is required", "ui:widget": "TextBox"}, "gr_shortcut": {"ui:formattedText": "Shortcut of Investor Type (Individual vs Entity)", "ui:multipleOption": {"options": [["indi", {"formattedText": "<p>natural person</p>", "blueprintMetadataMapping": ""}], ["entity", {"formattedText": "<p>entity</p>", "blueprintMetadataMapping": ""}]]}, "ui:widget": "Radio"}, "gr_is_joint": {"ui:formattedText": "Is user Joint?", "ui:multipleOption": {"options": [["yes", {"formattedText": "<p>yes</p>", "blueprintMetadataMapping": ""}], ["no", {"formattedText": "<p>no</p>", "blueprintMetadataMapping": ""}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}}}}, "rules": [], "defaultNamespace": "main", "libs": [{"value": "{\n    local _main = self,\n    local _rule = _main.rule,\n    local _collector = _rule.collector,\n\n    // ------ Public methods ------\n\n    unit: _collector,\n    show: _collector.show,\n    hide: _collector.hide,\n    deactivate: _collector.deactivate,\n    activate: _collector.activate,\n    setError: _collector.setError,\n    setWarning: _collector.setWarning,\n    fill: _collector.fill,\n    fillAllOptions: _collector.fillAllOptions,\n    deactivateOptions: _collector.deactivateOptions,\n    activateOptions: _collector.activateOptions,\n    addDeactivatedOptions: _collector.addDeactivatedOptions,\n    addActivatedOptions: _collector.addActivatedOptions,\n    excludeOptions: _collector.excludeOptions,\n    excludeOptionGroups: _collector.excludeOptionGroups,\n    excludeKeyOptionGroups: _collector.excludeKeyOptionGroups,\n    excludeOtherOptions: _collector.excludeOtherOptions,\n    excludeEachOptions: _collector.excludeEachOptions,\n    enableFiles: _collector.enableFiles,\n    enableAllFiles: _collector.enableAllFiles,\n    setSigningTypes: _collector.setSigningTypes,\n    autoCorrect: _collector.autoCorrect,\n    resetValue: _collector.resetValue,\n\n    rule: {\n        collector: {\n            local ref = self,\n\n            rules: [],\n\n            append: function(rule)\n                ref {\n                    rules: ref.rules + [rule],\n                },\n\n            show: _rule.showImpl(ref.append),\n            hide: _rule.hideImpl(ref.append),\n            deactivate: _rule.deactivateImpl(ref.append),\n            activate: _rule.activateImpl(ref.append),\n            setError: _rule.setErrorImpl(ref.append),\n            setWarning: _rule.setWarningImpl(ref.append),\n            fill: _rule.fillImpl(ref.append),\n            fillAllOptions: _rule.fillAllOptionsImpl(ref.append),\n            deactivateOptions: _rule.deactivateOptionsImpl(ref.append),\n            activateOptions: _rule.activateOptionsImpl(ref.append),\n            addDeactivatedOptions: _rule.addDeactivatedOptionsImpl(ref.append),\n            addActivatedOptions: _rule.addActivatedOptionsImpl(ref.append),\n            excludeOptions: _rule.excludeOptionsImpl(ref.append),\n            excludeOptionGroups: _rule.excludeOptionGroupsImpl(ref.append),\n            excludeKeyOptionGroups: _rule.excludeKeyOptionGroupsImpl(ref.append),\n            excludeOtherOptions: _rule.excludeOtherOptionsImpl(ref.append),\n            excludeEachOptions: _rule.excludeEachOptionsImpl(ref.append),\n            enableFiles: _rule.enableFilesImpl(ref.append),\n            enableAllFiles: _rule.enableAllFilesImpl(ref.append),\n            setSigningTypes: _rule.setSigningTypesImpl(ref.append),\n            autoCorrect: _rule.autoCorrectImpl(ref.append),\n            resetValue: _rule.resetValueImpl(ref.append),\n\n            apply: function(output, ctx, debug)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n            applyRepeatable: function(output, ctx, debug) function(onRow)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n            applyKey: function(key)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n            applyFakeCheckbox: function(targetObj, keyRules, debug)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n        },\n\n        // ------ Show/Hide ------\n\n        showImpl: function(converter)\n            function(condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        hideImpl: function(converter)\n            function(condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Activate/Deactivate ------\n\n        deactivateImpl: function(converter)\n            function(condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        activateImpl: function(converter)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Warning/Error ------\n\n        setErrorImpl: function(converter)\n            function(message, condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        setWarningImpl: function(converter)\n            function(message, condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Pre-fill value/Pre-check options ------\n\n        fillImpl: function(converter)\n            function(value, condition, disabled, orElse=\"clear\")\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        fillAllOptionsImpl: function(converter)\n            function(condition, disabled, orElse=\"clear\")\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Activate/Deactivate Options ------\n\n        deactivateOptionsImpl: function(converter)\n            function(options, condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        activateOptionsImpl: function(converter)\n            function(options, condition)\n                error \"Upgrading to latest version of `mixcalc` is required\",\n\n        addDeactivatedOptionsImpl: function(converter)\n            function(options, condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        addActivatedOptionsImpl: function(converter)\n            function(options, condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Exclude Between Options ------\n\n        excludeOptionsImpl: function(converter)\n            function(options, condition, disabled)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        excludeOptionGroupsImpl: function(converter)\n            function(\n                group1,\n                group2,\n                group3=[],\n                group4=[],\n                group5=[],\n                group6=[],\n                group7=[],\n                group8=[],\n                condition,\n                disabled\n            )\n            error \"Upgrading to latest version of `mixcalc` is required\",\n        excludeKeyOptionGroupsImpl: function(converter)\n            function(\n                group1,\n                group2,\n                group3={},\n                group4={},\n                group5={},\n                group6={},\n                group7={},\n                group8={},\n                condition,\n                disabled\n            )\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        excludeOtherOptionsImpl: function(converter)\n            function(options, condition, disabled)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        excludeEachOptionsImpl: function(converter)\n            function(condition, disabled)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ Supporting Documents ------\n\n        enableFilesImpl: function(converter)\n            function(files, condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        enableAllFilesImpl: function(converter)\n            function(condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        // ------ E-sign/Wet-sign ------\n\n        setSigningTypesImpl: function(converter)\n            function(singingTypes, condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n        // ------ Others ------\n\n        autoCorrectImpl: function(converter)\n            function(getValue, trimEnabled)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n\n        resetValueImpl: function(converter)\n            function(condition)\n            error \"Upgrading to latest version of `mixcalc` is required\",\n    },\n\n}\n", "name": "mixcalc"}, {"value": "{\n    purge: function(arr, elements)\n        error \"Upgrading to latest version of `gr` is required\",\n    intersect: function(arr1, arr2)\n        error \"Upgrading to latest version of `gr` is required\",\n    endsWith: function(arr, ele)\n        error \"Upgrading to latest version of `gr` is required\",\n    forceNum: function(value)\n        error \"Upgrading to latest version of `gr` is required\",\n    forceStr: function(value)\n        error \"Upgrading to latest version of `gr` is required\",\n    last: function(arr)\n        error \"Upgrading to latest version of `gr` is required\",\n    join: function(spliter, items)\n        error \"Upgrading to latest version of `gr` is required\",\n    safeInt: function(s)\n        error \"Upgrading to latest version of `gr` is required\",\n    empty: function(value)\n        error \"Upgrading to latest version of `gr` is required\",\n    getFiscalDaysInMonth: function(month)\n        error \"Upgrading to latest version of `gr` is required\",\n    getFiscalEndDate: function(startMonth, startDay)\n        error \"Upgrading to latest version of `gr` is required\",\n    yes: function(value)\n        error \"Upgrading to latest version of `gr` is required\",\n    no: function(value)\n        error \"Upgrading to latest version of `gr` is required\",\n    clear: function(targets, condition=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    touched: function(targets, value=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    disable: function(targets, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    disabledOptions: function(target, options)\n        error \"Upgrading to latest version of `gr` is required\",\n    enable: function(targets, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    hide: function(targets, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    setError: function(targets, message, condition=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    show: function(targets, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    value: function(targets, value, condition=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    warning: function(target, message)\n        error \"Upgrading to latest version of `gr` is required\",\n    signingType: function(target, options)\n        error \"Upgrading to latest version of `gr` is required\",\n    clearRep: function(targets, index, condition=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    touchedRep: function(targets, index, value=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    disableRep: function(targets, index, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    disabledOptionsRep: function(target, index, options)\n        error \"Upgrading to latest version of `gr` is required\",\n    enableRep: function(targets, index, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    hideRep: function(targets, index, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    setErrorRep: function(target, index, message)\n        error \"Upgrading to latest version of `gr` is required\",\n    showRep: function(targets, index, condition=true, clearedTargets=[])\n        error \"Upgrading to latest version of `gr` is required\",\n    valueRep: function(targets, index, value, condition=true)\n        error \"Upgrading to latest version of `gr` is required\",\n    warningRep: function(target, index, message)\n        error \"Upgrading to latest version of `gr` is required\",\n    disabled: function(alias)\n        error \"Upgrading to latest version of `gr` is required\",\n    enabled: function(alias)\n        error \"Upgrading to latest version of `gr` is required\",\n    hidden: function(alias)\n        error \"Upgrading to latest version of `gr` is required\",\n    shown: function(alias)\n        error \"Upgrading to latest version of `gr` is required\",\n    toOptions: function(configs)\n        error \"Upgrading to latest version of `gr` is required\",\n    uncheck: function(items)\n        error \"Upgrading to latest version of `gr` is required\",\n    checkbox: function(config)\n        error \"Upgrading to latest version of `gr` is required\",\n    radio: function(configs)\n        error \"Upgrading to latest version of `gr` is required\",\n    fileGroup: function(target, fileArray)\n        error \"Upgrading to latest version of `gr` is required\",\n    arrayContainsAny: function(arr, elements)\n        error \"Upgrading to latest version of `gr` is required\",\n    isDirtyField: function(field)\n        error \"Upgrading to latest version of `gr` is required\",\n\n}\n", "name": "gr"}, {"value": "{\n    EmptyValue:\n        error \"Upgrading to latest version of `utils` is required\",\n    forceNum: function(value, default)\n        error \"Upgrading to latest version of `utils` is required\",\n    forceStr: function(value)\n        error \"Upgrading to latest version of `utils` is required\",\n    isEmpty: function(value)\n        error \"Upgrading to latest version of `utils` is required\",\n    trim: function(str)\n        error \"Upgrading to latest version of `utils` is required\",\n    purge: function(arr, elements)\n        error \"Upgrading to latest version of `utils` is required\",\n    containsAny: function(arr, elements)\n        error \"Upgrading to latest version of `utils` is required\",\n    endsWith: function(arr, ele)\n        error \"Upgrading to latest version of `utils` is required\",\n    endsWithOneOf: function(arr, elements)\n        error \"Upgrading to latest version of `utils` is required\",\n    intersect: function(arr1, arr2)\n        error \"Upgrading to latest version of `utils` is required\",\n    last: function(arr)\n        error \"Upgrading to latest version of `utils` is required\",\n    join: function(spliter, items)\n        error \"Upgrading to latest version of `utils` is required\",\n    unique: function(arr)\n        error \"Upgrading to latest version of `utils` is required\",\n    stringContains: function(string, substr)\n        error \"Upgrading to latest version of `utils` is required\",\n    parserInt: function(s, default)\n        error \"Upgrading to latest version of `utils` is required\",\n    getLabel: function(value, exceptionalOptions, exceptionalOptionValues)\n        error \"Upgrading to latest version of `utils` is required\",\n    isDigit: function(value)\n        error \"Upgrading to latest version of `utils` is required\",\n    isAlphanumeric: function(value)\n        error \"Upgrading to latest version of `utils` is required\",\n    get: function(obj, parts, defaultVal=null)\n        error \"Upgrading to latest version of `utils` is required\",\n    set: function(obj, parts, updateFunc, defaultVal=null)\n        error \"Upgrading to latest version of `utils` is required\",\n    isDirty: function(field)\n        error \"Upgrading to latest version of `utils` is required\",\n    getAllOptions: function(schemaObject)\n        error \"Upgrading to latest version of `utils` is required\",\n    getDaysInMonth: function(mm, yy)\n        error \"Upgrading to latest version of `utils` is required\",\n    getFiscalDaysInMonth: function(month)\n        error \"Upgrading to latest version of `utils` is required\",\n    getFiscalEndDate: function(startMonth, startDay)\n        error \"Upgrading to latest version of `utils` is required\",\n}\n", "name": "utils"}], "triggerRuleByProperty": false, "gaiaLogicVersion": 200}, "uploadedPdf": [], "uploadedDocx": [], "embeddedPdf": [], "associatedLinks": []}