import { mirgrateAllBlueprintApplicationReportMetaData } from "./procedureUtils";

function executeScript(processFunction: () => Promise<void>): void {
    try {
        console.log(
            "Starting migration of Blueprint Application Report Metadata",
        );

        processFunction().catch((error) => {
            console.error(`Error in script execution: ${error}`);
            process.exit(1);
        });
    } catch (error) {
        console.error(error);
        process.exit(1);
    }
}

console.log(
    "Execute procedure of migrating Blueprint Application Report Metadata",
);
executeScript(mirgrateAllBlueprintApplicationReportMetaData);
