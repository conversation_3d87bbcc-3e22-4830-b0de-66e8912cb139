import { EnvironmentParameters } from "./components/utils";
import { BlueprintGenerator } from "@/generator";
import {
    ProcessBlueprintFormParameters,
    ProcessBlueprintFormResponse,
} from "./components/parameters";
import {
    fetchAnnotationDocuments,
    fetchBlueprintVersionData,
    fetchFormData,
    generateTemplateForm,
    getBlueprintApplicationForm,
} from "./procedureUtils";

export async function processParams(
    params: ProcessBlueprintFormParameters,
): Promise<ProcessBlueprintFormResponse> {
    console.log("Process Params: ", params);
    const environmentParams: EnvironmentParameters = {
        hostname: params.hostname,
        bearerToken: params.bearerToken,
    };

    const {
        encodedFormData,
        formModel,
        latestFormVersionModel,
        formVersionMetadataModel,
    } = await fetchFormData(environmentParams, params.formId);

    if (!formVersionMetadataModel.blueprintRef) {
        throw Error(
            `Cannot find any Blueprint Reference at form ${params.formId}`,
        );
    }

    const blueprintId = formVersionMetadataModel.blueprintRef.id;
    const blueprintVersionId = formVersionMetadataModel.blueprintRef.versionId;

    const { blueprintModel, blueprintVersionModel, blueprintCatalaCode } =
        await fetchBlueprintVersionData(
            params,
            blueprintId,
            blueprintVersionId,
        );

    const annotationDataResponses = await fetchAnnotationDocuments(
        blueprintVersionModel,
        environmentParams,
    );
    const generator = await BlueprintGenerator.create(
        blueprintCatalaCode,
        annotationDataResponses,
        blueprintVersionModel.baseDocuments,
    );

    await generateTemplateForm(generator, blueprintVersionId);

    await getBlueprintApplicationForm(
        params,
        blueprintModel,
        blueprintVersionModel,
        encodedFormData,
        formModel,
        latestFormVersionModel,
        generator,
        params.addMissingField,
        params.addMissingRule,
    );
    return {
        blueprintVersionId: blueprintVersionId,
        formVersionId: latestFormVersionModel.formVersionId,
        blueprintName: blueprintModel.name,
        formName: formModel.name,
        blueprintVersionIndex: blueprintVersionModel.versionIndex,
        formVersionIndex: latestFormVersionModel.versionNumber,
    };
}
