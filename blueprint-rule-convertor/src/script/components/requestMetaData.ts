import { BlueprintModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_model";
import { BaseDocument } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";

import {
    EnvironmentParameters,
    Request,
    StartEditFormParam,
} from "@/script/components/utils";

export const startEditFormRequest =
    (body: StartEditFormParam) =>
    (_: EnvironmentParameters): Request => ({
        path: "startEditForm",
        body,
        scope: "form",
        cacheKey: undefined,
    });

export const releaseFormLockRequest =
    (formId: string, sessionID: string) =>
    (_: EnvironmentParameters): Request => ({
        path: "releaseFormLock",
        body: { formId, sessionID },
        scope: "form",
        cacheKey: undefined,
    });

export const blueprintRequest =
    (blueprintId: string, blueprintVersionId: string | undefined = undefined) =>
    (_: EnvironmentParameters): Request => ({
        path: "getBlueprint",
        body: {
            blueprintId: blueprintId,
            blueprintVersionId: blueprintVersionId ? blueprintVersionId : null,
        },
        scope: "blueprint",
        cacheKey: blueprintVersionId ? blueprintVersionId : undefined,
    });
export const catalaCodeRequest =
    (blueprintVersionId: string) =>
    (_: EnvironmentParameters): Request => ({
        path: "getBlueprintVersionCatalaCode",
        body: {
            blueprintVersionId,
        },
        scope: "blueprint",
        cacheKey: blueprintVersionId,
    });
export const annotationDataRequest =
    (baseDocument: BaseDocument) =>
    (_: EnvironmentParameters): Request => ({
        path: "getAnnotationData",
        body: {
            annotationVersionId: baseDocument.documentVersionId,
        },
        scope: "blueprint",
        cacheKey: baseDocument.documentVersionId,
    });
export const formRequest =
    (formId: string, versionId: string | undefined = undefined) =>
    (_: EnvironmentParameters): Request => ({
        path: "getForm",
        body: {
            formId,
            versionIdOpt: versionId ? versionId : null,
        },
        scope: "form",
        cacheKey: versionId ? versionId : undefined,
    });
export const processAnnotationDocumentRequest =
    (formId: string, annotationDocumentVersionId: string) =>
    (_: EnvironmentParameters): Request => ({
        path: "processAnnotationDocument",
        body: {
            formId,
            annotationDocumentVersionId,
        },
        scope: "form",
        cacheKey: `${formId}/${annotationDocumentVersionId}`,
    });
export const saveFormRequest =
    (body: object) =>
    (_: EnvironmentParameters): Request => ({
        path: "saveForm",
        body,
        scope: "form",
        cacheKey: undefined,
    });
export const updateBlueprintRefRequest =
    (body: object) =>
    (_: EnvironmentParameters): Request => ({
        path: "updateBlueprintRef",
        body,
        scope: "form",
        cacheKey: undefined,
    });
