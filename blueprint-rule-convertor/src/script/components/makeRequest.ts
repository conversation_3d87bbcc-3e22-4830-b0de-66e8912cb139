import axios from "axios";
import $ from "lodash";
import fs from "fs";

import * as PantheonModel from "@/models/stargazer";
import { BlueprintModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_model";
import { BaseDocument } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";
import { FormModel } from "@/models/stargazer/proto/anduin/forms/model/form_models";

import {
    EnvironmentParameters,
    StartEditFormParam,
    Request,
    getOutputPath,
} from "@/script/components/utils";
import {
    blueprintRequest,
    catalaCodeRequest,
    annotationDataRequest,
    formRequest,
    processAnnotationDocumentRequest,
    saveFormRequest,
    updateBlueprintRefRequest,
    startEditFormRequest,
    releaseFormLockRequest,
} from "@/script/components/requestMetaData";
import { ensureOutputDirectoryExists } from "./requestUtils";

async function makePostRequest(
    getRequest: (params: EnvironmentParameters) => Request,
    params: EnvironmentParameters,
) {
    const request = getRequest(params);

    let cacheFilePath: string | undefined = undefined;

    if (typeof request.cacheKey == "string") {
        const folderPaths = request.cacheKey.split("/");
        folderPaths.reduce((accPath, folderPath): string => {
            const path =
                accPath == null ? folderPath : `${accPath}/${folderPath}`;
            const cacheFolderPath = getOutputPath(path);

            ensureOutputDirectoryExists(cacheFolderPath);

            return path;
        }, "");

        cacheFilePath = getOutputPath(request.cacheKey, request.path);

        try {
            const cachedData = JSON.parse(
                fs.readFileSync(cacheFilePath, "utf8"),
            );

            if (cachedData) {
                console.log(
                    ">>>> Return data from cache file: ",
                    cacheFilePath,
                );
                return cachedData;
            }
        } catch (_: any) {}
    }

    const url = `https://${params.hostname}/api/v3/${request.scope}/${request.path}`;

    console.log(`Fetching data from url: ${url}`);

    try {
        const response = await axios.post(url, request.body, {
            headers: {
                authorization: params.bearerToken,
                "Content-Type": "application/json",
            },
        });

        if (cacheFilePath) {
            fs.writeFileSync(cacheFilePath, JSON.stringify(response.data));
        }

        return response.data;
    } catch (error: any) {
        if (axios.isAxiosError(error)) {
            throw Error(
                `Cannot fetch data from server ${params.hostname}: ${error.message}. Please make sure you're providing valid environment and authentication information.`,
            );
        } else {
            throw Error(`An unexpected error occurred: ${error}`);
        }
    }
}

export async function makeBlueprintRequest(
    params: EnvironmentParameters,
    blueprintId: string,
    versionId: string | undefined = undefined,
) {
    console.log(`
** Fetch Blueprint: **
- Blueprint ID ${blueprintId}${
        versionId
            ? `
- Version Id: ${versionId}`
            : ""
    }
        `);

    const result = await makePostRequest(
        blueprintRequest(blueprintId, versionId),
        params,
    );

    console.log(
        `Latest Blueprint version ID is ${result.blueprintModel.latestVersionId}`,
    );

    return result;
}

export async function makeCatalaCodeRequest(
    blueprintVersionId: string,
    params: EnvironmentParameters,
): Promise<PantheonModel.GetBlueprintVersionCatalaCodeResponse> {
    console.log(
        `
** Fetching Blueprint with Catala Code: **
    - Blueprint Version ID: ${blueprintVersionId}
        `,
    );

    const result = (await makePostRequest(
        catalaCodeRequest(blueprintVersionId),
        params,
    )) as PantheonModel.GetBlueprintVersionCatalaCodeResponse;

    console.log(`Blueprint has ${result.formLogic.split("\n").length} line(s)`);

    return result;
}

export async function makeAnnotationDataRequest(
    baseDocument: BaseDocument,
    params: EnvironmentParameters,
) {
    console.log(
        `
** Fetching Annotation Document: **
- Document Version ID: ${baseDocument.documentVersionId}
        `,
    );

    return await makePostRequest(annotationDataRequest(baseDocument), params);
}

export async function makeFormRequest(
    formId: string,
    params: EnvironmentParameters,
    formVersionId: string | undefined = undefined,
) {
    console.log(`
** Fetching Form Data: **
- Form ID: ${formId}${
        formVersionId
            ? `
- Version Id: ${formVersionId}`
            : ""
    }
        `);

    const result = (await makePostRequest(
        formRequest(formId, formVersionId),
        params,
    )) as PantheonModel.GetFormResponse;

    console.log(`Form name is ${FormModel.fromJson(result.formModel).name}`);

    return result;
}

export async function makeSaveFormRequest(
    body: object,
    params: EnvironmentParameters,
    formId: string,
) {
    console.log(`
** Save form: **
- Form ID: ${$.get(body, ["formId"])}
- Session ID: ${$.get(body, ["sessionId"])}
        `);

    const result = await makePostRequest(saveFormRequest(body), params);

    if (!$.get(result, ["result", "r", "checkData"])) {
        throw Error(
            `Unable to save form. Server return: ${JSON.stringify(result)}`,
        );
    } else {
        console.log(`Save form response: ${JSON.stringify(result)}`);
        console.log(
            `Form link: https://${params.hostname}/pantheon/form/${formId}/`,
        );
    }

    return result;
}

export async function makeUpdateBlueprintRefRequest(
    blueprintModel: BlueprintModel,
    draftVersionId: PantheonModel.FormVersionId,
    params: EnvironmentParameters,
    blueprintId: string,
    formId: string,
) {
    const body = {
        blueprintId: blueprintId,
        blueprintVersionId: blueprintModel.latestVersionId,
        versionIndex: blueprintModel.versionCount,
        blueprintName: blueprintModel.name,
        formId: formId,
        formVersionIdOpt: draftVersionId,
    };
    console.log(`
** Update Blueprint Reference: **
- Form ID: ${$.get(body, ["formId"])}
- Blueprint ID: ${$.get(body, ["blueprintId"])}
        `);

    const result = await makePostRequest(
        updateBlueprintRefRequest(body),
        params,
    );

    console.log(
        `Updated Blueprint Reference for blueprint ${result.blueprintRefOpt.name}`,
    );

    return result;
}

export async function makeStartEditFormRequest(
    body: StartEditFormParam,
    params: EnvironmentParameters,
    formId: string,
) {
    console.log(
        `
** Start editing form: **
- Form ID: ${formId}
- Session ID: ${body.sessionID}
- Other Information ${JSON.stringify(body.checkData)}`,
    );

    const result = await makePostRequest(startEditFormRequest(body), params);

    const edittingUser = $.get(result, [
        "lockResult",
        "AssetLocked",
        "lockedUserInfo",
        "emailAddressStr",
    ]);
    if (edittingUser) {
        throw Error(`Form is being editted by ${edittingUser}`);
    }

    console.log(
        `
Form ${formId} is in Editting Mode
Response: ${JSON.stringify(result)}
        `,
    );

    return result;
}

export async function makeReleaseFormLockRequest(
    formId: string,
    sessionID: string,
    params: EnvironmentParameters,
) {
    console.log(
        `
** Finish editing form: **
- Form ID ${formId}
- Session ID: ${sessionID}`,
    );

    const result = await makePostRequest(
        releaseFormLockRequest(formId, sessionID),
        params,
    );

    console.log(`Form ${formId} existed from Editting Mode`);

    return result;
}

export async function makeProcessAnnotationDocument(
    formId: string,
    annotationDocumentVersionId: string,
    params: EnvironmentParameters,
) {
    console.log(
        `
** Process Annotation Document: **
- Form ID: ${formId}
- Document ID: ${annotationDocumentVersionId}
        `,
    );

    const result = (await makePostRequest(
        processAnnotationDocumentRequest(formId, annotationDocumentVersionId),
        params,
    )) as { files: [string, PantheonModel.ExtractedPdf][] };

    console.log(
        `Process Annotation Document has done
- Number of file(s): ${result.files.length}
- File names: ${result.files.map(([id, _]) => id).join(", ")}
        `,
    );

    return result;
}
