import * as fs from "fs";
import * as crypto from "crypto";
import $ from "lodash";

import { FormVersionMetadataModel } from "@/models/stargazer/proto/anduin/forms/model/form_version_models";
import { InstantMessage } from "@/models/stargazer/proto/date_time";
import {
    getRules,
    getSchema,
    getUiSchema,
    getUploadedPdf,
} from "@/utils/formData/formSchema";
import { BlueprintApplicationReport } from "@/models/formMapping";
import { FormRule } from "@/models/stargazer";

export interface Request {
    path: string;
    body: { [key: string]: any };
    scope: string;
    cacheKey: string | undefined;
}

export enum FormModifyType {
    Overwrite = "overwrite",
    Append = "append",
}

export interface EnvironmentParameters {
    hostname: string;
    bearerToken: string;
}

export type StartEditFormParam = {
    formId: string;
    checkData: {
        latestVersionIdOpt: string | undefined;
        latestDraftCreatedOpt: string | undefined;
    };
    sessionID: string;
};
export const getOutputPath = (
    cacheKey: string,
    fileName: string | undefined = undefined,
): string =>
    fileName ? `out/${cacheKey}/${fileName}.json` : `out/${cacheKey}`;

// Generate a session ID with format: fba-{uuid}
export function generateSessionId(): string {
    // Generate a random UUID v4
    const uuid = crypto.randomUUID
        ? crypto.randomUUID()
        : "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
              const r = (Math.random() * 16) | 0;
              const v = c === "x" ? r : (r & 0x3) | 0x8;
              return v.toString(16);
          });

    // Return the formatted session ID with prefix
    return `fba-${uuid}`;
}

const TEMPLATE_FORM_PATH = "src/script/template/form_data.json";

/**
 * Attach Logic Support and default libraries mixcalc and utils
 *
 * @param encoded FormData
 * @returns encoded FormData
 */
export function combineTemplateFormData(generatedFormData: object): object {
    const rawString = fs.readFileSync(TEMPLATE_FORM_PATH, "utf8");
    const templateData = JSON.parse(rawString);

    return {
        ...templateData,
        form: {
            ...templateData.form,
            rules: [...getRules(generatedFormData), ...getRules(templateData)],
            namespaceFormSchemaMap: {
                main: {
                    schema: {
                        type: "object",
                        title: "main",
                        properties: [
                            ...getSchema(generatedFormData).properties,
                            ...getSchema(templateData).properties,
                        ],
                    },
                    uiSchema: {
                        ...getUiSchema(generatedFormData),
                        ...getUiSchema(templateData),
                    },
                },
            },
        },
        uploadedPdf: [
            ...getUploadedPdf(generatedFormData),
            ...getUploadedPdf(templateData),
        ],
    };
}

export function getSavedForm(
    formVersionMetadataModel: FormVersionMetadataModel,
    formId: string,
    blueprintId: string,
    generatedFormData: object,
    documentIdMap: { [key: string]: string },
    sessionID: string,
    blueprintRef: object,
) {
    const formData = combineTemplateFormData(generatedFormData);

    return {
        formData,
        formId,
        formTypeOpt: null,
        metadataOpt: [
            {
                ...formVersionMetadataModel,
                blueprintRef,
            },
            {
                id: "for00000000000000000.fve0000000.fvs",
                annotationMapping: documentIdMap,
            },
        ],
        name: `Generate rule for blueprint ${$.get(blueprintRef, "name", blueprintId)}`,
        parentVersionIdOpt: null,
        sessionId: sessionID,
    };
}

export const FormBuilderTimeFormat = "MMMM dd, yyyy at hh:mma";

export function convertInstantMessageToDatetime(
    instantMessage: InstantMessage,
    format: string = "yyyy-mm-ddThh:mm:ss.nsZ",
): string {
    const seconds = Number(instantMessage.seconds); // Convert bigint to number
    const nanos = Number(instantMessage.nanos);

    const milliseconds = seconds * 1000 + Math.floor(nanos / 1000000); // Convert nanos to milliseconds

    const date = new Date(milliseconds);

    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const day = String(date.getUTCDate()).padStart(2, "0");
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    const secondsStr = String(date.getUTCSeconds()).padStart(2, "0");
    const nanosStr = String(nanos).padStart(9, "0");

    if (format == "yyyy-mm-ddThh:mm:ss.nsZ") {
        return `${year}-${month}-${day}T${hours}:${minutes}:${secondsStr}.${nanosStr}Z`;
    }

    if (format == "MMMM dd, yyyy at hh:mma") {
        const options: Intl.DateTimeFormatOptions = {
            year: "numeric",
            month: "long",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
        };
        return new Intl.DateTimeFormat("en-US", options)
            .format(date)
            .replace(",", "");
    }

    console.error("Unaccepted date time format " + format);
    return `${year}-${month}-${day}`;
}

export const BLUEPRINT_APPLICATION_REPORT_PREFIX =
    "blueprint_application_report";
export const BLUEPRINT_APPLICATION_FORM_PREFIX = "blueprint_application_form";
export const BLUEPRINT_TEMPLATE_FORM_PREFIX = "blueprint_template_form";
export const BLUEPRINT_PARSER_REPORT_PREFIX = "blueprint_parser_report";

export function getGeneratedOutput(prefixName: string, uniqueId: string) {
    return `out/generatedData/${prefixName}__${uniqueId.replaceAll(".", "_")}.json`;
}
