export interface ProcessBlueprintFormParameters {
    hostname: string;
    bearerToken: string;
    formId: string;
    addMissingRule: boolean;
    addMissingField: boolean;
}

export interface ProcessBlueprintFormResponse {
    blueprintName: string;
    blueprintVersionId: string;
    blueprintVersionIndex: number;

    formName: string;
    formVersionId: string;
    formVersionIndex: number;
}

export interface BlueprintApplicationReportMetaData {
    blueprintName: string;
    blueprintLink: string;
    blueprintVersionName: string;
    blueprintVersionCount: number;
    formName: string;
    formLink: string;
    formVersionName: string;
    formVersionCount: number;
    reportLink: string;
    successRate: string;
    formUpdatedAt: Date | undefined;
}

export interface ProcessBlueprintParameters {
    hostname: string;
    bearerToken: string;
    blueprintId: string;
}

export interface ProcessBlueprintResponse {
    blueprintVersionId: string;
}

export interface UpdateBlueprintFormApplicationParameters {
    hostname: string;
    bearerToken: string;
    formId: string;
    blueprintVersionId: string;
    formVersionId: string;
}

export interface UpdateBlueprintFormApplicationResponse {
    updatedTime: string;
    formName: string;
    formLink: string;
}

export interface DownloadTemplateParameters {
    blueprintVersionId: string;
}

export interface ErrorResponse {
    errorMessage: string;
}
