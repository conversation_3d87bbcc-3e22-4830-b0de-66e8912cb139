import * as fs from "fs";
import { EnvironmentParameters, getOutputPath } from "./utils";
import { tryParseEnumFromStringValue } from "@/utils/common";
import { FormModifyType } from "./utils";

/**
 * Parses command line arguments into a parameters object
 * @returns Object containing parsed parameters
 */
export function parseCommandLineArgs(): { [key: string]: string } {
    const args = process.argv.slice(2);
    const params: { [key: string]: string } = {};

    args.forEach((arg) => {
        const parts = arg.split("=");
        if (parts.length === 2) {
            params[parts[0]] = parts[1];
        }
    });

    return params;
}

/**
 * Validates required parameters and converts them to the EnvironmentParameters type
 * @param params Raw parameters from command line
 * @returns Typed EnvironmentParameters object
 */
export function validateAndConvertParams(params: {
    [key: string]: string;
}): EnvironmentParameters {
    if (!params.hostname || !params.bearerToken || !params.blueprintId) {
        throw new Error(
            "Missing required parameters: hostname, bearerToken, or blueprintId",
        );
    }

    return {
        hostname: params.hostname,
        bearerToken: params.bearerToken,
        blueprintId: params.blueprintId,
        formId: params.formId,
        modifyType: params.modifyType
            ? tryParseEnumFromStringValue(FormModifyType, params.modifyType)
            : undefined,
    } as EnvironmentParameters;
}

/**
 * Gets blueprint info from the output file
 * @param blueprintId The blueprint ID
 * @returns The blueprint response object
 */
export function getBlueprintInfoFromFile(blueprintId: string): any {
    const blueprintInfoFilePath = getOutputPath(blueprintId, "blueprint_info");

    // Ensure the blueprint info file exists
    if (!fs.existsSync(blueprintInfoFilePath)) {
        throw new Error(
            `Blueprint info file not found at ${blueprintInfoFilePath}`,
        );
    }

    return JSON.parse(fs.readFileSync(blueprintInfoFilePath, "utf-8"));
}

/**
 * Creates the output directory if it doesn't exist
 * @param dirPath The directory path to create
 */
export function ensureOutputDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath);
    }
}
