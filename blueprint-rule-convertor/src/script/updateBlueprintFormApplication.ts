import fs from "fs";

import {
    BLUEPRINT_APPLICATION_FORM_PREFIX,
    EnvironmentParameters,
    generateSessionId,
    getGeneratedOutput,
} from "./components/utils";
import {
    UpdateBlueprintFormApplicationParameters,
    UpdateBlueprintFormApplicationResponse,
} from "./components/parameters";
import {
    fetchBlueprintVersionData,
    fetchFormData,
    getSavedFormData,
    updateForm,
} from "./procedureUtils";

export async function processParams(
    params: UpdateBlueprintFormApplicationParameters,
): Promise<UpdateBlueprintFormApplicationResponse> {
    console.log("Process Params: ", params);
    const environmentParams: EnvironmentParameters = {
        hostname: params.hostname,
        bearerToken: params.bearerToken,
    };

    const { formModel, latestFormVersionModel, latestDraftCreatedTime } =
        await fetchFormData(environmentParams, params.formId);

    const [blueprintId] = params.blueprintVersionId.split(".");

    const { blueprintModel } = await fetchBlueprintVersionData(
        params,
        blueprintId,
        params.blueprintVersionId,
    );
    const cacheFormFilePath = getGeneratedOutput(
        BLUEPRINT_APPLICATION_FORM_PREFIX,
        `${params.formVersionId}__${params.blueprintVersionId}`,
    );

    const formDataContent = JSON.parse(
        fs.readFileSync(cacheFormFilePath, "utf-8"),
    );

    const sessionID = generateSessionId();

    const savedForm = await getSavedFormData(
        formDataContent,
        params.formId,
        latestFormVersionModel,
        blueprintModel,
        sessionID,
    );

    const updatedTime = await updateForm(
        environmentParams,
        params.formId,
        formModel,
        latestDraftCreatedTime,
        savedForm,
        sessionID,
    );

    return {
        updatedTime,
        formName: formModel.name,
        formLink: `https://${params.hostname}/pantheon/form/${params.formId}/versions`,
    };
}
