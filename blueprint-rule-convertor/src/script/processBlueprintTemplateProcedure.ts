import { EnvironmentParameters } from "./components/utils";
import { BlueprintGenerator } from "@/generator";
import {
    ProcessBlueprintParameters,
    ProcessBlueprintResponse,
} from "./components/parameters";
import {
    fetchAnnotationDocuments,
    fetchBlueprintData,
    generateTemplateForm,
} from "./procedureUtils";

export async function processParams(
    params: ProcessBlueprintParameters,
): Promise<ProcessBlueprintResponse> {
    console.log("Process Params: ", params);

    const environmentParams: EnvironmentParameters = {
        hostname: params.hostname,
        bearerToken: params.bearerToken,
    };

    const {
        latestBlueprintVersionModel,
        blueprintVersionId,
        blueprintCatalaCode,
    } = await fetchBlueprintData(environmentParams, params.blueprintId);

    const annotationDataResponses = await fetchAnnotationDocuments(
        latestBlueprintVersionModel,
        environmentParams,
    );

    const generator = await BlueprintGenerator.create(
        blueprintCatalaCode,
        annotationDataResponses,
        latestBlueprintVersionModel.baseDocuments,
    );

    await generateTemplateForm(generator, blueprintVersionId);

    return {
        blueprintVersionId: blueprintVersionId,
    };
}
