import {
    ControlSignatureProcess,
    SubscriptionDocumentAction,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/subsciptionDocumentAction";

import { LogicContext } from "@/models";
import {
    MixcalcLogicAPI,
    SetSigningTypesAPI,
    UnitAPI,
} from "@/models/mixcalcAPI";
import { fromBaseBlueprintComponent } from "@/generator/blueprint";

export const fromSubscriptionDocumentAction =
    (context: LogicContext) =>
    (effect: SubscriptionDocumentAction): MixcalcLogicAPI => {
        if (effect instanceof ControlSignatureProcess) {
            return fromControlSignatureProcess(context)(effect);
        }

        console.error(
            `Unsupported SubscriptionDocumentAction effect type: ${effect.constructor.name}`,
        );
        return new UnitAPI();
    };

export const fromControlSignatureProcess =
    (context: LogicContext) =>
    (effect: ControlSignatureProcess): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        return new SetSigningTypesAPI(condition, effect.types);
    };
