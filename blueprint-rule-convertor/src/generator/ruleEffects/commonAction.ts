import { WidgetType } from "@/models/stargazer/openapi/interface";
import {
    BlockFurtherProcess,
    ClearValue,
    CommonAction,
    ControlDisplay,
    OutputMessage,
    SetValue,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/commonAction";

import {
    ActivateAPI,
    DeactivateAPI,
    FillAPI,
    HideAPI,
    MixcalcLogicAPI,
    SetEnableFilesAPI,
    SetErrorAPI,
    SetWarningAPI,
    ShowAPI,
    UnitAPI,
    UnsafeResetValueAPI,
} from "@/models/mixcalcAPI";
import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataSource } from "@/generator/blueprint/dataSource";
import * as WidgetTypeUtils from "@/utils/widgetType";
import {
    ControlDisplayAction,
    ControlOutputMessageAction,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/actionEnums";
import { fromStringConstant } from "@/generator/blueprint/blueprintElement";
import { EmptyValue } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import { StringConstant } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/metaElement";
import { fromBaseBlueprintComponent } from "@/generator/blueprint";
import { NOTCondition } from "@anduintransaction/pangea/models/intermediateRepresentation/condition";
import {
    getSingleDataSourceExpression,
    isDisableAndFillRule,
} from "@/utils/rule/ruleEffects";

export const fromCommonAction =
    (context: LogicContext) =>
    (effect: CommonAction): MixcalcLogicAPI => {
        if (effect instanceof ClearValue) {
            return fromClearValue(context)(effect);
        }

        if (effect instanceof SetValue) {
            return fromSetValue(context)(effect);
        }

        if (effect instanceof ControlDisplay) {
            return fromControlDisplay(context)(effect);
        }

        if (effect instanceof OutputMessage) {
            return fromOutputMessage(context)(effect);
        }

        if (effect instanceof BlockFurtherProcess) {
            return fromBlockFurtherProcess(context)(effect);
        }

        console.error(
            `Unsupported CommonAction effect type: ${effect.constructor.name}`,
        );

        return new UnitAPI();
    };

export const handleSingleValueEffect =
    (context: LogicContext) =>
    (effect: SetValue): MixcalcLogicAPI => {
        const dataToFill = effect.dataToFill;

        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        const value = getSingleDataSourceExpression(context)(dataToFill);

        if (!value) {
            return new UnitAPI();
        }

        return new FillAPI(condition, value, isDisableAndFillRule(effect));
    };
const handleMultipleValueEffect =
    (context: LogicContext) =>
    (effect: SetValue): MixcalcLogicAPI => {
        const dataSource = fromDataSource(context)(effect.dataToFill);
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );
        return new FillAPI(condition, dataSource, isDisableAndFillRule(effect));
    };
export const fromSetValue =
    (context: LogicContext) =>
    (effect: SetValue): MixcalcLogicAPI => {
        const key = effect.target.key;
        const widgetType = WidgetTypeUtils.fromBlueprintElement(effect.target);

        if (effect.dataToFill instanceof EmptyValue) {
            return new UnitAPI();
        }

        if (!widgetType) {
            console.error("Cannot resolve widget type for target " + key);
            return new UnitAPI();
        }

        if (WidgetTypeUtils.getSingleValueWidgets.includes(widgetType)) {
            return handleSingleValueEffect(context)(effect);
        }

        if (widgetType == WidgetType.FileGroup) {
            const dataSource = fromDataSource(context)(effect.dataToFill);
            const condition = fromBaseBlueprintComponent(context)(
                effect.triggerCondition,
            );
            return new SetEnableFilesAPI(condition, dataSource);
        }

        if (
            WidgetTypeUtils.getMultipleValueWidgets.includes(widgetType) ||
            widgetType == WidgetType.Repeatable
        ) {
            return handleMultipleValueEffect(context)(effect);
        }

        console.error(
            "SetValue is not applicable for Widget Type " + widgetType,
        );
        return new UnitAPI();
    };

export const fromClearValue =
    (context: LogicContext) =>
    (effect: ClearValue): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        return new UnsafeResetValueAPI(condition);
    };

export const fromControlDisplay =
    (context: LogicContext) =>
    (effect: ControlDisplay): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition instanceof NOTCondition
                ? effect.triggerCondition.inside
                : effect.triggerCondition,
        );

        switch (effect.action) {
            case ControlDisplayAction.Disable:
                return effect.triggerCondition instanceof NOTCondition
                    ? new ActivateAPI(condition)
                    : new DeactivateAPI(condition);
            case ControlDisplayAction.Hide:
                return effect.triggerCondition instanceof NOTCondition
                    ? new ShowAPI(condition)
                    : new HideAPI(condition);
            default:
                console.error("Unknown ControlDisplayAction: " + effect.action);
                return new UnitAPI();
        }
    };

export const fromOutputMessage =
    (context: LogicContext) =>
    (effect: OutputMessage): MixcalcLogicAPI => {
        if (effect.message instanceof EmptyValue) {
            return new UnitAPI();
        }

        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        if (effect.message instanceof StringConstant) {
            const messageParam = fromStringConstant(context)(effect.message);

            switch (effect.outputAction) {
                case ControlOutputMessageAction.ShowError:
                    return new SetErrorAPI(condition, messageParam);
                case ControlOutputMessageAction.ShowWarning:
                    return new SetWarningAPI(condition, messageParam);
                default:
                    console.error(
                        "Unknown ControlOutputMessageAction: " +
                            effect.outputAction,
                    );

                    return new UnitAPI();
            }
        }

        console.error(`Unknow message type: ${effect.message}`);
        return new UnitAPI();
    };

export const fromBlockFurtherProcess =
    (context: LogicContext) =>
    (effect: BlockFurtherProcess): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        return new SetErrorAPI(condition, new FormRuleExpression(`" "`));
    };
