import { CommonAction } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/commonAction";

import { LogicContext } from "@/models";
import { fromCommonAction } from "@/generator/ruleEffects/commonAction";
import {
    ExcludeKeyOptionGroupsAPI,
    MixcalcLogicAPI,
    UnitAPI,
} from "@/models/mixcalcAPI";
import { fromSelectionFieldEffect } from "./selectionFieldAction";
import { SubscriptionDocumentAction } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/subsciptionDocumentAction";
import { fromSubscriptionDocumentAction } from "./subsciptionDocumentAction";
import { SelectionFieldEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/selectionFieldAction";
import {
    DirectBlueprintEffect,
    FormRuleEffect,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import {
    ExternalMutualExclusiveOptions,
    RelativeFormRuleEffect,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/blueprintRuleEffects";
import { fromBaseBlueprintComponent } from "@/generator/blueprint";

export const fromRuleEffect =
    (context: LogicContext) =>
    (effect: FormRuleEffect): MixcalcLogicAPI => {
        if (effect instanceof CommonAction) {
            return fromCommonAction(context)(effect);
        }

        if (effect instanceof SelectionFieldEffect) {
            return fromSelectionFieldEffect(context)(effect);
        }

        if (effect instanceof SubscriptionDocumentAction) {
            return fromSubscriptionDocumentAction(context)(effect);
        }

        console.error(
            `Unsupported FormRuleEffect effect type: ${effect.constructor.name}`,
        );
        return new UnitAPI();
    };
export const fromExternalMutualExclusiveOptions =
    (context: LogicContext) =>
    (effect: ExternalMutualExclusiveOptions): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        const groups: Map<string, string[]>[] = effect.optionsGroups.map(
            (group) => {
                return group.reduce((acc, item) => {
                    const key = item.element.key;

                    if (acc.has(key)) {
                        acc.set(key, [
                            ...(acc.get(key) || []),
                            ...item.options,
                        ]);
                    } else {
                        acc.set(key, item.options);
                    }

                    return acc;
                }, new Map<string, string[]>());
            },
        );
        return new ExcludeKeyOptionGroupsAPI(condition, groups);
    };

export const fromDirectBlueprintEffect =
    (context: LogicContext) =>
    (effect: DirectBlueprintEffect): MixcalcLogicAPI => {
        if (effect instanceof RelativeFormRuleEffect) {
            return fromRuleEffect(context)(effect.formRuleEffect);
        }

        if (effect instanceof ExternalMutualExclusiveOptions) {
            return fromExternalMutualExclusiveOptions(context)(effect);
        }

        console.error(
            `Unsupported FormRuleEffect effect type: ${effect.constructor.name}`,
        );

        return new UnitAPI();
    };
