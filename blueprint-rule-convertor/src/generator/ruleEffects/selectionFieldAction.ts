import { WidgetType } from "@/models/stargazer/openapi/interface";
import {
    AddSelectionToValue,
    ControlSelectableOptions,
    MutualExclusiveOptions,
    RemoveSelectionFromValue,
    SelectionFieldEffect,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/formRuleEffects/selectionFieldAction";
import { ElementOptions } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import { ControlDisplayAction } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/actionEnums";

import {
    AddDeactivatedOptionsAPI,
    EnableFilesAPI,
    ExcludeOptionGroupsAPI,
    FillAPI,
    MixcalcLogicAPI,
    UnitAPI,
    UnsafeDeselectOptionsAPI,
} from "@/models/mixcalcAPI";
import { LogicContext } from "@/models";
import { fromDataSource } from "@/generator/blueprint/dataSource";
import * as WidgetTypeUtils from "@/utils/widgetType";
import { fromBaseBlueprintComponent } from "@/generator/blueprint";
import {
    getSingleDataSourceExpression,
    isDisableAndFillRule,
} from "@/utils/rule/ruleEffects";

export const fromSelectionFieldEffect =
    (context: LogicContext) =>
    (effect: SelectionFieldEffect): MixcalcLogicAPI => {
        if (effect instanceof AddSelectionToValue) {
            return fromAddSelectionToValue(context)(effect);
        }

        if (effect instanceof RemoveSelectionFromValue) {
            return fromRemoveSelectionFromValue(context)(effect);
        }

        if (effect instanceof ControlSelectableOptions) {
            return fromControlSelectableOptions(context)(effect);
        }

        if (effect instanceof MutualExclusiveOptions) {
            return fromMutualExclusiveOptions(context)(effect);
        }

        console.error(
            `Unsupported SelectionFieldEffect effect type: ${effect.constructor.name}`,
        );
        return new UnitAPI();
    };
export const handleSingleValueEffect =
    (context: LogicContext) =>
    (effect: AddSelectionToValue): MixcalcLogicAPI => {
        const dataToFill = new ElementOptions(effect.target, effect.options);
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        const value = getSingleDataSourceExpression(context)(dataToFill);

        if (!value) {
            return new UnitAPI();
        }

        return new FillAPI(condition, value, isDisableAndFillRule(effect));
    };
export const fromAddSelectionToValue =
    (context: LogicContext) =>
    (effect: AddSelectionToValue): MixcalcLogicAPI => {
        const key = effect.target.key;
        const dataToFill = new ElementOptions(effect.target, effect.options);
        const dataSource = fromDataSource(context)(dataToFill);
        const widgetType = WidgetTypeUtils.fromBlueprintElement(effect.target);
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        if (effect.options.length == 0) {
            return new UnitAPI();
        }

        if (!widgetType) {
            console.error("Cannot resolve widget type for target " + key);
            return new UnitAPI();
        }

        if (WidgetTypeUtils.getSingleValueWidgets.includes(widgetType)) {
            console.error(
                "AddSelectionToValue should not be applicable for Single Value Widget Type " +
                    widgetType,
            );
            return handleSingleValueEffect(context)(effect);
        }

        if (widgetType == WidgetType.FileGroup) {
            return new EnableFilesAPI(condition, dataSource);
        }

        if (WidgetTypeUtils.getMultipleSelectionWidgets.includes(widgetType)) {
            return new FillAPI(
                condition,
                dataSource,
                isDisableAndFillRule(effect),
            );
        }

        return new UnitAPI();
    };
export const fromRemoveSelectionFromValue =
    (context: LogicContext) =>
    (effect: RemoveSelectionFromValue): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        return new UnsafeDeselectOptionsAPI(condition, effect.options);
    };

export const fromControlSelectableOptions =
    (context: LogicContext) =>
    (effect: ControlSelectableOptions): MixcalcLogicAPI => {
        switch (effect.controlAction) {
            case ControlDisplayAction.Disable:
                const condition = fromBaseBlueprintComponent(context)(
                    effect.triggerCondition,
                );

                return new AddDeactivatedOptionsAPI(
                    condition,
                    effect.controlOptions,
                );
            case ControlDisplayAction.Hide:
                console.error(
                    "ControlSelectableOptions does not support Hide action",
                );
                return new UnitAPI();
            default:
                console.error(
                    "Unknown ControlDisplayAction: " + effect.controlAction,
                );
                return new UnitAPI();
        }
    };

export const fromMutualExclusiveOptions =
    (context: LogicContext) =>
    (effect: MutualExclusiveOptions): MixcalcLogicAPI => {
        const condition = fromBaseBlueprintComponent(context)(
            effect.triggerCondition,
        );

        return new ExcludeOptionGroupsAPI(condition, [
            effect.optionsGroup1,
            effect.optionsGroup2,
        ]);
    };
