import $ from "lodash";
import * as PantheonModel from "@/models/stargazer/";
import { WidgetType } from "@/models/stargazer/openapi/interface";

import {
    PdfEmbeddedFileFieldImpl,
    PdfFieldImpl,
    PdfMultipleCheckboxesFieldImpl,
    PdfParagraphFieldImpl,
    PdfRadioGroupFieldImpl,
    PdfSignatureFieldImpl,
    PdfTextFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";
import {
    PdfFieldGroupImpl,
    PdfGroupFieldImpl,
    PdfPageFieldImpl,
    PdfRepeatableFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfGroup";
import { FormContext, FormElementIdSchema, FormElementIdUIs } from "@/models";
import { groupFormElementIdSchema } from "./utils";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import {
    PdfTextBlockImpl,
    PdfTextLabelImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/textHighlight";
import { PdfFieldOptionImpl } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfFieldOption";
import { toTitleCaseAlphanumeric } from "@/utils/common";
import { getErrorFormElementIdSchema } from "@/utils/formData/schema";
import { CombineMappingId } from "@/models/matchingId/blueprintMappingKey";
import * as SchemaUtils from "@/utils/formData/schema";

export interface FieldContext {
    schemaTitleDict: Map<string, string>;
    pdfDocument: PDFDocument;
    combineMappingId: CombineMappingId | undefined;
    attachChildrenFields: boolean;
}

const getDefaultFormattedText = (field: PdfFieldImpl) =>
    `<p>Please specify ${field.name}: <span style="color: rgb(219, 55, 55);">*</span></p>`;

const getTextHighlight =
    (context: FieldContext) =>
    (field: PdfFieldImpl): string => {
        const textHighlights = Array.from(
            context.pdfDocument.textHighlights.values(),
        )
            .filter((textHighlight) => {
                if (textHighlight instanceof PdfTextLabelImpl) {
                    return textHighlight.value.labelOf == field.id;
                }

                return false;
            })
            .map((textHighlight) => {
                if (textHighlight instanceof PdfTextBlockImpl) {
                    return `Unknow block content ${textHighlight.id}`;
                }

                if (textHighlight instanceof PdfTextLabelImpl) {
                    return Object.values(textHighlight.value.content)[1];
                }
                return undefined;
            })
            .filter((value) => !!value);

        if (textHighlights.length == 0) {
            return field instanceof PdfFieldOptionImpl
                ? field.name
                : getDefaultFormattedText(field);
        }

        return textHighlights.join("\n\n");
    };

const fromPdfTextField =
    (context: FieldContext) =>
    (field: PdfTextFieldImpl): FormElementIdSchema => {
        const schema = {
            title: context.schemaTitleDict.get(field.name) || field.name,
            getName: PantheonModel.SchemaTypes.String,
        } as PantheonModel.StringSchema;

        const uiSchema = {
            "ui:formattedText": getTextHighlight(context)(field),
            "ui:required": "This field is required",
            "ui:marginBottom": "12px",
            "ui:pdfMapping": context.combineMappingId ? [] : [field.name],
            "ui:widget": WidgetType.TextBox,
        } as FormElementIdUIs;

        return new FormElementIdSchema(field.name, schema, {
            [field.name]: uiSchema,
        });
    };

const fromPdfSignatureField =
    (context: FieldContext) =>
    (field: PdfSignatureFieldImpl): FormElementIdSchema => {
        const schema = {
            title: context.schemaTitleDict.get(field.name) || field.name,
            getName: PantheonModel.SchemaTypes.Null,
        } as PantheonModel.NullSchema;

        const uiSchema = {
            "ui:signatureSigner": "Signature Signer",
            "ui:formattedText": "Signature",
            "ui:marginBottom": "12px",
            "ui:signature": [
                {
                    mapping: field.name,
                    signatureType: "Signature",
                    isOptional: false,
                },
            ],
            "ui:widget": WidgetType.Signature,
        } as FormElementIdUIs;

        return new FormElementIdSchema(field.name, schema, {
            [field.name]: uiSchema,
        });
    };

const fromPdfEmbeddedFileField =
    (context: FieldContext) =>
    (field: PdfEmbeddedFileFieldImpl): FormElementIdSchema => {
        const schema = {
            title: context.schemaTitleDict.get(field.name) || field.name,
            getName: PantheonModel.SchemaTypes.Null,
        } as PantheonModel.NullSchema;

        const uiSchema = {
            "ui:embeddedPdf": field.value.fileName,
            "ui:formattedText": WidgetType.Pdf,
            "ui:height": "600pt",
            "ui:marginBottom": "12px",
            "ui:pdfMapping": [field.name],
            "ui:pdfCutInfo": {
                start: field.value.startPageIndex,
                end: field.value.endPageIndex,
                fileId: field.value.id,
                reviewingFileId: context.pdfDocument.documentId,
            },
            "ui:widget": WidgetType.Pdf,
        } as FormElementIdUIs;

        return new FormElementIdSchema(field.name, schema, {
            [field.name]: uiSchema,
        });
    };

const fromPdfMultipleCheckboxesField =
    (context: FieldContext) =>
    (field: PdfMultipleCheckboxesFieldImpl): FormElementIdSchema => {
        const schema = {
            title: context.schemaTitleDict.get(field.name) || field.name,
            componentType: {
                componentType: {
                    title: "String Value",
                    getName: PantheonModel.SchemaTypes.String,
                } as PantheonModel.StringSchema,
                values: field.getChildrenKeys(),
                getName: PantheonModel.SchemaTypes.Enum,
            } as PantheonModel.EnumSchema,
            getName: PantheonModel.SchemaTypes.Array,
        } as PantheonModel.ArraySchema;

        const uiSchema = {
            "ui:required": "This field is required",
            "ui:formattedText": getTextHighlight(context)(field),
            "ui:marginBottom": "12px",
            "ui:pdfMapping": context.combineMappingId ? [] : [field.name],
            "ui:multipleOption": {
                options: field.parent.getChildrenFields().map((option) => {
                    return [
                        option.name,
                        {
                            pdfMapping: context.combineMappingId
                                ? ""
                                : option.name,
                            formattedText: getTextHighlight(context)(option),
                        },
                    ];
                }),
            },
            "ui:widget": WidgetType.MultipleCheckbox,
        } as FormElementIdUIs;

        return new FormElementIdSchema(field.name, schema, {
            [field.name]: uiSchema,
        });
    };

const fromPdfRadioGroupField =
    (context: FieldContext) =>
    (field: PdfRadioGroupFieldImpl): FormElementIdSchema => {
        const schema = {
            componentType: {
                title: context.schemaTitleDict.get(field.name) || field.name,
                getName: PantheonModel.SchemaTypes.String,
            } as PantheonModel.StringSchema,
            values: field.getChildrenKeys(),
            getName: PantheonModel.SchemaTypes.Enum,
        } as PantheonModel.EnumSchema;

        const uiSchema = {
            "ui:required": "This field is required",
            "ui:formattedText": getTextHighlight(context)(field),
            "ui:marginBottom": "12px",
            "ui:pdfMapping": context.combineMappingId ? [] : [field.name],
            "ui:multipleOption": {
                options: field.parent.getChildrenFields().map((option) => {
                    return [
                        option.name,
                        {
                            pdfMapping: context.combineMappingId
                                ? ""
                                : option.name,
                            formattedText: getTextHighlight(context)(option),
                        },
                    ];
                }),
            },
            "ui:widget": WidgetType.Radio,
        } as FormElementIdUIs;

        return new FormElementIdSchema(field.name, schema, {
            [field.name]: uiSchema,
        });
    };

const fromPdfChildrenFields =
    (context: FieldContext) =>
    (field: PdfFieldGroupImpl<PdfFieldImpl>): FormElementIdSchema => {
        const childrenFieldSchemas = context.attachChildrenFields
            ? field.parent.getChildrenFields().map(fromPdfField(context))
            : [];

        const widgetType =
            field instanceof PdfGroupFieldImpl
                ? WidgetType.Group
                : field instanceof PdfPageFieldImpl
                  ? WidgetType.Page
                  : field instanceof PdfRepeatableFieldImpl
                    ? WidgetType.Repeatable
                    : null;

        if (!widgetType) {
            console.error(
                `widgetType must be page, group or repeatable. Field: ${field.name}`,
            );
        }

        const parentSchema = groupFormElementIdSchema(FormContext.DefaultValue)(
            field.name,
            childrenFieldSchemas,
            widgetType || WidgetType.Group,
            field.name,
        );

        return new FormElementIdSchema(field.name, parentSchema.schema, {
            ...parentSchema.uiSchema,
            [field.name]: {
                ...parentSchema.uiSchema[field.name],
                "ui:pdfMapping": [field.name],
            },
        });
    };
const fromPdfGroupField =
    (context: FieldContext) =>
    (field: PdfGroupFieldImpl): FormElementIdSchema => {
        return fromPdfChildrenFields(context)(field);
    };

const fromPdfPageField =
    (context: FieldContext) =>
    (field: PdfPageFieldImpl): FormElementIdSchema => {
        return fromPdfChildrenFields(context)(field);
    };

const fromPdfRepeatableField =
    (context: FieldContext) =>
    (field: PdfRepeatableFieldImpl): FormElementIdSchema => {
        const fieldSchema = fromPdfChildrenFields(context)(field);

        const schema = {
            title: context.schemaTitleDict.get(field.name) || field.name,
            componentType: fieldSchema.schema,
            getName: PantheonModel.SchemaTypes.Array,
        } as PantheonModel.ArraySchema;

        const uiSchema = {
            "ui:formattedText": WidgetType.Repeatable,
            "ui:marginBottom": "12px",
            "ui:maxLength": 1,
            "ui:widget": WidgetType.Repeatable,
            "ui:pdfMapping": [field.name],
        } as FormElementIdUIs;

        const uiSchemaMap = { ...fieldSchema.uiSchema, [field.name]: uiSchema };

        return new FormElementIdSchema(field.name, schema, uiSchemaMap);
    };

const updateCombineMappingTag =
    (context: FieldContext) =>
    (fieldSchema: FormElementIdSchema): FormElementIdSchema => {
        const extendedTitle = context.combineMappingId
            ? context.combineMappingId.buildTag()
            : undefined;

        if (extendedTitle) {
            return fieldSchema.appendTitle(
                `${extendedTitle} - Blueprint - To-review`,
            );
        }

        return fieldSchema;
    };

export const fromPdfField =
    (context: FieldContext) =>
    (field: PdfFieldImpl): FormElementIdSchema => {
        if (field instanceof PdfTextFieldImpl) {
            return updateCombineMappingTag(context)(
                fromPdfTextField(context)(field),
            );
        }

        if (field instanceof PdfSignatureFieldImpl) {
            return fromPdfSignatureField(context)(field);
        }

        if (field instanceof PdfEmbeddedFileFieldImpl) {
            return fromPdfEmbeddedFileField(context)(field);
        }

        if (field instanceof PdfMultipleCheckboxesFieldImpl) {
            return updateCombineMappingTag(context)(
                fromPdfMultipleCheckboxesField(context)(field),
            );
        }

        if (field instanceof PdfRadioGroupFieldImpl) {
            return updateCombineMappingTag(context)(
                fromPdfRadioGroupField(context)(field),
            );
        }

        if (field instanceof PdfGroupFieldImpl) {
            return fromPdfGroupField(context)(field);
        }

        if (field instanceof PdfPageFieldImpl) {
            return fromPdfPageField(context)(field);
        }

        if (field instanceof PdfRepeatableFieldImpl) {
            return fromPdfRepeatableField(context)(field);
        }

        if (field instanceof PdfParagraphFieldImpl) {
            return fromPdfParagraphField(context)(field);
        }

        return getErrorFormElementIdSchema(
            field.id,
            `Unknow type of PdfFieldImpl: ${field.name}`,
        );
    };

const fromPdfParagraphField =
    (_: FieldContext) =>
    (text: PdfParagraphFieldImpl): FormElementIdSchema => {
        const title = `ParagraphField ${text.id}`;

        const schema = {
            title: title,
            getName: PantheonModel.SchemaTypes.Null,
        } as PantheonModel.NullSchema;

        const uiSchema = {
            "ui:formattedText": $.get(text.value, "content[1]"),
            "ui:pdfMapping": [text.name],
            "ui:required": "This text is required",
            "ui:marginBottom": "12px",
            "ui:widget": WidgetType.Paragraph,
        } as FormElementIdUIs;

        return new FormElementIdSchema(toTitleCaseAlphanumeric(title), schema, {
            [toTitleCaseAlphanumeric(title)]: uiSchema,
        });
    };
