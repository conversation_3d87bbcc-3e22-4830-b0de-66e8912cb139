import $ from "lodash";
import * as PantheonModel from "@/models/stargazer/";

import {
    PdfTextBlockImpl,
    PdfTextLabelImpl,
    TextHighlightImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/textHighlight";
import { FieldContext } from "./pdfField";
import { FormContext, FormElementIdSchema, FormElementIdUIs } from "@/models";
import { WidgetType } from "@/models/stargazer/openapi/interface";
import { toTitleCaseAlphanumeric } from "@/utils/common";
import { groupFormElementIdSchema } from "./utils";
import { getErrorFormElementIdSchema } from "@/utils/formData/schema";

const fromPdfTextBlock =
    (_: FieldContext) =>
    (text: PdfTextBlockImpl): FormElementIdSchema => {
        const title = `Paragraph ${text.id}`;
        const schema = {
            title: title,
            getName: PantheonModel.SchemaTypes.Null,
        } as PantheonModel.NullSchema;

        const uiSchema = {
            "ui:formattedText": `Unable to retrieve content of block ${text.id}`,
            "ui:required": "This text is required",
            "ui:marginBottom": "12px",
            "ui:widget": WidgetType.Paragraph,
        } as FormElementIdUIs;

        return new FormElementIdSchema(toTitleCaseAlphanumeric(title), schema, {
            [toTitleCaseAlphanumeric(title)]: uiSchema,
        });
    };

const fromPdfTextLabel =
    (context: FieldContext) =>
    (text: PdfTextLabelImpl): FormElementIdSchema => {
        const title = `TextHighlight ${text.id}`;
        const label = $.get(text.value, ["content[1]"]);

        if (label) {
            const schema = {
                title: title,
                getName: PantheonModel.SchemaTypes.Null,
            } as PantheonModel.NullSchema;

            const uiSchema = {
                "ui:formattedText": label,
                "ui:required": "This text is required",
                "ui:marginBottom": "12px",
                "ui:widget": WidgetType.Paragraph,
            } as FormElementIdUIs;

            return new FormElementIdSchema(
                toTitleCaseAlphanumeric(title),
                schema,
                {
                    [toTitleCaseAlphanumeric(title)]: uiSchema,
                },
            );
        }

        const textLabelSchemas = text
            .getChildrenFields()
            .map((text) => fromTextHighlight(context)(text));

        return groupFormElementIdSchema(FormContext.DefaultValue)(
            title,
            textLabelSchemas,
        );
    };

const fromTextHighlight =
    (context: FieldContext) =>
    (text: TextHighlightImpl): FormElementIdSchema => {
        if (text instanceof PdfTextBlockImpl) {
            return fromPdfTextBlock(context)(text);
        }

        if (text instanceof PdfTextLabelImpl) {
            return fromPdfTextLabel(context)(text);
        }

        return getErrorFormElementIdSchema(
            text.id,
            `Unknow type of TextHighlightImpl ${text.id}`,
        );
    };
