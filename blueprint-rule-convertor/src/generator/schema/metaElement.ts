import * as PantheonModel from "@/models/stargazer/";
import * as OpenApiInterface from "@/models/stargazer/openapi/interface";
import {
    FormUIInputType,
    ParagraphStyle,
} from "@/models/stargazer/openapi/interface";
import { FormElementIdSchema, FormElementIdUIs } from "@/models";
import {
    ParagraphMessage,
    GatingQuestion,
    FileGroup,
    BlueprintSignature,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/metaElement";
import { tryParseEnumFromStringValue } from "@/utils/common";
import { getErrorFormElementIdSchema } from "@/utils/formData/schema";

const getWellType = (value: any): ParagraphStyle => {
    const obj = value as Map<string, object>;
    const str = Object.keys(obj)[0];
    const groupMap = new Map([
        ["Blue", ParagraphStyle.Blue],
        ["Default", ParagraphStyle.Default],
        ["Orange", ParagraphStyle.Orange],
    ]);

    let parsedGroup = groupMap.get(str);
    if (!parsedGroup) {
        console.error("Unknown well type: " + str);
    }

    return parsedGroup || ParagraphStyle.Default;
};

const toFormWellType = (value: ParagraphStyle): string => {
    switch (value) {
        case ParagraphStyle.Blue:
            return "Info";
        case ParagraphStyle.Default:
            return "Default";
        case ParagraphStyle.Orange:
            return "Warning";
        default:
            console.error("Unknown well type: " + value);
            return ParagraphStyle.Default;
    }
};

export const fromParagraphMessage = (
    paragraph: ParagraphMessage,
): FormElementIdSchema => {
    const schema = {
        title: `[Blueprint] Paragraph`,
        getName: PantheonModel.SchemaTypes.Null,
    } as PantheonModel.Schema;

    const blueprintWellType = getWellType(paragraph.style);

    const uiSchema = {
        "ui:blueprintMetadataMapping": paragraph.key,
        "ui:formattedText": paragraph.label,
        "ui:marginBottom": "12px",
        "ui:widget": OpenApiInterface.WidgetType.Paragraph,
        "ui:wellType": toFormWellType(blueprintWellType),
    } as FormElementIdUIs;

    return new FormElementIdSchema(paragraph.key, schema, {
        [paragraph.key]: uiSchema,
    });
};
const getInputType = (value: any): FormUIInputType => {
    const obj = value as Map<string, object>;
    const str = Object.keys(obj)[0];
    const groupMap = new Map([
        ["Dropdown", FormUIInputType.Dropdown],
        ["MultipleCheckbox", FormUIInputType.MultipleCheckbox],
        ["MultipleSuggest", FormUIInputType.MultipleSuggest],
        ["Radio", FormUIInputType.Radio],
    ]);

    let parsedGroup = groupMap.get(str);
    if (!parsedGroup) {
        console.error("Unknown input type: " + str);
    }

    return parsedGroup || FormUIInputType.Radio;
};
export const fromGatingQuestion = (
    gatingQuestion: GatingQuestion,
): FormElementIdSchema => {
    const inputType = getInputType(gatingQuestion.inputType);

    const widgetType = tryParseEnumFromStringValue(
        OpenApiInterface.WidgetType,
        inputType,
    );

    const optionKeys = gatingQuestion.options?.map((value) => value.key);

    const schema =
        widgetType == OpenApiInterface.WidgetType.MultipleSuggest ||
        widgetType == OpenApiInterface.WidgetType.MultipleCheckbox
            ? ({
                  title: `[Blueprint] Gating Question`,
                  componentType: {
                      componentType: {
                          title: "String Value",
                          getName: PantheonModel.SchemaTypes.String,
                      } as PantheonModel.Schema,
                      values: optionKeys || [],
                      getName: PantheonModel.SchemaTypes.Enum,
                  } as PantheonModel.EnumSchema,
                  getName: PantheonModel.SchemaTypes.Array,
              } as PantheonModel.ArraySchema)
            : widgetType == OpenApiInterface.WidgetType.Dropdown ||
                widgetType == OpenApiInterface.WidgetType.Radio
              ? ({
                    componentType: {
                        title: `[Blueprint] Gating Question`,
                        getName: PantheonModel.SchemaTypes.String,
                    } as PantheonModel.StringSchema,
                    values: optionKeys,
                    getName: PantheonModel.SchemaTypes.Enum,
                } as PantheonModel.EnumSchema)
              : undefined;

    if (!schema) {
        return getErrorFormElementIdSchema(
            gatingQuestion.id,
            `Unknown Widget Type's gating question ${gatingQuestion.key}: ${gatingQuestion.inputType}`,
        );
    }

    const optionWidgets = gatingQuestion.options?.map((option) => {
        return [
            option.key,
            {
                pdfMapping: "",
                formattedText: option.label,
                blueprintMetadataMapping: option.key,
            },
        ];
    });

    const uiSchema = {
        "ui:blueprintMetadataMapping": gatingQuestion.key,
        "ui:formattedText": gatingQuestion.label,
        "ui:multipleOption": {
            options: optionWidgets,
        },
        "ui:required": "This field is required",
        "ui:marginBottom": "12px",
        "ui:widget": widgetType,
    } as FormElementIdUIs;

    return new FormElementIdSchema(gatingQuestion.key, schema, {
        [gatingQuestion.key]: uiSchema,
    });
};

export const fromFileGroup = (fileGroup: FileGroup): FormElementIdSchema => {
    const optionKeys = fileGroup.fileItems?.map((value) => value.key);

    const schema = {
        title: `[Blueprint] File Group - ${fileGroup.name}`,
        componentType: {
            componentType: {
                title: "Enum Values",
                getName: PantheonModel.SchemaTypes.String,
            } as PantheonModel.StringSchema,
            values: optionKeys,
            getName: PantheonModel.SchemaTypes.Enum,
        } as PantheonModel.EnumSchema,
        getName: PantheonModel.SchemaTypes.Array,
    } as PantheonModel.ArraySchema;

    const fileWidgets = Object.fromEntries(
        Array.from(fileGroup.fileItems || []).map((fileItem) => {
            return [
                fileItem.key,
                {
                    description: fileItem.name,
                    helpText: fileItem.helpText,
                    blueprintMetadataMapping: fileItem.key,
                },
            ];
        }),
    );

    const uiSchema = {
        "ui:blueprintMetadataMapping": fileGroup.key,
        "ui:formattedText": "FileGroup",
        "ui:marginBottom": "12px",
        "ui:supportingFileGroup": {
            description: fileGroup.name,
            helpText: fileGroup.helpText,
            files: fileWidgets,
        },
        "ui:widget": OpenApiInterface.WidgetType.FileGroup,
    } as FormElementIdUIs;

    return new FormElementIdSchema(fileGroup.key, schema, {
        [fileGroup.key]: uiSchema,
    });
};

export const fromBlueprintSignature = (
    signature: BlueprintSignature,
): FormElementIdSchema => {
    const schema = {
        title: `[Blueprint] Signature`,
        getName: PantheonModel.SchemaTypes.Null,
    } as PantheonModel.Schema;

    const uiSchema = {
        "ui:blueprintMetadataMapping": signature.key,
        "ui:formattedText": "Signature",
        "ui:marginBottom": "12px",
        "ui:signature": signature.mappings,
        "ui:signatureSigner": signature.signer,
        "ui:signatureType": signature.signatureType,
        "ui:widget": OpenApiInterface.WidgetType.Signature,
    } as FormElementIdUIs;

    return new FormElementIdSchema(signature.key, schema, {
        [signature.key]: uiSchema,
    });
};
