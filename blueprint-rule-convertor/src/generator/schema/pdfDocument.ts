import $ from "lodash";

import * as PantheonModel from "@/models/stargazer/";

import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import {
    PdfFieldImpl,
    PdfOptionGroupFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";
import { WidgetType } from "@/models/stargazer/openapi/interface";
import { FormContext, FormElementIdSchema, FormElementIdUIs } from "@/models";
import { FieldContext, fromPdfField } from "./pdfField";
import { groupFormElementIdSchema } from "./utils";
import {
    PdfFieldGroupImpl,
    PdfGroupFieldImpl,
    PdfPageFieldImpl,
    PdfRepeatableFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfGroup";

export const fromPagePdfObjects =
    (formContext: FormContext) =>
    (
        pageIndex: number | undefined,
        pdfObjects: Map<string, PdfFieldImpl>,
        pdfDocument: PDFDocument,
        groupWidgetType: WidgetType.Page | WidgetType.Group = WidgetType.Page,
    ): FormElementIdSchema | undefined => {
        if (pdfObjects.size == 0) {
            return undefined;
        }

        const name =
            groupWidgetType == WidgetType.Group
                ? `Other Annotations`
                : `Page Number ${pageIndex}`;

        const context: FieldContext = {
            schemaTitleDict: new Map(),
            pdfDocument: pdfDocument,
            combineMappingId: undefined,
            attachChildrenFields: false,
        };

        const getTopLeft = (field: PdfFieldImpl) => {
            const value: number =
                field instanceof PdfFieldGroupImpl ||
                field instanceof PdfOptionGroupFieldImpl
                    ? getTopLeft(field.parent.getChildrenFields()[0])
                    : $.get(field, "value.area.positionX.y", 0);

            return value;
        };

        const sortedPagePdfObjects = Array.from(pdfObjects.values()).sort(
            (a, b) => getTopLeft(a) - getTopLeft(b),
        );

        const fields: FormElementIdSchema[] = sortedPagePdfObjects.map(
            (pdfObject) => {
                const fieldName =
                    formContext.getAllGeneratedMappings().get(pdfObject.name) ||
                    `notfound_${pdfObject.name}`;

                const field = fromPdfField({
                    ...context,
                    combineMappingId:
                        formContext.generatedKeyToCombinedMappingId.get(
                            pdfObject.name,
                        ),
                })(pdfObject);

                return field.rename(fieldName);
            },
        );

        return groupFormElementIdSchema(formContext)(
            name,
            fields,
            groupWidgetType,
        );
    };
const getFileFieldSchema =
    (context: FormContext) =>
    (pdfDocument: PDFDocument): FormElementIdSchema | undefined => {
        const isExisted = context.allExistingNames.includes(pdfDocument.key);

        if (isExisted) {
            return undefined;
        }
        const fileName = context.documentIdToNameMap.get(
            pdfDocument.documentId,
        );

        const title = `File: ${fileName || pdfDocument.documentName}`;

        const schema = {
            title,
            getName: PantheonModel.SchemaTypes.Null,
        } as PantheonModel.NullSchema;

        const uiSchema = {
            "ui:blueprintMetadataMapping": pdfDocument.key,
            "ui:fileName": fileName || "",
            "ui:formattedText": WidgetType.File,
            "ui:marginBottom": "12px",
            "ui:widget": WidgetType.File,
        } as FormElementIdUIs;

        return new FormElementIdSchema(pdfDocument.key, schema, {
            [pdfDocument.key]: uiSchema,
        });
    };

export const fromFakeCheckboxMissingKeys = (
    context: FormContext,
): FormElementIdSchema | undefined => {
    const fields: FormElementIdSchema[] = Array.from(
        context.generatedKeyToFieldNameMaps.fakeCheckboxTargetMappings,
    ).map(([mappingId, fieldName]) => {
        const schema = {
            componentType: {
                title: mappingId.buildTag(),
                getName: PantheonModel.SchemaTypes.String,
            } as PantheonModel.StringSchema,
            values: [],
            getName: PantheonModel.SchemaTypes.Enum,
        } as PantheonModel.EnumSchema;

        const uiSchema = {
            "ui:formattedText": "",
            "ui:marginBottom": "12px",
            "ui:multipleOption": {
                options: [],
            },
            "ui:widget": WidgetType.Radio,
        } as FormElementIdUIs;

        return new FormElementIdSchema(fieldName, schema, {
            [fieldName]: uiSchema,
        });
    });

    if (fields.length == 0) {
        return undefined;
    }

    return groupFormElementIdSchema(context)(
        `FakeCheckbox Additional Fields`,
        fields,
        WidgetType.Page,
    );
};

export const fromPdfDocuments =
    (context: FormContext) =>
    (pdfDocuments: PDFDocument[]): FormElementIdSchema | undefined => {
        const fields: FormElementIdSchema[] = pdfDocuments
            .map((pdfDocument) => {
                const fileElement = getFileFieldSchema(context)(pdfDocument);
                const annotationFields = fromPdfDocument(context)(pdfDocument);

                const fields = [
                    ...(fileElement ? [fileElement] : []),
                    ...(annotationFields ? [annotationFields] : []),
                ];

                if (fields.length == 0) {
                    return undefined;
                }

                const name = `Document: ${pdfDocument.documentName}`;

                return groupFormElementIdSchema(context)(
                    name,
                    fields,
                    WidgetType.Group,
                );
            })
            .filter((v) => v != undefined);

        if (fields.length == 0) {
            return undefined;
        }

        return groupFormElementIdSchema(context)(
            `Annotation Documents`,
            fields,
            WidgetType.Group,
        );
    };

export const fromPdfDocument =
    (context: FormContext) =>
    (pdfDocument: PDFDocument): FormElementIdSchema | undefined => {
        const [pageObjects, undefinedPageObjects]: [
            Map<number, Map<string, PdfFieldImpl>>,
            Map<string, PdfFieldImpl>,
        ] = Array.from(pdfDocument.flattenPdfFields.entries()).reduce(
            ([pageObjects, undefinedPageObjects], [key, pdfObject]) => {
                if (!context.getAllGeneratedMappings().has(key)) {
                    return [pageObjects, undefinedPageObjects];
                }

                if (context.renderedMappingNames.includes(key)) {
                    return [pageObjects, undefinedPageObjects];
                }

                context.addRenderedMappingNames([key]);

                const pageIndex = pdfObject.getPageIndex();

                if (!pageIndex) {
                    const isGroupField =
                        pdfObject instanceof PdfGroupFieldImpl ||
                        pdfObject instanceof PdfPageFieldImpl ||
                        pdfObject instanceof PdfRepeatableFieldImpl;
                    if (!isGroupField)
                        console.error(
                            `Page Index is undefined at field ${key}`,
                        );

                    undefinedPageObjects.set(key, pdfObject);

                    return [pageObjects, undefinedPageObjects];
                } else {
                    const fields =
                        pageObjects.get(pageIndex) ||
                        new Map<string, PdfFieldImpl>();

                    fields.set(key, pdfObject);

                    pageObjects.set(pageIndex, fields);

                    return [pageObjects, undefinedPageObjects];
                }
            },
            [
                new Map<number, Map<string, PdfFieldImpl>>(),
                new Map<string, PdfFieldImpl>(),
            ],
        );

        const name = `Fields from PDF ${pdfDocument.key ? `@${pdfDocument.key}` : pdfDocument.documentName}`;

        const sortedPageObjects = Array.from(pageObjects.entries()).sort(
            ([a], [b]) => a - b,
        );

        const fields: FormElementIdSchema[] = sortedPageObjects
            .map(([pageIndex, objects]) => {
                return fromPagePdfObjects(context)(
                    pageIndex,
                    objects,
                    pdfDocument,
                );
            })
            .filter((v) => v != undefined);

        const undefinedPage = fromPagePdfObjects(context)(
            0,
            undefinedPageObjects,
            pdfDocument,
            WidgetType.Group,
        );

        const childrenFields = undefinedPage
            ? [...fields, undefinedPage]
            : fields;

        if (childrenFields.length == 0) {
            return undefined;
        }

        return groupFormElementIdSchema(context)(
            name,
            childrenFields,
            WidgetType.Group,
        );
    };
