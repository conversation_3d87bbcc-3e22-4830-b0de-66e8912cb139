import * as PantheonModel from "@/models/stargazer/";
import { WidgetType } from "@/models/stargazer/openapi/interface";
import { FormContext, FormElementIdSchema, FormElementIdUIs } from "@/models";
import { toTitleCaseAlphanumeric } from "@/utils/common";
import { getUniqueName } from "@/utils/formData/formSchema";

export const groupFormElementIdSchema =
    (formContext: FormContext) =>
    (
        name: string,
        fields: FormElementIdSchema[],
        widgetType:
            | WidgetType.Group
            | WidgetType.Page
            | WidgetType.Repeatable = WidgetType.Group,
        defaultAlias: string | undefined = undefined,
    ): FormElementIdSchema => {
        const schema = {
            fields: fields.map((field) => ({
                name: field.name,
                tpe: field.schema,
            })),
            title: name,
            getName: PantheonModel.SchemaTypes.Obj,
        } as PantheonModel.ObjSchema;

        const fieldName = defaultAlias || toTitleCaseAlphanumeric(name);
        const alias = getUniqueName(fieldName, formContext.allExistingNames);

        formContext.addExistingNames([alias]);

        const uiSchema = {
            "ui:marginBottom": "12px",
            "ui:widget": widgetType,
        };

        const uiSchemaMap: FormElementIdUIs = {
            ...{ [alias]: uiSchema },
            ...Object.fromEntries(
                fields.flatMap((field) => Object.entries(field.uiSchema)),
            ),
        };

        return new FormElementIdSchema(alias, schema, uiSchemaMap);
    };
