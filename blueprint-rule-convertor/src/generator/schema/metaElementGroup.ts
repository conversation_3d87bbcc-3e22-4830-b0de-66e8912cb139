import { WidgetType } from "@/models/stargazer/openapi/interface";
import { groupFormElementIdSchema } from "./utils";
import {
    BlueprintMetaFieldGroups,
    FormContext,
    FormElementIdSchema,
} from "@/models";

const getFieldSchemas =
    (context: FormContext) =>
    (fieldMap: Map<string, FormElementIdSchema>): FormElementIdSchema[] => {
        return Array.from(fieldMap)
            .map(([key, field]) => {
                const fieldName = context.getAllGeneratedMappings().get(key);

                if (!fieldName) {
                    return undefined;
                }
                return field.rename(fieldName);
            })
            .filter((value) => value != undefined);
    };

export const fromProccessedBlueprintInfo =
    (context: FormContext) =>
    (groups: BlueprintMetaFieldGroups): FormElementIdSchema | undefined => {
        const investorTypeFields = getFieldSchemas(context)(
            new Map(
                Array.from(groups.investorTypeFields).map(([key, field]) => [
                    field.ui.name,
                    field.ui,
                ]),
            ),
        );

        const paragraphFields = getFieldSchemas(context)(
            groups.paragraphFields,
        );
        const gatingQuestionFields = getFieldSchemas(context)(
            groups.gatingQuestionFields,
        );
        const fileGroupFields = getFieldSchemas(context)(
            groups.fileGroupFields,
        );
        const signatureFields = getFieldSchemas(context)(
            groups.signatureFields,
        );

        const fieldGroups = {
            "Blueprint InvestorType": investorTypeFields,
            "Blueprint Paragraphs": paragraphFields,
            "Blueprint Gating Questions": gatingQuestionFields,
            "Blueprint File Groups": fileGroupFields,
            "Blueprint Signatures": signatureFields,
        };

        const fieldGroupSchemas = Object.entries(fieldGroups)
            .map(([groupName, groupFields]) =>
                groupFields.length > 0
                    ? groupFormElementIdSchema(context)(
                          groupName,
                          groupFields,
                          WidgetType.Page,
                      )
                    : undefined,
            )
            .filter((value) => value != undefined);

        return fieldGroupSchemas.length > 0
            ? groupFormElementIdSchema(context)(
                  "Blueprint Meta Elements",
                  fieldGroupSchemas,
              )
            : undefined;
    };
