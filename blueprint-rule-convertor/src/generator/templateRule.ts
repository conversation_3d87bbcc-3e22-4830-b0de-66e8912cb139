import $ from "lodash";

import * as WidgetTypeUtils from "@/utils/widgetType";
import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import {
    ExternalMutualExclusiveOptions,
    RelativeFormRuleEffect,
} from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects/blueprintRuleEffects";
import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import {
    getFakeCheckboxRuleContent,
    renderFormRule,
    getKeyRuleContent,
} from "@/utils/rule/renderTemplateContent";
import { getConditionNames } from "@/utils/rule/renderTemplateUtils";
import { intersect } from "@/utils/common";
import {
    BlueprintMetaFieldGroups,
    FormRuleTemplate,
    LogicContext,
} from "@/models";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";

export const generateFormRuleTemplates = (
    effects: DirectBlueprintEffect[],
    blueprintMetadataGroups: BlueprintMetaFieldGroups,
    pdfDocuments: PDFDocument[],
): FormRuleTemplate[] => {
    // Initialize context
    const initContext = {
        processBlueprintInfo: blueprintMetadataGroups,
        processPdfDocuments: pdfDocuments,
        baseConditionNames: new Map(),
    };

    // Handle multiple target effects first
    const multipleTargetRules =
        handleMultipleTargetEffects(initContext)(effects);

    // Then handle single target effects, excluding keys already handled
    const keysToIgnore = multipleTargetRules.flatMap((rule) =>
        rule.blueprintRuleKeys.getAllFieldKeys(),
    );
    const singleTargetRules = handleSingleTargetEffects(initContext)(
        effects,
        keysToIgnore,
    );

    // Combine and return all rules
    return [...singleTargetRules, ...multipleTargetRules];
};

/**
 * Handles effects with single targets
 */
const handleSingleTargetEffects =
    (initContext: LogicContext) =>
    (
        allEffects: DirectBlueprintEffect[],
        ignoreKeys: string[],
    ): FormRuleTemplate[] => {
        // Get all single targets from RelativeFormRuleEffect `instances`
        const allSingleTargets = allEffects
            .filter((effect) => effect instanceof RelativeFormRuleEffect)
            .flatMap((effect) => effect.getTargets());

        // Get unique keys not in the ignore list
        const targetKeys = $.uniq(
            allSingleTargets.map((value) => value.key),
        ).filter((key) => !ignoreKeys.includes(key));

        // Process each target key
        return targetKeys
            .map((targetKey) => {
                try {
                    // Find effects targeting this key
                    const targetEffects = allEffects.filter(
                        (effect) =>
                            effect instanceof RelativeFormRuleEffect &&
                            effect.formRuleEffect.target.key === targetKey,
                    );
                    const target = allSingleTargets.find(
                        (target) => target.key === targetKey,
                    );

                    if (!target) {
                        throw Error("Cannot find target of key: " + targetKey);
                    }

                    const widget = WidgetTypeUtils.fromBlueprintElement(target);

                    // Create context with condition names
                    const context = {
                        ...initContext,
                        baseConditionNames:
                            getConditionNames(initContext)(targetEffects),
                    };

                    // Render form rule
                    return renderFormRule(context)(
                        targetKey,
                        widget,
                        targetEffects,
                    );
                } catch (e) {
                    console.error(e);
                    return null;
                }
            })
            .filter((value) => !!value);
    };

/**
 * Handles effects with multiple targets (mutual exclusive options)
 */
const handleMultipleTargetEffects =
    (initContext: LogicContext) =>
    (allEffects: DirectBlueprintEffect[]): FormRuleTemplate[] => {
        // Group targets from ExternalMutualExclusiveOptions
        const targetGroups = groupMutualExclusiveTargets(allEffects);

        // Process each group
        return targetGroups
            .map((targets) => {
                try {
                    const targetKeys = targets.map((target) => target.key);

                    // Map child effects by target key
                    const childEffects = mapChildEffectsByKey(
                        allEffects,
                        targetKeys,
                    );

                    // Get parent effects related to these targets
                    const parentEffects = getParentEffects(
                        allEffects,
                        targetKeys,
                    );

                    // Create context with condition names
                    const context = {
                        ...initContext,
                        baseConditionNames: getConditionNames(initContext)([
                            ...Array.from(childEffects.values()).flat(),
                            ...parentEffects,
                        ]),
                    };

                    // Get rule content for each key
                    const keyRuleContents = Array.from(
                        childEffects.entries(),
                    ).map(([key, effects]) =>
                        getKeyRuleContent(context)(key, effects),
                    );

                    // Generate fake checkbox rule content
                    return getFakeCheckboxRuleContent(context)(
                        parentEffects,
                        keyRuleContents,
                    );
                } catch (e) {
                    console.error(e);
                    return null;
                }
            })
            .filter((value) => !!value);
    };

/**
 * Groups targets from mutual exclusive options
 */
const groupMutualExclusiveTargets = (
    allEffects: DirectBlueprintEffect[],
): BlueprintElement[][] => {
    return allEffects.reduce((groups, effect) => {
        if (effect instanceof ExternalMutualExclusiveOptions) {
            const targets = effect.getTargets();
            if (targets.length > 0) {
                const existingGroupIndex = findExistingGroupIndex(
                    groups,
                    targets,
                );

                if (existingGroupIndex >= 0) {
                    // Merge with existing group
                    groups[existingGroupIndex] = [
                        ...groups[existingGroupIndex],
                        ...targets,
                    ];
                } else {
                    // Create new group
                    groups.push(targets);
                }
            }
        }
        return groups;
    }, [] as BlueprintElement[][]);
};

/**
 * Finds index of existing group that has overlap with targets
 */
const findExistingGroupIndex = (
    groups: BlueprintElement[][],
    targets: BlueprintElement[],
): number => {
    return groups.findIndex((group) =>
        group.some((item) => targets.some((target) => target.key === item.key)),
    );
};

/**
 * Maps child effects by target key
 */
const mapChildEffectsByKey = (
    allEffects: DirectBlueprintEffect[],
    targetKeys: string[],
): Map<string, RelativeFormRuleEffect[]> => {
    const childEffects = new Map(
        targetKeys.map((key) => [key, [] as RelativeFormRuleEffect[]]),
    );

    allEffects.forEach((effect) => {
        if (effect instanceof RelativeFormRuleEffect) {
            const key = effect.formRuleEffect.target.key;
            if (targetKeys.includes(key)) {
                const effects = childEffects.get(key) || [];
                childEffects.set(key, [...effects, effect]);
            }
        }
    });

    return childEffects;
};

/**
 * Gets parent effects related to target keys
 */
const getParentEffects = (
    allEffects: DirectBlueprintEffect[],
    targetKeys: string[],
): ExternalMutualExclusiveOptions[] => {
    return allEffects
        .map((effect) => {
            if (effect instanceof ExternalMutualExclusiveOptions) {
                const effectTargetKeys = effect
                    .getTargets()
                    .map((target) => target.key);
                const isRelative =
                    intersect(targetKeys, effectTargetKeys).length > 0;
                return isRelative ? effect : undefined;
            }
            return undefined;
        })
        .filter((effect): effect is ExternalMutualExclusiveOptions => !!effect);
};
