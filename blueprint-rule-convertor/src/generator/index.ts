import _ from "lodash";

import * as PantheonModel from "@/models/stargazer/";
import { GetBlueprintVersionCatalaCodeResponse } from "@/models/stargazer";
import { BaseDocument } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_version";
import { processBlueprintInfo } from "@/utils/formData/blueprintInfo";
import { BlueprintMetaFieldGroups, FormRuleTemplate } from "@/models";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import { processPdfDocuments } from "@/utils/formData/annotationInfo";

import { parseBIRFromJSON } from "@anduintransaction/pangea/index";
import {
    Blueprint,
    BlueprintAnalytic,
    StructuredRule,
} from "@anduintransaction/pangea/models/intermediateRepresentation";
import { extractAllEffects } from "@/utils/rule/generator";
import { DirectBlueprintEffect } from "@anduintransaction/pangea/models/intermediateRepresentation/ruleEffects";
import { generateBlueprintTemplateForm } from "@/generator/templateForm";
import { generateFormRuleTemplates } from "./templateRule";
import {
    BlueprintApplicationResult,
    BlueprintLine,
} from "@/models/formMapping";
import { applyForm } from "./formApplication";

export class BlueprintGenerator {
    private _pdfDocuments: PDFDocument[];
    private _parsedBlueprint: Blueprint;
    private _blueprintEffects: DirectBlueprintEffect[];
    private _blueprintMetadataGroups: BlueprintMetaFieldGroups;
    private _rawBlueprintData: GetBlueprintVersionCatalaCodeResponse;

    // cache data
    private _generatedTemplates: FormRuleTemplate[] | undefined;

    private constructor(
        blueprint: GetBlueprintVersionCatalaCodeResponse,
        annotationDataResponses: Map<
            string,
            PantheonModel.GetAnnotationDataResponse
        >,
        baseDocuments: BaseDocument[],
        parsedBlueprint: Blueprint,
    ) {
        this._pdfDocuments = processPdfDocuments(
            annotationDataResponses,
            baseDocuments,
        );

        this._parsedBlueprint = parsedBlueprint;

        this._blueprintEffects = extractAllEffects(this._parsedBlueprint);

        this._blueprintMetadataGroups = processBlueprintInfo(blueprint);

        this._rawBlueprintData = blueprint;
    }

    static async create(
        blueprint: GetBlueprintVersionCatalaCodeResponse,
        annotationDataResponses: Map<
            string,
            PantheonModel.GetAnnotationDataResponse
        >,
        baseDocuments: BaseDocument[],
    ): Promise<BlueprintGenerator> {
        const pdfDocuments = processPdfDocuments(
            annotationDataResponses,
            baseDocuments,
        );

        const parsedBlueprint = await parseBIRFromJSON(
            JSON.stringify(blueprint),
            pdfDocuments,
        );

        return new BlueprintGenerator(
            blueprint,
            annotationDataResponses,
            baseDocuments,
            parsedBlueprint,
        );
    }

    private _generateFormRuleTemplates(): FormRuleTemplate[] {
        const templates = generateFormRuleTemplates(
            this._blueprintEffects,
            this._blueprintMetadataGroups,
            this._pdfDocuments,
        );

        this._generatedTemplates = templates;

        return this._generatedTemplates;
    }

    getAllBlueprintLines(): BlueprintLine[] {
        const structuredRules: BlueprintLine[] = this._parsedBlueprint.scopes
            .map((scope): BlueprintLine[] => {
                return scope.allRules
                    .map((rule): BlueprintLine | undefined => {
                        if (rule instanceof StructuredRule) {
                            const ruleAction = rule.rawBlueprintAction
                                ? `__fn_${rule.rawBlueprintAction}`
                                : "";
                            const ruleBody =
                                rule.rawCodeBody?.rawBodyNode.text || "";
                            const fullRuleContent = ruleAction
                                ? `${ruleAction} ${ruleBody}`.trim()
                                : ruleBody;
                            return {
                                lineNum: rule.startLineNumber,
                                content: fullRuleContent,
                                isComment: false,
                                targets: _.uniq(
                                    rule.originalEffects
                                        .map((effect) => effect.getTargets())
                                        .flat()
                                        .map((ele) => ele.key),
                                ),
                            };
                        }
                        return undefined;
                    })
                    .filter((v): v is BlueprintLine => v !== undefined);
            })
            .flat();

        const commentItems: BlueprintLine[] =
            this._parsedBlueprint.comments.reduce(
                (acc, comment): BlueprintLine[] => {
                    acc.push({
                        lineNum: comment.startLineNumber,
                        content: comment.comments.join("\n"),
                        targets: [],
                        isComment: true,
                    });
                    return acc;
                },
                [] as BlueprintLine[],
            );

        return [...structuredRules, ...commentItems];
    }

    generateBlueprintTemplateForm(): PantheonModel.FormData {
        const formData = generateBlueprintTemplateForm(
            this.getFormRuleTemplates(),
            this._pdfDocuments,
            this._blueprintMetadataGroups,
        );

        return formData;
    }

    getFormRuleTemplates(): FormRuleTemplate[] {
        if (!this._generatedTemplates) {
            return this._generateFormRuleTemplates();
        }

        return this._generatedTemplates;
    }

    getParseAnalytic(): BlueprintAnalytic {
        return this._parsedBlueprint.analytic;
    }

    applyForm(
        formData: PantheonModel.FormData,
        addMissingField: boolean = false,
        addMissingRule: boolean = false,
    ): BlueprintApplicationResult {
        return applyForm(
            formData,
            this.getFormRuleTemplates(),
            this._blueprintMetadataGroups,
            this._pdfDocuments,
            this._rawBlueprintData.blueprintInfo,
            addMissingField,
            addMissingRule,
        );
    }
}
