import * as PantheonModel from "@/models/stargazer/";
import {
    FormContext,
    FormElementIdSchema,
    BlueprintMetaFieldGroups,
    FormRuleTemplate,
} from "@/models";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";
import { fromProccessedBlueprintInfo } from "@/generator/schema/metaElementGroup";
import {
    fromFakeCheckboxMissingKeys,
    fromPdfDocuments,
} from "@/generator/schema/pdfDocument";
import { groupFormElementIdSchema } from "@/generator/schema/utils";
import { GaiaLogicVersion } from "@/models/stargazer/openapi/interface";
import { renderTemplate } from "@/utils/rule/renderTemplateUtils";
import { createDocumentIdToNameMap } from "@/utils/rule/generator";

export const generateBlueprintTemplateForm = (
    templates: FormRuleTemplate[],
    pdfDocuments: PDFDocument[],
    blueprintMetadataGroups: BlueprintMetaFieldGroups,
    uploadedPdfMap: PantheonModel.MapFileIdExtractedPdf = {},
    documentIdMap: Map<string, string> = new Map(),
): PantheonModel.FormData => {
    const documentIdToNameMap = createDocumentIdToNameMap(
        documentIdMap,
        uploadedPdfMap,
    );

    const mappings = new Set(
        templates.flatMap((rule) => rule.blueprintRuleKeys.getAllFieldKeys()),
    );

    const formContext = new FormContext(
        {
            combinedMappings: new Map(),
            fakeCheckboxTargetMappings: new Map(),
            blueprintMappings: new Map(
                [...mappings].map((mapping) => [mapping, mapping]),
            ),
        },
        uploadedPdfMap,
        documentIdToNameMap,
    );

    const form = generateFormSchema(formContext)(
        blueprintMetadataGroups,
        pdfDocuments,
    );

    const rules = templates.map((template) => renderTemplate(template));

    // Calculate form data by combining schema, UI schema and validation rules
    const formData: PantheonModel.FormData = {
        form: {
            namespaceFormSchemaMap: {
                main: {
                    schema: {
                        fields: form
                            ? [
                                  {
                                      name: form.name,
                                      tpe: form.schema,
                                  },
                              ]
                            : [],
                        getName: PantheonModel.SchemaTypes.Obj,
                    },
                    uiSchema: form ? form.uiSchema : {},
                },
            },
            rules: rules,
            defaultNamespace: "main",
            libs: [],
            triggerRuleByProperty: true,
            gaiaLogicVersion: GaiaLogicVersion.V2,
        },
        uploadedPdf: uploadedPdfMap,
        uploadedDocx: {},
        embeddedPdf: {},
        associatedLinks: {},
    };

    return formData;
};

export const generateFormSchema =
    (formContext: FormContext) =>
    (
        blueprintMetaFieldGroups: BlueprintMetaFieldGroups,
        pdfDocuments: PDFDocument[],
    ): FormElementIdSchema | undefined => {
        const fields: FormElementIdSchema[] = [
            fromProccessedBlueprintInfo(formContext)(blueprintMetaFieldGroups),
            fromPdfDocuments(formContext)(pdfDocuments),
            fromFakeCheckboxMissingKeys(formContext),
        ].filter((value) => value != undefined);

        return fields.length > 0
            ? groupFormElementIdSchema(formContext)(
                  "Blueprint Rule Generator",
                  fields,
              )
            : undefined;
    };
