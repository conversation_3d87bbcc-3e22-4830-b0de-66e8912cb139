import { MappingRelationship } from "@/models/formMapping";
import { createInverseFieldMapping } from "./mappingAnalysis";
import { BlueprintMappingKey } from "@/models/matchingId/blueprintMappingKey";
import * as MatchingIdUtils from "@/utils/application/matchingIdOperator";
import { FormElementIdUISchema } from "@/models";
import { FieldName } from "@/models/matchingId/formElementId";

/**
 * Analyzes cardinality relationships between mappings and field elements
 * @param mappingToFieldNamesMap Map of mapping keys to their associated field elements
 * @param fieldNameToMappingsMap Map of field elements to their associated mapping keys
 * @returns Object containing cardinality analysis results
 */
export function getMappingCardinalityCategories(
    mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>,
    fieldNameToMappingsMap: Map<FieldName, BlueprintMappingKey[]>,
): CardinalityCategories {
    // Get all fieldNames that have only one mapping and that mapping only maps to one field
    const oneOneMappings: [FieldName, BlueprintMappingKey][] = [];

    // Get all fieldNames that have more than 1 mappings and each mapping only maps to one field
    const oneManyMappings: [FieldName, BlueprintMappingKey[]][] = [];

    // Get all fieldNames that have only one mapping and that mapping maps to more than one field
    const manyOneMappings: [FieldName[], BlueprintMappingKey][] = [];

    // The other cases not listed above
    const manyManyMappings: [FieldName[], BlueprintMappingKey[]][] = [];

    // Process each field to categorize its mapping relationship
    Array.from(fieldNameToMappingsMap).forEach(([element, mappings]) => {
        if (mappings.length === 1) {
            const mapping = mappings[0];
            const fieldsWithThisMapping =
                mappingToFieldNamesMap.get(mapping) || [];

            if (fieldsWithThisMapping.length === 1) {
                // One-to-one: Field has one mapping, and that mapping only maps to this field
                oneOneMappings.push([element, mapping]);
            } else {
                // Many-to-one: Field has one mapping, but that mapping maps to multiple fields
                // Check if this mapping is already in manyOneMappings
                const existingIndex = manyOneMappings.findIndex(
                    ([_, m]) => m === mapping,
                );
                if (existingIndex >= 0) {
                    // Add this element to the existing entry
                    manyOneMappings[existingIndex][0] = MatchingIdUtils.uniq([
                        ...manyOneMappings[existingIndex][0],
                        element,
                    ]);
                } else {
                    const allElementsMappings = MatchingIdUtils.uniq(
                        fieldsWithThisMapping.flatMap(
                            (fieldElement) =>
                                fieldNameToMappingsMap.get(fieldElement) || [],
                        ),
                    );
                    if (allElementsMappings.length == 1) {
                        // Create a new entry
                        manyOneMappings.push([fieldsWithThisMapping, mapping]);
                    } else {
                        manyManyMappings.push([
                            fieldsWithThisMapping,
                            allElementsMappings,
                        ]);
                    }
                }
            }
        } else if (mappings.length > 1) {
            // Field has multiple mappings

            // Check if all mappings are one-to-one (each mapping only maps to this field)
            const allMappingsAreOneToOne = mappings.every(
                (mapping) =>
                    (mappingToFieldNamesMap.get(mapping) || []).length === 1,
            );

            if (allMappingsAreOneToOne) {
                // One-to-many: Field has multiple mappings, each mapping only maps to this field
                oneManyMappings.push([element, mappings]);
            } else {
                // Many-to-many: Field has multiple mappings, at least one mapping maps to multiple fields
                // Collect all mappings that map to multiple fields
                const manyMappings = mappings.filter(
                    (mapping) =>
                        (mappingToFieldNamesMap.get(mapping) || []).length > 1,
                );

                manyManyMappings.push([
                    // All elements that share at least one mapping with this field
                    MatchingIdUtils.uniq(
                        manyMappings.flatMap(
                            (mapping) =>
                                mappingToFieldNamesMap.get(mapping) || [],
                        ),
                    ),
                    manyMappings,
                ]);
            }
        }
    });

    const mergedManyManyMappings: [FieldName[], BlueprintMappingKey[]][] =
        manyManyMappings.reduce(
            (acc, [elements, mappings]) => {
                // Try to find an existing group that shares elements or mappings
                const existingGroupIndex = acc.findIndex(
                    ([existingElements, existingMappings]) => {
                        const hasCommonElements = elements.some((element) =>
                            existingElements.some((existing) =>
                                existing.equals(element),
                            ),
                        );
                        const hasCommonMappings = mappings.some((mapping) =>
                            existingMappings.includes(mapping),
                        );
                        return hasCommonElements || hasCommonMappings;
                    },
                );

                if (existingGroupIndex === -1) {
                    // No overlapping group found, create new group
                    acc.push([elements, mappings]);
                } else {
                    // Merge with existing group
                    const [existingElements, existingMappings] =
                        acc[existingGroupIndex];
                    acc[existingGroupIndex] = [
                        MatchingIdUtils.uniq([
                            ...existingElements,
                            ...elements,
                        ]),
                        MatchingIdUtils.uniq([
                            ...existingMappings,
                            ...mappings,
                        ]),
                    ];
                }
                return acc;
            },
            [] as [FieldName[], BlueprintMappingKey[]][],
        );

    return {
        oneOneItems: oneOneMappings,
        oneManyItems: oneManyMappings,
        manyOneItems: manyOneMappings,
        manyManyItems: mergedManyManyMappings,
    };
}

export type CardinalityCategories = {
    oneOneItems: [FieldName, BlueprintMappingKey][];
    oneManyItems: [FieldName, BlueprintMappingKey[]][];
    manyOneItems: [FieldName[], BlueprintMappingKey][];
    manyManyItems: [FieldName[], BlueprintMappingKey[]][];
};

export type PdfMappingCategories = {
    oneMappingItems: [BlueprintMappingKey, FieldName][];
    manyMappingItems: [BlueprintMappingKey, FieldName[]][];
};

// calculate out-degree analysis that calculate all Mapping that map to one FieldName and all Mapping that map to many FieldName
export function getPdfMappingCategories(
    mappingToFieldNamesMap: Map<BlueprintMappingKey, FieldName[]>,
): PdfMappingCategories {
    const oneMappingItems: [BlueprintMappingKey, FieldName][] = [];
    const manyMappingItems: [BlueprintMappingKey, FieldName[]][] = [];

    // Analyze each mapping and its associated fields
    mappingToFieldNamesMap.forEach((fields, mapping) => {
        if (fields.length === 1) {
            // One-to-one relationship
            oneMappingItems.push([mapping, fields[0]]);
        } else {
            // One-to-many relationship
            manyMappingItems.push([mapping, fields]);
        }
    });

    return {
        oneMappingItems,
        manyMappingItems,
    };
}

export function getMappingRelationship(
    uiSchema: FormElementIdUISchema,
    titleDict: Map<string, string>,
): MappingRelationship {
    const { fieldNameToMappingsMap, mappingToFieldNamesMap } =
        createInverseFieldMapping(uiSchema, titleDict);

    return {
        cardinalityCategories: getMappingCardinalityCategories(
            mappingToFieldNamesMap,
            fieldNameToMappingsMap,
        ),
        pdfMappingCategories: getPdfMappingCategories(mappingToFieldNamesMap),

        mappingToFieldNamesMap,
        fieldNameToMappingsMap,
    };
}
