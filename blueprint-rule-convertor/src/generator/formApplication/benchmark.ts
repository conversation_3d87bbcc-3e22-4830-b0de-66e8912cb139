export enum Status {
    Good = "good",
    Error = "error",
    Info = "info",
    Warning = "warning",
}

export type Description = {
    label: string;
    tooltip: string;
    status: Status;
};

export type NestedCategoryData = {
    description: Description;
    categories: {
        [key: string]: NestedCategoryData | string[];
    };
};

export type StatisticData = {
    totalItems: number;
    description: Description;
    categories: {
        [key: string]: StatisticData | number;
    };
};

export function calculateCategoryStatistics(
    data: NestedCategoryData,
): StatisticData {
    const allItems = new Set<string>();
    const statistic: StatisticData = {
        totalItems: 0,
        description: data.description,
        categories: {},
    };

    function processCategory(
        categoryData: NestedCategoryData,
        currentStatistic: StatisticData,
    ): Set<string> {
        const categoryItems = new Set<string>();

        for (const key in categoryData.categories) {
            if (Array.isArray(categoryData.categories[key])) {
                const items = categoryData.categories[key] as string[];
                items.forEach((item) => {
                    categoryItems.add(item);
                    allItems.add(item);
                });
                currentStatistic.categories[key] = items.length;
            } else if (
                typeof categoryData.categories[key] === "object" &&
                categoryData.categories[key] !== null
            ) {
                const subCategoryStatistic: StatisticData = {
                    totalItems: 0,
                    description: (
                        categoryData.categories[key] as NestedCategoryData
                    ).description,
                    categories: {},
                };
                const subItems = processCategory(
                    categoryData.categories[key] as NestedCategoryData,
                    subCategoryStatistic,
                );
                subCategoryStatistic.totalItems = subItems.size;
                currentStatistic.categories[key] = subCategoryStatistic;
                subItems.forEach((item) => categoryItems.add(item));
            }
        }

        return categoryItems;
    }

    const categoryItems = processCategory(data, statistic);
    statistic.totalItems = allItems.size;
    return statistic;
}

export type MappingCategoryData = {
    [key: string]: MappingCategoryData | string[];
};

export type MappingStatisticData = {
    totalItems: number;
    categories: {
        [key: string]: MappingStatisticData | number;
    };
};

export const calculateMappingCategoryStatistics = (
    data: MappingCategoryData,
): MappingStatisticData => {
    const allItems = new Set<string>();
    const statistic: MappingStatisticData = {
        totalItems: 0,
        categories: {},
    };

    function processCategory(
        categoryData: MappingCategoryData,
        currentStatistic: MappingStatisticData,
    ): Set<string> {
        const categoryItems = new Set<string>();

        for (const key in categoryData) {
            if (Array.isArray(categoryData[key])) {
                const items = categoryData[key] as string[];
                items.forEach((item) => {
                    categoryItems.add(item);
                    allItems.add(item);
                });
                currentStatistic.categories[key] = items.length;
            } else if (
                typeof categoryData[key] === "object" &&
                categoryData[key] !== null
            ) {
                const subCategoryStatistic: MappingStatisticData = {
                    totalItems: 0,
                    categories: {},
                };
                const subItems = processCategory(
                    categoryData[key] as MappingCategoryData,
                    subCategoryStatistic,
                );
                subCategoryStatistic.totalItems = subItems.size;
                currentStatistic.categories[key] = subCategoryStatistic;
                subItems.forEach((item) => categoryItems.add(item));
            }
        }

        return categoryItems;
    }

    const categoryItems = processCategory(data, statistic);
    statistic.totalItems = allItems.size;
    return statistic;
};

export function collectItems(
    data: MappingCategoryData,
    key?: string,
): string[] {
    const items: string[] = [];

    function processCategory(
        categoryData: MappingCategoryData,
        currentKey?: string,
    ) {
        if (currentKey) {
            if (categoryData[currentKey]) {
                const value = categoryData[currentKey];

                if (Array.isArray(value)) {
                    items.push(...value);
                } else if (typeof value === "object" && value !== null) {
                    for (const subKey in value) {
                        processCategory(value, subKey);
                    }
                }
            }
        } else {
            for (const k in categoryData) {
                processCategory(categoryData, k);
            }
        }
    }

    processCategory(data, key);
    return items;
}
export type MappingCategoryStatistic = {
    [key: string]: MappingCategoryData | number;
};
