import $ from "lodash";

import * as PantheonModel from "@/models/stargazer/";
import {
    FormContext,
    BlueprintMetaFieldGroups,
    FormRuleTemplate,
} from "@/models";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";

import { getUniqueName } from "@/utils/formData/formSchema";
import {
    BlueprintApplicationResult,
    RuleMatchingAnalysis,
    TemplateAnalytic,
} from "@/models/formMapping";
import { getRuleAnalysis } from "./mappingAnalysis";
import {
    ConstantFieldNames,
    getMappingReplacementValue,
    InvestorFieldNames,
} from "@/utils/application/blueprintMappingKey";
import { generateFormSchema } from "../templateForm";
import { FormDataImpl } from "@/models/formData";
import {
    CombineMappingId,
    FakeCheckboxTargetMappingId,
} from "@/models/matchingId/blueprintMappingKey";
import { OptionFieldName } from "@/models/matchingId/formElementId";
import { FormRule } from "@anduintransaction/pangea/models/stargazer";
import { TemplateFormApplicationAnalysisImpl } from "@/models/templateFormApplicationAnalysis";
import { TemplateManager } from "@/models/templateManager";
import { CombineMappingManager } from "@/models/combineMappingManager";

export const applyForm = (
    appliedFormData: PantheonModel.FormData,
    templates: FormRuleTemplate[],
    blueprintMetadataGroups: BlueprintMetaFieldGroups,
    pdfDocuments: PDFDocument[],
    rawBlueprintData: PantheonModel.BlueprintInfo,
    addMissingField: boolean = false,
    addMissingRule: boolean = false,
): BlueprintApplicationResult => {
    const templateManager = new TemplateManager(templates);

    const appliedFormDataImpl = new FormDataImpl(
        appliedFormData,
        pdfDocuments,
        rawBlueprintData.metadata,
    );

    const formAnalysis = appliedFormDataImpl.getAnalysis();

    const templateApplication = new TemplateFormApplicationAnalysisImpl(
        appliedFormDataImpl.getTemplateApplicationStatus(templateManager),
        appliedFormDataImpl.getAnalysis().getMappingCardinalityCategories(),
    );

    const combineMappingManager = new CombineMappingManager(
        templateApplication,
        appliedFormDataImpl,
    );

    // calculate right field name for missing fields
    const existingNames = appliedFormDataImpl.getAllFieldNames();

    const missingInvestorTypeFields = $.difference(
        InvestorFieldNames,
        existingNames,
    );
    const missingInvestorTypeOptions = missingInvestorTypeFields.flatMap(
        (key): string[] => {
            const field = Array.from(
                blueprintMetadataGroups.investorTypeFields,
            ).find(([_, value]) => {
                return value.ui.name == key;
            });

            if (!field) return [];

            return [...field[1].catalaValueMap.values()].map((option) => {
                const optionField = new OptionFieldName(key, option);

                return optionField.id;
            });
        },
    );
    const fakeCheckboxMappingMissingKeys =
        templateApplication.getFakeCheckboxMappingMissingKeys();

    const addingFakeCheckboxMappingIdToFieldNameMap = new Map(
        fakeCheckboxMappingMissingKeys
            .map((id): [string, [string, string]] | undefined => {
                const fakeCheckboxMappingId =
                    FakeCheckboxTargetMappingId.fromKeyString(id);

                if (!fakeCheckboxMappingId) {
                    console.error(
                        `Cannot find fakeCheckbox mapping id for ${id}`,
                    );
                    return undefined;
                }

                const mappingKey = fakeCheckboxMappingId.getRenderedName();
                const fieldName = appliedFormDataImpl.getUniqueName(mappingKey);

                return [id, [mappingKey, fieldName]];
            })
            .filter((v) => v != undefined),
    );

    const [_, addingKeyToFieldNameEntries] = templateApplication
        .getAllMissingFieldKeys()
        .reduce(
            (
                [existingFieldNames, fieldNameMap],
                key,
            ): [string[], [string, string][]] => {
                const combineMappingId = CombineMappingId.fromKeyString(key);

                if (combineMappingId) {
                    const combineField =
                        combineMappingManager.getAddingCombineMappingFieldByKey(
                            key,
                        );

                    if (!combineField) {
                        console.error(
                            "Cannot find field name for combine mapping id : " +
                                key,
                        );
                    } else {
                        existingFieldNames.push(combineField.fieldName.id);
                        fieldNameMap.push([
                            combineField.mappingId.mapping,
                            combineField.fieldName.id,
                        ]);
                        return [existingFieldNames, fieldNameMap];
                    }
                }

                if (fakeCheckboxMappingMissingKeys.includes(key)) {
                    const fieldName =
                        addingFakeCheckboxMappingIdToFieldNameMap.get(key);

                    if (!fieldName) {
                        console.error(
                            `Cannot find fakeCheckbox mapping id for ${key}`,
                        );
                    } else {
                        existingFieldNames.push(fieldName[1]);
                        fieldNameMap.push([fieldName[0], fieldName[1]]);
                        return [existingFieldNames, fieldNameMap];
                    }
                }

                const uniqueName = getUniqueName(key, [
                    ...existingFieldNames,
                    ...existingNames,
                ]);
                existingFieldNames.push(uniqueName);
                fieldNameMap.push([key, uniqueName]);

                return [existingFieldNames, fieldNameMap];
            },
            [[], []] as [string[], [string, string][]],
        );
    const addingKeyToFieldNameMap = new Map(addingKeyToFieldNameEntries);

    const existingOneMappingKeyToFieldNameMap: Map<string, string> = new Map(
        formAnalysis
            .getOneMappingItems()
            .map(([mappingKey, fieldElement]): [string, string] => [
                mappingKey.id,
                getMappingReplacementValue(fieldElement),
            ]),
    );

    const mappingToFieldNameInputs = new Map([
        ...addingKeyToFieldNameMap,
        ...existingOneMappingKeyToFieldNameMap,
        ...[...combineMappingManager.getExistingCombineMappingFields()].map(
            ([_, combineField]): [string, string] => [
                combineField.mappingId.mapping,
                combineField.fieldName.id,
            ],
        ),
        ...ConstantFieldNames.map((key): [string, string] => [key, key]),
    ]);

    const existingMappingToFieldNameMap: Map<string, string[]> = new Map(
        Array.from(formAnalysis.getMappingToParentFieldNamesMap()).map(
            ([mappingKey, fieldElements]): [string, string[]] => [
                mappingKey.id,
                fieldElements.map((e) => e.fieldName),
            ],
        ),
    );

    const addingTargetKeyToFieldNameMap: Map<string, string[]> = new Map(
        Array.from(addingKeyToFieldNameMap).map(([k, v]) => [k, [v]]),
    );

    const mappingToFieldNameTargets: Map<string, string[]> = new Map([
        ...existingMappingToFieldNameMap,
        ...addingTargetKeyToFieldNameMap,
    ]);

    // Build all rules based on template and templateApplication, and a map form from to a fieldName
    const [renderedRules] = templateManager.renderRules(
        mappingToFieldNameInputs,
        mappingToFieldNameTargets,
        formAnalysis.getMappingToFieldNamesMap(),
        new Map(
            [...missingInvestorTypeFields, ...missingInvestorTypeOptions].map(
                (key): [string, string] => [key, key],
            ),
        ),
    );

    const formContext = new FormContext(
        {
            blueprintMappings: new Map([
                ...Array.from(addingKeyToFieldNameMap).filter(([key, _]) =>
                    templateApplication
                        .getBlueprintMetaElementMissingKeys()
                        .includes(key),
                ),
                ...Array.from(addingKeyToFieldNameMap).filter(([key, _]) =>
                    templateApplication
                        .getPdfAnnotationMissingKeys()
                        .includes(key),
                ),
                ...missingInvestorTypeFields.map((key): [string, string] => [
                    key,
                    key,
                ]),
            ]),
            combinedMappings: new Map(
                [
                    ...combineMappingManager
                        .getMissingCombineMappingFields()
                        .values(),
                ].map((combineField) => [
                    combineField.mappingId.mapping,
                    combineField.fieldName.id,
                ]),
            ),
            fakeCheckboxTargetMappings: new Map(
                Array.from(addingFakeCheckboxMappingIdToFieldNameMap)
                    .map(
                        ([id, [_, fieldName]]):
                            | [FakeCheckboxTargetMappingId, string]
                            | undefined => {
                            const fakeCheckboxMappingId =
                                FakeCheckboxTargetMappingId.fromKeyString(id);

                            if (!fakeCheckboxMappingId) {
                                console.error(
                                    `Cannot find fakeCheckbox mapping id for ${id}`,
                                );
                                return undefined;
                            }
                            return [fakeCheckboxMappingId, fieldName];
                        },
                    )
                    .filter((v) => v != undefined),
            ),
        },
        {},
        new Map(),
        [...existingNames, ...addingKeyToFieldNameMap.values()],
        new Map(
            [...combineMappingManager.getMissingCombineMappingFields()].map(
                ([_, combineField]): [string, CombineMappingId] => {
                    return [
                        combineField.mappingId.mapping,
                        combineField.mappingId,
                    ];
                },
            ),
        ),
    );
    const missingFieldSchema = generateFormSchema(formContext)(
        blueprintMetadataGroups,
        pdfDocuments,
    );

    const fieldName = missingFieldSchema
        ? getUniqueName(missingFieldSchema.name, existingNames)
        : undefined;

    // Calculate missing rules
    const combineRules: FormRule[] = combineMappingManager.getCombineRules();
    const formRules = appliedFormData.form.rules || [];
    const blueprintRules = [...renderedRules.values(), ...combineRules].flat();
    const ruleCompareItems = getRuleAnalysis(formRules, blueprintRules);
    const ruleMatchingAnalysis = new RuleMatchingAnalysis(ruleCompareItems);
    const addingRuleNames = ruleMatchingAnalysis.getAddingRuleNames();

    // Build form based on built UI and missing rules
    const filteredRules = blueprintRules.flat().filter((rule) => {
        return addingRuleNames.includes(rule.name);
    });
    const existingRuleNames = (appliedFormData.form.rules || []).map(
        (rule) => rule.name,
    );
    const [extendingRules] = [...filteredRules].reduce(
        ([rules, existingNames], rule): [FormRule[], string[]] => {
            const uniqueName = getUniqueName(rule.name, existingNames);
            existingNames.push(uniqueName);
            rules.push({ ...rule, name: uniqueName });

            return [rules, existingNames];
        },
        [[], existingRuleNames] as [FormRule[], string[]],
    );

    // Calculate form data by combining schema, UI schema and validation filteredRules
    const formData: PantheonModel.FormData = appliedFormDataImpl.combineData(
        fieldName || "",
        addMissingField ? missingFieldSchema?.schema : undefined,
        addMissingField ? missingFieldSchema?.uiSchema : undefined,
        addMissingRule ? extendingRules : [],
    );

    const templateAnalytic: TemplateAnalytic[] = Array.from(
        templateManager.templateIdMap.entries(),
    ).map(([templateId, template]): TemplateAnalytic => {
        const application = appliedFormDataImpl.getTemplateApplicationStatus(
            new TemplateManager([template]),
        );
        const applicationInstance = new TemplateFormApplicationAnalysisImpl(
            application,
            formAnalysis.getMappingCardinalityCategories(),
        );
        const combineMappingManager = new CombineMappingManager(
            applicationInstance,
            appliedFormDataImpl,
        );

        return {
            templateId,
            templateContent: template.rule,
            templateLineKeys: template.blueprintRuleKeys.lineToKeysMap.map(
                ([lineNum, lineKey]) => [
                    lineNum,
                    {
                        allKeys: lineKey.getAllKeys(),
                        multipleMappingKeys: $.intersection(
                            lineKey.getAllKeys(),
                            applicationInstance.getAllMultipleMappingKeys(),
                        ),
                        missingMappingKeys: $.intersection(
                            lineKey.getAllKeys(),
                            applicationInstance.getAllMissingBlueprintKeys(),
                        ),
                    },
                ],
            ),
            combinedMappingInputs: [
                ...combineMappingManager.getAllCombineMappingFields(),
            ],
            renderedRules: renderedRules.get(templateId) || [],
            singleMappingKeys: applicationInstance.getAllSingleMappingKeys(),
            multipleMappingKeys:
                applicationInstance.getAllMultipleMappingKeys(),
            missingMappingKeys:
                applicationInstance.getAllMissingBlueprintKeys(),
        };
    });

    const blueprintKeyAnalytic = {
        allKeys: templateManager.getAllKeys(),
        missingKeys: templateApplication.getAllMissingBlueprintKeys(),
        otherMissingKeys: templateApplication.getOtherMissingKeys(),
        existingKeys:
            templateApplication.getTemplateMappingCardinalityCategories(),
    };

    return {
        formData,
        blueprintKeyAnalytic,
        templateAnalytic,
        ruleCompareItems,
        formRules,
        blueprintRules,
    };
};
