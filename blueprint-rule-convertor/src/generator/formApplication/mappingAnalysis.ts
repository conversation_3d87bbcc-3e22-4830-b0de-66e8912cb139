import $ from "lodash";
import { FormElementIdUISchema } from "@/models";
import { FormRule } from "@/models/stargazer";
import * as PantheonModel from "@/models/stargazer/";

import {
    BlueprintMappingKey,
    PdfMapping,
    BlueprintMetadataMapping,
    FakeCheckboxTargetMappingId,
    CombineMappingId,
    FieldPdfMapping,
    FieldBlueprintMetadataMapping,
    OptionPdfMapping,
    OptionBlueprintMetadataMapping,
    BlueprintAnnotationMappingKey,
    BlueprintMetaElementMappingKey,
    BlueprintFieldMappingKey,
} from "@/models/matchingId/blueprintMappingKey";
import {
    InverseFieldMapping,
    RuleCompareItem,
    ExistingFormRule,
    AddingFormRule,
    RemovingFormRule,
} from "@/models/formMapping";
import { RuleId } from "@/models/matchingId/ruleMappingId";
import {
    getMappingKeys,
    getRuleSignatureToFormRuleMap,
} from "@/utils/application/blueprintMappingKey";
import { collectItems, MappingCategoryData } from "./benchmark";
import { PDFDocument } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements";

import {
    PdfEmbeddedFileFieldImpl,
    PdfMultipleCheckboxesFieldImpl,
    PdfParagraphFieldImpl,
    PdfRadioGroupFieldImpl,
    PdfSignatureFieldImpl,
    PdfTextFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";
import {
    PdfGroupFieldImpl,
    PdfPageFieldImpl,
    PdfRepeatableFieldImpl,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfGroup";
import {
    FieldName,
    OptionFieldName,
    ParentFieldName,
} from "@/models/matchingId/formElementId";
import * as MatchingIdUtils from "@/utils/application/matchingIdOperator";
import { TemplateManager } from "@/models/templateManager";
import { TemplateFormApplicationAnalysis } from "@/models/templateFormApplicationAnalysis";
import { FormAnalysis, FormAnalysisImpl } from "@/models/formAnalysis";

export class BlueprintMetaMappingsCategoryData {
    private _metaData: PantheonModel.BlueprintMetaData;
    private _categoryData: MappingCategoryData | undefined;

    constructor(metaData: PantheonModel.BlueprintMetaData) {
        this._metaData = metaData;
    }

    private computeCategoryData(): MappingCategoryData {
        const {
            paragraphs,
            gatingQuestions: rawGatingQuestions,
            fileGroups: rawFileGroups,
            signatures,
        } = this._metaData;

        const gatingQuestions: PantheonModel.GatingQuestion[] =
            rawGatingQuestions || [];
        const fileGroups: PantheonModel.FileGroup[] = rawFileGroups || [];

        const gatingQuestionOptions = gatingQuestions.flatMap(
            (gatingQuestion) =>
                (gatingQuestion.options || []).map(
                    (option) =>
                        new OptionFieldName(gatingQuestion.key, option.key).id,
                ),
        );
        const fileGroupOptions = fileGroups.flatMap((fileGroup) =>
            (fileGroup.fileItems || []).map(
                (item) => new OptionFieldName(fileGroup.key, item.key).id,
            ),
        );

        return {
            paragraphs: (paragraphs || []).map((item) => item.key),
            gatingQuestions: {
                fields: gatingQuestions.map((item) => item.key),
                options: gatingQuestionOptions,
            },
            fileGroups: {
                fields: fileGroups.map((item) => item.key),
                options: fileGroupOptions,
            },
            signatures: (signatures || []).map((item) => item.key),
        };
    }

    getCategoryData(): MappingCategoryData {
        if (!this._categoryData) {
            this._categoryData = this.computeCategoryData();
        }
        return this._categoryData;
    }

    getAllMappingKeys(): string[] {
        return collectItems(this.getCategoryData());
    }
}

export class AnnotationMappingsCategoryData {
    private _pdfDocuments: PDFDocument[];

    // caching
    private _allMappingKeys: string[] | undefined;

    constructor(pdfDocuments: PDFDocument[]) {
        this._pdfDocuments = pdfDocuments;
    }

    computeCategoryData(
        includingMappings: string[] | undefined,
    ): MappingCategoryData {
        const fields = new Map([
            ...this._pdfDocuments.flatMap((document) => {
                return [...document.flattenPdfFields];
            }),
        ]);

        const embeddedFiles: string[] = [];
        const signatures: string[] = [];
        const textFields: string[] = [];
        const multipleCheckboxes = {
            fields: [] as string[],
            options: [] as string[],
        };
        const radioGroups = {
            fields: [] as string[],
            options: [] as string[],
        };
        const paragraphs: string[] = [];
        const groups: string[] = [];
        const pages: string[] = [];
        const repeatables: string[] = [];
        const others: string[] = [];

        Array.from(fields)
            .filter(([mapping]) =>
                includingMappings == undefined
                    ? true
                    : includingMappings.includes(mapping),
            )
            .forEach(([mapping, field]) => {
                if (field instanceof PdfEmbeddedFileFieldImpl) {
                    embeddedFiles.push(mapping);
                    return;
                }
                if (field instanceof PdfSignatureFieldImpl) {
                    signatures.push(mapping);
                    return;
                }
                if (field instanceof PdfTextFieldImpl) {
                    textFields.push(mapping);
                    return;
                }
                if (field instanceof PdfMultipleCheckboxesFieldImpl) {
                    multipleCheckboxes.fields.push(mapping);

                    field
                        .getChildrenKeys()
                        .forEach((option) =>
                            multipleCheckboxes.options.push(
                                new OptionFieldName(mapping, option).id,
                            ),
                        );
                    return;
                }
                if (field instanceof PdfRadioGroupFieldImpl) {
                    radioGroups.fields.push(mapping);

                    field
                        .getChildrenKeys()
                        .forEach((option) =>
                            radioGroups.options.push(
                                new OptionFieldName(mapping, option).id,
                            ),
                        );
                    return;
                }
                if (field instanceof PdfParagraphFieldImpl) {
                    paragraphs.push(mapping);
                    return;
                }
                if (field instanceof PdfGroupFieldImpl) {
                    groups.push(mapping);
                    return;
                }
                if (field instanceof PdfPageFieldImpl) {
                    pages.push(mapping);
                    return;
                }
                if (field instanceof PdfRepeatableFieldImpl) {
                    repeatables.push(mapping);
                    return;
                }

                console.error("Unknown field type", field);
            });

        return {
            embeddedFiles,
            signatures,
            textFields,
            multipleCheckboxes,
            radioGroups,
            paragraphs,
            groups,
            pages,
            repeatables,
            others,
        };
    }
    getCategoryData(
        includingMappings: string[] | undefined = undefined,
    ): MappingCategoryData {
        return this.computeCategoryData(includingMappings);
    }

    getAllMappingKeys(): string[] {
        if (!this._allMappingKeys) {
            this._allMappingKeys = collectItems(this.getCategoryData());

            return this._allMappingKeys;
        }
        return this._allMappingKeys;
    }
}

export const getMappingCategoryData = (
    pdfDocuments: PDFDocument[],
    rawBlueprintData: PantheonModel.BlueprintInfo,
): MappingCategoryData => {
    const annotationMappingsCategoryData = new AnnotationMappingsCategoryData(
        pdfDocuments,
    );
    const blueprintMetaMappingsCategoryData =
        new BlueprintMetaMappingsCategoryData(rawBlueprintData.metadata);

    return {
        annotations: annotationMappingsCategoryData.getCategoryData(),
        blueprintElements: blueprintMetaMappingsCategoryData.getCategoryData(),
    };
};

export const getBlueprintKeyToCombineMappingIdMap = (
    templateManager: TemplateManager,
    formAnalysis: FormAnalysisImpl,
): Map<BlueprintMappingKey, CombineMappingId> => {
    const allExistingMappings: BlueprintMappingKey[] =
        formAnalysis.getAllExistingMappings();

    const inputKeys = templateManager.getAllInputKeys();

    const inputMappingKeys: BlueprintFieldMappingKey[] = [
        ...MatchingIdUtils.filterIncludingIdsFromKeys(
            FieldPdfMapping,
            allExistingMappings,
        )(inputKeys),
        ...MatchingIdUtils.filterIncludingIdsFromKeys(
            FieldBlueprintMetadataMapping,
            allExistingMappings,
        )(inputKeys),
    ];

    const oneManyMappings = formAnalysis.getManyMappingItems().map((v) => v[0]);

    const oneManyKeysInputs = MatchingIdUtils.intersection(
        inputMappingKeys,
        oneManyMappings,
    );
    return new Map(
        Array.from(oneManyKeysInputs)
            .map(
                (
                    mapping,
                ): [BlueprintMappingKey, CombineMappingId] | undefined => {
                    if (
                        !templateManager
                            .getAllInputExcludeTargetKeys()
                            .includes(mapping.id)
                    ) {
                        return undefined;
                    }

                    const elements = formAnalysis
                        .getFieldNamesByMapping(mapping)
                        .filter((e) => e instanceof ParentFieldName);
                    const combineId = new CombineMappingId(
                        mapping.id,
                        elements || [],
                    );

                    return [mapping, combineId];
                },
            )
            .filter((v) => v != undefined),
    );
};

export const getTemplateApplicationToFormAnalysis = (
    templateManager: TemplateManager,
    formAnalysis: FormAnalysisImpl,
    allAnnotationMappings: string[],
    allBlueprintMappings: string[],
): TemplateFormApplicationAnalysis => {
    const allExistingMappings: BlueprintMappingKey[] =
        formAnalysis.getAllExistingMappings();

    const targetKeys = templateManager.getAllTargetKeys();
    const inputKeys = templateManager.getAllInputKeys();
    const optionKeys = templateManager.getAllOptionKeys();

    const multipleTargets = templateManager
        .getMultipleTargetTemplates()
        .map(([_, targets]) => targets);

    const oneOneMappings = formAnalysis.getOneMappingItems().map((v) => v[0]);
    const oneManyMappings = formAnalysis.getManyMappingItems().map((v) => v[0]);

    const keyToCombineMappingIdMap = getBlueprintKeyToCombineMappingIdMap(
        templateManager,
        formAnalysis,
    );
    const combineMappingIds = [...keyToCombineMappingIdMap.values()].map(
        (value) => value.id,
    );

    const keyToFakeCheckboxMappingIdMap = new Map(
        Array.from(multipleTargets).map((targetKeys) => {
            const id = new FakeCheckboxTargetMappingId(targetKeys);

            return [targetKeys, id.id];
        }),
    );
    const fakeCheckboxMappingIds = [...keyToFakeCheckboxMappingIdMap.values()];

    // Helper function to create mapping structure sections
    function filterMappingsByCategory<T extends BlueprintMappingKey>(
        mappingType: new (...args: any[]) => T,
        keys: string[],
        allMappings: string[] | undefined = undefined,
    ): {
        existingKeys: { oneOneKeys: T[]; oneManyKeys: T[] };
        missingKeys: string[];
    } {
        const missingKeys = MatchingIdUtils.filterExcludingIdsFromKeys(
            mappingType,
            allExistingMappings,
        )(keys);

        return {
            existingKeys: {
                oneOneKeys: MatchingIdUtils.filterIncludingIdsFromKeys(
                    mappingType,
                    oneOneMappings,
                )(keys),
                oneManyKeys: MatchingIdUtils.filterIncludingIdsFromKeys(
                    mappingType,
                    oneManyMappings,
                )(keys),
            },
            missingKeys: allMappings
                ? $.intersection(allMappings, missingKeys)
                : missingKeys,
        };
    }

    // Helper function to create mapping structure
    const filterMappings = <
        T1 extends BlueprintAnnotationMappingKey,
        T2 extends BlueprintMetaElementMappingKey,
    >(
        pdfMappingType: new (...args: any[]) => T1,
        blueprintMappingType: new (...args: any[]) => T2,
        keys: string[],
    ) => {
        const otherMappings = $.difference(keys, [
            ...allAnnotationMappings,
            ...allBlueprintMappings,
        ]);

        return {
            annotations: filterMappingsByCategory(
                pdfMappingType,
                keys,
                allAnnotationMappings,
            ),
            blueprint: filterMappingsByCategory(
                blueprintMappingType,
                keys,
                allBlueprintMappings,
            ),
            others: otherMappings,
        };
    };

    const inputMappings = filterMappings(
        FieldPdfMapping,
        FieldBlueprintMetadataMapping,
        inputKeys,
    );

    const optionMappings = filterMappings(
        OptionPdfMapping,
        OptionBlueprintMetadataMapping,
        optionKeys,
    );

    const targetMappings = filterMappings(
        FieldPdfMapping,
        FieldBlueprintMetadataMapping,
        targetKeys,
    );

    const specialMappings = {
        combines: filterMappingsByCategory(
            CombineMappingId,
            combineMappingIds,
            combineMappingIds,
        ),
        fakeCheckboxes: filterMappingsByCategory(
            FakeCheckboxTargetMappingId,
            fakeCheckboxMappingIds,
            fakeCheckboxMappingIds,
        ),
    };

    const ruleMappings: TemplateFormApplicationAnalysis = {
        inputs: inputMappings,
        options: optionMappings,
        targets: targetMappings,
        combines: specialMappings.combines,
        fakeCheckboxes: specialMappings.fakeCheckboxes,
    };

    return ruleMappings;
};

/**
 * Creates a mapping from field names to their associated PDF/blueprint metadata references
 * @param uiSchema The UI schema containing field mappings
 * @returns A map of field names to their associated mappings
 */
export const createInverseFieldMapping = (
    uiSchema: FormElementIdUISchema,
    titleDict: Map<string, string>,
): InverseFieldMapping => {
    const { fieldNameToMappings, mappingToFieldNames } = Object.entries(
        uiSchema,
    ).reduce(
        (
            acc,
            [fieldName, widgetValues],
        ): {
            fieldNameToMappings: [FieldName, BlueprintMappingKey[]][];
            mappingToFieldNames: [BlueprintMappingKey, FieldName[]][];
            mappingKeys: BlueprintMappingKey[];
            fieldElements: FieldName[];
        } => {
            const mappingKeys = getMappingKeys(
                fieldName,
                widgetValues,
                titleDict.get(fieldName),
            );

            return mappingKeys.reduce((accInner, [element, mappingKey]) => {
                accInner.fieldElements = MatchingIdUtils.include(
                    accInner.fieldElements,
                    element,
                )
                    ? accInner.fieldElements
                    : [...accInner.fieldElements, element];
                accInner.mappingKeys = MatchingIdUtils.include(
                    accInner.mappingKeys,
                    mappingKey,
                )
                    ? accInner.mappingKeys
                    : [...accInner.mappingKeys, mappingKey];

                const unifiedElement =
                    accInner.fieldElements.find((item) =>
                        item.equals(element),
                    ) || element;
                const unifiedMappingKey =
                    accInner.mappingKeys.find((item) =>
                        item.equals(mappingKey),
                    ) || mappingKey;

                const existingFieldEntry = accInner.fieldNameToMappings.find(
                    ([existingElement]) => existingElement == unifiedElement,
                );

                if (existingFieldEntry) {
                    if (!existingFieldEntry[1].includes(unifiedMappingKey)) {
                        existingFieldEntry[1].push(unifiedMappingKey);
                    }
                } else {
                    accInner.fieldNameToMappings.push([
                        unifiedElement,
                        [unifiedMappingKey],
                    ]);
                }

                const existingMappingEntry = accInner.mappingToFieldNames.find(
                    ([existingMapping]) => existingMapping == unifiedMappingKey,
                );

                if (existingMappingEntry) {
                    if (!existingMappingEntry[1].includes(unifiedElement)) {
                        existingMappingEntry[1].push(unifiedElement);
                    }
                } else {
                    accInner.mappingToFieldNames.push([
                        unifiedMappingKey,
                        [unifiedElement],
                    ]);
                }

                return accInner;
            }, acc);
        },
        {
            fieldNameToMappings: [] as [FieldName, BlueprintMappingKey[]][],
            mappingToFieldNames: [] as [BlueprintMappingKey, FieldName[]][],
            mappingKeys: [] as BlueprintMappingKey[],
            fieldElements: [] as FieldName[],
        },
    );

    return {
        fieldNameToMappingsMap: new Map(fieldNameToMappings),
        mappingToFieldNamesMap: new Map(mappingToFieldNames),
    };
};

export const toComparedRule = (ruleContent: string): string => {
    const importPattern = /import\s+(['"])([^'"]+)\1/g;

    // Replace all import statements with the IGNORE_CONTENT tag
    // This preserves the import structure but normalizes the imported module names
    const normalizedContent = ruleContent.replace(
        importPattern,
        "import 'IGNORE_CONTENT'",
    );

    return normalizedContent;
};

// Example usage:
// Input:  "local _ = import 'mixcalc_v3'; local utils = import \"utils_v2\";"
// Output: "local _ = import 'IGNORE_CONTENT'; local utils = import 'IGNORE_CONTENT';"
const compareRuleContent = (
    formRule: FormRule,
    blueprintRule: FormRule,
): boolean => {
    const comparedFormRule = toComparedRule(formRule.value);
    const comparedBlueprintRule = toComparedRule(blueprintRule.value);

    return comparedFormRule != comparedBlueprintRule;
};

export const getRuleAnalysis = (
    formRules: FormRule[],
    blueprintRules: FormRule[],
): RuleCompareItem[] => {
    const formRuleSignatureMap = getRuleSignatureToFormRuleMap(formRules);
    const generatedRuleSignatureMap =
        getRuleSignatureToFormRuleMap(blueprintRules);

    const result: RuleCompareItem[] = [];

    // First, process formRules in order to maintain their sequence
    formRules.forEach((formRule) => {
        const ruleId = RuleId.fromTagString(formRule.description);
        if (!ruleId) return;

        const ruleSignature = ruleId.buildTag();

        const rule = formRuleSignatureMap.get(ruleSignature);
        const blueprintRule = generatedRuleSignatureMap.get(ruleSignature);

        if (rule && blueprintRule) {
            // Rule exists in both form and blueprint
            const isModfied = compareRuleContent(rule, blueprintRule);
            result.push(
                new ExistingFormRule(
                    ruleId,
                    rule.name,
                    blueprintRule.name,
                    isModfied,
                ),
            );
        } else if (rule && !blueprintRule) {
            // Rule exists only in form (being removed)
            result.push(new RemovingFormRule(ruleId, rule.name));
        }
    });

    // Then, process blueprintRules in order to add new rules
    blueprintRules.forEach((blueprintRule) => {
        const ruleId = RuleId.fromTagString(blueprintRule.description);
        if (!ruleId) return;

        const ruleSignature = ruleId.buildTag();

        const formRule = formRuleSignatureMap.get(ruleSignature);
        const rule = generatedRuleSignatureMap.get(ruleSignature);

        if (!formRule && rule) {
            // Rule exists only in blueprint (being added)
            result.push(new AddingFormRule(ruleId, rule.name));
        }
    });

    return result;
};

export type BlueprintMappingKeyCategories = {
    pdfMappings: PdfMapping[];
    blueprintMetaDataMappings: BlueprintMetadataMapping[];
    combinedMappingIds: CombineMappingId[];
    fakeCheckboxTargetMappingIds: FakeCheckboxTargetMappingId[];
};

export const getMappingTypeCategories = (
    mappings: BlueprintMappingKey[],
): BlueprintMappingKeyCategories => {
    return {
        pdfMappings: mappings.filter(
            (mapping) => mapping instanceof PdfMapping,
        ),
        blueprintMetaDataMappings: mappings.filter(
            (mapping) => mapping instanceof BlueprintMetadataMapping,
        ),
        combinedMappingIds: mappings.filter(
            (mapping) => mapping instanceof CombineMappingId,
        ),
        fakeCheckboxTargetMappingIds: mappings.filter(
            (mapping) => mapping instanceof FakeCheckboxTargetMappingId,
        ),
    };
};
