import {
    DataSource,
    ElementOptions,
    EmptyValue,
    MultipleDataSources,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import { DataFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/dataFunction";
import { FixedValue } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/fixedValue";
import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import { Calculation } from "@anduintransaction/pangea/models/intermediateRepresentation/common/arithmetic";

import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataFunction } from "@/generator/blueprint/dataFunction";
import { fromFixedValue } from "@/generator/blueprint/fixedValue";
import { fromBlueprintElement } from "@/generator/blueprint/blueprintElement";
import { fromCalculation } from "@/generator/blueprint/calculation";
import {
    getErrorFormRuleExpression,
    optimizeExpression,
} from "@/utils/rule/renderTemplateUtils";
import { OptionFieldName } from "@/models/matchingId/formElementId";

export const fromDataSource =
    (context: LogicContext) =>
    (component: DataSource): FormRuleExpression => {
        if (component instanceof DataFunction) {
            return fromDataFunction(context)(component);
        }

        if (component instanceof EmptyValue) {
            return fromEmptyValue(context)(component);
        }

        if (component instanceof FixedValue) {
            return fromFixedValue(context)(component);
        }

        if (component instanceof BlueprintElement) {
            return fromBlueprintElement(context)(component);
        }

        if (component instanceof ElementOptions) {
            return fromElementOptions(context)(component);
        }

        if (component instanceof Calculation) {
            return fromCalculation(context)(component);
        }

        if (component instanceof MultipleDataSources) {
            return fromMultipleDataSources(context)(component);
        }
        return getErrorFormRuleExpression(
            `Unsupported DataSource component type`,
        );
    };

export const fromEmptyValue =
    (_: LogicContext) =>
    (_: EmptyValue): FormRuleExpression => {
        return {
            statement: `utils.EmptyValue`,
            inputs: [],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromElementOptions =
    (context: LogicContext) =>
    (component: ElementOptions): FormRuleExpression => {
        const getOptionName = (option: string): string => {
            const optionField = new OptionFieldName(
                component.element.key,
                option,
            );
            return optionField.id;
        };
        const statement =
            component.options.length == 0
                ? `[]`
                : `[\n${component.options
                      .map((op) => {
                          const optionName = getOptionName(op);
                          return `\t"{{${optionName}}}",`;
                      })
                      .join("\n")}\n]`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: [component.element.key],
            optionKeys: component.options.map((option) =>
                getOptionName(option),
            ),
            expressionToConditionNameMap: new Map([[statement, "optionList"]]),
        };
    };
export const fromMultipleDataSources =
    (context: LogicContext) =>
    (component: MultipleDataSources): FormRuleExpression => {
        const fromRules = component.dataSources.flatMap(
            (dataSource): FormRuleExpression[] => {
                if (dataSource instanceof ElementOptions) {
                    return dataSource.options.map((option) => {
                        const optionFieldName = new OptionFieldName(
                            dataSource.element.key,
                            option,
                        );
                        return {
                            statement: `"{{${optionFieldName.id}}}"`,
                            inputs: [],
                            optionKeys: [optionFieldName.id],
                            expressionToConditionNameMap: new Map(),
                        };
                    });
                }
                return [fromDataSource(context)(dataSource)];
            },
        );
        const statement =
            component.dataSources.length == 0
                ? `[]`
                : `[\n${fromRules.map((fromRule) => `\t${fromRule.statement},`).join("\n")}\n]`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: fromRules.flatMap((rule) => rule.inputs),
            optionKeys: fromRules.flatMap((rule) => rule.optionKeys),
            expressionToConditionNameMap: new Map([
                [statement, "valueList"],
                ...fromRules.flatMap((rule) => [
                    ...rule.expressionToConditionNameMap,
                ]),
            ]),
        };
    };
