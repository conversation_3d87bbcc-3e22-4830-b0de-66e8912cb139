import {
    <PERSON>Function,
    GetLabe<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    JoinValues,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/dataFunction";

import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataSource } from "@/generator/blueprint/dataSource";
import {
    getErrorFormRuleExpression,
    optimizeExpression,
} from "@/utils/rule/renderTemplateUtils";
import {
    BlueprintElement,
    BlueprintPdfElement,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import { PdfOptionGroupFieldImpl } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/pdfObject/pdfField";

export const fromDataFunction =
    (context: LogicContext) => (component: DataFunction) => {
        if (component instanceof GetLabel) {
            return fromGetLabel(context)(component);
        }

        if (component instanceof GetLength) {
            return fromGetLength(context)(component);
        }

        if (component instanceof JoinValues) {
            return from<PERSON>oinVal<PERSON>(context)(component);
        }
        return getErrorFormRuleExpression(
            `Unsupported DataFunction component type: ${component.constructor.name}`,
        );
    };

const getLabelMappingsStatement = (element: BlueprintElement): string => {
    const childrenKeys =
        element instanceof BlueprintPdfElement &&
        element.pdfField instanceof PdfOptionGroupFieldImpl
            ? element.pdfField.getChildrenKeys()
            : [];

    return `{
${childrenKeys.map((key) => `\t\t"${key}": "${key}",`).join("\n")}
    }`;
};

export const fromGetLabel =
    (context: LogicContext) =>
    (component: GetLabel): FormRuleExpression => {
        const element = fromDataSource(context)(component.element);
        const exceptionalOptions = fromDataSource(context)(
            component.exceptionalOptions,
        );
        const exceptionalOptionValues = fromDataSource(context)(
            component.exceptionalOptionValues,
        );
        const inputs = [
            ...element.inputs,
            ...exceptionalOptions.inputs,
            ...exceptionalOptionValues.inputs,
        ];
        const optionKeys = [
            ...element.optionKeys,
            ...exceptionalOptions.optionKeys,
            ...exceptionalOptionValues.optionKeys,
        ];
        const labelMappingStatement = getLabelMappingsStatement(
            component.element,
        );
        const statement = `utils.getLabel(${optimizeExpression(context)(labelMappingStatement)})(${element.statement}, ${exceptionalOptions.statement}, ${exceptionalOptionValues.statement})`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: inputs,
            optionKeys,
            expressionToConditionNameMap: new Map([
                [statement, "label"],
                [labelMappingStatement, "labelMappings"],
                ...element.expressionToConditionNameMap,
                ...exceptionalOptionValues.expressionToConditionNameMap,
            ]),
        };
    };

export const fromGetLength =
    (context: LogicContext) =>
    (component: GetLength): FormRuleExpression => {
        const key = component.element.key;
        const statement = `std.length({{${key}}}.value)`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: [key],
            optionKeys: [],
            expressionToConditionNameMap: new Map([[statement, "length"]]),
        };
    };

export const fromJoinValues =
    (context: LogicContext) =>
    (component: JoinValues): FormRuleExpression => {
        const values = fromDataSource(context)(component.values);
        const delimiter = fromDataSource(context)(component.delimiter);
        const inputs = [...values.inputs, ...delimiter.inputs];
        const statement = `utils.join(${values.statement}, ${delimiter.statement})`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: inputs,
            optionKeys: [...values.optionKeys, ...delimiter.optionKeys],
            expressionToConditionNameMap: new Map([
                [statement, "value"],
                ...values.expressionToConditionNameMap,
                ...delimiter.expressionToConditionNameMap,
            ]),
        };
    };
