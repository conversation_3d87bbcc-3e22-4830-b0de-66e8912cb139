import {
    FixedBoolean,
    FixedDate,
    FixedFloat,
    FixedInteger,
    FixedString,
    FixedValue,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/fixedValue";

import { FormRuleExpression, LogicContext } from "@/models";
import { getErrorFormRuleExpression } from "@/utils/rule/renderTemplateUtils";

export const fromFixedValue =
    (context: LogicContext) => (component: FixedValue) => {
        if (component instanceof FixedString) {
            return fromFixedString(context)(component);
        }

        if (component instanceof FixedFloat) {
            return fromFixedFloat(context)(component);
        }

        if (component instanceof FixedInteger) {
            return fromFixedInteger(context)(component);
        }

        if (component instanceof FixedBoolean) {
            return fromFixedBoolean(context)(component);
        }

        if (component instanceof FixedDate) {
            return fromFixedDate(context)(component);
        }
        return getErrorFormRuleExpression(
            `Unsupported FixedValue component type: ${component.constructor.name}`,
        );
    };

export const fromFixedString =
    (_: LogicContext) =>
    (component: FixedString): FormRuleExpression => {
        return new FormRuleExpression(`"${component.value}"`);
    };

export const fromFixedFloat =
    (_: LogicContext) =>
    (component: FixedFloat): FormRuleExpression => {
        return new FormRuleExpression(`${component.value}`);
    };

export const fromFixedInteger =
    (_: LogicContext) =>
    (component: FixedInteger): FormRuleExpression => {
        return new FormRuleExpression(`${component.value}`);
    };

export const fromFixedBoolean =
    (_: LogicContext) =>
    (component: FixedBoolean): FormRuleExpression => {
        return new FormRuleExpression(`${component.value}`);
    };

export const fromFixedDate =
    (_: LogicContext) =>
    (component: FixedDate): FormRuleExpression => {
        return new FormRuleExpression(`"${component.value}"`);
    };
