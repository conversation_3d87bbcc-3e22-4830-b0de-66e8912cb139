import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import {
    BlueprintMetaElement,
    StringConstant,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/metaElement";

import { FormRuleExpression, LogicContext } from "@/models";

export const fromBlueprintElement =
    (context: LogicContext) =>
    (component: BlueprintElement): FormRuleExpression => {
        if (component instanceof BlueprintMetaElement) {
            return fromBlueprintMetaElement(context)(component);
        }

        return {
            statement: `{{${component.key}}}.value`,
            inputs: [component.key],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromBlueprintElementDefault = fromBlueprintElement;
export const fromBlueprintPdfElement = fromBlueprintElement;
export const fromPDFDocument = fromBlueprintElement;
export const fromBlueprintMetaElement =
    (context: LogicContext) =>
    (component: BlueprintMetaElement): FormRuleExpression => {
        if (component instanceof StringConstant) {
            return fromStringConstant(context)(component);
        }

        return {
            statement: `{{${component.key}}}.value`,
            inputs: [component.key],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromStringConstant =
    (_: LogicContext) =>
    (component: StringConstant): FormRuleExpression => {
        return {
            statement: `"${component.stringValue}"`,
            inputs: [],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromParagraphMessage = fromBlueprintElement;
export const fromGatingQuestion = fromBlueprintElement;
export const fromFileGroup = fromBlueprintElement;
export const fromBlueprintSignature = fromBlueprintElement;
