import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";
import { BaseBlueprintComponent } from "@anduintransaction/pangea/models/blueprintData";
import { Calculation } from "@anduintransaction/pangea/models/intermediateRepresentation/common/arithmetic";
import { Condition } from "@anduintransaction/pangea/models/intermediateRepresentation/condition";
import { ConditionFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/condition/conditionFunction";
import {
    ElementOptions,
    EmptyValue,
} from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import { DataFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/dataFunction";
import { FixedValue } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/fixedValue";

import { FormRuleExpression, LogicContext } from "@/models";
import { fromBlueprintElement } from "@/generator/blueprint/blueprintElement";
import { fromCalculation } from "@/generator/blueprint/calculation";
import { fromConditionFunction } from "@/generator/blueprint/conditionFunction";
import { fromDataFunction } from "@/generator/blueprint/dataFunction";
import { fromCondition } from "@/generator/blueprint/condition";
import { fromFixedValue } from "@/generator/blueprint/fixedValue";
import {
    fromElementOptions,
    fromEmptyValue,
} from "@/generator/blueprint/dataSource";
import { getErrorFormRuleExpression } from "@/utils/rule/renderTemplateUtils";

export const fromBaseBlueprintComponent =
    (context: LogicContext) =>
    (component: BaseBlueprintComponent): FormRuleExpression => {
        if (component instanceof BlueprintElement) {
            return fromBlueprintElement(context)(component);
        }

        if (component instanceof Calculation) {
            return fromCalculation(context)(component);
        }

        if (component instanceof ConditionFunction) {
            return fromConditionFunction(context)(component);
        }

        if (component instanceof Condition) {
            return fromCondition(context)(component);
        }

        if (component instanceof DataFunction) {
            return fromDataFunction(context)(component);
        }

        if (component instanceof FixedValue) {
            return fromFixedValue(context)(component);
        }

        if (component instanceof EmptyValue) {
            return fromEmptyValue(context)(component);
        }

        if (component instanceof ElementOptions) {
            return fromElementOptions(context)(component);
        }
        return getErrorFormRuleExpression(
            `Unsupported BaseBlueprintComponent component type: ${component.constructor.name}`,
        );
    };
