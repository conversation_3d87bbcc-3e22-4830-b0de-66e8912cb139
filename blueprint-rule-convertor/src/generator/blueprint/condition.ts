import {
    <PERSON><PERSON><PERSON><PERSON>,
    AlwaysTrue,
    ANDCondition,
    ComparisonCondition,
    Condition,
    ConditionOperand,
    NOTCondition,
    ORCondition,
    SingleConditionFunctionEval,
} from "@anduintransaction/pangea/models/intermediateRepresentation/condition";
import { ConditionFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/condition/conditionFunction";
import { DataFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/dataFunction";
import { FixedValue } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/fixedValue";
import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";

import { fromConditionFunction } from "@/generator/blueprint/conditionFunction";
import { FormRuleExpression, LogicContext } from "@/models";
import { fromBlueprintElement } from "@/generator/blueprint/blueprintElement";
import { fromFixedValue } from "@/generator/blueprint/fixedValue";
import { fromDataFunction } from "@/generator/blueprint/dataFunction";
import { fromBaseBlueprintComponent } from ".";
import { DataSource } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource";
import {
    getComparisonTypeName,
    getErrorFormRuleExpression,
    optimizeExpression,
} from "@/utils/rule/renderTemplateUtils";
import { fromCalculation } from "./calculation";

export const fromCondition =
    (context: LogicContext) =>
    (component: Condition): FormRuleExpression => {
        if (component instanceof AlwaysTrue) {
            return fromAlwaysTrue(context)(component);
        }

        if (component instanceof AlwaysFalse) {
            return fromAlwaysFalse(context)(component);
        }

        if (component instanceof SingleConditionFunctionEval) {
            return fromSingleConditionFunctionEval(context)(component);
        }

        if (component instanceof ComparisonCondition) {
            return fromComparisonCondition(context)(component);
        }

        if (component instanceof ANDCondition) {
            return fromANDCondition(context)(component);
        }

        if (component instanceof ORCondition) {
            return fromORCondition(context)(component);
        }

        if (component instanceof NOTCondition) {
            return fromNOTCondition(context)(component);
        }

        if (component instanceof ConditionFunction) {
            return fromConditionFunction(context)(component);
        }

        return getErrorFormRuleExpression(
            `Unsupported Condition component type: ${component.constructor.name}`,
        );
    };

export const fromConditionOperand =
    (context: LogicContext) => (component: ConditionOperand) => {
        if (component instanceof ConditionFunction) {
            return fromConditionFunction(context)(component);
        }

        if (component instanceof DataFunction) {
            return fromDataFunction(context)(component);
        }

        if (component instanceof FixedValue) {
            return fromFixedValue(context)(component);
        }

        if (component instanceof BlueprintElement) {
            return fromBlueprintElement(context)(component);
        }

        if (component instanceof Condition) {
            return fromCondition(context)(component);
        }

        /**
         * To fix: extend Calculation to ConditionOperand?
         */
        if (component["type"] == "ArithmeticCalculation") {
            return fromCalculation(context)(component);
        }

        return getErrorFormRuleExpression(
            `Unsupported ConditionOperand component type: ${component["type"]}`,
        );
    };

export const fromAlwaysTrue =
    (_: LogicContext) =>
    (_: AlwaysTrue): FormRuleExpression => {
        return {
            statement: `true`,
            inputs: [],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromAlwaysFalse =
    (_: LogicContext) =>
    (_: AlwaysFalse): FormRuleExpression => {
        return {
            statement: `false`,
            inputs: [],
            optionKeys: [],
            expressionToConditionNameMap: new Map(),
        };
    };

export const fromSingleConditionFunctionEval =
    (context: LogicContext) =>
    (component: SingleConditionFunctionEval): FormRuleExpression => {
        return fromConditionFunction(context)(component.functionToEvel);
    };

const getSafeCheckExpr =
    (context: LogicContext) =>
    (source: DataSource): FormRuleExpression => {
        const expr = fromConditionOperand(context)(source);

        if (source instanceof BlueprintElement) {
            return {
                ...expr,
                statement: `utils.forceNum(${expr.statement})`,
            };
        }

        return expr;
    };

export const fromComparisonCondition =
    (context: LogicContext) =>
    (component: ComparisonCondition): FormRuleExpression => {
        const leftExpr = getSafeCheckExpr(context)(component.source);
        const rightExpr = getSafeCheckExpr(context)(component.targetToCompare);
        const statement = `${leftExpr.statement} ${component.comparisonType} ${rightExpr.statement}`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: leftExpr.inputs.concat(rightExpr.inputs),
            optionKeys: [...leftExpr.optionKeys, ...rightExpr.optionKeys],
            expressionToConditionNameMap: new Map([
                [
                    statement,
                    `isNumber${getComparisonTypeName(component.comparisonType)}`,
                ],
                ...leftExpr.expressionToConditionNameMap,
                ...rightExpr.expressionToConditionNameMap,
            ]),
        };
    };

export const fromANDCondition =
    (context: LogicContext) =>
    (component: ANDCondition): FormRuleExpression => {
        const leftExpr = fromBaseBlueprintComponent(context)(component.left);
        const rightExpr = fromBaseBlueprintComponent(context)(component.right);
        const groupedLeftExpr =
            component.left instanceof ORCondition
                ? `(${leftExpr.statement})`
                : leftExpr.statement;
        const groupRightExpr =
            component.right instanceof ORCondition
                ? `(${rightExpr.statement})`
                : rightExpr.statement;

        return {
            statement: `${groupedLeftExpr} && ${groupRightExpr}`,
            inputs: leftExpr.inputs.concat(rightExpr.inputs),
            optionKeys: [...leftExpr.optionKeys, ...rightExpr.optionKeys],
            expressionToConditionNameMap: new Map([
                ...leftExpr.expressionToConditionNameMap,
                ...rightExpr.expressionToConditionNameMap,
            ]),
        };
    };

export const fromORCondition =
    (context: LogicContext) =>
    (component: ORCondition): FormRuleExpression => {
        const leftExpr = fromBaseBlueprintComponent(context)(component.left);
        const rightExpr = fromBaseBlueprintComponent(context)(component.right);
        const groupedLeftExpr =
            component.left instanceof ANDCondition
                ? `(${leftExpr.statement})`
                : leftExpr.statement;
        const groupRightExpr =
            component.right instanceof ANDCondition
                ? `(${rightExpr.statement})`
                : rightExpr.statement;

        return {
            statement: `${groupedLeftExpr} || ${groupRightExpr}`,
            inputs: leftExpr.inputs.concat(rightExpr.inputs),
            optionKeys: [...leftExpr.optionKeys, ...rightExpr.optionKeys],
            expressionToConditionNameMap: new Map([
                ...leftExpr.expressionToConditionNameMap,
                ...rightExpr.expressionToConditionNameMap,
            ]),
        };
    };
export const fromNOTCondition =
    (context: LogicContext) =>
    (component: NOTCondition): FormRuleExpression => {
        const logic = fromBaseBlueprintComponent(context)(component.inside);
        const isOptimized = Array.from(
            context.baseConditionNames.values(),
        ).includes(logic.statement);

        return {
            ...logic,
            statement: isOptimized
                ? `!${logic.statement}`
                : `!(${logic.statement})`,
        };
    };
