import {
    ElementSelectedEveryValues,
    ElementSelectedSomeValues,
    SelectedEveryInvestorType,
    SelectedInvestorTypeInGroup,
    SatisfiedSomeConditions,
    SelectedSomeInvestorType,
    ConditionFunction,
} from "@anduintransaction/pangea/models/intermediateRepresentation/condition/conditionFunction";
import { StringConditionFunction } from "@anduintransaction/pangea/models/intermediateRepresentation/condition/stringConditionFunction";
import {
    ScopeInvestorType,
    WidgetType,
} from "@/models/stargazer/openapi/interface";
import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";

import {
    FormRuleExpression,
    BlueprintMetaFieldGroups,
    LogicContext,
} from "@/models";
import { fromStringConditionFunction } from "@/generator/blueprint/stringConditionFunction";
import * as WigetTypeUtils from "@/utils/widgetType";
import {
    getErrorFormRuleExpression,
    optimizeExpression,
} from "@/utils/rule/renderTemplateUtils";
import { toTitleCaseAlphanumeric } from "@/utils/common";
import { fromCondition } from "./condition";
import { OptionFieldName } from "@/models/matchingId/formElementId";

export const fromConditionFunction =
    (context: LogicContext) => (component: ConditionFunction) => {
        if (component instanceof ElementSelectedSomeValues) {
            return fromElementSelectedSomeValues(context)(component);
        }

        if (component instanceof ElementSelectedEveryValues) {
            return fromElementSelectedEveryValues(context)(component);
        }

        if (component instanceof SelectedSomeInvestorType) {
            return fromSelectedSomeInvestorType(context)(component);
        }

        if (component instanceof SelectedEveryInvestorType) {
            return fromSelectedEveryInvestorType(context)(component);
        }

        if (component instanceof SelectedInvestorTypeInGroup) {
            return fromSelectedInvestorTypeInGroup(context)(component);
        }

        if (component instanceof StringConditionFunction) {
            return fromStringConditionFunction(context)(component);
        }

        if (component instanceof SatisfiedSomeConditions) {
            return fromSatisfiedSomeConditions(context)(component);
        }

        return getErrorFormRuleExpression(
            `Unsupported ConditionFunction component type: ${component.constructor.name}`,
        );
    };

const getOptimizeOptionExpression =
    (context: LogicContext) =>
    (optionName: OptionFieldName, widgetType: WidgetType): [string, string] => {
        const conditionName = `is${toTitleCaseAlphanumeric(optionName.optionName)}`;

        if (WigetTypeUtils.getSingleSelectionWidgets.includes(widgetType)) {
            const statement = `{{${optionName.parentName}}}.value == "{{${optionName.id}}}"`;

            return [optimizeExpression(context)(statement), conditionName];
        }

        if (WigetTypeUtils.getMultipleSelectionWidgets.includes(widgetType)) {
            const statement = `std.member({{${optionName.parentName}}}.value, "{{${optionName.id}}}")`;

            return [optimizeExpression(context)(statement), conditionName];
        }

        return [
            getErrorFormRuleExpression(
                "Unsupported option check for widget type " + widgetType,
            ).statement,
            conditionName,
        ];
    };

const fromFieldOptions =
    (context: LogicContext) =>
    (
        optionNames: OptionFieldName[],
        widgetType: WidgetType,
        joinOperator: string,
    ): FormRuleExpression => {
        const expressionToConditionNameMap = optionNames.map((optionName) =>
            getOptimizeOptionExpression(context)(optionName, widgetType),
        );
        const checkLength = expressionToConditionNameMap.length;

        const statement =
            checkLength == 0
                ? "true"
                : checkLength == 1
                  ? expressionToConditionNameMap[0][0]
                  : `(${expressionToConditionNameMap.map((values) => values[0]).join(` ${joinOperator} `)})`;

        return {
            statement: statement,
            inputs: Array.from(
                new Set(optionNames.map(({ parentName }) => parentName)),
            ),
            optionKeys: optionNames.map((value) => value.id),
            expressionToConditionNameMap: new Map(expressionToConditionNameMap),
        };
    };

const getMultipleOptionsCheckCode =
    (context: LogicContext) =>
    (
        element: BlueprintElement,
        checkedOptions: string[],
        joinOperator: string,
    ): FormRuleExpression => {
        const widgetType = WigetTypeUtils.fromBlueprintElement(element);
        const optionNames = checkedOptions.map(
            (option) => new OptionFieldName(element.key, option),
        );

        return fromFieldOptions(context)(optionNames, widgetType, joinOperator);
    };

export const fromElementSelectedSomeValues =
    (context: LogicContext) =>
    (component: ElementSelectedSomeValues): FormRuleExpression => {
        return getMultipleOptionsCheckCode(context)(
            component.element,
            component.optionsToCheck,
            `||`,
        );
    };

export const fromElementSelectedEveryValues =
    (context: LogicContext) =>
    (component: ElementSelectedEveryValues): FormRuleExpression => {
        return getMultipleOptionsCheckCode(context)(
            component.element,
            component.optionsToCheck,
            `&&`,
        );
    };

const handleCatalaInvestorTypeCheck = (
    catalaInvestorTypes: string[],
    groups: BlueprintMetaFieldGroups,
): OptionFieldName[] => {
    const investorTypesFields = groups.investorTypeFields;
    const investorTypesFieldKeys = [...investorTypesFields.keys()];

    return catalaInvestorTypes
        .map((catalaInvestorType) => {
            return Array.from(investorTypesFieldKeys).reduce(
                (
                    res: OptionFieldName[],
                    groupName: string,
                ): OptionFieldName[] => {
                    const field = investorTypesFields.get(groupName);

                    if (!field) {
                        return res;
                    }

                    const optionName =
                        field.catalaValueMap.get(catalaInvestorType);

                    if (!optionName) {
                        return res;
                    }

                    const option = new OptionFieldName(
                        field.ui.name as string,
                        optionName,
                    );

                    return [...res, option];
                },
                [],
            );
        })
        .flat();
};

const fromInvestorTypeOptions =
    (context: LogicContext) =>
    (investorTypes: string[], joinOperator: string): FormRuleExpression => {
        const optionNames = handleCatalaInvestorTypeCheck(
            investorTypes,
            context.processBlueprintInfo,
        );

        return fromFieldOptions(context)(
            optionNames,
            WigetTypeUtils.BLUEPRINT_INVESTOR_TYPE_WIDGET,
            joinOperator,
        );
    };

export const fromSelectedSomeInvestorType =
    (context: LogicContext) =>
    (component: SelectedSomeInvestorType): FormRuleExpression =>
        fromInvestorTypeOptions(context)(component.investorTypes, "||");

export const fromSelectedEveryInvestorType =
    (context: LogicContext) =>
    (component: SelectedEveryInvestorType): FormRuleExpression =>
        fromInvestorTypeOptions(context)(component.investorTypes, "&&");

export const fromSatisfiedSomeConditions =
    (context: LogicContext) =>
    (component: SatisfiedSomeConditions): FormRuleExpression => {
        const conditions = component.conditions.map(fromCondition(context));
        const inputs = conditions.map((condition) => condition.inputs).flat();
        const optionKeys = conditions
            .map((condition) => condition.optionKeys)
            .flat();

        const conditionItems =
            conditions.length == 0
                ? `[]`
                : `[\n${conditions.map((condition) => `\t${condition.statement},`).join("\n")}\n]`;
        const statement = `std.length(std.filter(function(condition) condition, ${conditionItems})) ${component.satisfyCountComparison} ${component.compareTo}`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs,
            optionKeys,
            expressionToConditionNameMap: new Map([
                [statement, `isSasfiedAtLeast${component.compareTo}`],
                ...conditions
                    .map((condition) =>
                        Array.from(condition.expressionToConditionNameMap),
                    )
                    .flat(),
            ]),
        };
    };

export const fromSelectedInvestorTypeInGroup =
    (context: LogicContext) =>
    (component: SelectedInvestorTypeInGroup): FormRuleExpression => {
        switch (component.selectedGroup) {
            case ScopeInvestorType.Individual: {
                const statement = `gr_shortcut.value == "indi"`;
                return {
                    statement: optimizeExpression(context)(statement),
                    inputs: ["gr_shortcut"],
                    optionKeys: [],
                    expressionToConditionNameMap: new Map([
                        [statement, "isIndividual"],
                    ]),
                };
            }
            case ScopeInvestorType.Entity: {
                const statement = `gr_shortcut.value == "entity"`;
                return {
                    statement: optimizeExpression(context)(statement),
                    inputs: ["gr_shortcut"],
                    optionKeys: [],
                    expressionToConditionNameMap: new Map([
                        [statement, "isEntity"],
                    ]),
                };
            }
            case ScopeInvestorType.Common: {
                return {
                    statement: `true`,
                    inputs: [],
                    optionKeys: [],
                    expressionToConditionNameMap: new Map(),
                };
            }

            default:
                return getErrorFormRuleExpression(
                    "Unknow ScopeInvestorType type",
                );
        }
    };
