import {
    ArithmeticCalculation,
    Calculation,
    IdentityValue,
} from "@anduintransaction/pangea/models/intermediateRepresentation/common/arithmetic";

import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataSource } from "@/generator/blueprint/dataSource";
import { getErrorFormRuleExpression } from "@/utils/rule/renderTemplateUtils";
import { BlueprintElement } from "@anduintransaction/pangea/models/intermediateRepresentation/dataSource/elements/other";

// Define operator precedence levels
const getOperatorPrecedence = (operator: string): number => {
    switch (operator) {
        case "*":
        case "/":
        case "%":
            return 2; // Higher precedence
        case "+":
        case "-":
            return 1; // Lower precedence
        default:
            return 0; // Unknown operator
    }
};

// Check if parentheses are needed based on operator precedence
const needParentheses = (
    innerOperator: string,
    outerOperator: string,
): boolean => {
    const innerPrecedence = getOperatorPrecedence(innerOperator);
    const outerPrecedence = getOperatorPrecedence(outerOperator);

    // If inner operator has lower or equal precedence than outer operator, we need parentheses
    return (
        innerPrecedence < outerPrecedence ||
        // Special case: for same precedence, we need parentheses for subtraction and division
        (innerPrecedence === outerPrecedence &&
            (innerOperator === "-" || innerOperator === "/"))
    );
};

export const fromCalculation =
    (context: LogicContext) => (component: Calculation) => {
        if (component instanceof ArithmeticCalculation) {
            return fromArithmeticCalculation(context)(component);
        }

        if (component instanceof IdentityValue) {
            return fromIdentityValue(context)(component);
        }

        return getErrorFormRuleExpression(
            `Unsupported Calculation component type: ${component.constructor.name}`,
        );
    };

export const fromArithmeticCalculation =
    (context: LogicContext) =>
    (component: ArithmeticCalculation): FormRuleExpression => {
        const logic = fromDataSource(context)(component.input);
        const innerLogic = fromCalculation(context)(component.withValue);
        const needsParentheses =
            component.withValue instanceof ArithmeticCalculation &&
            needParentheses(component.withValue.operator, component.operator);

        // Apply parentheses to inner statement if needed
        const innerStatement = needsParentheses
            ? `(${innerLogic.statement})`
            : innerLogic.statement;

        const leftStatement =
            component.input instanceof BlueprintElement
                ? `utils.forceNum(${logic.statement})`
                : logic.statement;
        const rightStatement =
            component.withValue instanceof IdentityValue &&
            component.withValue.input instanceof BlueprintElement
                ? `utils.forceNum(${innerStatement})`
                : innerStatement;

        const statement = `${leftStatement} ${component.operator} ${rightStatement}`;
        const inputs = [...logic.inputs, ...innerLogic.inputs];

        return {
            statement,
            inputs,
            optionKeys: [...logic.optionKeys, ...innerLogic.optionKeys],
            expressionToConditionNameMap: new Map([
                ...logic.expressionToConditionNameMap,
                ...innerLogic.expressionToConditionNameMap,
            ]),
        };
    };

export const fromIdentityValue =
    (context: LogicContext) =>
    (component: IdentityValue): FormRuleExpression => {
        return fromDataSource(context)(component.input);
    };
