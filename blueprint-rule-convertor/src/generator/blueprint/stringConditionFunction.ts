import {
    IsAlphanumbericString,
    IsD<PERSON>talString,
    StringComparisonCondition,
    StringConditionFunction,
    StringContains,
} from "@anduintransaction/pangea/models/intermediateRepresentation/condition/stringConditionFunction";

import { FormRuleExpression, LogicContext } from "@/models";
import { fromDataSource } from "./dataSource";
import {
    getErrorFormRuleExpression,
    getStringComparisonTypeName,
    optimizeExpression,
} from "@/utils/rule/renderTemplateUtils";

export const fromStringConditionFunction =
    (context: LogicContext) =>
    (component: StringConditionFunction): FormRuleExpression => {
        if (component instanceof IsDigitalString) {
            return fromIsDigitalString(context)(component);
        }

        if (component instanceof IsAlphanumbericString) {
            return fromIsAlphanumbericString(context)(component);
        }

        if (component instanceof StringContains) {
            return fromStringContains(context)(component);
        }

        if (component instanceof StringComparisonCondition) {
            return fromStringComparisonCondition(context)(component);
        }
        return getErrorFormRuleExpression(
            `Unsupported StringConditionFunction component type: ${component.constructor.name}`,
        );
    };

export const fromIsDigitalString =
    (context: LogicContext) =>
    (component: IsDigitalString): FormRuleExpression => {
        const eleKey = component.element.key;
        const statement = `utils.isDigitString({{${eleKey}}}.value)`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: [eleKey],
            optionKeys: [],
            expressionToConditionNameMap: new Map([
                [statement, "isDigitString"],
            ]),
        };
    };

export const fromIsAlphanumbericString =
    (context: LogicContext) =>
    (component: IsAlphanumbericString): FormRuleExpression => {
        const eleKey = component.element.key;
        const statement = `utils.isAlphanumericString({{${eleKey}}}.value)`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: [eleKey],
            optionKeys: [],
            expressionToConditionNameMap: new Map([
                [statement, "isAlphanumericString"],
            ]),
        };
    };

export const fromStringContains =
    (context: LogicContext) =>
    (component: StringContains): FormRuleExpression => {
        const value = fromDataSource(context)(component.value);
        const eleKey = component.element.key;
        const statement = `utils.stringContains({{${eleKey}}}.value, ${value.statement})`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs: [eleKey, ...value.inputs],
            optionKeys: value.optionKeys,
            expressionToConditionNameMap: new Map([
                [statement, `isStringContained`],
                ...value.expressionToConditionNameMap,
            ]),
        };
    };

export const fromStringComparisonCondition =
    (context: LogicContext) =>
    (component: StringComparisonCondition): FormRuleExpression => {
        const source = fromDataSource(context)(component.source);
        const targetToCompare = fromDataSource(context)(
            component.targetToCompare,
        );
        const inputs = [...source.inputs, ...targetToCompare.inputs];
        const optionKeys = [
            ...source.optionKeys,
            ...targetToCompare.optionKeys,
        ];
        const statement = `${source.statement} ${component.comparisonType} ${targetToCompare.statement}`;

        return {
            statement: optimizeExpression(context)(statement),
            inputs,
            optionKeys,
            expressionToConditionNameMap: new Map([
                [
                    statement,
                    `isString${getStringComparisonTypeName(component.comparisonType)}`,
                ],
                ...source.expressionToConditionNameMap,
                ...targetToCompare.expressionToConditionNameMap,
            ]),
        };
    };
