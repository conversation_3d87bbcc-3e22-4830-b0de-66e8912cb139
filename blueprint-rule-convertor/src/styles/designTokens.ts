// Design tokens for consistent styling across components
export const colors = {
    primary: {
        main: "#3498db",
        light: "#5dade2",
        dark: "#2980b9",
    },
    success: {
        bg: "#e8f5e9",
        text: "#2e7d32",
        border: "#27ae60",
        main: "#28a745",
    },
    error: {
        bg: "#ffebee",
        text: "#c62828",
        border: "#e74c3c",
        main: "#dc3545",
    },
    warning: {
        bg: "#fff8e1",
        text: "#ff8f00",
        border: "#f39c12",
        main: "#ffc107",
    },
    info: {
        bg: "#e3f2fd",
        text: "#1976d2",
        border: "#2196f3",
        main: "#17a2b8",
    },
    neutral: {
        bg: "#ffffff",
        border: "#e9ecef",
        text: "#495057",
        icon: "#6c757d",
        muted: "#6c757d",
        light: "#f8f9fa",
        medium: "#e0e0e0",
        dark: "#2c3e50",
    },
    grey: {
        bg: "#f5f5f5",
        text: "#616161",
        border: "#bdbdbd",
        light: "#e0e0e0",
    },
    diff: {
        header: "#f1f3f4",
        border: "#dee2e6",
        formLabel: "#dc3545",
        blueprintLabel: "#28a745",
    },
    popup: {
        bg: "#ffffff",
        shadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
        border: "#e0e0e0",
        overlay: "rgba(0, 0, 0, 0.5)",
    },
};

export const spacing = {
    xs: "4px",
    sm: "8px",
    md: "12px",
    lg: "16px",
    xl: "20px",
    xxl: "24px",
    xxxl: "32px",
};

export const typography = {
    xs: "10px",
    sm: "11px",
    md: "12px",
    base: "14px",
    lg: "16px",
    xl: "18px",
    xxl: "20px",
    xxxl: "24px",
    xxxxl: "32px",
};

export const borderRadius = {
    sm: "4px",
    md: "6px",
    lg: "8px",
    xl: "16px",
};

export const shadows = {
    sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
    md: "0 2px 8px rgba(0, 0, 0, 0.1)",
    lg: "0 4px 16px rgba(0, 0, 0, 0.1)",
    xl: "0 10px 30px rgba(0, 0, 0, 0.3)",
};

export const transitions = {
    fast: "all 0.2s ease",
    medium: "all 0.3s ease",
    slow: "all 0.5s ease",
};

export const zIndex = {
    dropdown: 100,
    sticky: 10,
    overlay: 999,
    modal: 1000,
    tooltip: 1001,
};
