import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import {
    Container,
    Paper,
    Typography,
    CircularProgress,
    Box,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@mui/material";

interface AnalyzeData {
    totalBlueprintRuleCount: number;
    parsedRuleLinesPercent: number;
    missingRuleLinesPercent: number;
}

export default function AnalyzePage() {
    const router = useRouter();
    const { blueprintVersionId } = router.query;
    const [analyzeData, setAnalyzeData] = useState<AnalyzeData | null>(null);
    const [error, setError] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        if (!blueprintVersionId) return;

        const fetchAnalyzeData = async () => {
            try {
                const response = await fetch(
                    `/api/analyze/${blueprintVersionId}`,
                );
                if (!response.ok) {
                    throw new Error("Failed to fetch analyze data");
                }
                const data = await response.json();
                setAnalyzeData(data);
            } catch (err) {
                setError(
                    err instanceof Error ? err.message : "An error occurred",
                );
            } finally {
                setLoading(false);
            }
        };

        fetchAnalyzeData();
    }, [blueprintVersionId]);

    if (loading) {
        return (
            <Container maxWidth="md" sx={{ py: 6 }}>
                <Box display="flex" justifyContent="center">
                    <CircularProgress />
                </Box>
            </Container>
        );
    }

    if (error) {
        return (
            <Container maxWidth="md" sx={{ py: 6 }}>
                <Typography color="error">{error}</Typography>
            </Container>
        );
    }

    return (
        <Container maxWidth="md" sx={{ py: 6 }}>
            <Paper sx={{ p: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Blueprint Analysis Report
                </Typography>
                {analyzeData && (
                    <Box sx={{ mt: 4 }}>
                        <TableContainer component={Paper} sx={{ boxShadow: 3 }}>
                            <Table
                                sx={{ minWidth: 250 }}
                                aria-label="analysis results table"
                            >
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{ fontWeight: "bold" }}>
                                            Name
                                        </TableCell>
                                        <TableCell
                                            align="right"
                                            sx={{ fontWeight: "bold" }}
                                        >
                                            Count
                                        </TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    <TableRow>
                                        <TableCell component="th" scope="row">
                                            Total Rule Count
                                        </TableCell>
                                        <TableCell align="right">
                                            {
                                                analyzeData.totalBlueprintRuleCount
                                            }
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell component="th" scope="row">
                                            % Generated Rules
                                        </TableCell>
                                        <TableCell align="right">
                                            {analyzeData.parsedRuleLinesPercent}
                                            %
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell component="th" scope="row">
                                            % Missing Rules
                                        </TableCell>
                                        <TableCell align="right">
                                            {
                                                analyzeData.missingRuleLinesPercent
                                            }
                                            %
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                )}
            </Paper>
        </Container>
    );
}
