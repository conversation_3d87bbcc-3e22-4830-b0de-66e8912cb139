import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { Container, Typography, CircularProgress, Box } from "@mui/material";
import { BlueprintApplicationReport } from "@/models/formMapping";
import Report from "@/ui-components/report/report";
import Header from "@/ui-components/report/Header";

export default function ApplicationReportPage() {
    const router = useRouter();
    const { id } = router.query;
    const [reportData, setReportData] =
        useState<BlueprintApplicationReport | null>(null);
    const [error, setError] = useState<string>("");
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (!id) return;

        const [blueprintVersionId, formVersionId] = (id as string).split("--");

        const fetchReportData = async () => {
            try {
                const response = await fetch(
                    `/api/getBlueprintApplicationReport/${blueprintVersionId}--${formVersionId}`,
                );
                if (!response.ok) {
                    throw new Error("Failed to fetch report data");
                }
                const data = await response.json();
                setReportData(data);
            } catch (err: any) {
                setError(err?.message || "An unexpected error occurred");
            } finally {
                setLoading(false);
            }
        };

        fetchReportData();
    }, [id]);

    if (loading) {
        return (
            <Container>
                <Box display="flex" justifyContent="center">
                    <CircularProgress />
                </Box>
            </Container>
        );
    }

    if (error) {
        return (
            <Container>
                <Typography color="error">{error}</Typography>
            </Container>
        );
    }

    return (
        <>
            <Header />
            <Container
                maxWidth="xl"
                sx={{ py: 6, px: { xs: 2, sm: 3 }, pt: 15 }}
            >
                {reportData && (
                    <Report blueprintApplicationResult={reportData} />
                )}
            </Container>
        </>
    );
}
