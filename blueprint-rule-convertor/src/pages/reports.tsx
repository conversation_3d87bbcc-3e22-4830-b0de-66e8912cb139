import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import {
    Box,
    Container,
    Typography,
    Link,
    Paper,
    CircularProgress,
    TableContainer,
    TableHead,
    Table,
    TableCell,
    TableRow,
    TableBody,
    TableSortLabel,
    TextField,
    InputAdornment,
    IconButton,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import Header from "../ui-components/report/Header";
import { BlueprintApplicationReportMetaData } from "@/script/components/parameters";

type Order = "asc" | "desc";
type OrderBy =
    | "formName"
    | "formVersionCount"
    | "blueprintName"
    | "formUpdatedAt"
    | "successRate";

function Reports() {
    const router = useRouter();
    const [reports, setReports] = useState<
        BlueprintApplicationReportMetaData[]
    >([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string>("");
    const [order, setOrder] = useState<Order>("desc");
    const [orderBy, setOrderBy] = useState<OrderBy>("formUpdatedAt");
    const [searchInput, setSearchInput] = useState<string>("");

    // Extract searchText from query parameters
    const searchText = router.query.searchText as string | undefined;

    // Initialize search input from URL parameter
    useEffect(() => {
        if (router.isReady && searchText) {
            setSearchInput(searchText);
        }
    }, [router.isReady, searchText]);

    useEffect(() => {
        // Only fetch when router is ready to avoid undefined query params
        if (router.isReady) {
            setIsLoading(true);
            setError("");
            fetchReports();
        }
    }, [router.isReady, searchText]);

    const fetchReports = async () => {
        try {
            // Build the API URL with optional searchText parameter
            let apiUrl = "/api/getAllBlueprintApplicationReports";
            if (searchText) {
                apiUrl += `?searchText=${encodeURIComponent(searchText)}`;
            }

            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error("Failed to fetch reports");
            }
            const data = await response.json();
            setReports(data);
        } catch (err) {
            setError("Failed to load reports. Please try again later.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleRequestSort = (property: OrderBy) => {
        const isAsc = orderBy === property && order === "asc";
        setOrder(isAsc ? "desc" : "asc");
        setOrderBy(property);
    };

    const handleSearch = () => {
        if (searchInput.trim()) {
            // Update URL with searchText parameter
            router.push(
                `/reports?searchText=${encodeURIComponent(searchInput.trim())}`,
                undefined,
                { shallow: true },
            );
        } else {
            // Clear searchText parameter
            router.push("/reports", undefined, { shallow: true });
        }
    };

    const handleClearSearch = () => {
        setSearchInput("");
        // Clear searchText parameter from URL
        router.push("/reports", undefined, { shallow: true });
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === "Enter") {
            handleSearch();
        }
    };

    const sortedReports = React.useMemo(() => {
        const comparator = (
            a: BlueprintApplicationReportMetaData,
            b: BlueprintApplicationReportMetaData,
        ) => {
            let aValue = a[orderBy];
            let bValue = b[orderBy];

            if (orderBy === "successRate") {
                aValue = parseFloat(a.successRate);
                bValue = parseFloat(b.successRate);
            } else if (orderBy === "formUpdatedAt") {
                aValue = a.formUpdatedAt
                    ? new Date(a.formUpdatedAt).getTime()
                    : 0;
                bValue = b.formUpdatedAt
                    ? new Date(b.formUpdatedAt).getTime()
                    : 0;
            }
            if ((bValue as any) < (aValue as any))
                return order === "asc" ? 1 : -1;
            if ((bValue as any) > (aValue as any))
                return order === "asc" ? -1 : 1;
            return 0;
        };

        return [...reports].sort(comparator);
    }, [reports, order, orderBy]);

    return (
        <>
            <Header />
            <Container
                maxWidth="xl"
                sx={{ py: 6, px: { xs: 2, sm: 3 }, pt: 15 }}
            >
                <Paper
                    elevation={6}
                    sx={{
                        p: { xs: 3, sm: 5 },
                        borderRadius: 3,
                        background:
                            "linear-gradient(to bottom right, #ffffff, #f8f9fa)",
                        boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
                    }}
                >
                    <Typography
                        variant="h4"
                        component="h1"
                        gutterBottom
                        sx={{
                            mb: 3,
                            fontWeight: "700",
                            color: "#1976d2",
                            textAlign: "center",
                            fontSize: { xs: "1.8rem", sm: "2.2rem" },
                        }}
                    >
                        Blueprint Application Reports
                    </Typography>

                    {/* Search Section */}
                    <Box
                        sx={{
                            mb: 4,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            gap: 2,
                        }}
                    >
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 2,
                                width: "100%",
                                maxWidth: "600px",
                            }}
                        >
                            <TextField
                                fullWidth
                                variant="outlined"
                                placeholder="Enter a Form ID, Form Name, Blueprint ID or Blueprint Name to filter reports."
                                value={searchInput}
                                onChange={(e) => setSearchInput(e.target.value)}
                                onKeyDown={handleKeyDown}
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon color="action" />
                                            </InputAdornment>
                                        ),
                                        endAdornment: searchInput && (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={handleClearSearch}
                                                    edge="end"
                                                    size="small"
                                                    sx={{ mr: 1 }}
                                                >
                                                    <ClearIcon />
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                    },
                                }}
                                sx={{
                                    "& .MuiOutlinedInput-root": {
                                        borderRadius: "25px",
                                        backgroundColor: "#f8f9fa",
                                        "&:hover": {
                                            backgroundColor: "#e9ecef",
                                        },
                                        "&.Mui-focused": {
                                            backgroundColor: "white",
                                        },
                                    },
                                }}
                            />
                            <IconButton
                                onClick={handleSearch}
                                color="primary"
                                sx={{
                                    backgroundColor: "#1976d2",
                                    color: "white",
                                    "&:hover": {
                                        backgroundColor: "#1565c0",
                                    },
                                    borderRadius: "50%",
                                    width: 48,
                                    height: 48,
                                }}
                            >
                                <SearchIcon />
                            </IconButton>
                        </Box>
                    </Box>

                    {isLoading && (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                my: 4,
                            }}
                        >
                            <CircularProgress />
                        </Box>
                    )}

                    {error && (
                        <Typography
                            color="error"
                            sx={{ mt: 2, textAlign: "center" }}
                        >
                            {error}
                        </Typography>
                    )}

                    {!isLoading && !error && reports.length === 0 && (
                        <Typography sx={{ textAlign: "center", mt: 4 }}>
                            {searchText
                                ? `No reports found for search text: ${searchText}`
                                : "No reports available."}
                        </Typography>
                    )}

                    {!isLoading && !error && reports.length > 0 && (
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ textAlign: "center", mb: 2 }}
                        >
                            {searchText
                                ? `Found ${reports.length} report${reports.length === 1 ? "" : "s"} for search text: ${searchText}`
                                : `Showing ${reports.length} report${reports.length === 1 ? "" : "s"}`}
                        </Typography>
                    )}

                    <Box sx={{ mt: 4 }}>
                        <TableContainer>
                            <Table
                                sx={{ minWidth: 650 }}
                                aria-label="blueprint reports table"
                            >
                                <TableHead>
                                    <TableRow
                                        sx={{
                                            bgcolor: "primary.main",
                                            "& th": {
                                                color: "white",
                                                fontWeight: 600,
                                            },
                                        }}
                                    >
                                        <TableCell width="30%">
                                            <TableSortLabel
                                                active={orderBy === "formName"}
                                                direction={
                                                    orderBy === "formName"
                                                        ? order
                                                        : "asc"
                                                }
                                                onClick={() =>
                                                    handleRequestSort(
                                                        "formName",
                                                    )
                                                }
                                                sx={{
                                                    "&.MuiTableSortLabel-root":
                                                        { color: "white" },
                                                    "&.MuiTableSortLabel-root:hover":
                                                        { color: "white" },
                                                    "&.Mui-active": {
                                                        color: "white",
                                                    },
                                                    "& .MuiTableSortLabel-icon":
                                                        {
                                                            color: "white !important",
                                                        },
                                                }}
                                            >
                                                Form Name
                                            </TableSortLabel>
                                        </TableCell>
                                        <TableCell width="12%">
                                            <TableSortLabel
                                                active={
                                                    orderBy ===
                                                    "formVersionCount"
                                                }
                                                direction={
                                                    orderBy ===
                                                    "formVersionCount"
                                                        ? order
                                                        : "asc"
                                                }
                                                onClick={() =>
                                                    handleRequestSort(
                                                        "formVersionCount",
                                                    )
                                                }
                                                sx={{
                                                    "&.MuiTableSortLabel-root":
                                                        { color: "white" },
                                                    "&.MuiTableSortLabel-root:hover":
                                                        { color: "white" },
                                                    "&.Mui-active": {
                                                        color: "white",
                                                    },
                                                    "& .MuiTableSortLabel-icon":
                                                        {
                                                            color: "white !important",
                                                        },
                                                }}
                                            >
                                                Form Version
                                            </TableSortLabel>
                                        </TableCell>
                                        <TableCell width="30%">
                                            <TableSortLabel
                                                active={
                                                    orderBy === "blueprintName"
                                                }
                                                direction={
                                                    orderBy === "blueprintName"
                                                        ? order
                                                        : "asc"
                                                }
                                                onClick={() =>
                                                    handleRequestSort(
                                                        "blueprintName",
                                                    )
                                                }
                                                sx={{
                                                    "&.MuiTableSortLabel-root":
                                                        { color: "white" },
                                                    "&.MuiTableSortLabel-root:hover":
                                                        { color: "white" },
                                                    "&.Mui-active": {
                                                        color: "white",
                                                    },
                                                    "& .MuiTableSortLabel-icon":
                                                        {
                                                            color: "white !important",
                                                        },
                                                }}
                                            >
                                                Application Report
                                            </TableSortLabel>
                                        </TableCell>
                                        <TableCell width="14%">
                                            <TableSortLabel
                                                active={
                                                    orderBy === "formUpdatedAt"
                                                }
                                                direction={
                                                    orderBy === "formUpdatedAt"
                                                        ? order
                                                        : "asc"
                                                }
                                                onClick={() =>
                                                    handleRequestSort(
                                                        "formUpdatedAt",
                                                    )
                                                }
                                                sx={{
                                                    "&.MuiTableSortLabel-root":
                                                        { color: "white" },
                                                    "&.MuiTableSortLabel-root:hover":
                                                        { color: "white" },
                                                    "&.Mui-active": {
                                                        color: "white",
                                                    },
                                                    "& .MuiTableSortLabel-icon":
                                                        {
                                                            color: "white !important",
                                                        },
                                                }}
                                            >
                                                Version Created At
                                            </TableSortLabel>
                                        </TableCell>
                                        <TableCell width="14%">
                                            <TableSortLabel
                                                active={
                                                    orderBy === "successRate"
                                                }
                                                direction={
                                                    orderBy === "successRate"
                                                        ? order
                                                        : "asc"
                                                }
                                                onClick={() =>
                                                    handleRequestSort(
                                                        "successRate",
                                                    )
                                                }
                                                sx={{
                                                    "&.MuiTableSortLabel-root":
                                                        { color: "white" },
                                                    "&.MuiTableSortLabel-root:hover":
                                                        { color: "white" },
                                                    "&.Mui-active": {
                                                        color: "white",
                                                    },
                                                    "& .MuiTableSortLabel-icon":
                                                        {
                                                            color: "white !important",
                                                        },
                                                }}
                                            >
                                                Success Rate
                                            </TableSortLabel>
                                        </TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {sortedReports.map((report, index) => (
                                        <TableRow
                                            key={index}
                                            sx={{
                                                "&:nth-of-type(odd)": {
                                                    backgroundColor:
                                                        "rgba(0, 0, 0, 0.02)",
                                                },
                                                "&:hover": {
                                                    backgroundColor:
                                                        "rgba(25, 118, 210, 0.04)",
                                                },
                                            }}
                                        >
                                            <TableCell
                                                width="30%"
                                                sx={{
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    maxWidth: 0,
                                                }}
                                            >
                                                {report.formName}
                                            </TableCell>
                                            <TableCell
                                                width="12%"
                                                sx={{
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    maxWidth: 0,
                                                }}
                                            >
                                                <Link
                                                    href={report.formLink}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    sx={{
                                                        textDecoration: "none",
                                                        color: "#1976d2",
                                                        "&:hover": {
                                                            textDecoration:
                                                                "underline",
                                                        },
                                                    }}
                                                >
                                                    {`Version ${report.formVersionCount}`}
                                                </Link>
                                            </TableCell>
                                            <TableCell
                                                width="30%"
                                                sx={{
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    maxWidth: 0,
                                                }}
                                            >
                                                <Link
                                                    href={report.reportLink}
                                                    sx={{
                                                        textDecoration: "none",
                                                        color: "#1976d2",
                                                        "&:hover": {
                                                            textDecoration:
                                                                "underline",
                                                        },
                                                    }}
                                                >
                                                    {report.blueprintName}
                                                </Link>
                                            </TableCell>
                                            <TableCell width="14%">
                                                {report.formUpdatedAt
                                                    ? new Date(
                                                          report.formUpdatedAt,
                                                      ).toLocaleDateString(
                                                          undefined,
                                                          {
                                                              year: "numeric",
                                                              month: "short",
                                                              day: "numeric",
                                                              hour: "2-digit",
                                                              minute: "2-digit",
                                                          },
                                                      )
                                                    : ""}
                                            </TableCell>
                                            <TableCell width="14%">
                                                <Typography>
                                                    {report.successRate}
                                                </Typography>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                </Paper>
            </Container>
        </>
    );
}

export default Reports;
