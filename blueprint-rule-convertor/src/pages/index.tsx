import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import Container from "@mui/material/Container";
import Divider from "@mui/material/Divider";
import Alert from "@mui/material/Alert";
import { Link } from "@mui/material";
import Header from "../ui-components/report/Header";
import {
    ProcessBlueprintFormResponse,
    UpdateBlueprintFormApplicationParameters,
    UpdateBlueprintFormApplicationResponse,
} from "@/script/components/parameters";

function Home() {
    const [hostname, setHostname] = useState<string>("");
    const [bearerToken, setBearerToken] = useState<string>("");
    const [formId, setFormId] = useState<string>("");
    const [addMissingRule, setAddMissingRule] = useState<boolean>(true);
    const [addMissingField, setAddMissingField] = useState<boolean>(true);
    const [processParameterResult, setProcessParameterResult] = useState<
        ProcessBlueprintFormResponse | undefined
    >(undefined);
    const [result, setResult] = useState<string>("");
    const [error, setError] = useState<string>("");
    const [updated, setUpdated] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isUpdatingForm, setIsUpdatingForm] = useState<boolean>(false);
    const [updateFormSuccess, setUpdateFormSuccess] =
        useState<UpdateBlueprintFormApplicationResponse | null>(null);

    useEffect(() => {
        const storedData = localStorage.getItem("processBlueprintForm");
        if (storedData) {
            const storedForm = JSON.parse(storedData);
            setHostname(storedForm.hostname || "");
            setBearerToken(storedForm.bearerToken || "");
            setFormId(storedForm.formId || "");
            setAddMissingRule(storedForm.addMissingRule ?? true);
            setAddMissingField(storedForm.addMissingField ?? true);
        }
        setUpdated(true);
    }, []);

    useEffect(() => {
        if (!updated) {
            return;
        }
        const processBlueprintFormData = {
            hostname,
            bearerToken,
            formId,
            addMissingRule,
            addMissingField,
        };
        localStorage.setItem(
            "processBlueprintForm",
            JSON.stringify(processBlueprintFormData),
        );
    }, [
        hostname,
        bearerToken,
        formId,
        addMissingRule,
        addMissingField,
        updated,
    ]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError("");
        setResult("");
        setIsLoading(true);
        setProcessParameterResult(undefined);
        setUpdateFormSuccess(null);

        const params = {
            hostname,
            bearerToken,
            formId,
            addMissingRule,
            addMissingField,
        };

        try {
            const processingResponse = await fetch(
                `${window.location.href}api/processBlueprintForm`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(params),
                },
            );

            if (!processingResponse.ok) {
                const errorText = await processingResponse.text();
                setError(errorText || "Server error.");
                return;
            }

            const data =
                (await processingResponse.json()) as ProcessBlueprintFormResponse;
            setProcessParameterResult(data);

            setResult(
                `The application of blueprint <strong>${data.blueprintName} (Version ${data.blueprintVersionIndex})</strong> on form <strong>${data.formName} (Version ${data.formVersionIndex})</strong> has been processed.`,
            );
        } catch (err) {
            setError("Network error.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleUpdateForm = async () => {
        if (!processParameterResult) return;

        setIsUpdatingForm(true);
        setError("");
        setUpdateFormSuccess(null);

        try {
            const params: UpdateBlueprintFormApplicationParameters = {
                hostname,
                bearerToken,
                formId,
                formVersionId: processParameterResult.formVersionId,
                blueprintVersionId: processParameterResult.blueprintVersionId,
            };

            const response = await fetch(
                "/api/updateBlueprintFormApplication",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(params),
                },
            );

            if (!response.ok) {
                const errorText = await response.text();
                setError(errorText || "Failed to update form.");
                return;
            }

            const data =
                (await response.json()) as UpdateBlueprintFormApplicationResponse;
            setUpdateFormSuccess(data);
        } catch (err) {
            setError("Network error while updating form.");
        } finally {
            setIsUpdatingForm(false);
        }
    };

    // Render a header bar at the top of this page. The header should have 2 navigation which is Home and Reports
    return (
        <>
            <Header />
            <Container
                maxWidth="md"
                sx={{ py: 6, px: { xs: 2, sm: 3 }, pt: 15 }}
            >
                <Paper
                    elevation={6}
                    sx={{
                        p: { xs: 3, sm: 5 },
                        borderRadius: 3,
                        background:
                            "linear-gradient(to bottom right, #ffffff, #f8f9fa)",
                        boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
                    }}
                >
                    <Typography
                        variant="h4"
                        component="h1"
                        gutterBottom
                        sx={{
                            mb: 3,
                            fontWeight: "700",
                            color: "#1976d2",
                            textAlign: "center",
                            fontSize: { xs: "1.8rem", sm: "2.2rem" },
                        }}
                    >
                        ✨ Blueprint Rule Generator
                    </Typography>
                    <Divider sx={{ mb: 4, borderColor: "#e0e0e0" }} />

                    <Box
                        component="form"
                        onSubmit={handleSubmit}
                        sx={{
                            "& .MuiFormControl-root": { mb: 3 },
                            "& .MuiInputLabel-root": { fontWeight: "500" },
                            "& .MuiOutlinedInput-root": {
                                borderRadius: 2,
                                "&:hover fieldset": {
                                    borderColor: "#1976d2",
                                },
                            },
                        }}
                    >
                        {/* Environment Information */}
                        <Typography
                            variant="h6"
                            sx={{
                                mb: 2,
                                color: "#1976d2",
                                fontWeight: "600",
                            }}
                        >
                            Environment Information
                        </Typography>

                        <FormControl fullWidth>
                            <InputLabel
                                id="hostname-label"
                                sx={{ backgroundColor: "white" }}
                            >
                                Host Name *
                            </InputLabel>
                            <Select
                                labelId="hostname-label"
                                value={hostname}
                                onChange={(e) => setHostname(e.target.value)}
                                required
                                sx={{ "& .MuiSelect-select": { py: 1.5 } }}
                            >
                                <MenuItem value="portal.anduin.app">
                                    portal.anduin.app
                                </MenuItem>
                                <MenuItem value="portal.eu.anduin.app">
                                    portal.eu.anduin.app
                                </MenuItem>
                            </Select>
                        </FormControl>

                        <TextField
                            label="Bearer Token"
                            value={bearerToken}
                            onChange={(e) => setBearerToken(e.target.value)}
                            required
                            fullWidth
                            sx={{ "& .MuiInputBase-input": { py: 1.5 } }}
                        />

                        {/* Blueprint Information */}
                        <Typography
                            variant="h6"
                            sx={{
                                mt: 4,
                                mb: 2,
                                color: "#1976d2",
                                fontWeight: "600",
                            }}
                        >
                            Blueprint Information
                        </Typography>

                        <TextField
                            label="Blueprint Form ID"
                            value={formId}
                            onChange={(e) => setFormId(e.target.value)}
                            required
                            fullWidth
                            sx={{ "& .MuiInputBase-input": { py: 1.5 } }}
                        />

                        {/* Additional Configuration */}
                        <Typography
                            variant="h6"
                            sx={{
                                mt: 4,
                                mb: 2,
                                color: "#1976d2",
                                fontWeight: "600",
                            }}
                        >
                            Additional Configuration
                        </Typography>

                        <Box sx={{ mb: 2 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={addMissingRule}
                                        onChange={(e) =>
                                            setAddMissingRule(e.target.checked)
                                        }
                                        color="primary"
                                    />
                                }
                                label="Add missing rules to Form Rule"
                            />
                        </Box>

                        <Box sx={{ mb: 3 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={addMissingField}
                                        onChange={(e) =>
                                            setAddMissingField(e.target.checked)
                                        }
                                        color="primary"
                                    />
                                }
                                label="Add missing mapping fields to Form Schema"
                            />
                        </Box>

                        {/* Generate Button */}
                        <Box sx={{ mt: 4, textAlign: "center" }}>
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                size="large"
                                disabled={isLoading}
                                sx={{
                                    px: 5,
                                    py: 1.5,
                                    borderRadius: 6,
                                    fontWeight: "600",
                                    fontSize: "1rem",
                                    textTransform: "none",
                                    boxShadow:
                                        "0 4px 12px rgba(25, 118, 210, 0.3)",
                                    transition: "all 0.3s",
                                    "&:hover": {
                                        boxShadow:
                                            "0 6px 16px rgba(25, 118, 210, 0.4)",
                                        transform: "translateY(-2px)",
                                    },
                                }}
                            >
                                {isLoading ? (
                                    <>
                                        <span style={{ marginRight: "10px" }}>
                                            Processing
                                        </span>
                                        <CircularProgress
                                            size={20}
                                            color="inherit"
                                        />
                                    </>
                                ) : (
                                    "Process Blueprint Form"
                                )}
                            </Button>
                        </Box>

                        {/* Success Alert with Actions */}
                        {processParameterResult && result && (
                            <Box sx={{ mt: 3 }}>
                                <Alert
                                    severity="success"
                                    sx={{
                                        borderRadius: 2,
                                        fontSize: "0.95rem",
                                        "& .MuiAlert-icon": {
                                            fontSize: "1.5rem",
                                        },
                                        mb: 2,
                                    }}
                                >
                                    <Typography
                                        variant="body1"
                                        dangerouslySetInnerHTML={{
                                            __html: result,
                                        }}
                                    />
                                    <Box sx={{ mt: 1 }}>
                                        <Button
                                            component="a"
                                            href={`/blueprintApplicationReport/${processParameterResult.formVersionId}--${processParameterResult.blueprintVersionId}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            variant="outlined"
                                            size="small"
                                            sx={{ mr: 2, mb: 1 }}
                                        >
                                            View Report
                                        </Button>
                                        <Button
                                            component="a"
                                            href={`/api/downloadTemplateForm/${processParameterResult.blueprintVersionId}`}
                                            variant="outlined"
                                            size="small"
                                            sx={{ mb: 1 }}
                                        >
                                            Download Form Template
                                        </Button>
                                    </Box>
                                </Alert>

                                {/* Update Form Button */}
                                <Box sx={{ textAlign: "center", mt: 3 }}>
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        onClick={handleUpdateForm}
                                        disabled={isUpdatingForm}
                                        sx={{
                                            px: 4,
                                            py: 1.5,
                                            borderRadius: 6,
                                            fontWeight: "600",
                                            fontSize: "1rem",
                                            textTransform: "none",
                                            backgroundColor: "white",
                                            borderWidth: "2px",
                                            transition: "all 0.3s",
                                            "&:hover": {
                                                borderWidth: "2px",
                                                backgroundColor: "#f5f5f5",
                                                transform: "translateY(-2px)",
                                            },
                                        }}
                                    >
                                        {isUpdatingForm ? (
                                            <>
                                                <span
                                                    style={{
                                                        marginRight: "10px",
                                                    }}
                                                >
                                                    Updating...
                                                </span>
                                                <CircularProgress
                                                    size={20}
                                                    color="primary"
                                                />
                                            </>
                                        ) : (
                                            "Update Form"
                                        )}
                                    </Button>
                                </Box>
                            </Box>
                        )}

                        {/* Form Update Success Alert */}
                        {updateFormSuccess && (
                            <Alert
                                severity="success"
                                sx={{
                                    mt: 3,
                                    borderRadius: 2,
                                    fontSize: "0.95rem",
                                    "& .MuiAlert-icon": { fontSize: "1.5rem" },
                                }}
                            >
                                <Typography variant="body1">
                                    Form{" "}
                                    <Link
                                        href={updateFormSuccess.formLink}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        sx={{
                                            textDecoration: "none",
                                            fontWeight: "600",
                                            color: "#1976d2",
                                            "&:hover": {
                                                textDecoration: "underline",
                                            },
                                        }}
                                    >
                                        {updateFormSuccess.formName}
                                    </Link>{" "}
                                    is updated successfully at{" "}
                                    {updateFormSuccess.updatedTime}.
                                </Typography>
                            </Alert>
                        )}

                        {/* Error Alert */}
                        {error && (
                            <Alert
                                severity="error"
                                sx={{
                                    mt: 3,
                                    borderRadius: 2,
                                    fontSize: "0.95rem",
                                    "& .MuiAlert-icon": { fontSize: "1.5rem" },
                                }}
                            >
                                <div
                                    dangerouslySetInnerHTML={{ __html: error }}
                                />
                            </Alert>
                        )}
                    </Box>
                </Paper>
            </Container>
        </>
    );
}

export default Home;
