import type { NextApiRequest, NextApiResponse } from "next";
import fs from "fs";

import {
    BLUEPRINT_PARSER_REPORT_PREFIX,
    getGeneratedOutput,
} from "@/script/components/utils";

export const config = {
    api: {
        bodyParser: true,
    },
};

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    // Accept both GET and POST methods
    if (req.method !== "GET") {
        res.status(405).end(); // Method Not Allowed
        return;
    }

    const { blueprintVersionId } = req.query;

    try {
        const cacheParserReportFilePath = getGeneratedOutput(
            BLUEPRINT_PARSER_REPORT_PREFIX,
            blueprintVersionId as string,
        );

        const parserReport = JSON.parse(
            fs.readFileSync(cacheParserReportFilePath, "utf-8"),
        );

        res.status(200).json(parserReport);
    } catch (error) {
        console.error(`Error in template handler: ${error}`);
        return res.status(500).send(`Error processing template: ${error}`);
    }
}
