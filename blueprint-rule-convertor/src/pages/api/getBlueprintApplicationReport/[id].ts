import type { NextApiRequest, NextApiResponse } from "next";
import fs from "fs";

import {
    BLUEPRINT_APPLICATION_REPORT_PREFIX,
    getGeneratedOutput,
} from "@/script/components/utils";

export const config = {
    api: {
        bodyParser: true,
    },
};

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    // Accept both GET and POST methods
    if (req.method !== "GET") {
        res.status(405).end(); // Method Not Allowed
        return;
    }

    const { id } = req.query;

    const [formVersionId, blueprintVersionId] = (id as string).split("--");

    try {
        const cacheReportFilePath = getGeneratedOutput(
            BLUEPRINT_APPLICATION_REPORT_PREFIX,
            `${formVersionId}__${blueprintVersionId}`,
        );

        const report = JSON.parse(
            fs.readFileSync(cacheReportFilePath, "utf-8"),
        );

        res.status(200).json(report);
    } catch (error) {
        console.error(`Error in template handler: ${error}`);
        return res.status(500).send(`Error processing template: ${error}`);
    }
}
