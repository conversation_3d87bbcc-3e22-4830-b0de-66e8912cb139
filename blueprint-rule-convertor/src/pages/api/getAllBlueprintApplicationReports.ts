import { NextApiRequest, NextApiResponse } from "next";
import fs from "fs";
import path from "path";
import { BlueprintApplicationReportMetaData } from "@/script/components/parameters";
import { extractVersionFromUrl } from "@/script/procedureUtils";

// Simple CSV parser function
function parseCSV(csvContent: string): Record<string, any>[] {
    const lines = csvContent.trim().split("\n");
    if (lines.length < 2) return [];

    // Parse header row
    const headers = lines[0]
        .split(",")
        .map((header) => header.replace(/^"|"$/g, "").trim());

    // Parse data rows
    const records: Record<string, any>[] = [];
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        if (!line.trim()) continue;

        // Simple CSV parsing - handles quoted fields
        const values: string[] = [];
        let currentValue = "";
        let inQuotes = false;

        for (let j = 0; j < line.length; j++) {
            const char = line[j];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === "," && !inQuotes) {
                values.push(currentValue.replace(/^"|"$/g, "").trim());
                currentValue = "";
            } else {
                currentValue += char;
            }
        }
        // Add the last value
        values.push(currentValue.replace(/^"|"$/g, "").trim());

        // Create record object
        const record: Record<string, any> = {};
        headers.forEach((header, index) => {
            let value = values[index] || "";

            // Cast values based on column type
            if (header === "Created At") {
                record[header] = new Date(value);
            } else if (
                header === "Blueprint Version Count" ||
                header === "Form Version Count"
            ) {
                record[header] = parseInt(value) || 0;
            } else {
                record[header] = value;
            }
        });

        records.push(record);
    }

    return records;
}

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    if (req.method !== "GET") {
        return res.status(405).json({ message: "Method not allowed" });
    }

    try {
        const { searchText } = req.query;
        const dataDir = path.join(process.cwd(), "out", "generatedData");
        const csvFilePath = path.join(dataDir, "__reports.csv");

        // Read and parse CSV file
        const csvContent = fs.readFileSync(csvFilePath, "utf-8");
        const records = parseCSV(csvContent);

        // Map CSV records to BlueprintApplicationReportMetaData
        let reports = records.map(
            (record) =>
                ({
                    blueprintName: record["Blueprint Name"],
                    blueprintLink: record["Blueprint Link"],
                    blueprintVersionName: record["Blueprint Version"],
                    blueprintVersionCount: record["Blueprint Version Count"],
                    formName: record["Form Name"],
                    formLink: record["Form Link"],
                    formVersionName: record["Form Version"],
                    formVersionCount: record["Form Version Count"],
                    reportLink: record["Report Link"],
                    successRate: record["Success Rate"],
                    formUpdatedAt: record["Created At"],
                }) as BlueprintApplicationReportMetaData,
        );

        // Filter by searchText if provided
        if (searchText && typeof searchText === "string") {
            reports = reports.filter((report) => {
                const text = (
                    report.blueprintName +
                    report.formName +
                    report.blueprintLink +
                    report.formLink
                ).toLowerCase();

                return text?.includes(searchText.toLowerCase());
            });
        }

        res.status(200).json(reports);
    } catch (error) {
        console.error("Error reading reports:", error);
        res.status(500).json({ message: "Internal server error" });
    }
}
