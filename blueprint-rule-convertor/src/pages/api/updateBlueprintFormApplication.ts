import type { NextApiRequest, NextApiResponse } from "next";

import { processParams } from "@/script/updateBlueprintFormApplication";

export const config = {
    api: {
        bodyParser: true,
    },
};

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    if (req.method !== "POST") {
        res.status(405).end(); // Method Not Allowed
        return;
    }

    try {
        const reponse = await processParams(req.body);

        res.status(200).json(reponse);
    } catch (error) {
        console.error(`Error in template handler: ${error}`);
        return res.status(500).send(`Error processing template: ${error}`);
    }
}
