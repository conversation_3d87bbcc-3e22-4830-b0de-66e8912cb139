import type { NextApiRequest, NextApiResponse } from "next";
import archiver from "archiver";
import fs from "fs";

import { getTemplateFilePath } from "@/utils/pages/api";
import {
    BLUEPRINT_TEMPLATE_FORM_PREFIX,
    getGeneratedOutput,
    getOutputPath,
} from "@/script/components/utils";
import { toSnakeCase } from "@/utils/common";
import { BlueprintModel } from "@/models/stargazer/proto/anduin/blueprint/model/blueprint_model";

export const config = {
    api: {
        bodyParser: true,
    },
};

function returnArchivedFiles(res: NextApiResponse, blueprintVersionId: string) {
    const cacheTemplateFormFilePath = getGeneratedOutput(
        BLUEPRINT_TEMPLATE_FORM_PREFIX,
        blueprintVersionId,
    );

    const cacheBlueprintFilePath = getOutputPath(
        blueprintVersionId,
        "getBlueprint",
    );

    const getBlueprintResponse = JSON.parse(
        fs.readFileSync(cacheBlueprintFilePath, "utf-8"),
    );

    const blueprintModel = BlueprintModel.create(
        getBlueprintResponse.blueprintModel,
    );

    const formDataContent = JSON.parse(
        fs.readFileSync(cacheTemplateFormFilePath, "utf-8"),
    );
    const formIntegrationConfigContent = JSON.parse(
        fs.readFileSync(
            getTemplateFilePath("form_integration_config"),
            "utf-8",
        ),
    );
    const formMetadataContent = JSON.parse(
        fs.readFileSync(getTemplateFilePath("form_metadata"), "utf-8"),
    );
    const recordDataContent = JSON.parse(
        fs.readFileSync(getTemplateFilePath("record_data"), "utf-8"),
    );

    const archive = archiver("zip", {
        zlib: { level: 9 }, // Sets the compression level.
    });

    res.setHeader("Content-Type", "application/zip");
    res.setHeader(
        "Content-Disposition",
        `attachment; filename=form_${toSnakeCase(blueprintModel.name)}.zip`,
    );

    archive.pipe(res);
    archive.append(JSON.stringify(formDataContent, null, 2), {
        name: "form_data.json",
    });
    archive.append(JSON.stringify(formIntegrationConfigContent, null, 2), {
        name: "form_integration_config.json",
    });
    archive.append(JSON.stringify(formMetadataContent, null, 2), {
        name: "form_metadata.json",
    });
    archive.append(JSON.stringify(recordDataContent, null, 2), {
        name: "record_data.json",
    });
    archive.finalize();
}

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    if (req.method !== "GET") {
        res.status(405).end(); // Method Not Allowed
        return;
    }

    const { blueprintVersionId } = req.query;

    try {
        returnArchivedFiles(res, blueprintVersionId as string);
    } catch (error) {
        console.error(`Error in template handler: ${error}`);
        return res.status(500).send(`Error processing template: ${error}`);
    }
}
