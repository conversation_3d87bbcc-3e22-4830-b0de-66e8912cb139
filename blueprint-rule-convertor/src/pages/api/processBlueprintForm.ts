import type { NextApiRequest, NextApiResponse } from "next";

import { generateGeneralErrorMessage } from "@/utils/pages/api";
import { processParams } from "@/script/processBlueprintFormProcedure";

export const config = {
    api: {
        bodyParser: true,
    },
};

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse,
) {
    if (req.method !== "POST") {
        res.status(405).end(); // Method Not Allowed
        return;
    }

    try {
        const response = await processParams(req.body);

        res.status(200).json(response);
    } catch (error) {
        console.trace();
        console.error(`Error in API handler: ${error}`);
        res.setHeader("Content-Type", "text/html");
        return res.status(500).send(generateGeneralErrorMessage(error));
    }
}
