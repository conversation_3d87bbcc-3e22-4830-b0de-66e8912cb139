{"name": "blueprint-rule-convertor", "description": "Blueprint Rule Convertor", "version": "1.0.0", "main": "index.ts", "scripts": {"setup": "bun install && mkdir out && mkdir out/generatedData", "test": "bun test", "format": "prettier --write --tab-width 4 .", "openapi": "openapi-typescript", "protoc": "npx protoc", "ui:dev": "next dev -H 0.0.0.0 -p 3000", "ui:build": "next build", "ui:start": "next start -H 0.0.0.0 -p 3000", "ui:lint": "next lint"}, "keywords": ["blueprint"], "author": "<EMAIL>", "license": "ISC", "homepage": "https://github.com/anduintransaction/gandalf/tree/master/blueprint-rule-convertor#readme", "dependencies": {"@anduintransaction/pangea": "^0.4.4b", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "next": "^13.5.11", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@protobuf-ts/plugin": "^2.9.4", "@types/bun": "^1.2.4", "@types/lodash": "^4.17.16", "@types/node": "^22.13.14", "openapi-typescript": "^7.6.1", "prettier": "^3.5.2", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.7.3", "axios": "^1.8.4", "lodash": "^4.17.21", "node-gyp": "^11.0.0", "@tailwindcss/postcss": "^4", "@types/archiver": "^6.0.3", "@types/express": "^5.0.1", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "archiver": "^7.0.1", "cors": "^2.8.5", "express": "^4.21.2", "tailwindcss": "^4"}}