{
    "compilerOptions": {
        "target": "ES2023",
        "useDefineForClassFields": true,
        "lib": ["ES2023"],
        "skipLibCheck": true,
        "paths": {
            "pangea/*": ["./node_modules/@anduintransaction/pangea/*"],
            "@/*": ["./src/*"]
        },
        "outDir": "dist",

        /* Bundler mode */
        "module": "NodeNext",
        "moduleResolution": "nodenext",
        "isolatedModules": true,
        "moduleDetection": "force",
        // "noEmit": true,
        // "declaration":true,
        // "emitDeclarationOnly": true,
        // "allowImportingTsExtensions": true,

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true
    },
    "exclude": ["**/*.test.ts", "**/tests/**"],
    "esModuleInterop": true,
    "noResolve": false,
    "include": ["src"]
}
