{"compilerOptions": {"target": "ES2023", "useDefineForClassFields": true, "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "paths": {"pangea/*": ["./node_modules/@anduintransaction/pangea/*"], "@/*": ["./src/*"]}, "outDir": "dist", "plugins": [{"transform": "typescript-transform-paths"}, {"name": "next"}], "moduleDetection": "force", "declaration": true, "emitDeclarationOnly": true, "allowImportingTsExtensions": true, "strict": true, "allowJs": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true}, "esModuleInterop": true, "noResolve": false, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "src/models/stargazer/openapi", "src/models/stargazer/proto"]}