# Blueprint Rule Convertor

## Overview

The Blueprint Rule Convertor is a tool designed to automate the generation of form logic from structured Blueprints. This automation aims to significantly reduce the manual effort involved in implementing form rules, leading to increased efficiency and accuracy.

## Setup

1.  **Clone the repository:**

    ```bash
    <NAME_EMAIL>:anduintransaction/gandalf.git
    cd gandalf/blueprint-rule-convertor/
    ```

2.  **Setup NPM authentication token:**

    Follow [Pangea's instructions](https://github.com/anduintransaction/pangea?tab=readme-ov-file#to-install-this-npm-package)

3.  **Install dependencies:**

    ```bash
    bun setup
    ```

4.  **Initialize tree-sitter:**
    ```bash
    bun init:tree-sitter
    ```
    VP<PERSON> might need turned off for this step.

## Access to UI

1.  **Start the UI development server:**

    ```bash
    bun ui:dev
    ```

2.  **Open in browser:**
    ```
    http://localhost:3000
    ```

## Testing

- Run tests:
    ```bash
    bun test
    ```

## Additional Information

- Rule Convertor is built based on parser **Pangea - Blueprint Intermediate Representation (BIR):**
    - GitHub: [https://github.com/votruongan/pangea](https://github.com/votruongan/pangea)

## Issue Reporting

For any questions or issues, please refer to the ClickUp task:

- [https://app.clickup.com/7510096/v/l/li/901607271309](https://app.clickup.com/7510096/v/l/li/901607271309)

## Demo

Find demo [here](https://www.notion.so/anduin/Blueprint-Rule-Convertor-Progress-Report-Demo-1c73f653b1df806eb158ee47430fdaba?pvs=4#1c73f653b1df80b6b9c2cefbe3880c7e)
