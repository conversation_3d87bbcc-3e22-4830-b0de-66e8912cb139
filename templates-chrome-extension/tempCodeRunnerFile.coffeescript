#arr = [1, 5, 3, 2]
# Output: 2
# Explanation: There are 2 triplets: 
# 1 + 2 = 3 and 3 +2 = 5 

arr = [1, 5, 3, 2]
arr.sort()

output = []

for i in range(len(arr)):
  for j in range(i + 1, len(arr)):
    start = j + 1
    end = len(arr) - 1
    
    while start < end:
      if start == arr[i] + arr[j] or end == arr[i] + arr[j]:
        output.append((arr[i], arr[j], arr[i] + arr[j]))
        break
      else:
        middle = int((start + end) / 2)
        if arr[i] + arr[j] > arr[middle]:
          start = middle + 1
        else:
          end = middle - 1
          
  print(len(output))
