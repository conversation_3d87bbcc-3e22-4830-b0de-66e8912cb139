function countTriplet(arr)
	{
	   let ans=0;
	    arr = arr.sort();
	    for(let i=0;i<arr.length;i++){
	       let l=i+1,r=l+1;
	   while(r<arr.length){
	       if(arr[i]+arr[l]==arr[r]){
	           ans++;
	           l++;r++;
	       }
	       else if(arr[i]+arr[l]>arr[r]){
	           r++;
	       }
	       else if(arr[i]+arr[l]<arr[r] && l !== r-1){
	           l++;
	       }
	       else{
	           l++;r++;
	       }
	   }
	   }
	    return ans;
	}

  const r = countTriplet([1, 5, 3, 2, 8, 4]);

  console.log('result: ', r)