export default interface ClickHelperEffectOption {
    swapInputsOutputs?: boolean;
    isImmediateEffect?: boolean;
    validator?: (activeId: string) => boolean;
    preprocessor?: (inputs: string[], outputs: string[]) => {inputs: string[], outputs: string[]};
    mainHandler?: ClickHelperHandler;
    menuDisplayElement?: HTMLElement;
    effectDisplayName: string;
} 

export type ClickHelperHandler = (inputs: string[], outputs: string[], formData?: Record<string, any>) => string;