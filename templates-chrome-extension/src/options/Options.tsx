import React, {useEffect, useState} from 'react';
import './Options.css';

function Options() {
    const [mainFile, setMainFile] = useState("");
    const [mainFileName, setMainFileName] = useState("");
    const [noti, setNoti] = useState("");
    const [refNoti, setRefNoti] = useState("");
    const [refs, setRefs] = useState([]);
    const [refFile, setRefFile] = useState("");
    const [refName, setRefName] = useState("");
    const [form, setForm] = useState([]);

    useEffect(() => {
        chrome.storage.local.get(['ref', 'form'], function(result) {
            const form = result.form;
            if (form) {
                setForm(form);
            }
            const refForm = result.ref;
            if (refForm) {
                setRefs(refForm);
            }
        });
    }, []);

    const handleFormChange = (e: any) => {
        const fileReader = new FileReader();
        fileReader.readAsText(e.target.files[0], "UTF-8");
        fileReader.onload = e => {
            // @ts-ignore
            const result = e.target.result;
            // @ts-ignore
            setMainFile(result);
        };
    };

    const submitMainForm = () => {
        const form = [{name: mainFileName, content: mainFile}];
        chrome.storage.local.set({form: form}, function() {
            setNoti('Form is set successfully');
            // @ts-ignore
            setForm(form);
        });
    }

    const deleteForm = (index : number) => {
        chrome.storage.local.set({form: []}, function() {
            setNoti('Form is deleted successfully');
            // @ts-ignore
            setForm([]);
        });
    }

    const handleRefFormChange = (e: any) => {
        const fileReader = new FileReader();
        fileReader.readAsText(e.target.files[0], "UTF-8");
        fileReader.onload = e => {
            // @ts-ignore
            const result = e.target.result;
            // @ts-ignore
            setRefFile(result);
        };
    };

    const submitRefForm = () => {
        const oldRefs = refs.length > 0 ? refs : [];
        const ext = [{name: refName, content: refFile}];
        const newRefs = [...oldRefs, ...ext];
        chrome.storage.local.set({ref: newRefs}, function() {
            setRefNoti('Ref Form is set successfully');
            // @ts-ignore
            setRefs(newRefs);
        });
    }

    const deleteRef = (index : number) => {
        const newRefs = refs.filter((item, idx) => idx !== index);
        chrome.storage.local.set({ref: newRefs}, function() {
            setRefNoti('Ref Form is deleted successfully');
            // @ts-ignore
            setRefs(newRefs);
        });
    }

    const handleRefName = (e: any) => setRefName(e.target.value);

    const handleFormName = (e: any) => setMainFileName(e.target.value);


    return (
        <div className="pl-20 pr-20 pb-10">
            <h1>Upload Form Content File </h1>
            <p className="upload-notification">{noti}</p>
            <input value={mainFileName} onChange={handleFormName}/>
            <input type="file" onChange={handleFormChange} />
            <button onClick={submitMainForm}>Add Form</button>
            <p className="upload-notification">{noti}</p>
            <br />
            {
                form.length > 0 && form.map && form.map((item, index) => {
                    return (<div>
                        <span className="mr-10"> {item['name']} </span>
                        <button onClick={() => deleteForm(index)}>Delete</button>
                    </div>);
                })
            }
            <br />
            <h1>Upload Reference form </h1>
            <p className="upload-notification">{refNoti}</p>
            <input value={refName} onChange={handleRefName}/>
            <input type="file" onChange={handleRefFormChange} />
            <button onClick={submitRefForm}>Add Ref Form</button>
            <p className="upload-notification">{refNoti}</p>
            <br />
            {
                refs.length > 0 && refs.map((item, index) => {
                    return (<div>
                        {item['name']}
                        <button onClick={() => deleteRef(index)}>Delete</button>
                    </div>);
                })
            }
        </div>
    );
}

export default Options;
