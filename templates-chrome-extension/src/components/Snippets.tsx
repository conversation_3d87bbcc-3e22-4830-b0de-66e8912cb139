import React, {useEffect, useState, useMemo} from "react";
import {v1, v2} from './functions';

const Function = ({func, index}: {func: any, index: number}) => {
    const [copied, setCopy] = useState(false);

    function copyTextToClipboard(text: string) {
        //Create a textbox field where we can insert text to.
        var copyFrom = document.createElement("textarea");

        //Set the text content to be the text you wished to copy.
        copyFrom.textContent = text;
        //Append the textbox field into the body as a child.
        //"execCommand()" only works when there exists selected text, and the text is inside
        //document.body (meaning the text is part of a valid rendered HTML element).
        document.body.appendChild(copyFrom);
        //Select all the text!
        copyFrom.select();
        //Execute command
        document.execCommand('copy');
        //(Optional) De-select the text using blur().
        copyFrom.blur();
        //Remove the textbox field from the document.body, so no other JavaScript nor
        //other elements can get access to this.
        document.body.removeChild(copyFrom);
    }

    const handleCopy = () => {
        setCopy(true);
        copyTextToClipboard(func.function);
        setTimeout(() => {
            setCopy(false);
        }, 1000);
    }

    return (<div key={`function_${index}`} className="function-item">
        <div className="tooltip">
            <button className="copy-button" onClick={handleCopy}>Copy</button>
            <span className="tooltiptext">{copied ? 'Copied' : 'Copy'}</span>
        </div>
        <span className="function-name">{func.name}</span>
    </div>)
}

function Snippet() {
    const [searchKeyword, setKeyword] = useState("");
    const [version, setVersion] = useState('v1');

    const functionList = useMemo(() => {
        let functionList = version === 'v1' ? v1 : v2;

        if (searchKeyword !== "") {
            return functionList.filter(i => i.name?.toLowerCase().includes(searchKeyword.toLowerCase()));
        } return functionList;
    }, [searchKeyword, version]);

    const handleSearch = (e: any) => {
        setKeyword(e.target.value);
    }

    const changeVersion = (e: any) => {
        setVersion(e.target.value)
    }

    // @ts-ignore
    return (
        <section id="snippet">
                <div className="pl-20 pr-20 pb-10">
                    <div className="df align-center justify-between">
                        <h3>List of functions</h3>
                        <div className="df align-center">
                            <h3 style={{marginRight: 4}}>Version:</h3>
                            <select name="version" id="version" onChange={changeVersion}>
                                <option value="v1">V1</option>
                                <option value="v2">V2</option>
                            </select>
                        </div>
                    </div>
                    <div className="search-box">
                        <input value={searchKeyword} type="text" onChange={handleSearch} />
                        <button onClick={() => setKeyword("")}>Clear</button>
                    </div>
                </div>
                <div className="padding-20 function-content">
                    {functionList.map((f, i) => (<Function index={i} func={f} />))}
                </div>
            </section>
    );
}

export default Snippet;
