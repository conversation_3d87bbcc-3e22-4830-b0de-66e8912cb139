import React, {useEffect, useState} from "react";
import <PERSON>ynta<PERSON><PERSON>ighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { <PERSON><PERSON>, <PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON><PERSON>se, OverlayToaster, Position, Intent } from "@blueprintjs/core";

var fuzzyEqual = require('fuzzy-equal');

const AppToaster = OverlayToaster.create({
    className: "recipe-toaster",
    position: Position.TOP,
});

function copyTextToClipboard(text: string) {
    //Create a textbox field where we can insert text to.
    var copyFrom = document.createElement("textarea");

    //Set the text content to be the text you wished to copy.
    copyFrom.textContent = text;
    //Append the textbox field into the body as a child.
    //"execCommand()" only works when there exists selected text, and the text is inside
    //document.body (meaning the text is part of a valid rendered HTML element).
    document.body.appendChild(copyFrom);
    //Select all the text!
    copyFrom.select();
    //Execute command
    document.execCommand('copy');
    //(Optional) De-select the text using blur().
    copyFrom.blur();
    //Remove the textbox field from the document.body, so no other JavaScript nor
    //other elements can get access to this.
    document.body.removeChild(copyFrom);
}

function getComponentsFromForm(obj: any) : any {
    // @ts-ignore
    const objs = Object.entries(obj)
        // @ts-ignore
        .reduce((acc, [key, value]) => (value != null && value.hasOwnProperty('name') && (value.hasOwnProperty('enum') || value.hasOwnProperty('items')))
                // @ts-ignore
                ? acc.concat({name: value['name'], value: value})//['schema']})
                : (value != null && typeof value === 'object')
                    ? acc.concat(getComponentsFromForm(value))
                    : acc
            , []);
    return objs;
}

function getUICompFromForm(compName: any, fullForm: any): Array<any> {
    const objs = Object.entries(fullForm)
        // @ts-ignore
        .reduce((acc, [key, value]) => (key === compName && value.hasOwnProperty("ui:formattedText"))
                // @ts-ignore
                ? acc.concat(value)
                : (value != null && typeof value === 'object')
                    // @ts-ignore
                    ? acc.concat(getUICompFromForm(compName, value))
                    : acc
            , []);
    return objs;
}

function getUIRefCompBySimilarity(component: any, refComponents: Array<any>, ratio: number): Array<any> {
    const _ratio = ratio > 0 ? ratio : 80;
    const compContent = JSON.stringify(component);
    const objs = refComponents
        .map((refComponent: any) => ({ratio: fuzzyEqual(component, refComponent).similarity * 100, ...refComponent}))
        .filter((refComponent: any) => refComponent.ratio >= _ratio)
        .sort((a, b) => b.ratio - a.ratio);
    return objs;
}

function getAllInputFromComp(component: any) : any {
    const objs = Object.entries(component)
        // @ts-ignore
        .reduce((acc, [key, value]) => (value != null && (value['type'] === 'string' || value['type'] === 'array'))
                // @ts-ignore
                ? acc.concat({name: key, value: value})
                : (value != null && typeof value === 'object')
                    ? acc.concat(getAllInputFromComp(value))
                    : acc
            , []);
    return objs;
}

function componentsToString(components: any) {
    return components.map((item: any)  => ({
        name: item.name,
        value: JSON.stringify(item.value)
    }));
}

function getTheFirstComponentThatHasRules(components: Array<any>, form: any): any {
    return components.find((component: any) => getRelatedRules(component.name, form).length > 0);
}

function getRelatedRules(compName: any, form: any) {
    const rules = form['form']['rules'];
    return rules.filter((item: any) => item['value'].includes(compName));
}

function getFlattenSchema(schema: any) {
    const flattenSchema: any[] = [];

    function traverse(obj: any, parent = null) {
      for (const key in obj) {
        const value = obj[key];

        if (Array.isArray(value) && value !== null) {
          traverse(value, obj);
        } else if (value !== null && value.hasOwnProperty("name")) {
            flattenSchema.push({ parent, ...value });
            traverse(value, obj);
        } else {
        }
      }
    }

    traverse(schema, null);
    return flattenSchema;
}

function getSimplifiedSchema(schema: any) {
    const simplifiedSchema: any[] = [];

    function traverse(obj: any, parent = null) {
      if (obj && obj.hasOwnProperty("name")) {
          simplifiedSchema.push({
              name: obj.hasOwnProperty("name") ? obj.name : "",
              type: obj.type,
              title: obj.title,
              parent,
              // @ts-ignore
              siblings: parent != null && parent.hasOwnProperty("properties") ? parent.properties : [],
              children: obj.hasOwnProperty("properties") ? obj.properties : [],
          });
      }
      if (obj && obj.hasOwnProperty("properties")) {
           obj.properties.map((field: any) => traverse(field, obj));
      }
    }

    traverse(schema, null);
    // @ts-ignore
    const simplifiedNames = [...simplifiedSchema].map((item) => item.name);
    // @ts-ignore
    return simplifiedSchema.map((field: any) => ({
        ...field,
        parentIndex: simplifiedNames.indexOf(field.parent.name),
        siblingsIndexes: field.siblings.map((sibling: any) => simplifiedNames.indexOf(sibling.name)),
        childrenIndexes: field.children.map((child: any) => simplifiedNames.indexOf(child.name)),
    }));
}

function getFlattenUISchema(schema: any) {
    const flattenSchema: any[] = [];

    function traverse(obj: any, parent = null) {
      for (const key in obj) {
        const value = obj[key];

        if (value !== null && value.hasOwnProperty("ui:widget")) {
            flattenSchema.push({name: key, parent, ...value });
        } else {
        }
      }
    }

    traverse(schema, null);
    return flattenSchema;
}

function getFlattenForm(form: any): any {
    const schema = form['form']['namespaceFormSchemaMap']['main']['schema'];
    const uiSchema = form['form']['namespaceFormSchemaMap']['main']['uiSchema'];
    const flattenSchema = getFlattenSchema(schema);
    const simplifiedSchema = getSimplifiedSchema(schema);
    const flattenUISchema = getFlattenUISchema(uiSchema);
    const components = flattenUISchema.map((uiComp: any) => ({schema: flattenSchema.find(item => item.name === uiComp.name), ...uiComp}))
    return {
        flattenSchema,
        flattenUISchema,
        simplifiedSchema,
        components,
        ...form,
    };
}

function getAliasList(functionBody: string, form: any): string[] {
    const cleanedStr = functionBody.replace(/\s/g, '');
    const subStrings = cleanedStr.split(/[.,'"()[\]]/);
    // @ts-ignore
    const nonDuplicated = [...new Set(subStrings)];
    // @ts-ignore
    const fieldNames = form.simplifiedSchema.map((item: any) => item.name);
    return nonDuplicated.filter((str: string) => fieldNames.indexOf(str) >= 0);
}

function findRoute(tree: any[], startName: string, targetName: string, currentRoute: any[] = [], ignoredIndexes: number[] = [], type: string = "self", childIndex: number = -1): number[] {
  const startIndex = tree.findIndex(item => item.name === startName);
  const targetIndex = tree.findIndex(item => item.name === targetName);

  // If either start or target item is not found in the tree, return an empty route
  if (startIndex === -1 || targetIndex === -1 || ignoredIndexes.indexOf(startIndex) >= 0) {
    return [];
  }

  // Add the current item's index to the route
  currentRoute.push({
    index: startIndex,
    type: type,
    childIndex: childIndex,
    dataType: tree[startIndex].type,
  });

  // If the current item is the target item, return the current route
  if (startIndex === targetIndex) {
    return currentRoute;
  }

  // Recursively search for the target item in the children of the current item
  const currentItem = tree[startIndex];
  for (let i = 0; i < currentItem.childrenIndexes.length; i++) {
    const childIndexInTree = currentItem.childrenIndexes[i];
    const childRoute = findRoute(tree, tree[childIndexInTree].name, targetName, currentRoute.slice(), [...ignoredIndexes, startIndex], "child", i);
    if (childRoute.length > 0) {
      return childRoute;
    }
  }

  // If the target item is not found in the current item's children, backtrack to siblings
  const parentIndex = currentItem.parentIndex;
  if (parentIndex !== -1) {
    const parentRoute = findRoute(tree, tree[parentIndex].name, targetName, currentRoute.slice(), [...ignoredIndexes, startIndex], "parent");
    if (parentRoute.length > 0) {
      return parentRoute;
    }
  }

  // If the target item is not found in the current item's siblings or ancestors, return an empty route
  return [];
}

function getAliasByTransversingRoute(route: any[], refFormSimplifiedSchema: any[], mainFormCompIndex: number, mainFormSimplifiedSchema: any[]) {
    const startIndex = mainFormCompIndex;
    const INVALID = -1;
    //route: array of [{index, type (self, child, parent), childIndex, dataType}]
    const endIndex = route.reduce(
        (endIndex, currentRoute) => {
            if (endIndex != INVALID) {
                const nodeType = currentRoute.type;
                const refDataType = currentRoute.dataType;
                const childIndex = currentRoute.childIndex;
                const newEndIndex = nodeType == "self" ? endIndex :
                    nodeType == "child" && mainFormSimplifiedSchema[endIndex].childrenIndexes.length > childIndex ?
                        mainFormSimplifiedSchema[endIndex].childrenIndexes[childIndex] :
                    nodeType == "parent" ? mainFormSimplifiedSchema[endIndex].parentIndex : INVALID;
                return newEndIndex;
            }
            return endIndex;
        }
        , startIndex);
    return endIndex !== INVALID ? mainFormSimplifiedSchema[endIndex].name : "";
}

function findAliasInMainForm(alias: string, refCompName: string, refFormSimplifiedSchema: any[], mainFormCompIndex: number, mainFormSimplifiedSchema: any[]) {
    const route = findRoute(refFormSimplifiedSchema, refCompName, alias, [], [], "self");
    return getAliasByTransversingRoute(route, refFormSimplifiedSchema, mainFormCompIndex, mainFormSimplifiedSchema);
}

function Suggestions() {
    const [components, setComponents] = useState([]);
    const [component, setComponent] = useState("");
    const [fullForm, setFullForm] = useState({});
    const [compName, setCompName] = useState("");
    const [refForms, setRefForms] = useState([]);
    const [suggestions, setSuggestions] = useState<any[]>([]);
    const [ratio, setRatio] = useState(80);

    const [flattenFullForm, setFlattenFullForm] = useState({});
    const [flattenRefForms, setFlattenRefForms] = useState([]);

    const onCompInput = (e: any) => setCompName(e.target.value);

    //Get rules suggestion
    const getSuggestion = () => {
        setSuggestions([]);
        // @ts-ignore
        const component = flattenFullForm.components.find(component => component.name == compName);
        const matchedRules = flattenRefForms.map((form: any, index: number) => {
            const refComponents = getUIRefCompBySimilarity(component, form.components, ratio);
            const firstComponentHasRules = getTheFirstComponentThatHasRules(refComponents, form);
            const refName = firstComponentHasRules ? firstComponentHasRules['name'] : "";
            if (refName === "") {
                return "";
            }
            const relatedRules = getRelatedRules(refName, form);
            if (relatedRules.length === 0) {
                return [];
            } else {
                return relatedRules.map((rule: any) => ({value: rule.value, refFormIndex: index, refCompName: refName}));
            }
        })
        const suggestions = matchedRules.flat();
        if (matchedRules.length > 0) {
            setSuggestions(suggestions);
        } else
            setSuggestions([]);
    }

    const replaceAliases = (suggestion: any, suggestionIndex: number) => {
        const functionBody = suggestion.value ? suggestion.value : '';
        // @ts-ignore
        const refForm = flattenRefForms[suggestion.refFormIndex];
        const refCompName = suggestion.refCompName;
        const aliasesFound = getAliasList(functionBody, refForm);
        // @ts-ignore
        const mainFormSimplifiedSchema = flattenFullForm.simplifiedSchema;
        // @ts-ignore
        const refFormSimplifiedSchema = refForm.simplifiedSchema;
        const mainFormCompIndex = mainFormSimplifiedSchema.map((item: any) => item.name).indexOf(compName);
        const matchedAliases = aliasesFound
            .map((alias: string) => ({refFormAlias: alias, mainFormAlias: findAliasInMainForm(alias, refCompName, refFormSimplifiedSchema, mainFormCompIndex, mainFormSimplifiedSchema)}))
            .filter((item: any) => item.mainFormAlias != "");
        const functionBodyWithReplacedAlias = matchedAliases.reduce(
            (acc: string, item: any) => {
                return acc.replaceAll(item.refFormAlias, item.mainFormAlias);
            }
            , functionBody);
        const suggestionWithReplacedAlias = {...suggestion, value: functionBodyWithReplacedAlias};
        const newSuggestions = suggestions.map((item: any, index: number) => (index === suggestionIndex ? suggestionWithReplacedAlias : item));
        setSuggestions(newSuggestions);
        copyTextToClipboard(functionBodyWithReplacedAlias);
        const toastMessage = "Copied to clipboard. " + matchedAliases.length + "/" + aliasesFound.length + " alias(es) replaced.";
        AppToaster.show({ message: toastMessage, intent: Intent.SUCCESS, timeout: 3000 });
    }

    const onRatioInput = (e: number) => { setRatio(e); }

    useEffect(() => {
        chrome.storage.local.get(['form', 'ref'], function(result) {
            if (result.form && result.form.length > 0) {
                const form = JSON.parse(result.form[0].content);
                const components = componentsToString(getComponentsFromForm(form));
                setComponents(components);
                setFullForm(form);
                const flattenForm = getFlattenForm(form);
                setFlattenFullForm(flattenForm);
                const logicVersion = flattenForm['form']['gaiaLogicVersion'];
                const refForms = result.ref;
                setRefForms(refForms);
                setFlattenRefForms(refForms
                    .map((ref: any) => getFlattenForm(JSON.parse(ref.content)))
                    .filter((form: any) => form['form']['gaiaLogicVersion'] == logicVersion)
                );
            }

        });
    }, []);

    // @ts-ignore
    return (
        <section>
            <div className="pl-20 pr-20 pb-10">
                <h3>Rule suggestions</h3>
                <div className="spacing-1">
                    <input className="mb-10" type="text" placeholder="Get related functions" value={compName} onChange={onCompInput}/>
                     <Slider
                        min={0}
                        max={100}
                        stepSize={10}
                        labelStepSize={10}
                        onChange={onRatioInput}
                        value={ratio}
                        className="mb-10"
                      />
                    <button onClick={getSuggestion}>Get Suggestion</button>
                </div>
                {compName != "" && (
                    <div className="spacing-1">
                        <h4>Related rules</h4>
                        <div className="editable-div" id="kr-edit" contentEditable>
                            {suggestions.map((suggestion: any, index: number) => (suggestion && suggestion.hasOwnProperty("value") &&
                                <div className="mt-20">
                                    <Button onClick={() => replaceAliases(suggestion, index)}>Replace aliases</Button>
                                    <SyntaxHighlighter language="javascript" style={docco}>
                                        {suggestion.value}
                                    </SyntaxHighlighter>
                                    <Divider />
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </section>
    );
}

export default Suggestions;
