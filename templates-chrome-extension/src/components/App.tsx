import React, {useEffect, useState, useMemo} from "react";
import {v1, v2} from './functions';
import Snippet from './Snippets';
import Suggestions from './Suggestions';

function App() {
    const [tab, setShowTab] = useState(0);

    // @ts-ignore
    return (
        <div>
            <div className="tab">
                <button className={`tablinks ${tab === 0 && 'active'}`} onClick={() => setShowTab(0)}>Snippet</button>
                <button className={`tablinks ${tab === 1 && 'active'}`} onClick={() => setShowTab(1)}>Template</button>
            </div>
            {tab === 0 ? <Snippet/> : <Suggestions/>}
        </div>
    );
}

export default App;
