export const v1 = [
  { "name": "Import Lib", "function": "local _ = import 'lib';\n" },
  { "name": "Purge", "function": "_.purge(array, elements)\n" },
  { "name": "Contains", "function": "_.contains(array, elements)\n" },
  { "name": "Ends with", "function": "_.endsWith(array, element)\n" },
  { "name": "Ends with one of", "function": "_.endsWithOneOf(array, elements)\n" },
  { "name": "Intersect", "function": "_.intersect(arr1, arr2)\n" },
  { "name": "String Contains", "function": "_.strContains('string', 'subString')\n" },
  { "name": "Show if", "function": "_.showIf('alias', condition)\n" },
  { "name": "Show multiple if", "function": "_.showMultipleIf(['alias'], condition)\n" },
  { "name": "Hide if", "function": "_.hideIf('alias', condition)\n" },
  { "name": "Hide multiple if", "function": "_.hideMultipleIf(['alias'], condition)\n" },
  { "name": "Enable if", "function": "_.enableIf('alias', condition, [childArr])\n" },
  { "name": "Disable if", "function": "_.disableIf('alias', condition, [childArr])\n" },
  { "name": "Fill", "function": "_.fill('alias', value)\n" },
  { "name": "Get value with default", "function": "_.getOrElse(value, default)\n" },
  { "name": "safeInt", "function": "_.safeInt(s)\n" },
  { "name": "Get day of month", "function": "_.getDayOfMonth('mm', 'yyyy')\n" },
  { "name": "Apply for Checkbox", "function": "local ruleDisableAndClear = _.a_checkboxDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition);\nlocal rulePrefillWithoutDisable = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition);\nlocal ruleClearPrefill = _.a_checkboxClearPrefill([options], condition);\nlocal ruleExclusive = _.a_checkboxExclusive([arr1], [arr2]);\n\n_.applyCheckbox({\n  target: 'checkbox',\n  value: checkbox.value,\n  rules: [\n    // array of rules\n  ]\n})\n" },
  { "name": "==> Checkbox Disable and Clear", "function": "local ruleName = _.a_checkboxDisableAndClear([disabledOptions], condition);\n"},
  { "name": "==> Checkbox Disable and Prefill", "function": "local ruleName = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition);\n"},
  { "name": "==> Checkbox Prefill Without Disable", "function": "local ruleName = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition);\n"},
  { "name": "==> Checkbox Clear Prefill", "function": "local ruleName = _.a_checkboxClearPrefill([options], condition);\n"},
  { "name": "==> Checkbox Exclusive", "function": "local ruleName = _.a_checkboxExclusive([arr1], [arr2]);\n"},
  { "name": "==> Checkbox Disable all", "function": "local ruleDisableAll = _.a_checkDisableAll(condition);\n"},
  { "name": "Apply for Radio", "function": "local ruleDisableAndClear = _.a_radioDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition);\nlocal rulePrefillWithoutDisable = _.a_radioPrefillWithoutDisable(prefillOption, condition);\nlocal ruleClearPrefill = _.a_radioClearPrefill([options], condition);\n\n_.applyRadio({\n  target: 'radio',\n  value: radio.value,\n  rules: [\n    // array of rules\n  ]\n})\n" },
  { "name": "==> Radio Disable and Clear", "function": "local ruleName = _.a_radioDisableAndClear([disabledOptions], condition);\n"},
  { "name": "==> Radio Disable and Prefill", "function": "local ruleName = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition);\n"},
  { "name": "==> Radio Prefill Without Disable", "function": "local ruleName = _.a_radioPrefillWithoutDisable(prefillOption, condition);\n"},
  { "name": "==> Radio Disable all", "function": "local ruleDisableAll = _.a_radioDisableAll(condition);\n"},
  { "name": "Function Radio Clear Prefill", "function": "local ruleName = _.a_radioClearPrefill([options], condition);\n"},
  { "name": "Show US state", "function": "_.showUSState(countryValue, 'us_alias', 'non-us_alias')\n" },
  { "name": "Merge name", "function": "_.merge('target', ' ', parts)\n" },
  { "name": "Merge Address", "function": "function(country, numberandstreet, city, us_state, nonus_state, us_zip, nonus_zip)\nlocal _ = import 'lib';\n\nlocal isUS = country.value == 'United States';\n\n_.merge('mergedaddress', ' ', [\n    numberandstreet.value,\n    city.value,\n    if isUS then us_state.value else nonus_state.value,\n    if isUS then us_zip.value else nonus_zip.value,\n    country.value\n])\n" },
  { "name": "Validate date", "function": "_.validateDate('target', dateValue)\n" },
  { "name": "Validate year", "function": "_.validateYear('target', yearValue)\n" },
  { "name": "Validate commitment", "function": "_.validateCommitment('target', value)\n" },
  { "name": "Validate percentage", "function": "_.validatePercentage('target', value)\n" },
  { "name": "Validate fiscal year", "function": "_.validateFiscalYear('target', monthValue, dayValue)\n" },
  { "name": "Validate min number", "function": "_.validateMinNumber('target', value, min)\n" },
  { "name": "Validate min money", "function": "_.validateMinMoney('target', value, min)\n" },
  { "name": "Validate total profit", "function": "_.validateTotalProfits('target', repeatableValue, 'percentage_alias')\n" },
  { "name": "Get constant value", "function": "local constant = _.getConstantValue()\n" },
  { "name": "Repeatable get empty row", "function": "local emptyRow = _.getEmptyRow(repeatable.value);\n" },
  { "name": "Repeatable hide field with index", "function": "_.hideRepeatable('repeatable', 'field_alias', index, condition)\n" },
  { "name": "Repeatable disable field with index", "function": "_.disableRepeatable('repeatable', 'field_alias', index, condition)\n" },
  { "name": "Repeatable disable and clear", "function": "_.disableAndClearRepeatable('repeatable_alias', repeatable_alias.value, condition)\n" },
  { "name": "Repeatable fill a field with index", "function": "_.fillRepeatable('repeatable', 'field_alias', index, value)\n" },
  { "name": "Repeatable check all option", "function": "function(repeatable)\nlocal _ = import 'lib';\n\nlocal allValue = [\n    'value1',\n    'value2',\n    'value3'\n];\n\nlocal func = function(index, element)\n  local all = element['checkbox_all'].value != [];\n  local selectAllFromItem = std.length(element['checkbox_items'].value) == std.length(allValue);\n\n    local e1 = if all && element['checkbox_all'].touched then _.fillRepeatable('repeatable', 'checkbox_items', index, allValue) \n    else if !all && element['checkbox_all'].touched then _.fillRepeatable('repeatable', 'checkbox_items', index, []) \n    else if selectAllFromItem && element['checkbox_items'].touched then _.fillRepeatable('repeatable', 'checkbox_all', index, ['allinfo_correspondences_additionalcontact_contactinfo']) \n    else if !selectAllFromItem && element['checkbox_items'].touched then _.fillRepeatable('repeatable', 'checkbox_all', index, []) else [];\n    local e2 = atd.add('repeatable', ['value', index, 'checkbox_all', 'touched'], false)\n        + atd.add('repeatable', ['value', index, 'checkbox_items', 'touched'], false);    \n\n    local e3 = atd.add('repeatable', ['value', index, 'radio5', 'hide'], std.length(element['checkbox_items'].value) > 0);\n    \n    e1 + e2 + e3;\n\n_.applyRepeatable(func, repeatable.value)\n" },
  { "name": "Apply a function for repeatable", "function": "local func = function(index, element)\n  local value = element['alias'].value;\n\n_.applyRepeatable(func, repeatableValue)\n" },
  { "name": "Copy value between two repeatable", "function": "local mappings = [\n    {from: 'alias', to: 'alias1'},\n  {from: 'alias2', to: 'alias3'},\n];\n\n_.prefillValueBetweenTwoRepeatable(sourceValue, destinationValue, destinationAlias, mappings)\n" },
  { "name": "Untouch a field", "function": "_.untouch('alias')\n" },
  { "name": "Untouch a list of fields", "function": "_.multipleUntouch([targets])\n" },
  { "name": "Show file group with different conditions", "function": "local files = [\n    {file: 'file1', condition: op1},\n    {file: 'file2', condition: op2},\n];\n\n_.showFileGroup('filegroup', files)\n" },
  { "name": "Show file group with same condition", "function": "_.showFileGroupWithSameCondition('filegroup', ['file1', 'file2'], condition)\n" },
  { "name": "Show field below for Tax ID", "function": "// Use null for not existed option\n// _.showTaxIDFieldBelow(radioValue, null, einAlias, null, otherAlias)\n_.showTaxIDFieldBelow(radioValue, 'ssnAlias', 'einAlias', 'itinAlias', 'otherAlias')\n"},
  { "name": "NB Validate country code", "function": "_.nb_validate_country_code(country.value, alias)\n"},
  { "name": "NB Validate area code", "function": "_.nb_validate_area_code(country.value, phoneValue, alias)\n"},
  { "name": "NB Validate phone number", "function": "_.nb_validate_phone_number(country.value, phoneValue, alias)\n"},
  { "name": "NB Validate date field", "function": "_.nb_validate_date_field(date.alue, alias)\n"},
  { "name": "NB Validate brokerage account number", "function": "_.nb_brokerage_account_number(number.value, alias, 'custom message')\n"},
  { "name": "NB Validate NFS number", "function": "_.nb_validate_nfs_number(number.value, alias)\n"},
  { "name": "NB Validate ITIN number", "function": "_.nb_validate_ITIN(number.value, alias)\n"},
  { "name": "NB Validate ABA number", "function": "_.nb_validate_aba(number.value, alias)\n"},
  { "name": "NB Validate GNumber", "function": "_.nb_validate_GNumber(number.value, alias)\n"},
];

export const v2 = [
  { "name": "Import Lib", "function": "local _ = import 'lib';\nlocal schema = afs.schema.main;\nlocal logic = afs.logic.main;\n" },
  { "name": "Purge", "function": "_.purge(array, elements)\n" },
  { "name": "Contains", "function": "_.containsAny(array, elements)\n" },
  { "name": "Ends with", "function": "_.endsWith(array, element)\n" },
  { "name": "Ends with one of", "function": "_.endsWithOneOf(array, elements)\n" },
  { "name": "Intersect", "function": "_.intersect(arr1, arr2)\n" },
  { "name": "String Contains", "function": "_.strContains('string', 'subString')\n" },
  { "name": "Show if", "function": "_.showIf([targetLogic], condition)\n" },
  { "name": "Hide if", "function": "_.hideIf([targetLogic], condition)\n" },
  { "name": "Clear Value", "function": "_.clearIf(targetLogic, condition)\n" },
  { "name": "Enable if", "function": "_.enableIf(targetLogic, condition, [childArrLogic])\n" },
  { "name": "Disable if", "function": "_.disableIf(targetLogic, condition, [childArrLogic])\n" },
  { "name": "Fill", "function": "_.fill(targetLogic, value)\n" },
  { "name": "Set disabled options", "function": "_.setDisabledOptions(targetLogic, [options])\n" },
  { "name": "Get value with default", "function": "_.getOrElse(value, default)\n" },
  { "name": "safeInt", "function": "_.safeInt(s)\n" },
  { "name": "Get All Options", "function": "local allOptions = _.getAllOptions(schema.alias);\n" },
  { "name": "Get day in month", "function": "_.getDaysInMonth('mm', 'yyyy')\n" },
  { "name": "Apply for Checkbox", "function": "local ruleDisableAndClear = _.a_checkboxDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition, 'key');\nlocal rulePrefillWithoutDisable = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition, 'key');\nlocal ruleExclusive = _.a_checkboxExclusive([arr1], [arr2]);\n\n_.applyCheckbox({\n  target: checkboxLogic,\n  value: checkbox.value,\n  // prefillState: prefill_state.value,\n  // prefillStateLogic: logic.prefill_state,\n  rules: [\n    // array of rules\n  ]\n})\n" },
  { "name": "==> Checkbox Disable and Clear", "function": "local ruleName = _.a_checkboxDisableAndClear([disabledOptions], condition);\n"},
  { "name": "==> Checkbox Disable and Prefill", "function": "local ruleName = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition, 'key');\n"},
  { "name": "==> Checkbox Prefill Without Disable", "function": "local ruleName = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition, 'key');\n"},
  { "name": "==> Checkbox Exclusive", "function": "local ruleName = _.a_checkboxExclusive([arr1], [arr2]);\n"},
  { "name": "==> Checkbox Disable all", "function": "local ruleDisableAll = _.a_checkboxDisableAll(condition);\n"},
  { "name": "Apply for Radio", "function": "local ruleDisableAndClear = _.a_radioDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition, 'key');\nlocal rulePrefillWithoutDisable = _.a_radioPrefillWithoutDisable(prefillOption, condition, 'key');\nlocal ruleClearPrefill = _.a_radioClearPrefill([options], condition);\n\n_.applyRadio({\n  target: radioLogic,\n  value: radio.value,\n  // prefillState: prefill_state.value,\n  // prefillStateLogic: logic.prefill_state,\n  rules: [\n    // array of rules\n  ]\n})\n" },
  { "name": "==> Radio Disable and Clear", "function": "local ruleName = _.a_radioDisableAndClear([disabledOptions], condition);\n"},
  { "name": "==> Radio Disable and Prefill", "function": "local ruleName = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition, 'key');\n"},
  { "name": "==> Radio Prefill Without Disable", "function": "local ruleName = _.a_radioPrefillWithoutDisable(prefillOption, condition, 'key');\n"},
  { "name": "==> Radio Disable all", "function": "local ruleDisableAll = _.a_radioDisableAll(condition);\n"},
  { "name": "Show US state", "function": "_.showEither(USLogic, nonUSLogic, isUS)\n" },
  { "name": "Merge name", "function": "_.merge(targetLogic, ' ', parts)\n" },
  { "name": "Merge Address", "function": "function(country, numberandstreet, city, us_state, nonus_state, us_zip, nonus_zip)\nlocal _ = import 'lib';\n\nlocal isUS = country.value == 'United States';\n\n_.merge(targetLogic, ' ', [\n    numberandstreet.value,\n    city.value,\n    if isUS then us_state.value else nonus_state.value,\n    if isUS then us_zip.value else nonus_zip.value,\n    country.value\n])\n" },
  { "name": "Validate date", "function": "_.validateDate(targetLogic, dateValue)\n" },
  { "name": "Validate year", "function": "_.validateYear(targetLogic, yearValue)\n" },
  { "name": "Validate commitment", "function": "_.validateCommitment(targetLogic, value)\n" },
  { "name": "Validate percentage", "function": "_.validatePercentage(targetLogic, value)\n" },
  { "name": "Validate fiscal year", "function": "_.validateFiscalYear(targetLogic, monthValue, dayValue)\n" },
  { "name": "Validate min number", "function": "_.validateMinNumber(targetLogic, value, min)\n" },
  { "name": "Validate min money", "function": "_.validateMinMoney(targetLogic, value, min)\n" },
  { "name": "Validate total profit", "function": "_.validateTotalProfits(targetLogic, repeatableValue, 'percentage_alias')\n" },
  { "name": "Apply a function for repeatable", "function": "local func = function(index, element)\n  local value = element['alias'].value;\n\n_.applyRepeatable(func, repeatableValue)\n" },
  { "name": "Copy value between two repeatable", "function": "local destinationLogicObj = logic.repeatable1;\nlocal sourceVal = repeatable.value;\nlocal destVal = repeatable1.value;\n\nlocal createMapping = function(data)\n    [\n          { value: data.name.value, to: destinationLogicObj.children.name1},\n          { value: data.title.value, to: destinationLogicObj.children.title1},\n    ];\n\n_.prefillValueBetweenTwoRepeatable(sourceVal, destVal, destinationLogicObj, createMapping)\n" },
  { "name": "Show file group with different conditions", "function": "local files = [\n    {file: 'file1', condition: op1},\n    {file: 'file2', condition: op2},\n];\n\n_.showFileGroup(filegroupLogic, files)\n" },
  { "name": "Show file group with same condition", "function": "_.showFileGroupWithSameCondition(filegroupLogic, ['file1', 'file2'], condition)\n" },
  { "name": "Show field below for Tax ID", "function": "// Use null for not existed option\n// _.showTaxIDFieldBelow(radioValue, null, einAlias, null, otherAlias)\n_.showTaxIDFieldBelow(radioValue, 'ssnAlias', 'einAlias', 'itinAlias', 'otherAlias')\n"},
  { "name": "NB Validate country code", "function": "_.nb_validate_country_code(country.value, logic.phone)\n"},
  { "name": "NB Validate area code", "function": "_.nb_validate_area_code(country.value, phone.value, logic.phone)\n"},
  { "name": "NB Validate phone number", "function": "_.nb_validate_phone_number(country.value, phone.value, logic.phone)\n"},
  { "name": "NB Validate date field", "function": "_.nb_validate_date_field(date.value, logic.date)\n"},
  { "name": "NB Validate brokerage account number", "function": "_.nb_brokerage_account_number(phone.value, logic.phone, 'custom message')\n"},
  { "name": "NB Validate NFS number", "function": "_.nb_validate_nfs_number(phone.value, logic.phone)\n"},
  { "name": "NB Validate ITIN number", "function": "_.nb_validate_ITIN(phone.value, logic.phone)\n"},
  { "name": "NB Validate ABA number", "function": "_.nb_validate_aba(phone.value, logic.phone)\n"},
  { "name": "NB Validate GNumber", "function": "_.nb_validate_GNumber(phone.value, logic.phone)\n"},
];