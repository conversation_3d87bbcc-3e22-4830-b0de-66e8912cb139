import {addClickHelperMenuItem} from './clickHelper'


// HELPERS for the features
const getElementIndent = (element: Element) => {
    const marginLeft = (element.children[0] as HTMLElement)?.style.marginLeft.split('px')[0];
    return parseInt(marginLeft);
}
const findActiveGroupChildren = (activeElement: HTMLElement) => {
    const parentElement = activeElement.parentElement;
    if (!parentElement) throw `NO ROOT Element found for ${activeElement}`;
    const eles = parentElement?.children;
    let isActive = false;
    const activeIndent = getElementIndent(activeElement);
    const result: string[] = [];
    for (let i = 0; i < eles.length; i++) {
        if (eles[i].id === activeElement.id) {
            isActive = true; continue;
        }
        if (!isActive) continue;
        const indent = getElementIndent(eles[i]);
        if (indent == null || Number.isNaN(indent)) throw `NO INDENT for Element ${eles[i].id}`;
        if (indent <= activeIndent) {
            break;
        }
        if (eles[i].id != '') result.push(eles[i].id);
    }
    return result;
}
const checkIfNotGroup = (eId: string) => {
    return !checkIfGroupElement(eId);
}
const checkIfGroupElement = (eId: string) => {
    const arr = findActiveGroupChildren(document.querySelector(`#${eId}`)!);
    return arr.length > 0;
}


export default function initFeatures() {
    // inputs is start element, output is all elements that come after.

    addClickHelperMenuItem('JOIN FROM other elements', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0 && outputs?.length == 0) return '';
        const declare = `function (${inputs.join(', ')})`;
        const userEdit = `//Easy edit\nlocal seperator = ', ';`;
        const tmpVar = `local all = [${inputs.map(o => o + '.value').join(',\n\t')}];`;
        const content = `local combined = std.join(seperator, std.filter(function (item) item != null && item != '', all));`;
        const defineEmmitter = `local output = afs.logic.main;`;
        const emmit = `output.${outputs[0]}.value(combined)`;
        return [declare, userEdit, tmpVar, content, defineEmmitter, emmit].join('\n\n');
    }, {
        effectDisplayName: 'JOIN',
        swapInputsOutputs: true,
        validator: checkIfNotGroup,
    })


    //Prefill from this elemnt to another
    addClickHelperMenuItem('PREFILL TO other elements', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0 && outputs?.length == 0) return '';
        const declare = `function (${[...inputs, ...outputs].join(', ')})`;
        const tmpVar = `local iVal = ${inputs[0]}.value;`;
        const defineEmmitter = `local output = afs.logic.main;`;
        const emmit = outputs.map(o => `(if !${o}.touched && ${o}.value != iVal\nthen output.${o}.value(iVal)\n\telse [])`).join('\n+ ');
        return [declare, tmpVar, defineEmmitter, emmit].join('\n\n');
    }, {
        effectDisplayName: 'PREFILL',
        validator: checkIfNotGroup,
    })

    addClickHelperMenuItem('DISABLE other elements', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0 && outputs?.length == 0) return '';
        const declare = `function (${inputs.join(', ')})`;
        const content = inputs.map(iid => `local isDisable = ${iid}.value != [];`).join('\n');
        const defineEmmitter = `local output = afs.logic.main;`;
        const emmit = outputs.map(oid => `output.${oid}.disable(!isDisable)`).join('\n+ ');
        return [declare, content, defineEmmitter, emmit].join('\n\n');
    }, {
        effectDisplayName: 'TOGGLE',
        validator: checkIfNotGroup,
    })

    addClickHelperMenuItem('SHOW other elements', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0 && outputs?.length == 0) return '';
        const declare = `function (${inputs.join(', ')})`;
        const content = inputs.map(iid => `local isShow = ${iid}.value != [];`).join('\n');
        const defineEmmitter = `local output = afs.logic.main;`;
        const emmit = outputs.map(oid => `output.${oid}.hide(!isShow)`).join('\n+ ');
        return [declare, content, defineEmmitter, emmit].join('\n\n');
    }, {
        effectDisplayName: 'SHOW',
        validator: checkIfNotGroup,
    })

    addClickHelperMenuItem('GROUP PREFILL to other', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0) return '';
        console.log(inputs, outputs);
        const declare = `function (${[...inputs, ...outputs].join(', ')})`;
        const content = inputs.map((e, i) => `local value_${i} = ${e}.value;`).join('\n');
        const defineEmmitter = `local output = afs.logic.main;`;
        const emmit = outputs.map((o, i) => `(if !${o}.touched && ${o}.value != value_${i}\nthen output.${o}.value(value_${i})\nelse [])`).join('\n+ ');
        return [declare, content, defineEmmitter, emmit].join('\n\n');
    }, {
        effectDisplayName: 'GROFILL',
        validator: checkIfGroupElement,
        preprocessor: (ins: string[], outs: string[]) => {
            const currentElement = document.querySelector<HTMLElement>(`#${ins[0]}`);
            const outElement = document.querySelector<HTMLElement>(`#${outs[0]}`);
            const inputs = findActiveGroupChildren(currentElement!).filter(eid => !checkIfGroupElement(eid));
            const outputs = findActiveGroupChildren(outElement!).filter(eid => !checkIfGroupElement(eid));
            return {inputs, outputs}
        },
    })
    addClickHelperMenuItem('GROUP CLEAR children ON DISABLE', (inputs: string[], outputs: string[]) => {
        if (inputs?.length == 0) return '';
        console.log(inputs, outputs);
        const declare = `function (${[...inputs].join(', ')})`;
        const content = `local isDis = ${inputs[0]}.disable;`;
        const defineEmmitter = `local output = afs.logic.main;`;
        const start = `(if isDis then`;
        const emmit = outputs.map((o, i) => `\toutput.${o}.value('')`).join('\n+ ');
        const end = `else [])`;
        return [declare, content, defineEmmitter, start, emmit, end].join('\n\n');
    }, {
        effectDisplayName: 'GRDISnCLEAR',
        isImmediateEffect: true,
        validator: checkIfGroupElement,
        preprocessor: (inputs: string[], outs: string[]) => {
            const currentElement = document.querySelector<HTMLElement>(`#${inputs[0]}`);
            const outElement = document.querySelector<HTMLElement>(`#${outs[0]}`);
            const outputs = findActiveGroupChildren(outElement!).filter(eid => !checkIfGroupElement(eid));
            return {inputs, outputs}
        },
    })
}