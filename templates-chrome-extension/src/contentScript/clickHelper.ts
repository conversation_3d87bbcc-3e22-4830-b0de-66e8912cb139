import ClickHelperEffectOption, { Click<PERSON>elperHandler } from '../models/ClickHelperEffectOption';
import initFeatures  from './clckHelperFeatures';

class HelperState {
    isShowingContextMenu: boolean = false;
    isOnEffect: boolean = false;
    activeElementId: string = "";
    inputIds: string[] = [];
    outputIds: Set<string> = new Set();
    outputHandlerMapper: Record<string, () => void> = {};
    activeRootElement: HTMLElement = document.body;
    formJSON: Record<string, any> = {};
    effectOptions: Record<string, ClickHelperEffectOption> = {};
}

const _state = new HelperState();

//----------------------------------------------------------------
//SETUP the advanced button to show every time user clicks an element

const clickHelperButton = Object.assign(document.createElement('button'),{id: 'clickHelperButton'});
clickHelperButton.className = 'bg-warning-4 p-4 h-px24';

// -- show the context menu when user clicks the advanced button
clickHelperButton.addEventListener('mousedown', (event: MouseEvent) => {
    console.log(event);
    clickHelperButtonClicked();
    event.stopPropagation()
})

export function setupClickHelper(){
    const icon = document.createElement('img');
    icon.src = chrome.extension.getURL("/css/radioactive.svg");
    clickHelperButton.appendChild(icon);
}

export function callClickHelper(e: Event){
    const activeElement = document.querySelector('.absolute:not(.hidden) .flex.justify-end .bg-primary-4.text-gray-0.flex.items-center.h-px24');
    let rootElement = activeElement?.parentElement?.parentElement?.parentElement;
    if (!activeElement || !rootElement // CANNOT find active element and root element
        || activeElement.children[0].id === 'clickHelperButton') //The advanced button is ADDED
        return;
    const activeElementId = rootElement?.id?.split('widget-')[1] || activeElement?.textContent?.split('\n')[0].split('@')[1];
    if (!activeElementId) {
        throw "Cannot find ID of active element";
    };
    rootElement?.addEventListener('dblclick', activeElementRightClicked);
    rootElement?.addEventListener('contextmenu', activeElementRightClicked);
    _state.activeRootElement = rootElement;
    activeElement.insertBefore(clickHelperButton, activeElement.childNodes[0]);
    _state.activeElementId = activeElementId;
}

function toggleContextMenu(isShow: boolean = true){
    if (_state.isShowingContextMenu === isShow) return;
    _state.isShowingContextMenu = isShow;
    if (isShow){
        Object.values(_state.effectOptions).forEach((effect) => {
            if (effect.validator) {
                const valid = effect.validator(_state.activeElementId);
                if (!valid) {
                    return effect.menuDisplayElement!.style.display = 'none';
                }
            }
            effect.menuDisplayElement!.style.display = 'block';
            // THE Preprocessor is not satisfied -> hide the effect
        });
        return document.body.appendChild(clickHelperContextMenu);
    }
    //remove event listener and content
    document.body.removeChild(clickHelperContextMenu);
    // If user clicks other element -> close the context menu
    document.removeEventListener('mousedown', closeContextMenuOnClick);
    // Remove event listener from old active root elements
    _state.activeRootElement?.removeEventListener('dblclick', activeElementRightClicked);
    _state.activeRootElement?.removeEventListener('contextmenu', activeElementRightClicked);
}

const closeContextMenuOnClick = (event: MouseEvent) => {
    toggleContextMenu(false)
}

//Show the Advanced button when user make an element active
const openContextMenu = (mouseEvent?: MouseEvent) => {
    let rect = clickHelperButton.getBoundingClientRect()
    clickHelperContextMenu.style.top = `${rect.y + 24}px`;
    clickHelperContextMenu.style.left = `${rect.x}px`;
    if (mouseEvent){
        clickHelperContextMenu.style.top = `${mouseEvent.clientY}px`;
        clickHelperContextMenu.style.left = `${mouseEvent.clientX}px`;
    }
    toggleContextMenu();
    
    // If user clicks other element -> close the context menu
    document.addEventListener('mousedown', closeContextMenuOnClick);
}


function activeElementRightClicked(mouseEvent: MouseEvent) {
    if (_state.isOnEffect)
        return;

    // close the current context menu user right click anywhere else
    if (_state.isShowingContextMenu)
        toggleContextMenu(false)

    openContextMenu(mouseEvent);
    mouseEvent.preventDefault();
}

function clickHelperButtonClicked() {
    if (_state.isOnEffect)
        return;
    // close the context menu if advanced button is clicked again and don't trigger the openContextMenu
    if (_state.isShowingContextMenu)
        return toggleContextMenu(false)

    openContextMenu();
}

//----------------------------------------------------------------
//SETUP for context menu that overlay when user clicks the advanced button

const clickHelperContextMenu = Object.assign(document.createElement('div'), {id: 'clickHelperContextMenuContent'})
clickHelperContextMenu.className = 'w-px256 fixed top-0 opacity-1 bg-warning-2 shadow-3 p-8'
clickHelperContextMenu.style.zIndex = '3000';

clickHelperContextMenu.addEventListener('mousedown', (event) =>{
    event.stopPropagation();
})

//----------------------------------------------------------------
//SETUP an prototype for a row in the context menu
const makeclickHelperContextMenuRow = (innerText: string) => Object.assign(document.createElement('button'), {
    className: "p-4 hover:bg-warning-1",
    innerText,
});

//----------------------------------------------------------------
//SETUP UI to show effect status

const clickHelperEffectStatus = Object.assign(document.createElement('div'), {id: 'clickHelperContextMenuContent'})
clickHelperEffectStatus.className = 'fixed top-0 opacity-1 bg-warning-2 shadow-3 p-8'
clickHelperEffectStatus.style.zIndex = '3000';
clickHelperEffectStatus.style.top = `${document.documentElement.clientHeight - 256}px`;
clickHelperEffectStatus.style.left = `${document.documentElement.clientWidth - 512}px`;
clickHelperEffectStatus.innerHTML = `
<div class="flex">
    <div id="clickHelperEffectInputs" class="bg-gray-2 h-auto w-px128 overflow-y-scroll px-4">
    </div>
    <h5 id="clickHelperEffectName" class="text-gray-1 p-8">PREFILL></h5>
    <div id="clickHelperEffectOutputs" class="bg-gray-2 h-auto w-px128 overflow-y-scroll px-4">
    </div>
</div>`

const clickHelperEffectInputs = clickHelperEffectStatus.querySelector('#clickHelperEffectInputs');
const clickHelperEffectOutputs = clickHelperEffectStatus.querySelector('#clickHelperEffectOutputs');
const clickHelperEffectName = clickHelperEffectStatus.querySelector('#clickHelperEffectName');

//----------------------------------------------------------------
//SETUP functions for elements in context menu

const getFormJSON: () => Promise<any> = (() => {
    const formID = window.location.pathname.split('/form/')[1];

    let db: IDBDatabase;
    const request = window.indexedDB.open("form");
    request.onerror = (event: any) => {
        console.error("EXTENSION ERROR: " + (event as IDBRequest).error);
    };
    request.onsuccess = (event: any) => {
        console.log(request.result);
        db = event.target!.result;
        console.log(db);
    };

    return () => new Promise((resolve, reject) => {
        const keysTX = db.transaction('form').objectStore('form')?.getAllKeys();
        if (!keysTX) return resolve({});
        keysTX.onerror = () => {
            console.log(`EXTENSION ERROR: Transaction failed db.transaction('form').objectStore('form').getAllKeys();`)
            resolve({});
        }
        keysTX.onsuccess = () => {
            const allFormKeys = (keysTX.result as string[]).filter(key => key.includes(formID));
            const currentKey = allFormKeys[allFormKeys.length - 1];
            if (!currentKey) return resolve({});
            const getTX = db.transaction('form').objectStore('form')?.get(currentKey);
            if (!getTX) return resolve({});
            getTX.onsuccess = e => resolve(getTX.result.formData);
        }
    });
})();

const updateClickHelperStatus = (isSwapInsOuts?: boolean) => {
    const istr = _state.inputIds.map(i => `<p class="my-4">${i}</p>`).join('');
    const ostr = Array.from(_state.outputIds).map(o => `<p class="my-4">${o}</p>`).join('');
    if (isSwapInsOuts){
        clickHelperEffectInputs!.innerHTML = ostr;
        clickHelperEffectOutputs!.innerHTML = istr;
    } else {
        clickHelperEffectInputs!.innerHTML = istr;
        clickHelperEffectOutputs!.innerHTML = ostr;
    }
}

export function addClickHelperMenuItem(displayName: string, outputHandler: ClickHelperHandler, option: ClickHelperEffectOption){
    const newRow = makeclickHelperContextMenuRow(displayName);

    const newHandler = () => {
        console.log('active', _state.activeElementId);
        _state.outputIds.add(_state.activeElementId);
        console.log(_state.inputIds, _state.outputIds);
        let i = _state.inputIds;
        let o = Array.from(_state.outputIds);
        if (option.preprocessor){
            const t = option.preprocessor(i, o);
            i = t.inputs;
            o = t.outputs;
        }
        if (option.swapInputsOutputs) {
            const t = o;
            o = i;
            i = t;
        }

        let out = outputHandler(i, o, _state.formJSON);
        updateClickHelperStatus(option.swapInputsOutputs)

        console.log(out)
        if (!out) {
            return;
        }
        navigator.clipboard.writeText(out);
    }

    _state.outputHandlerMapper[displayName] = newHandler;

    newRow.addEventListener('mousedown', async () => {
        _state.inputIds = [_state.activeElementId];
        _state.outputIds = new Set<string>();
        toggleContextMenu(false);
        if (option.isImmediateEffect){
            return newHandler();
        }
        _state.isOnEffect = true;
        clickHelperEffectName!.innerHTML = `${option.effectDisplayName}>`;
        document.body.appendChild(clickHelperEffectStatus);
        updateClickHelperStatus(option.swapInputsOutputs)
        const formData = await getFormJSON();
        _state.formJSON = formData;
        console.log(_state.formJSON);
        document.body.addEventListener('dblclick', newHandler);
    });

    _state.effectOptions[displayName] = {...option, mainHandler: outputHandler, menuDisplayElement: newRow};
    clickHelperContextMenu.appendChild(newRow);
}

initFeatures();

const stopButton = clickHelperContextMenu.appendChild(makeclickHelperContextMenuRow('STOP all effects'));

function stopAllEffect () {
    console.trace('STOP ALL EFFECTS');
    Object.values(_state.outputHandlerMapper).forEach(cb => {
        document.body.removeEventListener('dblclick', cb)
    });
    _state.isOnEffect = false;
    document.body.appendChild(clickHelperEffectStatus);
    document.body.removeChild(clickHelperEffectStatus);
    return '';
}

stopButton.addEventListener('mousedown', stopAllEffect);

// let TOC_ELEMENT = document.querySelector('.flex.h-pc100.w-pc100.flex-row .bg-gray-0.flex.flex-col');

//get the table of content element, cache the result
let getTableOfContentElement: () => Element | null = (() => {
    let tocElement: Element | null = null;
    return () => {
        if (!tocElement) {
            tocElement = document.querySelector('.flex.h-pc100.w-pc100.flex-row .bg-gray-0.flex.flex-col');
            return tocElement;
        }
        return tocElement;
    }
})();

// when user clicks on other elements that not TOC or the main body 
document.body.addEventListener('click', (event: MouseEvent) => {
    // if (!TOC_ELEMENT) TOC_ELEMENT = document.querySelector('#\\$root_page_id')?.parentElement?.parentElement?.parentElement?.children[0];
    // if (!TOC_ELEMENT) TOC_ELEMENT = document.querySelector('.flex.h-pc100.w-pc100.flex-row .bg-gray-0.flex.flex-col');
    const tocElement = getTableOfContentElement();
    const tocRect = tocElement?.getClientRects()[0];
    console.log(tocElement)
    // if the clicked element stay in ToC -> don't stop click helper effect
    const isInsideTOC = tocRect && event.x >= tocRect.x && event.x <= tocRect.width + tocRect.x 
                                && event.y >= tocRect.y && event.y <= tocRect.height + tocRect.y
    // if ((event as any)['path'] && (event as any)['path'].includes(tocElement)) return;
    if (isInsideTOC) return;
    console.log(event)
    stopAllEffect()
});


// Set the form data when the full page of the pantheon is loaded
// chrome.webRequest.onCompleted.addListener((details) => {
//     console.log(details);
// },{urls:['*form/getForm']})
