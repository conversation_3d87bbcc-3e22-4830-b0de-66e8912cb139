[{"name": "Import Lib", "value": "local _ = import 'lib';\n"}, {"name": "Purge", "value": "_.purge(array, elements)\n"}, {"name": "Contains", "value": "_.contains(array, elements)\n"}, {"name": "Ends with", "value": "_.endsWith(array, element)\n"}, {"name": "Ends with one of", "value": "_.endsWithOneOf(array, elements)\n"}, {"name": "Intersect", "value": "_.intersect(arr1, arr2)\n"}, {"name": "Show if", "value": "_.showIf('alias', condition)\n"}, {"name": "Show multiple if", "value": "_.showMultipleIf(['alias'], condition)\n"}, {"name": "Hide if", "value": "_.hideIf('alias', condition)\n"}, {"name": "Hide multiple if", "value": "_.hideMultipleIf(['alias'], condition)\n"}, {"name": "Enable if", "value": "_.enableIf('alias', condition, [childArr])\n"}, {"name": "Disable if", "value": "_.disableIf('alias', condition, [childArr])\n"}, {"name": "Fill", "value": "_.fill('alias', value)\n"}, {"name": "Get value with default", "value": "_.getOrElse(value, default)\n"}, {"name": "safeInt", "value": "_.safeInt(s)\n"}, {"name": "Get day of month", "value": "_.getDayOfMonth('mm', 'yyyy')\n"}, {"name": "Apply for Checkbox", "value": "local ruleDisableAndClear = _.a_checkboxDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition);\nlocal rulePrefillWithoutDisable = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition);\nlocal ruleClearPrefill = _.a_checkboxClearPrefill([options], condition);\nlocal ruleExclusive = _.a_checkboxExclusive([arr1], [arr2]);\n\n_.applyCheckbox({\n  target: 'checkbox',\n  value: checkbox.value,\n  rules: [\n    // array of rules\n  ]\n})\n"}, {"name": "==> Checkbox Disable and Clear", "value": "local ruleName = _.a_checkboxDisableAndClear([disabledOptions], condition);\n"}, {"name": "==> Checkbox Disable and Prefill", "value": "local ruleName = _.a_checkboxDisableAndPrefill([disabledOptions], [prefillOptions], [clearOptions], condition);\n"}, {"name": "==> Checkbox Prefill Without Disable", "value": "local ruleName = _.a_checkboxPrefillWithoutDisable([prefillOptions], condition);\n"}, {"name": "==> Checkbox Clear Prefill", "value": "local ruleName = _.a_checkboxClearPrefill([options], condition);\n"}, {"name": "==> Checkbox Exclusive", "value": "local ruleName = _.a_checkboxExclusive([arr1], [arr2]);\n"}, {"name": "==> Checkbox Disable all", "value": "local ruleDisableAll = _.a_checkDisableAll(condition);\n"}, {"name": "Apply for Radio", "value": "local ruleDisableAndClear = _.a_radioDisableAndClear([disabledOptions], condition);\nlocal ruleDisableAndPrefill = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition);\nlocal rulePrefillWithoutDisable = _.a_radioPrefillWithoutDisable(prefillOptions, condition);\nlocal ruleClearPrefill = _.a_radioClearPrefill([options], condition);\n\n_.applyRadio({\n  target: 'radio',\n  value: radio.value,\n  rules: [\n    // array of rules\n  ]\n})\n"}, {"name": "==> Radio Disable and Clear", "value": "local ruleName = _.a_radioDisableAndClear([disabledOptions], condition);\n"}, {"name": "==> Radio Disable and Prefill", "value": "local ruleName = _.a_radioDisableAndPrefill([disabledOptions], prefillOptions, condition);\n"}, {"name": "==> Radio Prefill Without Disable", "value": "local ruleName = _.a_radioPrefillWithoutDisable(prefillOptions, condition);\n"}, {"name": "==> Radio Disable all", "value": "local ruleDisableAll = _.a_radioDisableAll(condition);\n"}, {"name": "Function Radio Clear Prefill", "value": "local ruleName = _.a_radioClearPrefill([options], condition);\n"}, {"name": "Show US state", "value": "_.showUSState(countryValue, 'us_alias', 'non-us_alias')\n"}, {"name": "Merge name", "value": "_.merge('target', ' ', parts)\n"}, {"name": "Radio show if", "value": "_.radioShowIf(radioValue, 'option', 'target')\n"}, {"name": "Validate date", "value": "_.validateDate('target', dateValue)\n"}, {"name": "Validate year", "value": "_.validateYear('target', yearValue)\n"}, {"name": "Validate commitment", "value": "_.validateCommitment('target', value)\n"}, {"name": "Validate fiscal year", "value": "_.validateFiscalYear('target', monthValue, dayValue)\n"}, {"name": "Validate min number", "value": "_.validateMinNumber('target', value, min)\n"}, {"name": "Validate min money", "value": "_.validateMinMoney('target', value, min)\n"}, {"name": "Validate total profit", "value": "_.validateTotalProfits('target', repeatableValue, 'percentage_alias')\n"}, {"name": "Get constant value", "value": "local constant = _.getConstantValue()\n"}, {"name": "Repeatable get empty row", "value": "local emptyRow = _.getEmptyRow(repeatable.value);\n"}, {"name": "Repeatable hide field with index", "value": "_.hideRepeatable('repeatable', 'field_alias', index, condition)\n"}, {"name": "Repeatable disable field with index", "value": "_.disableRepeatable('repeatable', 'field_alias', index, condition)\n"}, {"name": "Repeatable disable and clear", "value": "_.disableAndClearRepeatable('repeatable_alias', repeatable_alias.value, condition)\n"}, {"name": "Repeatable fill a field with index", "value": "_.fillRepeatable('repeatable', 'field_alias', index, value)\n"}, {"name": "Apply a function for repeatable", "value": "local func = function(index, element)\n  local value = element['alias'].value;\n\n_.applyRepeatable(func, repeatableValue)\n"}, {"name": "Copy value between two repeatable", "value": "local mappings = [\n    {from: 'alias', to: 'alias1'},\n  {from: 'alias2', to: 'alias3'},\n];\n\n_.prefillValueBetweenTwoRepeatable(sourceValue, destinationValue, destinationAlias, mappings)\n"}, {"name": "Untouch a field", "value": "_.untouch('alias')\n"}, {"name": "Untouch a list of fields", "value": "_.multipleUntouch([targets])\n"}, {"name": "Show file group with different conditions", "value": "local files = [\n    {file: 'file1', condition: op1},\n    {file: 'file2', condition: op2},\n];\n\n_.showFileGroup('filegroup', files)\n"}, {"name": "Show file group with same condition", "value": "_.showFileGroupWithSameCondition('filegroup', ['file1', 'file2'], condition)\n"}]