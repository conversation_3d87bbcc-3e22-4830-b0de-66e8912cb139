const fs = require('fs');
const path = require('path');

fs.copyFile(
    path.join(__dirname, 'node_modules/@blueprintjs/icons/lib/css/blueprint-icons.css'),
    path.join(__dirname, 'public/css/blueprint-icons.css'),
    (err) => {
        if (err) throw err;
        console.log('node_modules/@blueprintjs/icons/lib/css/blueprint-icons.css was copied to public/css/blueprint-icons.css');
    }
);

fs.copyFile(
    path.join(__dirname, 'node_modules/@blueprintjs/core/lib/css/blueprint.css'),
    path.join(__dirname, 'public/css/blueprint.css'),
    (err) => {
        if (err) throw err;
        console.log('node_modules/@blueprintjs/core/lib/css/blueprint.css was copied to public/css/blueprint.css');
    }
);

fs.copyFile(
    path.join(__dirname, 'node_modules/normalize.css/normalize.css'),
    path.join(__dirname, 'public/css/normalize.css'),
    (err) => {
        if (err) throw err;
        console.log('node_modules/normalize.css/normalize.css was copied to public/css/normalize.css');
    }
);