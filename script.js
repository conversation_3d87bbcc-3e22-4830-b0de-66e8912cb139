(() => {
    // Initialize the array
    const inputsOutputs = [];
    
    // Get all snippet elements
    const snippetElements = document.querySelectorAll('.border-right .hover\\:bg-gray-1');
    
    // Iterate over each snippet element
    snippetElements.forEach(snippetElement => {
        // Get all input elements under text-gray-7 hover:underline
        const inputElements = snippetElement.querySelectorAll('.text-gray-7 .hover\\:underline .truncate');
        
        // Get all output elements under text-gray-8 hover:underline
        const outputElements = snippetElement.querySelectorAll('.text-gray-8 .hover\\:underline .truncate');
        
        // Create pairs of inputs and outputs
        inputElements.forEach((inputElement, index) => {
            const input = inputElement ? inputElement.textContent : '';
            const output = outputElements[index] ? outputElements[index].textContent : '';
            
            // Push input-output pair to the array
            inputsOutputs.push({ input, output });
        });
    });

    // Log the array containing input-output pairs
    console.log('Input-Output Pairs:', inputsOutputs);
    return inputsOutputs;
})(); 