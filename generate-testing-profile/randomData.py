
import random
from faker import Faker
from utils import isFieldEmpty
from thefuzz import fuzz

fake = Faker()

integerWidgetTypes = ["Integer"]
floatWidgetTypes = ["Float", "Percentage", "Money"]
multipleSelectionTypes = ["MultipleCheckbox", "MultipleSuggest"]
singleSelectionTypes = ["Radio", "Dropdown"]
formattedTypes = ["Types", "Dropdown"]

countries = ["United States", "Belgium", "Canada", "France", "Germany", "Denmark", "Afghanistan", "Turkey", "United Kingdom of Great Britain and Northern Ireland"]
usStates = ["Arizona", "California", "Texas", "Maryland", "Guam", "Washington", "Virginia"]

def generateRandomData(widgetType, options, alias, question):
    toFillValue = ""
    random.seed()
    optionKeys = list([] if options is None else options.keys())
    optLen = len(optionKeys)

    if widgetType in multipleSelectionTypes:
        if optLen == 0:
            return ""
        shouldFill = (optLen == 1 and random.randint(0, 9) % 2 == 0) or optLen > 1
        if shouldFill:
            randomLen = random.randint(1, optLen)
            selected = []
            for i in range(0, randomLen):
                selected.append(optionKeys[random.randint(0, optLen - 1)])
            toFillValue = ";".join(set(selected))
        else:
            toFillValue = ""

    elif widgetType in singleSelectionTypes:
        if optLen == 0:
            return ""
        toFillValue = random.choice(optionKeys)
        
    elif widgetType == "Country":
        toFillValue = random.choice(countries)

    elif widgetType == "State":
        toFillValue = random.choice(usStates)

    elif widgetType == "Phone":
        toFillValue = fake.phone_number().split('x')[0]

    elif widgetType == "CustomFormat":
        toFillValue = guessValueFromAliasOrQuestion(alias, question)

    elif widgetType in integerWidgetTypes:
        toFillValue = random.randint(0, 2000000)

    elif widgetType in floatWidgetTypes:
        toFillValue = random.randint(0, 2000000) / random.randint(0, 1000)
    elif widgetType == "Date":
        toFillValue = fake.date()
    else:
        toFillValue = "AnduinTestString"

    return toFillValue

fakables = []
with open("./available_faker.txt", encoding="utf-8") as f:
    mylist = f.read().splitlines() 
    fakables = mylist
print(fakables)


def guessValueFromAliasOrQuestion(alias, question = ""):
    toGuessFrom = question
    if isFieldEmpty(question):
        toGuessFrom = alias

    calc = max(map(lambda a: (a, (toGuessFrom, a)), fakables), key=lambda b: b[1])
    matchedFakable = calc[0]
    result = getattr(fake, matchedFakable)()
    return result
    