import os
import openpyxl
import pandas as pd
from thefuzz import fuzz
from openpyxl.utils.cell import get_column_letter

from randomData import generateRandomData
from utils import *

# Define the path to the input file
input_file_path = 'template.xlsx'

# Define the path to the output file
output_file_path = 'output.xlsx'

DEMO_DATA_PATH = 'Demo LP Data.xlsx'

START_LP_DATA_ROW = 7

START_COLUMN = 'I'

start_column_index = ord(START_COLUMN) - ord('A')


def loadWorkBook(filename: str):
    return openpyxl.load_workbook(os.path.join(os.getcwd(), filename))

def parseOptions(s):
    if isFieldEmpty(s): # s != s when s is NaN
        return None
    rows = s.split('\n')
    splitted = map(lambda r: r.split('>> '), rows)
    cleaned = map(lambda r: (r[0].split('<<')[1], r[1]), splitted)
    options = {}
    for opt in cleaned:
        options[opt[0]] = opt[1]

    return options



questionFieldAliasMap = {}
def loadAnswerMap(workbook: openpyxl.Workbook):
    sheet = workbook["Data To Import"]
    fieldAliasAnswerMap = {}
    for col in sheet.iter_cols(min_row=1, min_col=start_column_index + 1, max_col=sheet.max_column):
        question = col[1].value
        field_alias = col[START_LP_DATA_ROW - 2].value
        questionFieldAliasMap[question] = field_alias
        fieldAliasAnswerMap[field_alias] = {
            "widgetType": col[2].value,
            "question": question,
            "lpDatas": [],
            "options": parseOptions(col[3].value),
        }
        for lp_idx in range(START_LP_DATA_ROW - 1, len(col)):
            lp_answer = col[lp_idx].value
            if lp_answer != None:
                fieldAliasAnswerMap[field_alias]["lpDatas"].append(lp_answer)
            
    return fieldAliasAnswerMap

def findAliasWithQuestion(questionToFind, allQuestions):
    calc = max(map(lambda a: (a, fuzz.ratio(questionToFind, a)), allQuestions), key=lambda b: b[1])
    confident_score = calc[1]
    if confident_score < 65 or questionToFind == "" or questionToFind is None:
        return None
    
    return questionFieldAliasMap[calc[0]]

def findAliasInAliases(aliasToFind, fieldMapKeys):
    calc = max(map(lambda a: (a, fuzz.ratio(aliasToFind, a)), fieldMapKeys), key=lambda b: b[1])
    confident_score = calc[1]
    if confident_score < 65:
        return None
    
    return calc[0]
    
def fillDataToTemplate(template: pd.DataFrame, fieldLPAnswerMap: dict):

    allQuestions = questionFieldAliasMap.keys()
    allAliases = fieldLPAnswerMap.keys()
    numberOfLP = max(map(lambda a: len(a['lpDatas']), fieldLPAnswerMap.values()))
    
    endRow = START_LP_DATA_ROW - 1 + numberOfLP
    
    sheet = template
    sheetLen = len(template.index)
    while endRow > sheetLen:
        line = pd.DataFrame({}, index=[sheetLen+1])
        sheet = pd.concat([sheet, line]).reset_index(drop=True)
        sheetLen = len(sheet.index)
    

    for colName in sheet.columns:
        col = sheet[colName]
        if int(colName) < start_column_index:
            continue
        question = col[1]
        field_alias = col[START_LP_DATA_ROW - 2]

        #Compare question to question
        foundQuestion = findAliasWithQuestion(question, allQuestions)

        #Compare alias with all aliases
        foundAlias = findAliasInAliases(field_alias, allAliases)
        
        foundData = None
        if (foundQuestion is not None) and (question != "") and (question == question):
            foundData = fieldLPAnswerMap[foundQuestion]
        elif foundAlias is not None:
            foundData = fieldLPAnswerMap[foundAlias]

        if foundData is not None:
            #Start filling if found any data

            lp_datas = foundData['lpDatas']
            finalRow = START_LP_DATA_ROW - 1 + len(lp_datas)
            for lp_idx in range(START_LP_DATA_ROW - 1, finalRow):
                col[lp_idx] = lp_datas[lp_idx - START_LP_DATA_ROW + 1]
        else:
            #Not found data -> filling randomly
            print("random value - column " + get_column_letter(colName))
            widgetType = col[2]
            options = parseOptions(col[3])
            for lp_idx in range(START_LP_DATA_ROW - 1, endRow):
                toFillValue = generateRandomData(widgetType, options, field_alias, question)
                col[lp_idx] = toFillValue

    return sheet

def writeWorkbook(workbook: pd.DataFrame):
    workbook.to_excel(output_file_path, header=False, index=False)

demo_data = loadWorkBook(DEMO_DATA_PATH)

template_obj = pd.read_excel(input_file_path, header=None)

loadedAnswerMap = loadAnswerMap(demo_data)

filledWorkbook = fillDataToTemplate(template_obj, loadedAnswerMap)

writeWorkbook(filledWorkbook)

