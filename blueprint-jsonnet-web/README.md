# How to start dev
```bash
bun run dev
```

Open browser and go to `http://localhost:5173/`.

The page is hot-reloading, which mean whenever a code change, site will auto reload, change reflected instantly to the browser.

All element are style with tailwind, document: [https://tailwindcss.com](https://tailwindcss.com)

We use `shadcn` for Web component, document: [https://ui.shadcn.com/docs/installation/vite#add-components](https://ui.shadcn.com/docs/installation/vite#add-components)
new component could be added with command:

```bash
bunx --bun shadcn@latest add <<ELEMENT_NAME>>
```

# Folder structure

- `store`: implement all store (state management), form json , blueprint data,... are all stored in here
- `webUI`: all code relate to the web page element and UI
- `components`: For shadcn, don't need to touch
