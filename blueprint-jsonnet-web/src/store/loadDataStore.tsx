import { PantheonResourceType } from '@/models/constants';
import { BlueprintDataStore, FormBuilderDataStore } from '@/models/storeModels';
import { BlueprintServices, FormServices } from '@/services/pantheonServices';
import RequestUtils from '@/utils/requestUtils';
import { create } from 'zustand';

export const useFormBuilderStore = create<FormBuilderDataStore>((set) => ({
  formBuilderData: {},
  isLoading: false,
  isSucceed: false,
  isError: false,
  error: '',
  url: '',
  authToken: '',

  fetch: async (url: string, authToken: string) => {
    set({ url, isLoading: true, isSucceed: false, isError: false, error: '' });

    console.log(authToken)

    try {
      const id = RequestUtils.getIdFromURL(url, PantheonResourceType.Form);
      if (!id) throw new Error('Invalid form url');
      const origin = RequestUtils.getEnvironmentOrigin(url);
      if (!origin) throw new Error('Invalid pantheon origin in url');

      console.log('fetch form', id, origin)
      const formService = new FormServices(authToken);
      const formResponse = await formService.fetchForm(id, origin);
      if (!formResponse.ok) throw new Error('Failed to fetch FormBuilder data');

      const data = await formResponse.json();
      set({ formBuilderData: data, isLoading: false, isSucceed: true });
    } catch (error: any) {
      set({ isError: true, error: error.message, isLoading: false });
    }
  },
}));

export const useBlueprintStore = create<BlueprintDataStore>((set) => ({
  blueprintInfo: {}, // General info like name, ....
  blueprintData: {}, // Every thing needed for parser, code gen ...
  blueprintDocuments: new Map<string, object>(),
  isLoading: false,
  isSucceed: false,
  isError: false,
  error: '',
  url: '',
  authToken: '',

  fetch: async (url: string, authToken: string) => {
    set({ url, isLoading: true, isSucceed: false, isError: false, error: '' });

    try {
      const id = RequestUtils.getIdFromURL(url, PantheonResourceType.Blueprint);
      if (!id) throw new Error('Invalid blueprint url');
      const origin = RequestUtils.getEnvironmentOrigin(url);
      if (!origin) throw new Error('Invalid pantheon origin in url');

      const service = new BlueprintServices(authToken)
      let response = await service.fetchBlueprint(id, origin);
      if (!response.ok) throw new Error('Failed to fetch Blueprint general info');
      const generalInfo = await response.json();
      const { versionModel } = generalInfo;
      const { baseDocuments, id: blueprintVersionId } = versionModel;

      // Fetch the catala code
      response = await service.fetchBlueprintData(blueprintVersionId, origin);
      if (!response.ok) throw new Error('Failed to fetch Blueprint data');
      const catalaData = await response.json();

      // Fetch all annotation data
      const documents = new Map<string, object>();
      await Promise.all([
        baseDocuments.map(
          (document: any) => {
            const { documentVersionId, keyOpt } = document
            return new Promise(async (resolve) => {
              const response = await service.fetchAnnotationData(documentVersionId, origin)
              const data = await response.json()
              documents.set(keyOpt, data)
              resolve(keyOpt)
            })
          }
        )
      ]).then(fulfilled => {
        console.log(fulfilled.length, baseDocuments.length)
      });

      console.log(documents)

      set({
        blueprintInfo: generalInfo,
        blueprintData: catalaData,
        blueprintDocuments: documents,
        isLoading: false,
        isSucceed: true
      });

    } catch (error: any) {
      set({ isError: true, error: error.message, isLoading: false });
    }
  },
}));

export default {
  useFormBuilderStore,
  useBlueprintStore
};
