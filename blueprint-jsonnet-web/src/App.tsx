import { ThemeProvider } from "@/components/theme/theme-provider"

import './App.css'
import { ModeToggle } from './components/theme/mode-toggle'
import { LoadFormBuilderDataPopup } from './webUI/loadData/loadDataPopup'
import { useBlueprintStore, useFormBuilderStore } from './store/loadDataStore'
import { GeneratedCodeList } from "./webUI/resolvingLogic/generatedCode/generatedCodeList"

function App() {

  const {formBuilderData} = useFormBuilderStore()
  const {blueprintInfo} = useBlueprintStore()

  return (
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
    <>
      <div className="flex justify-between">
        <div className="flex">
          <LoadFormBuilderDataPopup />
        </div>
        <div className="">
          {formBuilderData.formModel && 
          <div className="text-left">
              <h3><b>Form:</b> {formBuilderData.formModel.name}</h3>
          </div>
          }
          {blueprintInfo.blueprintModel && 
          <div className="text-left">
              <h3><b>Blueprint:</b> {blueprintInfo.blueprintModel.name}</h3>
          </div>
          }
        </div>
        <ModeToggle />
      </div>
      <div className="flex items-center pt-4">
        <div className="h-[90vh]">
          <GeneratedCodeList />
        </div>
      </div>
    </>
    </ThemeProvider>
  )
}

export default App
