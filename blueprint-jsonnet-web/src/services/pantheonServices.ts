import RequestUtils from "@/utils/requestUtils"

export class FormServices {
    constructor(
        private authToken: string
    ) {}
    async fetchForm(id: string, origin: string) {
        console.log(this.authToken)
        const url = `https://${origin}/api/v3/form/getForm`
        const fetching = RequestUtils.postToPantheon(url, this.authToken, {
            formId: id,
            versionIdOpt: null
        })
        return fetching
    }
}

export class BlueprintServices {
    constructor(
        private authToken: string
    ) {}
    async fetchBlueprint(id: string, origin: string) {
        console.log(this.authToken)
        const url = `https://${origin}/api/v3/blueprint/getBlueprint`
        const fetching = RequestUtils.postToPantheon(url, this.authToken, {
            blueprintId: id,
            versionIdOpt: null
        })
        return fetching
    }
    async fetchBlueprintData(bpVersionId: string, origin: string) {
        console.log(this.authToken)
        const url = `https://${origin}/api/v3/blueprint/getBlueprintVersionCatalaCode`
        const fetching = RequestUtils.postToPantheon(url, this.authToken, {
            blueprintVersionId: bpVersionId
        })
        return fetching
    }
    async fetchAnnotationData(annotationVersionId: string, origin: string) {
        console.log(this.authToken)
        const url = `https://${origin}/api/v3/blueprint/getAnnotationData`
        const fetching = RequestUtils.postToPantheon(url, this.authToken, {
            annotationVersionId: annotationVersionId
        })
        return fetching
    }
}


export default {
    FormServices,
    BlueprintServices
}