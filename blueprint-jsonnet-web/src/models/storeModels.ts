
export interface BaseFetchDataStore {
    isLoading: boolean,
    isSucceed: boolean,
    isError: boolean,
    error: string,
    authToken: string,

    url: string,
    fetch: (url: string, token: string) => any
}


export interface FormBuilderDataStore extends BaseFetchDataStore {
  formBuilderData: any,
}

export interface BlueprintDataStore extends BaseFetchDataStore {
  blueprintInfo: any,
  blueprintData: any,
  blueprintDocuments: Map<string, any>,
}

export interface AuthTokenStore{
  authToken: string,
  setAuthToken: (token: string) => void
}