import { PantheonResourceType } from "@/models/constants"

export function getIdFromURL(url: string, resource: PantheonResourceType): string {
    const paths = url.split('/')
    const indicatorIndex = paths.indexOf(resource)
    if (indicatorIndex === -1) {
        throw new Error(`Invalid ${resource} URL`)
    }
    return paths[indicatorIndex + 1]
}

export function getEnvironmentOrigin(url: string): string {
    const paths = url.split('/')
    const indicatorIndex = paths.indexOf("https:")
    if (indicatorIndex === -1) {
        throw new Error(`Cannot infer the environment`)
    }
    return paths[indicatorIndex + 2]
}

export async function postToPantheon(url: string, authToken: string, body: object, headers?: HeadersInit): Promise<Response> {
    return fetch (url, {
        method: 'POST',
        headers: {
            ... headers,
            "Authorization": authToken,
        },
        body: JSON.stringify(body),
    })
}


const RequestUtils =  {
    getIdFromURL,
    getEnvironmentOrigin,
    postToPantheon,
}


export default RequestUtils