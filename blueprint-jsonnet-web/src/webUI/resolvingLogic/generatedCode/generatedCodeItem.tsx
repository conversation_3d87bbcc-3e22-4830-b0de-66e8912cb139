
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader
} from "@/components/ui/card"

export function GeneratedCodeItem({id, codeContent}) {
  return (
    <Card className="w-[350px] mb-1">
      <CardHeader>
        <CardDescription>Some item #{id}</CardDescription>
      </CardHeader>
      <CardContent className="h-[100px]">
        {codeContent}
      </CardContent>
      <CardFooter className="flex justify-between">
      </CardFooter>
    </Card>
  )
}
