
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { GeneratedCodeItem } from "./generatedCodeItem"

const tags = Array.from({ length: 50 }).map(
  (_, i, a) => `v1.2.0-beta.${a.length - i}`
)

export function GeneratedCodeList() {
  return (
    <ScrollArea className="h-[100%] rounded-md border p-6">
      {/* <div className="p-4">
        <h4 className="mb-4 text-sm font-medium leading-none">Tags</h4>
        {tags.map((tag) => (
          <>
            <div key={tag} className="text-sm">
              {tag}
            </div>
            <Separator className="my-2" />
          </>
        ))}
      </div> */}
      {tags.map((value, index) =>
      <GeneratedCodeItem id={index} codeContent={value} />
    )

      }
    </ScrollArea>
  )
}
