import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    <PERSON>alogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogClose
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useEffect, useState } from "react";

import {useBlueprintStore, useFormBuilderStore} from '@/store/loadDataStore'

export function LoadFormBuilderDataPopup() {
    const {isLoading: fbLoading, error: fbError, fetch: fbFetch} = useFormBuilderStore();
    const {isLoading: bpLoading, error: bpError, fetch: bpFetch} = useBlueprintStore();

    const [open, setOpen] = useState(false);
    const [localFBURL, setLocalFBURL] = useState("");
    const [localBPURL, setLocalBPURL] = useState("");
    const [localToken, setLocalToken] = useState("");
    const [isClosable, setIsClosable] = useState(false);
    
    if (isClosable) {
        if (fbError == "" && bpError == "") {
            setOpen(false);
            setIsClosable(false)
        } else {
            setIsClosable(false)
        }
    }

    const handleSave = async () => {
        await Promise.all([
            fbFetch(localFBURL, localToken),
            bpFetch(localBPURL, localToken)
        ]).then(() => {
        })
        console.log(fbError)
        console.log(bpError)

        if (localFBURL == "" || localBPURL == ""
            || fbLoading || bpLoading
        ) return;
        setIsClosable(true)
    }
    const disabledDialog = fbLoading || bpLoading;

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline">Load FB/BP Data</Button>
            </DialogTrigger>
            <DialogContent className={"min-w-1/2 translate-y-[-60%] " + (disabledDialog ? "pointer-events-none opacity-50" : "")}>
                <DialogHeader>
                    <DialogTitle>Load Data by link</DialogTitle>
                    <DialogDescription>
                        Input the links to the resource here.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                            FormBuilder
                        </Label>
                        <Input className="col-span-3" placeholder="https://portal.anduin.dev/pantheon/form/for...."
                            onChange={(e) => setLocalFBURL(e.target.value)}
                            value={localFBURL}
                        />
                        {fbError && <div className="col-span-4 text-red-500">{fbError}</div>}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="username" className="text-right">
                            Blueprint
                        </Label>
                        <Input className="col-span-3" placeholder="https://portal.anduin.app/pantheon/blueprint/bpr....."
                            onChange={(e) => setLocalBPURL(e.target.value)}
                            value={localBPURL}
                        />
                        {bpError && <div className="col-span-4 text-red-500">{bpError}</div>}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="username" className="text-right">
                            Auth Token
                        </Label>
                        <Input id="auth-token" className="col-span-3" placeholder="Bearer eyJhb......." 
                            onChange={(e) => setLocalToken(e.target.value)}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" disabled={ disabledDialog }
                        onClick={handleSave}
                    >
                        Save changes
                        </Button>
                    <DialogClose asChild>
                        <Button type="button" variant="secondary">
                            Close
                        </Button>
                    </DialogClose>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
