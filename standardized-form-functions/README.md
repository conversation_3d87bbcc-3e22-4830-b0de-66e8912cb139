#Standardized Library

##Usages:
###Step 1: Import libary

```javascript
local _ = import 'LibName';
```
###Step 2: Use functions
| Functions | Usage                                                            | Parameters                                                                                                                                                                                                                                                                                                                                        | Examples                                                                                                |
|--------|------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------|
| showIf(component, condition) | show a component by condition                                    | component: Name of the component in String <br> <br> condition: condition to show the component                                                                                                                                                                                                                                                   | ```_.showIf(”paragraph1”, investortype.value == "indi")```                                              |
| enableIf(component, condition, childComponents)    | enable a component by condition                                  | component: Name of the component in String <br> <br> condition: condition to enable the component <br> <br> childComponents: Array of component names that are nested in the component. These components will be cleared upon disabled                                                                                                            | ```_.enableIf('section1', investortype.value == "indi", ['radio1', 'radio2', 'radio3'])```              |
| createClearHook(hookname, component, componentValue, componentOption)  | create a hook (that will be used to clear the prefilled values)  | a hook component should be a multiplecheckbox with 1 option<br> <br>hookName: unique hook name (in the same _.use function)<br> <br>component: hook component name (should be created in logic support page)<br> <br>componentValue: value of hook component<br> <br>componentOption: option name of hook component                               | ```local hook = _.createClearHook(’hook1’, ‘hook1’, hook1.value, ‘checked’);```                         |
| use({ target, value, rules, additionalFunctions})  | a function to combine all the rules of a multiplecheckbox or radio | target: multiplecheckbox or radio name<br> <br>value: multiplecheckbox or radio value<br> <br>rules: array of rules, each rule is a function that accept a state (object with format {value, disabledOptions}) as input and a state as output<br> <br>additionalFunctions: (optional) any functions that should be called after the new value is set | ```_.use({    target: ‘multiplecheckbox1’,    value: multiplecheckbox1.value,    rules: [rule1, rule2]})``` |
| multipleExclusive(op1, op2) | options of multiplecheckbox that uncheck each other   | op1: array of component names <br> <br> op2: array of component names                                                                                                                                                                                                                                                                             | ```local rule1 = _.multipleExclusive([’op1’], [‘op2]);```                                               |
| multipleDisableIf(disabledOptions, condition)  | disable options of a multiplecheckbox   | disabledOptions: array of options that need to be disabled <br> <br> condition: logic condition to disable options     | ```local disableOp1IfIsUS = _.multipleDisableIf([’op1’], radio.value == ‘us’);```                             |
| multiplePrefillAndDisableIf(prefillOptions, disabledOptions, conditions, hook)   | disable and prefill options of a multiplecheckbox | prefillOptions: array of options that need to be prefilled<br> <br>disabledOptions: array of options that need to be disabled<br> <br>condition: logic condition to disable options<br> <br>hook: the hook that you created with createClearHook function | ```local disableAndPrefillOp2IfEntity = _.multiplePrefillAndDisableIf([’op2’], ['op2', 'op4'], radio.value == ‘entity’, hook);```  |
| radioDisableIf(disabledOptions, conditions)| disable options of a radio | disabledOptions: array of options that need to be disabled<br> <br>condition: logic condition to disable options  | ```local disableOp2Op3IfIsIndi = _.radioDisableIf([’op2’, 'op3'], radio.value == ‘indi’);``` |
| radioPrefillAndDisableIf(prefillOption, disabledOptions, conditions, hook) | disable and prefill options of a radio | prefillOptions: array of options that need to be prefilled<br> <br>disabledOptions: array of options that need to be disabled<br> <br>condition: logic condition to disable options<br> <br>hook: the hook that you created with createClearHook function  | ```local disableAndPrefillOp5IfEntity = _.radioPrefillAndDisableIf([’op5’], ['op5'], radio.value == ‘entity’, hook);``` |
| radioPrefillIf(prefillOption, conditions, hook)  | prefill options of a radio  |  prefillOptions: array of options that need to be prefilled<br> <br>condition: logic condition to disable options<br> <br>hook: the hook that you created with createClearHook function   |  ```local prefillOp2IfCandada = _.radioPrefillIf([’op2’], country.value == ‘canada’, hook);```  |