{
getState: function(allFunctions, init) std.foldl(function(state, func) func(state), allFunctions, init),
purge: function(arr, elements) std.filter(function(ele) !std.member(elements, ele), arr),
purgeItem: function(value, arr) if std.member(arr, value) then "" else value,
updateFields: function(obj, fields) std.mapWithKey(function(field, value) if std.objectHas(fields, field) then fields[field] else value, obj),
contains: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,
endWith: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) arr[std.length(arr) - 1] == item, elements)) > 0,
showIf: function(component, cond) atd.add(component, 'hide', !cond),
enableIf: function(component, cond, childArr) local clearChildren = if !cond then std.mapWithIndex(function(index, value) atd.add(value, 'value', ''), childArr) else []; atd.add(component, 'disable', !cond) + std.flattenArrays(clearChildren),
value: function(target, value) atd.add(target, 'value', value),
use: function(x)
    local target = x.target;
    local value = x.value;
    local rules = std.map(function(value) if std.type(value) != "function" && std.objectHas(value, 'rule') then value.rule else value, x.rules);
    local hookNames = std.filterMap(function(value) std.type(value) != "function" && std.objectHas(value, 'hookName'), function(value) value.hookName, x.rules);
    local initState = {
            value: value,
            disabledOptions: []
        } + {
        [name]: null for name in hookNames
    };
    local clearFunctions = std.filterMap(function(value) std.type(value) != "function" && std.objectHas(value, 'clearFunction'), function(value) value.clearFunction, x.rules);
    local additionalFunctions = if std.objectHas(x, 'additionalFunctions') then x.additionalFunctions else [];
    local getState = function(allFunctions, init) std.foldl(function(state, func) func(state), allFunctions, init);
    local finalState = getState(rules, initState);
    (if finalState.value != initState.value then atd.add(target, "value", finalState.value) else [])
    + atd.add(target, "disabledOptions", finalState.disabledOptions)
    + std.flattenArrays(
        std.map(function(func) func(finalState), 
        additionalFunctions + clearFunctions
    )),
createClearHook: function(hookname, component, componentValue, componentOption)
    {
        name: hookname,
        state: std.member(componentValue, componentOption),
        setState: function(cleared)
            if cleared == true then atd.add(component, "value", [componentOption]) 
            else if cleared == false then atd.add(component, "value", []) 
            else [],
    },
multipleExclusive: function(op1, op2)
    local _ = self;
    function(state)
        if _.endWith(state.value, op1) then _.updateFields(state, {
            value: _.purge(state.value, op2)
        }) else if _.endWith(state.value, op2) then _.updateFields(state, {
            value: _.purge(state.value, op1)
        }) else state,
multipleDisableIf: function(disabledOptions, cond)
    local _ = self;
    function(state)
        if cond then _.updateFields(state, {
            value: _.purge(state.value, disabledOptions),
            disabledOptions: disabledOptions + _.purge(state.disabledOptions, disabledOptions),
        }) else state,
multiplePrefillAndDisableIf: function(prefillOptions, disabledOptions, cond, hook)
    local _ = self;
    local hookName = hook.name;
    local shouldBeCleared = hook.state;
    local setShouldBeCleared = hook.setState;
    {
        hookName: hookName,
        rule: function(state)
            if cond then _.updateFields(state, {
                value: prefillOptions + _.purge(state.value, prefillOptions + disabledOptions),
                disabledOptions: disabledOptions + _.purge(state.disabledOptions, disabledOptions),
                [hookName]: true, 
            }) else if !cond && shouldBeCleared then _.updateFields(state, {
                value: _.purge(state.value, prefillOptions),
                [hookName]: false
            }) else state,
        clearFunction: function(finalState) 
            if std.objectHas(finalState, hookName) && finalState[hookName] != null then setShouldBeCleared(finalState[hookName]) else [],
    },
multiplePrefillIf: function(prefillOptions, cond, hook)
    local _ = self;
    local hookName = hook.name;
    local shouldBeCleared = hook.state;
    local setShouldBeCleared = hook.setState;
    {
        hookName: hookName,
        rule: function(state)
            if cond then _.updateFields(state, {
                value: prefillOptions + _.purge(state.value, prefillOptions),
                [hookName]: true, 
            }) else if !cond && shouldBeCleared then _.updateFields(state, {
                value: _.purge(state.value, prefillOptions),
                [hookName]: false
            }) else state,
        clearFunction: function(finalState) 
            if std.objectHas(finalState, hookName) && finalState[hookName] != null then setShouldBeCleared(finalState[hookName]) else [],
    },
radioDisableIf: function(disabledOptions, cond)
    local _ = self;
    function(state)
        if cond then _.updateFields(state, {
            value: _.purgeItem(state.value, disabledOptions),
            disabledOptions: disabledOptions + _.purge(state.disabledOptions, disabledOptions),
        }) else state,
radioPrefillAndDisableIf: function(prefillOption, disabledOptions, cond, hook)
    local _ = self;
    local hookName = hook.name;
    local shouldBeCleared = hook.state;
    local setShouldBeCleared = hook.setState;
    {
        hookName: hookName,
        rule: function(state)
            if cond then _.updateFields(state, {
                value: prefillOption,
                disabledOptions: disabledOptions + _.purge(state.disabledOptions, disabledOptions),
                [hookName]: true, 
            }) else if !cond && shouldBeCleared then _.updateFields(state, {
                value: _.purgeItem(state.value, prefillOption),
                [hookName]: false
            }) else state,
        clearFunction: function(finalState) 
            if std.objectHas(finalState, hookName) && finalState[hookName] != null then setShouldBeCleared(finalState[hookName]) else [],
    },
radioPrefillIf: function(prefillOption, cond, hook)
    local _ = self;
    local hookName = hook.name;
    local shouldBeCleared = hook.state;
    local setShouldBeCleared = hook.setState;
    {
        hookName: hookName,
        rule: function(state)
            if cond then _.updateFields(state, {
                value: prefillOption,
                [hookName]: true, 
            }) else if !cond && shouldBeCleared then _.updateFields(state, {
                value: _.purgeItem(state.value, prefillOption),
                [hookName]: false
            }) else state,
        clearFunction: function(finalState) 
            if std.objectHas(finalState, hookName) && finalState[hookName] != null then setShouldBeCleared(finalState[hookName]) else [],
    },
showUSState: function(country, us, nonUS)
    local isUS = country == "United States";
    atd.add(us, "hide", !isUS)
    + atd.add(nonUS, "hide", isUS),
mergeIndiName: function(target, first, middle, last, suffix)
    local wrap = function(s) if s == '' then '' else s + ' ';
    local fullName = wrap(first) + wrap(middle) + wrap(last) + wrap(suffix);
    atd.add(target, 'value', fullName),
radioShowIf: function(value, option, target)
    local selected = std.member(value, option);
    atd.add(target, "hide", !selected),
combineInvestorName: function(parts)
    local filtered = std.filter(function(str) str != "", parts);

    local combine = std.join("", filtered);

    std.flattenArrays([
        atd.add('aggregated_name', "value", combine),
    ]),
validateDate: function(target, value)
    if atd.compareToday(value) > 0 then
        atd.add(target, 'error', 'Must not be later than today.')
    else
        atd.add(target, 'error', null),
validateCommitment: function(target, value)
    local floor = std.floor(value);
    local valid = std.isNumber(value);

    if valid && value == 0 then
        atd.add(target, 'error', 'Must be greater than $0.')
    else if valid && value > floor then
        atd.add(target, 'error', "Must not include cents.")
    else
        atd.add(target, 'error', null),
}
