import openpyxl, sys
from math import log10
from string import ascii_uppercase as alphabet

wb = openpyxl.load_workbook("/home/<USER>/Downloads/d.xlsx")
ws = wb.active

numHeaderRows = 2
charAliasRow = 0

repeatableCol = 1
fieldTypeCol = 2
targetAliasCol = 4
charsStartCol = 6
charsEndCol = 42
prefillCol = charsEndCol + 1
uncheckCol = charsEndCol + 2
validationCol = charsEndCol + 3

matrix = tuple(ws.rows)
valuesMatrix = matrix[numHeaderRows:]
charMatrix = [row[charsStartCol:charsEndCol+1] for row in valuesMatrix]
numRows = len(matrix)
numRowsDigits = int(log10(numRows)) + 1

validChildTypeByParentType = {
    'Radio Group Field': 'Radio Field',
    'Multiple Checkboxes Field': 'Checkbox Field',
    'File group': 'File item',
}

repeatableByField = {}

def printError(rowNumber: int, msg: str, severity: str = "Error"):
    print(f"Error: (row {rowNumber: >{numRowsDigits}}): {msg}")

def main():
    rulesData = []
    for (index, row) in enumerate(valuesMatrix):
        rowNumber = index + numHeaderRows + 1
        fieldType = valueOf(row[fieldTypeCol])
        targetAlias = valueOf(row[targetAliasCol])
        repeatableAlias = valueOf(row[repeatableCol])

        if repeatableAlias != '':
            repeatableByField[removeRepeatableIndex(targetAlias)] = repeatableAlias

        if targetAlias == '':
            printError(rowNumber, f"invalid or empty alias")
        # elif targetAlias.split('_')[-1].isnumeric() and repeatableAlias == '':
        #     printError(rowNumber, f"alias ends with index but no repeatable found in column {alphabet[repeatableCol]}", severity = "Warning")
        elif fieldType in {'Text Field', 'Radio Group Field', 'Paragraph Field', 'Multiple Checkboxes Field', 'Page Field', 'Signature Field', 'Group Field', 'File group', 'Repeatable Field'}:
            if len(rulesData) > 0 and rulesData[-1].alias == targetAlias:
                rulesData[-1].personas.append(generatePersonaData(row))
            else:
                rulesData.append(RuleData(targetAlias, fieldType, [generatePersonaData(row)], []))
        elif fieldType in {'Radio Field', 'Checkbox Field', 'File item'}:
            if len(rulesData) > 0:
                lastRule = rulesData[-1]
                if lastRule.fieldType in validChildTypeByParentType and validChildTypeByParentType[lastRule.fieldType] == fieldType:
                    if len(lastRule.options) > 0 and lastRule.options[-1].alias == targetAlias:
                        lastRule.options[-1].personas.append(generatePersonaData(row))
                    else:
                        lastRule.options.append(OptionData(targetAlias, [generatePersonaData(row)]))
                else:
                    printError(rowNumber, f"can't add '{fieldType}' to '{lastRule.fieldType}'")
            else:
                printError(rowNumber, f"option came before group")
        else:
            printError(rowNumber, f"unhandled field type '{fieldType}'")

    rules = [generateRule(data) for data in rulesData]

    rulesJson = ["""
      {
        "value" : "%s",
        "name" : "Rule%i",
        "defaultNamespace" : "main",
        "description" : "",
        "templateId" : ""
      }""".replace("\n", "") % (  
                rule
                    .replace("\n", "\\n")
                    .replace('"', '\\"'),
                index
             ) for (index, rule) in enumerate(rules)]

    f = open("out.txt", "w")
    f.write(",".join(rulesJson))


class Characteristic:
    def __init__(self, alias: str, expectedValue: str):
        self.alias = alias
        self.expectedValue = expectedValue

    def __repr__(self):
        return f"{self.alias}, {self.expectedValue}"

class PersonaData:
    def __init__(self, characteristics: list[Characteristic], prefillSource: str, uncheck: list[str]):
        self.characteristics = characteristics
        self.prefillSource = prefillSource
        self.uncheck = uncheck

    def __repr__(self):
        return f"{self.characteristics}, {self.prefillSource}, {self.uncheck}"

class OptionData:
    def __init__(self, alias: str, personas: list[PersonaData]):
        self.alias = alias
        self.personas = personas

    def __repr__(self):
        return f"{self.alias}, {self.personas}"

class RuleData:
    def __init__(self, alias: str, fieldType: str, personas: list[PersonaData], options: list[OptionData]):
        self.alias = alias
        self.personas = personas
        self.options = options
        self.fieldType = fieldType

    def __repr__(self):
        return f"{self.alias}, {self.personas}, {self.options}, {self.fieldType}"


ruleTemplate = """function(
    {inputAliases}
)

local config = import 'persona';
local _ = import 'mixcalc_v2';
local logic = afs.logic.main;

local isApplicable = {applicableCond};
local overrideHideConfig = {overrideHideConfigBool};

local ruleStub =
    if config.hide || overrideHideConfig then
        _.show(isApplicable)
    else
        _.activate(isApplicable)
    ;

ruleStub{optionsApplicableRules}{prefillRules}{uncheckRules}
    .apply(logic.{targetAlias}, ctx = {targetAlias})
"""

ruleTemplateRepeatable = """function(
    {inputAliases}
)

local config = import 'persona';
local _ = import 'mixcalc_v2';
local logic = afs.logic.main;

local isApplicable = {applicableCond};
local overrideHideConfig = {overrideHideConfigBool};

local ruleStub =
    if config.hide || overrideHideConfig then
        _.show(isApplicable)
    else
        _.activate(isApplicable)
    ;

if std.length({targetAlias}.value) > {repeatableIndex} then
    ruleStub{optionsApplicableRules}{prefillRules}{uncheckRules}
        .apply(logic.{outputAlias}, ctx = {outputCtx})
else
    []
"""

def dropEmpty(arr: list[str]):
    return list(filter(None, arr))

def valueOf(cell):
    return '' if cell.value is None else cell.value.strip()

def getOptionArray(cellValue: str):
    return [line.replace("[x]", "").strip() for line in cellValue.split("\n") if "[x]" in line]

def generatePersonaData(row):
    inputAliasesAndValues = [Characteristic(valueOf(matrix[charAliasRow][col + charsStartCol]), valueOf(cell)) for (col, cell) in enumerate(row[charsStartCol:charsEndCol+1]) if cell.value != None]
    prefillSource = valueOf(row[prefillCol])
    uncheck = dropEmpty([alias.strip() for alias in valueOf(row[uncheckCol]).split("\n")])
    return PersonaData(inputAliasesAndValues, prefillSource, uncheck)

def getCharacteristicCond(c: Characteristic):
    def getCondString(alias: str):
        opArray = getOptionArray(c.expectedValue)
        hasMultipleApplicableValues = len(opArray) > 0
        if hasMultipleApplicableValues:
            opsString = ', '.join([f"'{opValue}'" for opValue in opArray])
            return f"(if std.type({alias}.value) == 'array' then std.length(std.setInter(std.set([{opsString}]), std.set({alias}.value))) > 0 else std.member([{opsString}], {alias}.value))"
        else:
            return f"{alias}.value == '{c.expectedValue}'"

    if (cleanAlias := removeRepeatableIndex(c.alias)) in repeatableByField:
        repeatableAlias = repeatableByField[cleanAlias]
        repeatableIndex = getRepeatableIndex(c.alias)
        fieldAlias = f"{repeatableAlias}.value[{repeatableIndex}].{cleanAlias}"
        return prependRepeatableLengthCheck(c.alias, getCondString(fieldAlias))
    else:
        return getCondString(c.alias)

def getPersonaCond(p: PersonaData):
    return ' && '.join([getCharacteristicCond(c) for c in p.characteristics])

def getPersonasCond(personas: list[PersonaData]):
    conds = dropEmpty([getPersonaCond(p) for p in personas])
    return ' || '.join(conds)

def getOptionValue(rawValue: str):
    return "no" if rawValue[:3] == "no_" else "yes" if rawValue[:4] == "yes_" else rawValue

def getOptionRule(cond: str, optionAlias: str, parentFieldType: str):
    if parentFieldType in ['Radio Group Field', 'Multiple Checkboxes Field']:
        return f'\n    .addDeactivatedOptions(["{optionAlias}"], !({cond}))'
    elif parentFieldType == 'File group':
        return f'\n    .enableFiles(["{optionAlias}"], {cond})'
    else:
        print(f"Error: `getOptionRule` received unhandled `parentFieldType` '{parentFieldType}'")

def flattenPersonas(data: RuleData):
    optionPersonas = [persona for op in data.options for persona in op.personas]
    return data.personas + optionPersonas

def getRepeatableIndex(alias: str):
    indexStr = alias.split('_')[-1]
    return int(indexStr) + 1 if indexStr.isnumeric() else 0

def removeRepeatableIndex(alias: str):
    parts = alias.split('_')[-1]
    return alias[:-1-len(parts[-1])] if parts[-1].isnumeric() else alias

def getFieldAlias(alias: str):
    return f"{repeatableByField[cleanAlias]}.value[{getRepeatableIndex(alias)}].{cleanAlias}" if (cleanAlias := removeRepeatableIndex(alias)) in repeatableByField else alias

def prependRepeatableLengthCheck(alias: str, string: str):
    if (cleanAlias := removeRepeatableIndex(alias)) in repeatableByField:
        repeatableAlias = repeatableByField[cleanAlias]
        repeatableIndex = getRepeatableIndex(alias)
        return f"std.length({repeatableAlias}.value) > {repeatableIndex} && {string}"
    else:
        return string

def generateRule(data: RuleData):
    if len(data.personas) > 0:
        if (cleanAlias := removeRepeatableIndex(data.alias)) in repeatableByField:
            template = ruleTemplateRepeatable
            targetAlias =  repeatableByField[cleanAlias]
            repeatableIndex = getRepeatableIndex(data.alias)
            outputAlias = f"{repeatableByField[cleanAlias]}.atIndex({repeatableIndex}).{cleanAlias}"
            outputCtx = f"{repeatableByField[cleanAlias]}.value[{repeatableIndex}].{cleanAlias}"
        else:
            template = ruleTemplate
            targetAlias = data.alias

        allFieldAliases = [targetAlias] + [alias for persona in flattenPersonas(data) for char in persona.characteristics for alias in [char.alias, persona.prefillSource] if alias != '']
        inputAliasesSet = {alias if (cleanAlias := removeRepeatableIndex(alias)) not in repeatableByField else repeatableByField[cleanAlias] for alias in allFieldAliases}
        inputAliases = '\n    '.join([f"{alias}," for alias in inputAliasesSet])
        cond = getPersonasCond(data.personas)
        applicableCond = 'true' if cond == '' else cond

        options = data.options
        optionCondsAndAliases = [(getPersonasCond(op.personas), getOptionValue(op.alias)) for op in options]
        optionsApplicableRules = ''.join([getOptionRule(cond, optionAlias, data.fieldType) for (cond, optionAlias) in optionCondsAndAliases if cond != "" and (cond != applicableCond or data.fieldType == 'File group')])

        prefillSourcesAndConds = ((getFieldAlias(p.prefillSource), prependRepeatableLengthCheck(p.prefillSource, getPersonaCond(p))) for p in data.personas if p.prefillSource != '')
        prefillRules = ''.join([f'\n    .fill(if std.type({source}.value) == "array" then std.join(", ", {source}.value) else {source}.value, {cond})' for (source, cond) in prefillSourcesAndConds])
        # TODO: add prefill rules for options (remember to use getOptionValue) and handle prefilling all source-destination combinations, e.g., array -> array, array -> integer (sum?)

        optionExclusiveGroups = ((getOptionValue(op.alias), [getOptionValue(otherAlias) for otherAlias in persona.uncheck], getPersonaCond(persona)) for op in options for persona in op.personas if len(persona.uncheck) > 0)
        uncheckRules = ''.join([f'\n    .excludeOptionGroups(["{opAlias}"], ["{'", "'.join(otherAliases)}"], condition = {cond if cond != applicableCond else "true"})' for (opAlias, otherAliases, cond) in optionExclusiveGroups if len(otherAliases) > 0 and cond != ''])

        overrideHideConfigBool = "true" if data.fieldType in ['Signature Field', 'Page Field', 'File group'] else "false"

        return template.format(**locals())
    else:
        print(f"Error: `personas` was empty for alias {data.alias}")
        return ""


if __name__ == '__main__':
    sys.exit(main())