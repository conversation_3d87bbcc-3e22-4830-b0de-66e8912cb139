!function(n){var t={};function e(a){if(t[a])return t[a].exports;var o=t[a]={i:a,l:!1,exports:{}};return n[a].call(o.exports,o,o.exports,e),o.l=!0,o.exports}e.m=n,e.c=t,e.d=function(n,t,a){e.o(n,t)||Object.defineProperty(n,t,{enumerable:!0,get:a})},e.r=function(n){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},e.t=function(n,t){if(1&t&&(n=e(n)),8&t)return n;if(4&t&&"object"===typeof n&&n&&n.__esModule)return n;var a=Object.create(null);if(e.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var o in n)e.d(a,o,function(t){return n[t]}.bind(null,o));return a},e.n=function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,"a",t),t},e.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},e.p="",e(e.s=16)}({16:function(n,t,e){"use strict";e.r(t);document.createElement;const a="mapping-assist-button",o={inputAlias:"",inputOptions:[],inputIsRadio:!1,outputAlias:"",outputOptions:[],outputIsRadio:!1,namespace:""},i=Object.assign(document.createElement("button"),{id:a});function u(n){var t;const e=document.querySelector('div[style*="min-width: 600px;"] .flex-fill.flex.items-center.pt-8');e&&(null===(t=e.children[3])||void 0===t?void 0:t.id)!==a&&(e.appendChild(i),setTimeout((()=>function(n){const t=n.children[2].innerText||"";o.namespace="import"===t?"main":"export";const e=Array.from(document.querySelectorAll(".mb-8 .flex.items-start"));if(e.length<2)return void console.log("Mapping Assist --- Cannot find data rows");const a=e[0].children,i=e[1].children;o.inputAlias=a[0].innerText,a.length<3?o.inputOptions=[]:o.inputOptions=c(a[2]);o.outputAlias=i[0].innerText,i.length<3?o.outputOptions=[]:o.outputOptions=c(i[2]);const u=document.querySelector(".mb-24 .rounded-full.border-gray-4"),p=document.querySelector('[class="mt-20"] .rounded-full.border-gray-4');o.inputIsRadio=!!u,o.outputIsRadio=!!p,console.log(o)}(e)),300))}function c(n){const t=[];for(let e=0;e<n.children.length;e++){const a=n.children[e];t.push(a.innerText)}return t}i.className="bg-warning-3 p-4 h-px24 ml-8",i.innerHTML="Mapping assist",i.addEventListener("mousedown",(n=>{!function(){let n="function (".concat(o.inputAlias,")\n\n  if ").concat(o.inputAlias,'.value != "" then\n    atd.add("').concat(o.outputAlias,'","value", mapped, "').concat(o.namespace,'")\n  else []\n'),t=o.inputOptions.length>0,e=o.outputOptions.length>0;const a=o.inputOptions.map(((n,t)=>'    "'.concat(n,'": "').concat(o.outputOptions[t]||"",'"'))).join(",\n");o.inputIsRadio&&o.outputIsRadio&&(n="function (".concat(o.inputAlias,")\n  local mapping = {\n").concat(a,"\n  };\n  local inputValue = ").concat(o.inputAlias,".value;\n  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';\n\n  if ").concat(o.inputAlias,'.value != "" then\n    atd.add("').concat(o.outputAlias,'","value", mapped, "').concat(o.namespace,'")\n  else []\n'));!o.inputIsRadio&&t&&!o.outputIsRadio&&e&&(n="function (".concat(o.inputAlias,")\n  local mapping = {\n").concat(a,"\n  };\n  local inputValue = ").concat(o.inputAlias,".value;\n  local mapped = std.filter(\n    function (v) v != ''\n    , std.map(\n        function(v) if std.objectHas(mapping, v) then mapping[v] else '',\n        inputValue\n    ));\n\n  if ").concat(o.inputAlias,'.value != "" then\n    atd.add("').concat(o.outputAlias,'","value", mapped, "').concat(o.namespace,'")\n  else []\n'));o.inputIsRadio&&!o.outputIsRadio&&e&&(n="function (".concat(o.inputAlias,")\n  local mapping = {\n").concat(a,"\n  };\n  local inputValue = ").concat(o.inputAlias,".value;\n  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';\n    \n  if ").concat(o.inputAlias,'.value != "" then\n    atd.add("').concat(o.outputAlias,'","value", [mapped], "').concat(o.namespace,'")\n  else []\n'));!o.inputIsRadio&&t&&o.outputIsRadio&&(n="function (".concat(o.inputAlias,")\n  local mapping = {\n").concat(a,"\n  };\n  local inputValue = ").concat(o.inputAlias,".value;\n  local mapped = std.filter(\n    function (v) v != ''\n    , std.map(\n        function(v) if std.objectHas(mapping, v) then mapping[v] else '',\n        inputValue\n    ));\n\n  if ").concat(o.inputAlias,'.value != [] then\n    atd.add("').concat(o.outputAlias,'","value", mapped[0], "').concat(o.namespace,'")\n  else []\n'));navigator.clipboard.writeText(n)}(),n.stopPropagation()})),document.addEventListener("mousedown",(n=>{setTimeout((()=>u()),100),setTimeout((()=>u()),200)}))}});
//# sourceMappingURL=contentScript.bundle.js.map