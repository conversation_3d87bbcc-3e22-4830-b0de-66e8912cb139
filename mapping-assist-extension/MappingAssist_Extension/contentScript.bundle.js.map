{"version": 3, "sources": ["../webpack/bootstrap", "contentScript/mappingAssist.ts", "contentScript/index.ts"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "document", "createElement", "MAPPING_ASSIST_BUTTON_ID", "_APP_STATE", "inputAlias", "inputOptions", "inputIsRadio", "outputAlias", "outputOptions", "outputIsRadio", "namespace", "mappingAssistButton", "assign", "id", "updateMappingAssist", "e", "_rootElement$children", "rootElement", "querySelector", "children", "append<PERSON><PERSON><PERSON>", "setTimeout", "defaultNS", "innerText", "dataRows", "Array", "from", "querySelectorAll", "length", "console", "log", "inputRowItems", "outputRowItems", "optionsRawTexts", "inputRadioElement", "outputRadioElement", "updateState", "parent", "allTexts", "element", "push", "className", "innerHTML", "addEventListener", "event", "generatedRule", "concat", "isInputArray", "isOutputtArray", "mappingContent", "map", "inputOption", "index", "join", "navigator", "clipboard", "writeText", "mappingAssistClicked", "stopPropagation"], "mappings": "aACE,IAAIA,EAAmB,CAAC,EAGxB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,CAAC,GAUX,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,OACf,CAIAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,GAEhE,EAGAZ,EAAoBkB,EAAI,SAAShB,GACX,qBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,GACvD,EAOArB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,EAAM,EAAEC,KAAK,KAAMD,IAC9I,OAAOF,CACR,EAGAzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,OAAG,EAClD,WAA8B,OAAOA,CAAQ,EAE9C,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,CACR,EAGAZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,EAAW,EAGpH/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,0CCjF7CC,SAASC,cAEjB,MAAMC,EAA2B,wBAE3BC,EAQF,CACAC,WAAY,GACZC,aAAc,GACdC,cAAc,EACdC,YAAa,GACbC,cAAe,GACfC,eAAe,EACfC,UAAW,IAGTC,EAAsBjC,OAAOkC,OAAOZ,SAASC,cAAc,UAAW,CAAEY,GAAIX,IAS3E,SAASY,EAAoBC,GAAW,IAADC,EAC1C,MAAMC,EAAcjB,SAASkB,cAAc,qEACtCD,IACyB,QAAvBD,EAAAC,EAAYE,SAAS,UAAE,IAAAH,OAAA,EAAvBA,EAAyBH,MAAOX,IAEvCe,EAAYG,YAAYT,GACxBU,YAAW,IAGf,SAAqBJ,GACjB,MAAMK,EAAqBL,EAAYE,SAAS,GAAmBI,WAAa,GAChFpB,EAAWO,UAA0B,WAAdY,EAAyB,OAAS,SACzD,MAAME,EAAWC,MAAMC,KAAK1B,SAAS2B,iBAAiB,4BACtD,GAAIH,EAASI,OAAS,EAElB,YADAC,QAAQC,IAAI,4CAGhB,MAAMC,EAAgBP,EAAS,GAAGL,SAC5Ba,EAAiBR,EAAS,GAAGL,SAEnChB,EAAWC,WAAa2B,EAAc,GAAGR,UAErCQ,EAAcH,OAAS,EACvBzB,EAAWE,aAAe,GAE1BF,EAAWE,aAAe4B,EAAgBF,EAAc,IAG5D5B,EAAWI,YAAcyB,EAAe,GAAGT,UAEvCS,EAAeJ,OAAS,EACxBzB,EAAWK,cAAgB,GAE3BL,EAAWK,cAAgByB,EAAgBD,EAAe,IAG9D,MAAME,EAAoBlC,SAASkB,cAAc,sCAC3CiB,EAAqBnC,SAASkB,cAAc,+CAElDf,EAAWG,eAAe4B,EAC1B/B,EAAWM,gBAAgB0B,EAE3BN,QAAQC,IAAI3B,EAChB,CArCqBiC,CAAYnB,IAAc,KAC/C,CAyIA,SAASgB,EAAgBI,GACrB,MAAMC,EAAqB,GAC3B,IAAK,IAAItE,EAAI,EAAGA,EAAIqE,EAAOlB,SAASS,OAAQ5D,IAAK,CAC7C,MAAMuE,EAAUF,EAAOlB,SAASnD,GAEhCsE,EAASE,KAAKD,EAAQhB,UAC1B,CACA,OAAOe,CACX,CAhKA3B,EAAoB8B,UAAY,+BAEhC9B,EAAoB+B,UAAY,iBAChC/B,EAAoBgC,iBAAiB,aAAcC,KAkDnD,WACI,IAAIC,EAAa,aAAAC,OACJ3C,EAAWC,WAAU,cAAA0C,OAE/B3C,EAAWC,WAAU,oCAAA0C,OACb3C,EAAWI,YAAW,wBAAAuC,OAAuB3C,EAAWO,UAAS,mBAIxEqC,EAAe5C,EAAWE,aAAauB,OAAS,EAChDoB,EAAiB7C,EAAWK,cAAcoB,OAAS,EAEvD,MAAMqB,EAAiB9C,EAAWE,aAAa6C,KAC3C,CAACC,EAAaC,IAAK,QAAAN,OAAaK,EAAW,QAAAL,OAAO3C,EAAWK,cAAc4C,IAAU,GAAE,OACzFC,KAAK,OAGHlD,EAAWG,cAAgBH,EAAWM,gBACtCoC,EAAa,aAAAC,OACR3C,EAAWC,WAAU,4BAAA0C,OAEhCG,EAAc,iCAAAH,OAEO3C,EAAWC,WAAU,8GAAA0C,OAGrC3C,EAAWC,WAAU,oCAAA0C,OACb3C,EAAWI,YAAW,wBAAAuC,OAAuB3C,EAAWO,UAAS,qBAMvEP,EAAWG,cAAgByC,IAAiB5C,EAAWM,eAAiBuC,IACzEH,EAAa,aAAAC,OACR3C,EAAWC,WAAU,4BAAA0C,OAEhCG,EAAc,iCAAAH,OAEO3C,EAAWC,WAAU,oMAAA0C,OAQrC3C,EAAWC,WAAU,oCAAA0C,OACb3C,EAAWI,YAAW,wBAAAuC,OAAuB3C,EAAWO,UAAS,oBAMxEP,EAAWG,eAAiBH,EAAWM,eAAiBuC,IACxDH,EAAa,aAAAC,OACR3C,EAAWC,WAAU,4BAAA0C,OAEhCG,EAAc,iCAAAH,OAEO3C,EAAWC,WAAU,kHAAA0C,OAGrC3C,EAAWC,WAAU,oCAAA0C,OACb3C,EAAWI,YAAW,0BAAAuC,OAAyB3C,EAAWO,UAAS,qBAMzEP,EAAWG,cAAgByC,GAAgB5C,EAAWM,gBACvDoC,EAAa,aAAAC,OACR3C,EAAWC,WAAU,4BAAA0C,OAEhCG,EAAc,iCAAAH,OAEO3C,EAAWC,WAAU,oMAAA0C,OAQrC3C,EAAWC,WAAU,oCAAA0C,OACb3C,EAAWI,YAAW,2BAAAuC,OAA0B3C,EAAWO,UAAS,oBAM/E4C,UAAUC,UAAUC,UAAUX,EAClC,CA7IIY,GACAb,EAAMc,iBAAiB,ICtB3B1D,SAAS2C,iBAAiB,aAAc5B,IACpCM,YAAW,IAAMP,KAAwB,KACzCO,YAAW,IAAMP,KAAwB,IAAI,G", "file": "contentScript.bundle.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 16);\n", "\nlet a = document.createElement\n\nconst MAPPING_ASSIST_BUTTON_ID = \"mapping-assist-button\"\n\nconst _APP_STATE: {\n    inputAlias: String,\n    inputOptions: String[],\n    inputIsRadio: Boolean,\n    outputAlias: String,\n    outputOptions: String[],\n    outputIsRadio: <PERSON>olean,\n    namespace: String,\n} = {\n    inputAlias: \"\",\n    inputOptions: [],\n    inputIsRadio: false,\n    outputAlias: \"\",\n    outputOptions: [],\n    outputIsRadio: false,\n    namespace: \"\",\n};\n\nconst mappingAssistButton = Object.assign(document.createElement('button'), { id: MAPPING_ASSIST_BUTTON_ID });\nmappingAssistButton.className = 'bg-warning-3 p-4 h-px24 ml-8';\n// -- show the context menu when user clicks the advanced button\nmappingAssistButton.innerHTML = 'Mapping assist';\nmappingAssistButton.addEventListener('mousedown', (event: MouseEvent) => {\n    mappingAssistClicked();\n    event.stopPropagation()\n})\n\nexport function updateMappingAssist(e: Event) {\n    const rootElement = document.querySelector('div[style*=\"min-width: 600px;\"] .flex-fill.flex.items-center.pt-8');\n    if (!rootElement // CANNOT find root element\n        || rootElement.children[3]?.id === MAPPING_ASSIST_BUTTON_ID) //The advanced button is ADDED\n        return;\n    rootElement.appendChild(mappingAssistButton);\n    setTimeout(() => updateState(rootElement), 300);\n}\n\nfunction updateState(rootElement: Element) {\n    const defaultNS: String = (rootElement.children[2] as HTMLElement).innerText || \"\";\n    _APP_STATE.namespace = defaultNS === \"import\" ? \"main\" : \"export\";\n    const dataRows = Array.from(document.querySelectorAll('.mb-8 .flex.items-start'));\n    if (dataRows.length < 2) {\n        console.log('Mapping Assist --- Cannot find data rows')\n        return;\n    }\n    const inputRowItems = dataRows[0].children\n    const outputRowItems = dataRows[1].children\n    // @ts-ignore\n    _APP_STATE.inputAlias = inputRowItems[0].innerText;\n\n    if (inputRowItems.length < 3) {\n        _APP_STATE.inputOptions = [];\n    } else {\n        _APP_STATE.inputOptions = optionsRawTexts(inputRowItems[2]);\n    }\n    // @ts-ignore\n    _APP_STATE.outputAlias = outputRowItems[0].innerText;\n\n    if (outputRowItems.length < 3) {\n        _APP_STATE.outputOptions = [];\n    } else {\n        _APP_STATE.outputOptions = optionsRawTexts(outputRowItems[2]);\n    }\n\n    const inputRadioElement = document.querySelector(\".mb-24 .rounded-full.border-gray-4\");\n    const outputRadioElement = document.querySelector(`[class=\"mt-20\"] .rounded-full.border-gray-4`);\n\n    _APP_STATE.inputIsRadio = inputRadioElement ? true : false;\n    _APP_STATE.outputIsRadio = outputRadioElement ? true : false;\n\n    console.log(_APP_STATE)\n}\n\nfunction mappingAssistClicked() {\n    let generatedRule = \n    `function (${_APP_STATE.inputAlias})\n\n  if ${_APP_STATE.inputAlias}.value != \"\" then\n    atd.add(\"${_APP_STATE.outputAlias}\",\"value\", mapped, \"${_APP_STATE.namespace}\")\n  else []\n`;\n\n    let isInputArray = _APP_STATE.inputOptions.length > 0;\n    let isOutputtArray = _APP_STATE.outputOptions.length > 0;\n\n    const mappingContent = _APP_STATE.inputOptions.map(\n        (inputOption, index) => `    \"${inputOption}\": \"${_APP_STATE.outputOptions[index] || ''}\"`\n    ).join(',\\n');\n\n    // -- INPUT: radio, OUTPUT: radio\n    if (_APP_STATE.inputIsRadio && _APP_STATE.outputIsRadio) {\n        generatedRule = \n`function (${_APP_STATE.inputAlias})\n  local mapping = {\n${mappingContent}\n  };\n  local inputValue = ${_APP_STATE.inputAlias}.value;\n  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';\n\n  if ${_APP_STATE.inputAlias}.value != \"\" then\n    atd.add(\"${_APP_STATE.outputAlias}\",\"value\", mapped, \"${_APP_STATE.namespace}\")\n  else []\n`\n    }\n    \n    // -- INPUT: multiplecheckbox, OUTPUT: multiplecheckbox\n    if (!_APP_STATE.inputIsRadio && isInputArray && !_APP_STATE.outputIsRadio && isOutputtArray) {\n        generatedRule = \n`function (${_APP_STATE.inputAlias})\n  local mapping = {\n${mappingContent}\n  };\n  local inputValue = ${_APP_STATE.inputAlias}.value;\n  local mapped = std.filter(\n    function (v) v != ''\n    , std.map(\n        function(v) if std.objectHas(mapping, v) then mapping[v] else '',\n        inputValue\n    ));\n\n  if ${_APP_STATE.inputAlias}.value != \"\" then\n    atd.add(\"${_APP_STATE.outputAlias}\",\"value\", mapped, \"${_APP_STATE.namespace}\")\n  else []\n`\n    }\n\n    // -- INPUT: radio, OUTPUT: multiplecheckbox\n    if (_APP_STATE.inputIsRadio && !_APP_STATE.outputIsRadio && isOutputtArray) {\n        generatedRule = \n`function (${_APP_STATE.inputAlias})\n  local mapping = {\n${mappingContent}\n  };\n  local inputValue = ${_APP_STATE.inputAlias}.value;\n  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';\n    \n  if ${_APP_STATE.inputAlias}.value != \"\" then\n    atd.add(\"${_APP_STATE.outputAlias}\",\"value\", [mapped], \"${_APP_STATE.namespace}\")\n  else []\n`\n    }\n\n    // -- INPUT: multiplecheckbox, OUTPUT: radio\n    if (!_APP_STATE.inputIsRadio && isInputArray && _APP_STATE.outputIsRadio) {\n        generatedRule = \n`function (${_APP_STATE.inputAlias})\n  local mapping = {\n${mappingContent}\n  };\n  local inputValue = ${_APP_STATE.inputAlias}.value;\n  local mapped = std.filter(\n    function (v) v != ''\n    , std.map(\n        function(v) if std.objectHas(mapping, v) then mapping[v] else '',\n        inputValue\n    ));\n\n  if ${_APP_STATE.inputAlias}.value != [] then\n    atd.add(\"${_APP_STATE.outputAlias}\",\"value\", mapped[0], \"${_APP_STATE.namespace}\")\n  else []\n`\n    }\n\n\n    navigator.clipboard.writeText(generatedRule)\n}\n\nfunction extractRawTextsInHTML(htmlInput: string) {\n    const allValues = htmlInput.split(/<[^>]+>/g);\n    return allValues;\n}\n\nfunction optionsRawTexts(parent: Element) {\n    const allTexts: String[] = []\n    for (let i = 0; i < parent.children.length; i++) {\n        const element = parent.children[i];\n        //@ts-ignore\n        allTexts.push(element.innerText);\n    }\n    return allTexts\n}\n", "// If your extension doesn't need a content script, just leave this file empty\n\n// This is an example of a script that will run on every page. This can alter pages\n// Don't forget to change `matches` in manifest.json if you want to only change specific webpages\n\nimport { updateMappingAssist } from \"./mappingAssist\";\n\ndocument.addEventListener('mousedown', (e) => {\n    setTimeout(() => updateMappingAssist(e), 100);\n    setTimeout(() => updateMappingAssist(e), 200);\n});\n"], "sourceRoot": ""}