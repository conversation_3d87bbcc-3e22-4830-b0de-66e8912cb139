{"name": "mapping-assist-extension", "author": "AnVo", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^17.0.0", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^17.0.0", "@types/styled-components": "^5.1.26", "fuse.js": "^6.5.3", "leven": "^4.0.0", "normalize.css": "^8.0.1", "react": "^17.0.2", "react-browser-extension-scripts": "4.0.10", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^17.0.2", "react-draggable": "^4.4.3", "react-resizable": "^3.0.4", "react-select-search": "^3.0.9", "react-syntax-highlighter": "^15.4.5", "styled-components": "^5.3.5", "typescript": "^4.1.2"}, "scripts": {"start": "react-scripts start", "build": "export SET NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "postinstall": "node ./copy-dep.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/chrome": "0.0.154", "@types/jquery": "^3.5.13", "@types/react-resizable": "^1.7.3", "@types/react-syntax-highlighter": "^13.5.2"}}