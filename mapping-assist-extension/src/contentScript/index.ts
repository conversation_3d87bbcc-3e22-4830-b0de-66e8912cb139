// If your extension doesn't need a content script, just leave this file empty

// This is an example of a script that will run on every page. This can alter pages
// Don't forget to change `matches` in manifest.json if you want to only change specific webpages

import { updateMappingAssist } from "./mappingAssist";

document.addEventListener('mousedown', (e) => {
    setTimeout(() => updateMappingAssist(e), 100);
    setTimeout(() => updateMappingAssist(e), 200);
});
