
let a = document.createElement

const MAPPING_ASSIST_BUTTON_ID = "mapping-assist-button"

const _APP_STATE: {
    inputAlias: String,
    inputOptions: String[],
    inputIsRadio: Boolean,
    outputAlias: String,
    outputOptions: String[],
    outputIsRadio: <PERSON>olean,
    namespace: String,
} = {
    inputAlias: "",
    inputOptions: [],
    inputIsRadio: false,
    outputAlias: "",
    outputOptions: [],
    outputIsRadio: false,
    namespace: "",
};

const mappingAssistButton = Object.assign(document.createElement('button'), { id: MAPPING_ASSIST_BUTTON_ID });
mappingAssistButton.className = 'bg-warning-3 p-4 h-px24 ml-8';
// -- show the context menu when user clicks the advanced button
mappingAssistButton.innerHTML = 'Mapping assist';
mappingAssistButton.addEventListener('mousedown', (event: MouseEvent) => {
    mappingAssistClicked();
    event.stopPropagation()
})

export function updateMappingAssist(e: Event) {
    const rootElement = document.querySelector('div[style*="min-width: 600px;"] .flex-fill.flex.items-center.pt-8');
    if (!rootElement // CANNOT find root element
        || rootElement.children[3]?.id === MAPPING_ASSIST_BUTTON_ID) //The advanced button is ADDED
        return;
    rootElement.appendChild(mappingAssistButton);
    setTimeout(() => updateState(rootElement), 300);
}

function updateState(rootElement: Element) {
    const defaultNS: String = (rootElement.children[2] as HTMLElement).innerText || "";
    _APP_STATE.namespace = defaultNS === "import" ? "main" : "export";
    const dataRows = Array.from(document.querySelectorAll('.mb-8 .flex.items-start'));
    if (dataRows.length < 2) {
        console.log('Mapping Assist --- Cannot find data rows')
        return;
    }
    const inputRowItems = dataRows[0].children
    const outputRowItems = dataRows[1].children
    // @ts-ignore
    _APP_STATE.inputAlias = inputRowItems[0].innerText;

    if (inputRowItems.length < 3) {
        _APP_STATE.inputOptions = [];
    } else {
        _APP_STATE.inputOptions = optionsRawTexts(inputRowItems[2]);
    }
    // @ts-ignore
    _APP_STATE.outputAlias = outputRowItems[0].innerText;

    if (outputRowItems.length < 3) {
        _APP_STATE.outputOptions = [];
    } else {
        _APP_STATE.outputOptions = optionsRawTexts(outputRowItems[2]);
    }

    const inputRadioElement = document.querySelector(".mb-24 .rounded-full.border-gray-4");
    const outputRadioElement = document.querySelector(`[class="mt-20"] .rounded-full.border-gray-4`);

    _APP_STATE.inputIsRadio = inputRadioElement ? true : false;
    _APP_STATE.outputIsRadio = outputRadioElement ? true : false;

    console.log(_APP_STATE)
}

function mappingAssistClicked() {
    let generatedRule = 
    `function (${_APP_STATE.inputAlias})

  if ${_APP_STATE.inputAlias}.value != "" then
    atd.add("${_APP_STATE.outputAlias}","value", mapped, "${_APP_STATE.namespace}")
  else []
`;

    let isInputArray = _APP_STATE.inputOptions.length > 0;
    let isOutputtArray = _APP_STATE.outputOptions.length > 0;

    const mappingContent = _APP_STATE.inputOptions.map(
        (inputOption, index) => `    "${inputOption}": "${_APP_STATE.outputOptions[index] || ''}"`
    ).join(',\n');

    // -- INPUT: radio, OUTPUT: radio
    if (_APP_STATE.inputIsRadio && _APP_STATE.outputIsRadio) {
        generatedRule = 
`function (${_APP_STATE.inputAlias})
  local mapping = {
${mappingContent}
  };
  local inputValue = ${_APP_STATE.inputAlias}.value;
  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';

  if ${_APP_STATE.inputAlias}.value != "" then
    atd.add("${_APP_STATE.outputAlias}","value", mapped, "${_APP_STATE.namespace}")
  else []
`
    }
    
    // -- INPUT: multiplecheckbox, OUTPUT: multiplecheckbox
    if (!_APP_STATE.inputIsRadio && isInputArray && !_APP_STATE.outputIsRadio && isOutputtArray) {
        generatedRule = 
`function (${_APP_STATE.inputAlias})
  local mapping = {
${mappingContent}
  };
  local inputValue = ${_APP_STATE.inputAlias}.value;
  local mapped = std.filter(
    function (v) v != ''
    , std.map(
        function(v) if std.objectHas(mapping, v) then mapping[v] else '',
        inputValue
    ));

  if ${_APP_STATE.inputAlias}.value != "" then
    atd.add("${_APP_STATE.outputAlias}","value", mapped, "${_APP_STATE.namespace}")
  else []
`
    }

    // -- INPUT: radio, OUTPUT: multiplecheckbox
    if (_APP_STATE.inputIsRadio && !_APP_STATE.outputIsRadio && isOutputtArray) {
        generatedRule = 
`function (${_APP_STATE.inputAlias})
  local mapping = {
${mappingContent}
  };
  local inputValue = ${_APP_STATE.inputAlias}.value;
  local mapped = if std.objectHas(mapping, inputValue) then mapping[inputValue] else '';
    
  if ${_APP_STATE.inputAlias}.value != "" then
    atd.add("${_APP_STATE.outputAlias}","value", [mapped], "${_APP_STATE.namespace}")
  else []
`
    }

    // -- INPUT: multiplecheckbox, OUTPUT: radio
    if (!_APP_STATE.inputIsRadio && isInputArray && _APP_STATE.outputIsRadio) {
        generatedRule = 
`function (${_APP_STATE.inputAlias})
  local mapping = {
${mappingContent}
  };
  local inputValue = ${_APP_STATE.inputAlias}.value;
  local mapped = std.filter(
    function (v) v != ''
    , std.map(
        function(v) if std.objectHas(mapping, v) then mapping[v] else '',
        inputValue
    ));

  if ${_APP_STATE.inputAlias}.value != [] then
    atd.add("${_APP_STATE.outputAlias}","value", mapped[0], "${_APP_STATE.namespace}")
  else []
`
    }


    navigator.clipboard.writeText(generatedRule)
}

function extractRawTextsInHTML(htmlInput: string) {
    const allValues = htmlInput.split(/<[^>]+>/g);
    return allValues;
}

function optionsRawTexts(parent: Element) {
    const allTexts: String[] = []
    for (let i = 0; i < parent.children.length; i++) {
        const element = parent.children[i];
        //@ts-ignore
        allTexts.push(element.innerText);
    }
    return allTexts
}
