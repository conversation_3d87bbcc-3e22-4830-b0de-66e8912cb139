# Changelog

All notable changes to library `mixcalc` on [Portal Tool Mananger](https://portal.anduin.app/pantheon/tool-manager/form-rule-library-version/ftc28dm1oejnx80g5env) will be documented in this file.

## Version 3 - Feb 14, 2025

### Fixed

*   Support trimming leading and trailing spaces for rule autocorrect and add more supported widgets ([#86cw6455h](https://app.clickup.com/t/86cw6455h))
*   `excludeOptions` now correctly functions even if the option's value is a substring of another option's value ([#86cxdem4f](https://app.clickup.com/t/86cxdem4f))
*   Import and Autofill now work for fake checkboxes ([#86cww4kc6](https://app.clickup.com/t/86cww4kc6))
*   All test profiles cover the fixes above:
    1. SVF - Multiple Rules - AUTO-CORRECT - EXAMPLE 1
    2. Use Case - CEP XIII, CIP XII and GPE V - EXAMPLE 1
    3. MVF - Single Rule - MUTUALLY EXCLUSIVE OPTIONS - EXAMPLE 1.4 - excludeOptions
    4. SVF - Single Rule - AUTO-CORRECT - EXAMPLE 1
    5. SVF - Side Effects - SIDE EFFECT I - EXAMPLE 1

## Version 2 - Nov 14, 2024

### Fixed

*   Resolved an issue where the `repeatable` property was not cleared when deactivated or disabled by a parent element ([#86cx2hxju](https://app.clickup.com/t/86cx2hxju))

## Version 1 - Aug 5, 2024

### Added

*   Initial release of the library.