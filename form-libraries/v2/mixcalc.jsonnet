{
    local _main = self,
    local _utils = _main.utils,
    local _fakeCheckboxUtils = _main.fakeCheckbox.utils,
    local _validate = _main.validate,
    local _calc = _main.calculation,
    local _const = _main.constants,
    local _rule = _main.rule,
    local _ruleType = _main.rule.typ,
    local _gaiaCmd = _main.gaiaCommand,
    local _widget = _main.widget,
    local _widgetKey = _main.widget.key,
    local _collector = _rule.collector,

    // ------ Public methods ------

    unit: _collector,
    show: _collector.show,
    hide: _collector.hide,
    deactivate: _collector.deactivate,
    activate: _collector.activate,
    setError: _collector.setError,
    setWarning: _collector.setWarning,
    fill: _collector.fill,
    fillAllOptions: _collector.fillAllOptions,
    deactivateOptions: _collector.deactivateOptions,
    activateOptions: _collector.activateOptions,
    addDeactivatedOptions: _collector.addDeactivatedOptions,
    addActivatedOptions: _collector.addActivatedOptions,
    excludeOptions: _collector.excludeOptions,
    excludeOptionGroups: _collector.excludeOptionGroups,
    excludeOtherOptions: _collector.excludeOtherOptions,
    excludeEachOptions: _collector.excludeEachOptions,
    enableFiles: _collector.enableFiles,
    enableAllFiles: _collector.enableAllFiles,
    setSigningTypes: _collector.setSigningTypes,
    autoCorrect: _collector.autoCorrect,
    resetValue: _collector.resetValue,

    purge: _utils.purge,
    intersect: _utils.intersect,
    last: _utils.last,
    unique: _utils.unique,
    get: _utils.get,
    trim: _utils.trim,

    AnduinSignatureFlow: ["wet-sign", "e-sign"],
    WetSignOnlySignatureFlow: ["wet-sign"],
    DocuSignatureFlow: ["wet-sign", "qes"],
    AllWidgetOptions: _const.AllWidgetOptions,

    // for editor suggestion
    apply: function() [],
    applyFakeCheckbox: function() [],
    applyRepeatable: function() [],
    applyKey: function() [],
    condition: "",
    orElse: "",
    clear: "",
    none: "",
    disabled: "",

    widget: {
        key: {
            // string
            TextBox: "TextBox",
            TextArea: "TextArea",
            Email: "Email",
            Date: "Date",
            Phone: "Phone",
            CustomFormat: "CustomFormat",
            Country: "Country",
            State: "State",
            Radio: "Radio",
            Dropdown: "Dropdown",

            // number
            Integer: "Integer",
            Float: "Float",
            Money: "Money",
            Percentage: "Percentage",
            Year: "Year",

            // repeatable
            Repeatable: "Repeatable",

            // array
            FileGroup: "FileGroup",
            MultipleCheckbox: "MultipleCheckbox",
            MultipleSuggest: "MultipleSuggest",
            FakeMultipleCheckbox: "FakeMultipleCheckbox",

            // null
            Group: "Group",
            Page: "Page",
            Header: "Header",
            Paragraph: "Paragraph",
            File: "File",
            Signature: "Signature",
            Pdf: "Pdf",
            AnchorLink: "AnchorLink",
        },

        getInvisibleWidgets: [
            _widgetKey.File,
            _widgetKey.FileGroup,
            _widgetKey.Signature,
        ],

        getSingleValueWidgets: [
            _widgetKey.TextBox,
            _widgetKey.TextArea,
            _widgetKey.Email,
            _widgetKey.Date,
            _widgetKey.Phone,
            _widgetKey.CustomFormat,
            _widgetKey.Country,
            _widgetKey.State,
            _widgetKey.Radio,
            _widgetKey.Dropdown,
            _widgetKey.Integer,
            _widgetKey.Float,
            _widgetKey.Money,
            _widgetKey.Percentage,
            _widgetKey.Year,
        ],

        getMultipleValueWidgets: [
            _widgetKey.FileGroup,
            _widgetKey.MultipleCheckbox,
            _widgetKey.FakeMultipleCheckbox,
            _widgetKey.MultipleSuggest,
        ],

        getMultipleSelectionWidgets: [
            _widgetKey.MultipleCheckbox,
            _widgetKey.FakeMultipleCheckbox,
            _widgetKey.MultipleSuggest,
        ],

        getSingleSelectionWidgets: [
            _widgetKey.Radio,
            _widgetKey.Dropdown,
            _widgetKey.Country,
            _widgetKey.State,
        ],

        getOptionWidgets: _widget.getMultipleSelectionWidgets + _widget.getSingleSelectionWidgets,

        getStringValueWidgets: [
            _widgetKey.TextBox,
            _widgetKey.TextArea,
            _widgetKey.Email,
            _widgetKey.Date,
            _widgetKey.Phone,
            _widgetKey.CustomFormat,
            _widgetKey.Country,
            _widgetKey.State,
            _widgetKey.Radio,
            _widgetKey.Dropdown,
        ],

        getNumberValueWidgets: [
            _widgetKey.Integer,
            _widgetKey.Float,
            _widgetKey.Money,
            _widgetKey.Percentage,
            _widgetKey.Year,
        ],

        getArrayValueWidgets: [
            _widgetKey.FileGroup,
            _widgetKey.Repeatable,
            _widgetKey.MultipleCheckbox,
            _widgetKey.FakeMultipleCheckbox,
            _widgetKey.MultipleSuggest,
        ],

    },

    rule: {
        typ: {
            local _allWidgets = std.objectValues(_widgetKey),
            local _allInteractiveWidgets = _widget.getStringValueWidgets + [_widgetKey.Date] + _widget.getNumberValueWidgets + _widget.getMultipleSelectionWidgets + [_widgetKey.Repeatable],

            show: {
                key: "show",
                applicableWidgets: _allWidgets,
            },
            hide: _ruleType.show {
                key: "hide",
            },

            deactivate: {
                key: "deactivate",
                applicableWidgets: _utils.purge(_allWidgets, _widget.getInvisibleWidgets),
            },
            activate: _ruleType.deactivate {
                key: "activate",
            },

            setError: {
                key: "setError",
                applicableWidgets: _allInteractiveWidgets,
            },
            setWarning: _ruleType.setError {
                key: "setWarning",
            },

            fill: {
                key: "fill",
                applicableWidgets: _allInteractiveWidgets,
            },

            fillAllOptions: {
                key: "fillAllOptions",
                applicableWidgets: _utils.purge(_widget.getMultipleSelectionWidgets, [_widgetKey.FakeMultipleCheckbox]),
            },

            deactivateOptions: {
                key: "deactivateOptions",
                applicableWidgets: _widget.getOptionWidgets,
            },
            activateOptions: _ruleType.deactivateOptions {
                key: "activateOptions",
            },
            addDeactivatedOptions: _ruleType.deactivateOptions {
                key: "addDeactivatedOptions",
            },
            addActivatedOptions: _ruleType.deactivateOptions {
                key: "addActivatedOptions",
            },

            excludeOptions: {
                key: "excludeOptions",
                applicableWidgets: _widget.getMultipleSelectionWidgets,
            },
            excludeOptionGroups: _ruleType.excludeOptions {
                key: "excludeOptionGroups",
            },
            excludeOtherOptions: _ruleType.excludeOptions {
                key: "excludeOtherOptions",
            },
            excludeEachOptions: _ruleType.excludeOptions {
                key: "excludeEachOptions",
            },
            enableFiles: {
                key: "enableFiles",
                applicableWidgets: [_widgetKey.FileGroup],
            },
            enableAllFiles: _ruleType.enableFiles {
                key: "enableAllFiles",
            },

            setSigningTypes: {
                key: "setSigningTypes",
                applicableWidgets: [_widgetKey.File],
            },

            autoCorrect: {
                key: "autoCorrect",
                applicableWidgets: [
                    _widgetKey.TextBox,
                    _widgetKey.TextArea,
                    _widgetKey.Email,
                    _widgetKey.Date,
                    _widgetKey.Phone,
                ],
            },

            resetValue: {
                key: "resetValue",
                applicableWidgets: _allInteractiveWidgets,
            },

            unsafeSetValue: {
                key: "unsafeSetValue",
                applicableWidgets: _allInteractiveWidgets,
            },

            unsafeDeselectOptions: {
                key: "unsafeDeselectOptions",
                applicableWidgets: _utils.purge(_widget.getMultipleSelectionWidgets, [_widgetKey.FakeMultipleCheckbox]),
            },
        },

        getRulesWithOptions: [
            _rule.typ.deactivateOptions.key,
            _rule.typ.activateOptions.key,
            _rule.typ.addDeactivatedOptions.key,
            _rule.typ.addActivatedOptions.key,
            _rule.typ.excludeOptions.key,
            _rule.typ.excludeOtherOptions.key,
            _rule.typ.unsafeDeselectOptions.key,
        ],

        getRulesWithValue: [
            _rule.typ.fill.key,
        ],

        getRulesWithOptionGroups: [
            _rule.typ.excludeOptionGroups.key,
        ],

        collector: {
            local ref = self,

            rules: [],

            append: function(rule)
                ref {
                    rules: ref.rules + [rule],
                },

            show: _rule.showImpl(ref.append),
            hide: _rule.hideImpl(ref.append),
            deactivate: _rule.deactivateImpl(ref.append),
            activate: _rule.activateImpl(ref.append),
            setError: _rule.setErrorImpl(ref.append),
            setWarning: _rule.setWarningImpl(ref.append),
            fill: _rule.fillImpl(ref.append),
            fillAllOptions: _rule.fillAllOptionsImpl(ref.append),
            deactivateOptions: _rule.deactivateOptionsImpl(ref.append),
            activateOptions: _rule.activateOptionsImpl(ref.append),
            addDeactivatedOptions: _rule.addDeactivatedOptionsImpl(ref.append),
            addActivatedOptions: _rule.addActivatedOptionsImpl(ref.append),
            excludeOptions: _rule.excludeOptionsImpl(ref.append),
            excludeOptionGroups: _rule.excludeOptionGroupsImpl(ref.append),
            excludeOtherOptions: _rule.excludeOtherOptionsImpl(ref.append),
            excludeEachOptions: _rule.excludeEachOptionsImpl(ref.append),
            enableFiles: _rule.enableFilesImpl(ref.append),
            enableAllFiles: _rule.enableAllFilesImpl(ref.append),
            setSigningTypes: _rule.setSigningTypesImpl(ref.append),
            autoCorrect: _rule.autoCorrectImpl(ref.append),
            resetValue: _rule.resetValueImpl(ref.append),
            unsafeSetValue: _rule.unsafeSetValueImpl(ref.append),
            unsafeDeselectOptions: _rule.unsafeDeselectOptionsImpl(ref.append),

            apply: function(output, ctx, debug=false)
                local result = _calc.init(ctx, output, ref.rules);

                if debug then
                    result
                else
                    result.gaiaEvents,

            applyRepeatable: function(output, ctx=_const.EmptyInputValue, debug=false) function(onRow)
                local result = _calc.init(ctx, output, ref.rules);

                local removedIndexes = _calc.getSetValue(result.commands, "removeItem", []);
                local rules = _utils.getRulesByType([_rule.typ.fill.key], ref.rules);
                local repeatableInitValues = std.map(function(rule) rule.value, rules);
                local childKeys = std.flatMap(
                    function(repeatableValue)
                        if std.length(repeatableValue) > 0 then std.objectFields(repeatableValue[0]) else []
                    , repeatableInitValues
                );
                local getRowInitValue = function(index)
                    std.foldl(
                        function(res, repeatableValue)
                            res + (if index < std.length(repeatableValue) then repeatableValue[index] else {})
                        , repeatableInitValues
                        , std.foldl(function(res, key) res { [key]: _const.DefaultInputValue }, childKeys, {})
                    );

                local f = function(index, element)
                    local isDisabled = _calc.getDisabled(result.commands) || ctx.disableByParent;

                    if std.member(removedIndexes, index) || (isDisabled && index > 0) then
                        []
                    else
                        onRow(element, output.atIndex(index), index, getRowInitValue(index));
                local rowResult = std.flattenArrays(std.mapWithIndex(f, ctx.value));

                if debug then
                    result { children: rowResult }
                else
                    result.gaiaEvents + rowResult,

            applyKey: function(key)
                { key: key, rules: ref.rules },

            applyFakeCheckbox: function(targetObj, keyRules, debug=false)
                _calc.combineFakeCheckboxRules(targetObj, ref.rules, keyRules, debug),
        },

        // ------ Show/Hide ------

        showImpl: function(converter)
            function(condition=true)
                converter({
                    typ: _rule.typ.show.key,
                    condition: _validate.asBoolean(condition),
                }),

        hideImpl: function(converter)
            function(condition=true)
                converter({
                    typ: _rule.typ.hide.key,
                    condition: _validate.asBoolean(condition),
                }),

        // ------ Activate/Deactivate ------

        deactivateImpl: function(converter)
            function(condition=true)
                converter({
                    typ: _rule.typ.deactivate.key,
                    condition: _validate.asBoolean(condition),
                }),

        activateImpl: function(converter)
            function(condition=true)
                converter({
                    typ: _rule.typ.activate.key,
                    condition: _validate.asBoolean(condition),
                }),

        // ------ Warning/Error ------

        setErrorImpl: function(converter)
            function(message, condition=true)
                converter({
                    typ: _rule.typ.setError.key,
                    condition: _validate.asBoolean(condition),
                    message: _validate.asString(message),
                }),

        setWarningImpl: function(converter)
            function(message, condition=true)
                converter({
                    typ: _rule.typ.setWarning.key,
                    condition: _validate.asBoolean(condition),
                    message: _validate.asString(message),
                }),

        // ------ Pre-fill value/Pre-check options ------

        fillImpl: function(converter)
            function(value, condition=true, disabled=false, orElse="clear")
                converter({
                    typ: _rule.typ.fill.key,
                    condition: _validate.asBoolean(condition),
                    value: value,
                    disabled: _validate.asBoolean(disabled),
                    orElse: _validate.asFillOrElseArg(_validate.asString(orElse)),
                }),

        fillAllOptionsImpl: function(converter)
            function(condition=true, disabled=false, orElse="clear")
                converter({
                    typ: _rule.typ.fillAllOptions.key,
                    condition: _validate.asBoolean(condition),
                    disabled: _validate.asBoolean(disabled),
                    orElse: _validate.asFillOrElseArg(_validate.asString(orElse)),
                }),

        // ------ Activate/Deactivate Options ------

        deactivateOptionsImpl: function(converter)
            function(options, condition=true)
                converter({
                    typ: _rule.typ.deactivateOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                }),

        activateOptionsImpl: function(converter)
            function(options, condition=true)
                converter({
                    typ: _rule.typ.activateOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                }),

        addDeactivatedOptionsImpl: function(converter)
            function(options, condition=true)
                converter({
                    typ: _rule.typ.addDeactivatedOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                }),

        addActivatedOptionsImpl: function(converter)
            function(options, condition=true)
                converter({
                    typ: _rule.typ.addActivatedOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                }),

        // ------ Exclude Between Options ------

        excludeOptionsImpl: function(converter)
            function(options, condition=true, disabled=false)
                converter({
                    typ: _rule.typ.excludeOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                    disabled: _validate.asBoolean(disabled),
                }),

        excludeOptionGroupsImpl: function(converter)
            function(
                group1,
                group2,
                group3=[],
                group4=[],
                group5=[],
                group6=[],
                group7=[],
                group8=[],
                condition=true,
                disabled=false
            )
                local groups = [
                    _validate.asArray(group1),
                    _validate.asArray(group2),
                    _validate.asArray(group3),
                    _validate.asArray(group4),
                    _validate.asArray(group5),
                    _validate.asArray(group6),
                    _validate.asArray(group7),
                    _validate.asArray(group8),
                ];
                local filteredGroups = std.filter(function(options) std.length(options) > 0, groups);

                converter({
                    typ: _rule.typ.excludeOptionGroups.key,
                    condition: _validate.asBoolean(condition),
                    optionGroups: filteredGroups,
                    disabled: _validate.asBoolean(disabled),
                }),

        excludeOtherOptionsImpl: function(converter)
            function(options, condition=true, disabled=false)
                converter({
                    typ: _rule.typ.excludeOtherOptions.key,
                    condition: _validate.asBoolean(condition),
                    options: options,
                    disabled: _validate.asBoolean(disabled),
                }),

        excludeEachOptionsImpl: function(converter)
            function(condition=true, disabled=false)
                converter({
                    typ: _rule.typ.excludeEachOptions.key,
                    condition: _validate.asBoolean(condition),
                    disabled: _validate.asBoolean(disabled),
                }),

        // ------ Supporting Documents ------

        enableFilesImpl: function(converter)
            function(files, condition=true)
                converter({
                    typ: _rule.typ.enableFiles.key,
                    condition: _validate.asBoolean(condition),
                    files: _validate.asArray(files),
                }),

        enableAllFilesImpl: function(converter)
            function(condition=true)
                converter({
                    typ: _rule.typ.enableAllFiles.key,
                    condition: _validate.asBoolean(condition),
                }),

        // ------ E-sign/Wet-sign ------

        setSigningTypesImpl: function(converter)
            function(singingTypes, condition=true)
                converter({
                    typ: _rule.typ.setSigningTypes.key,
                    singingTypes: _validate.asArray(singingTypes),
                    condition: _validate.asBoolean(condition),
                }),

        // ------ Others ------

        autoCorrectImpl: function(converter)
            function(getValue, trimEnabled=true)
                converter({
                    typ: _rule.typ.autoCorrect.key,
                    getValue: _validate.asFunction(getValue),
                    trimEnabled: trimEnabled,
                }),

        unsafeSetValueImpl: function(converter)
            function(propertyName, value, condition=true)
                converter({
                    typ: _rule.typ.unsafeSetValue.key,
                    value: value,
                    propertyName: propertyName,
                    condition: _validate.asBoolean(condition),
                }),

        unsafeDeselectOptionsImpl: function(converter)
            function(options, condition=true)
                converter({
                    typ: _rule.typ.unsafeDeselectOptions.key,
                    options: options,
                    condition: _validate.asBoolean(condition),
                }),

        resetValueImpl: function(converter)
            function(condition=true)
                error "API resetValue is deprecated.",
    },

    gaiaCommand: {
        typ: {
            disable: "disable",
            hide: "hide",
            setError: "setError",
            warning: "warning",
            value: "value",
            disabledOptions: "disabledOptions",
            touched: "touched",
            imported: "imported",
            autoFilled: "autoFilled",
            signingType: "signingType",
            addItem: "addItem",
            removeItem: "removeItem",
        },

        disable: function(value)
            { typ: _gaiaCmd.typ.disable, value: value },

        hide: function(value)
            { typ: _gaiaCmd.typ.hide, value: value },

        setError: function(value)
            { typ: _gaiaCmd.typ.setError, value: value },

        warning: function(value)
            { typ: _gaiaCmd.typ.warning, value: value },

        value: function(value)
            { typ: _gaiaCmd.typ.value, value: value },

        disabledOptions: function(value)
            { typ: _gaiaCmd.typ.disabledOptions, value: value },

        touched: function(value)
            { typ: _gaiaCmd.typ.touched, value: value },

        imported: function(value)
            { typ: _gaiaCmd.typ.imported, value: value },

        autoFilled: function(value)
            { typ: _gaiaCmd.typ.autoFilled, value: value },

        signingType: function(value)
            { typ: _gaiaCmd.typ.signingType, value: value },

        addItem: function(value)
            { typ: _gaiaCmd.typ.addItem, value: value },

        removeItem: function(value)
            { typ: _gaiaCmd.typ.removeItem, value: value },
    },

    constants: {
        DefaultJsonValue: "default_json_value",
        EmptyInputValue: "empty_input_value",
        AllWidgetOptions: "all_widget_options",
        DefaultInputValue: {
            value: _const.DefaultJsonValue,
            disableByParent: false,
            hideByParent: false,
            touched: false,
            imported: false,
            autoFilled: false,
        },
    },

    utils: {
        identity: function(obj) obj,

        purge: function(arr, elements)
            std.filter(function(ele) !std.member(elements, ele), arr),

        intersect: function(arr1, arr2)
            std.filter(function(ele) std.member(arr2, ele), arr1),

        last: function(arr)

            local len = std.length(arr);

            if len == 0 then null else arr[len - 1],

        unique: function(arr)
            std.foldr(
                function(item, res)
                    if std.member(res, item) then
                        res
                    else
                        [item] + res
                , arr
                , []
            ),

        get: function(obj, parts, defaultVal=null)
            local key = parts[0];
            local rest = parts[1:];

            if parts == [] then
                obj
            else if obj != null && std.type(obj) == "object" && std.objectHas(obj, key) then
                self.get(obj[key], rest, defaultVal)
            else
                defaultVal,

        set: function(obj, parts, updateFunc, defaultVal=null)
            if parts == [] then
                updateFunc(obj)
            else
                local key = parts[0];
                local rest = parts[1:];
                local value =
                    if std.objectHas(obj, key) then
                        obj[key]
                    else
                        if rest == [] then defaultVal else {};
                local newValue =
                    if rest == [] then
                        updateFunc(value)
                    else
                        self.set(value, rest, updateFunc, defaultVal);

                obj { [key]: newValue },
        trim: function(str)
            // Non-breaking space (\u00A0) is intentionally represented here.
            // Please do not change without understanding the context [here](https://github.com/anduintransaction/gandalf/pull/30#pullrequestreview-2617218189)
            std.stripChars(str, " \t\n\f\r\u0085 "),
        getTargetObject: function(input, output)
            local namespaceKey = output.__gaia_controlled__.name;
            local key = _utils.last(std.split(namespaceKey, "."));

            {
                input: input,
                schema: _utils.get(afs, ["schema", "main", key]),
                uiSchema: _utils.get(afs, ["uiSchema", "main", key]),
                output: output,
            },

        getRulesByType: function(ruleTypes, rules)
            std.filter(function(rule) std.member(ruleTypes, rule.typ), rules),

        isWidgetType: function(target, widgetTypes)
            local widgetType = target.uiSchema["ui:widget"];

            std.member(widgetTypes, widgetType)
        ,

        getRepeatableClearEvent: function(target)
            if std.length(target.input.value) > 1 then
                target.output.clear()
            else
                target.output.clearItem(0)
        ,

        toGaiaEvent: function(target, gaiaCmd)
            if gaiaCmd == null then
                []
            else
                local
                    o = target.output,
                    v = gaiaCmd.value,
                    t = _gaiaCmd.typ;
                local is = function(typ) gaiaCmd.typ == typ;

                local cmds = if is(t.value) then
                    if v == _const.DefaultJsonValue then
                        if _utils.isWidgetType(target, [_widgetKey.Repeatable]) then
                            _utils.getRepeatableClearEvent(target)
                        else
                            o.clear()
                    else
                        o.value(v)
                else if is(t.hide) then o.hide(v)
                else if is(t.disable) then o.disable(v)
                else if is(t.disabledOptions) then o.disabledOptions(v)
                else if is(t.setError) then o.setError(v)
                else if is(t.warning) then o.warning(v)
                else if is(t.touched) then o.touched(v)
                else if is(t.imported) then o.imported(v)
                else if is(t.autoFilled) then o.autoFilled(v)
                else if is(t.signingType) then o.signingType(v)
                else [];

                local addItemCmds =
                    if is(t.addItem) && v != null then
                        std.flatMap(
                            function(_) o.addItem(), std.range(1, v)
                        )
                    else
                        [];

                local removeItemCmds =
                    if is(t.removeItem) then
                        local orderedIdxes = std.sort(v);

                        std.foldr(function(idx, acc) acc + o.removeItem(idx), orderedIdxes, [])
                    else
                        [];

                cmds + addItemCmds + removeItemCmds,

        getDefaultCommand: {
            gaiaCmd: null,
            rules: [],
            sideEffects: [],
            events: [],
        },

        isEmptyValue: function(input)
            input.value == [] || input.value == "" || input.value == null || input.value == _const.DefaultJsonValue,
    },

    validate: {
        getTypeErrorMessage: function(typ, value) "Argument must be a " + typ + ". Invalid value: " + std.toString(value),

        asArray: function(value, extraMessage="")
            if std.isArray(value) then
                value
            else
                error _validate.getTypeErrorMessage("array", value) + extraMessage,
        asBoolean: function(value)
            if std.isBoolean(value) then
                value
            else
                error _validate.getTypeErrorMessage("boolean", value),
        asFunction: function(value)
            if std.isFunction(value) then
                value
            else
                error _validate.getTypeErrorMessage("function", value),
        asNumber: function(value)
            if std.isNumber(value) then
                value
            else
                error _validate.getTypeErrorMessage("number", value),
        asObject: function(value)
            if std.isObject(value) then
                value
            else
                error _validate.getTypeErrorMessage("object", value),
        asString: function(value)
            if std.isString(value) then
                value
            else
                error _validate.getTypeErrorMessage("string", value),

        asFillOrElseArg: function(orElse)
            if !std.member(["none", "clear"], orElse) then
                error "Argument value must be `none` or `clear`. Invalid value: " + orElse
            else
                orElse,

        onRuleOptions: function(widgetOptions, options, extraFailMessage)
            local invalidOptions =
                if std.isArray(options) then
                    _utils.purge(options, widgetOptions)
                else
                    error "\nOptions must be an array. " + extraFailMessage;

            if widgetOptions == [] then
                true
            else if std.length(invalidOptions) > 0 then
                error "Invalid option: invalid option keys are given. Invalid options are " + std.toString(invalidOptions) + ". " + extraFailMessage
            else
                true,

        onRule: function(target, rule)
            local widgetType = target.uiSchema["ui:widget"];

            local widgetOptions = _calc.getAllOptions(target);

            local ruleMessage = "Rule: \n" + std.toString(rule);

            local validateValue =
                if std.member(_rule.getRulesWithValue, rule.typ) && rule.value != _const.DefaultJsonValue then
                    local isInvalid =
                        (std.member(_widget.getStringValueWidgets, widgetType) && !(std.isString(rule.value) || rule.value == null))
                        || (std.member(_widget.getNumberValueWidgets, widgetType) && !(std.isNumber(rule.value) || rule.value == null))
                        || (std.member(_widget.getArrayValueWidgets, widgetType) && !std.isArray(rule.value));

                    if isInvalid then
                        error "Invalid rule: given value type is not for widget " + widgetType + ". " + ruleMessage
                    else
                        true
                else
                    true;

            // TODO: fix this fail detection excludeOptions(["op1", "op2"], ["op3", "op4"])
            local validateOptions =
                if std.member(_rule.getRulesWithOptions, rule.typ) then
                    _validate.onRuleOptions(widgetOptions, rule.options, ruleMessage)
                else
                    true;

            local validateOptionGroups =
                if std.member(_rule.getRulesWithOptionGroups, rule.typ) then
                    local options = std.flattenArrays(rule.optionGroups);
                    local repeatedOptions = std.set(std.filter(
                        function(op) std.length(std.find(op, options)) >= 2,
                        options
                    ));

                    if _validate.onRuleOptions(widgetOptions, options, ruleMessage) && std.length(repeatedOptions) > 0 then
                        error "Options are excluding themself: " + std.join(",", repeatedOptions) + ". " + ruleMessage
                    else
                        true
                else
                    true;

            validateValue
            && validateOptions
            && validateOptionGroups
        ,
        onRules: function(target, rules)
            std.foldl(
                function(res, rule)
                    res && _validate.onRule(target, rule)
                , rules
                , true
            )
        ,

        onKeysRules: function(keyRules)
            local rules = std.flatMap(function(item) item.rules, keyRules);
            local invalidRules = _utils.getRulesByType([
                _rule.typ.fill.key,
                _rule.typ.fillAllOptions.key,
                _rule.typ.setError.key,
                _rule.typ.setWarning.key,
                _rule.typ.deactivate.key,
                _rule.typ.activate.key,
                _rule.typ.show.key,
                _rule.typ.hide.key,
            ], rules);

            if invalidRules != [] then
                error "Rule `fill`, `fillAllOptions`, `setError`, `setWarning`, `deactivate`, `activate`, `hide` and `show` are not supported on applyKey. Invalid rules: " + std.toString(invalidRules)
            else
                true
        ,

        onApplicableWidgets: function(target, rules)
            local widgetType = target.uiSchema["ui:widget"];

            std.foldl(
                function(_, rule)
                    local applicableWidgets = _rule.typ[rule.typ].applicableWidgets;
                    local message = "Rule: " + std.toString(rule);

                    if !std.member(applicableWidgets, widgetType) then
                        local errorMsg =
                            "Not support rule type " + _rule.typ[rule.typ].key + " on widget " + widgetType + ".\n" + message;

                        error errorMsg

                    else
                        true
                , rules
                , true
            ),
    },

    calculation: {
        isDirty: function(field)
            field.touched || field.imported || field.autoFilled,

        combineCondition: function(state, rule, whiteCondition, blackCondition)
            if blackCondition then
                state || rule.condition
            else if whiteCondition then
                state && !rule.condition
            else
                state
        ,

        addSideEffects: function(res, rules, event)
            local value = _utils.get(res, ["gaiaCmd", "value"]);

            res {
                sideEffects: std.set(res.sideEffects + rules, function(obj) obj.order),
                events: res.events + [{ typ: event, value: value, isSideEffect: true, rules: std.map(function(rule) rule.order, rules) }],
            }
        ,

        addRules: function(res, rules, event)
            local value = _utils.get(res, ["gaiaCmd", "value"]);

            res {
                rules: res.rules + rules,
                events: res.events + [{ typ: event, value: value, rules: std.map(function(rule) rule.order, rules) }],
            }
        ,

        getAllOptions: function(target)
            local widgetType = target.uiSchema["ui:widget"];

            if std.member(_widget.getSingleSelectionWidgets, widgetType) then
                _utils.get(target, ["schema", "enum"], [])

            else if std.member(_widget.getMultipleValueWidgets, widgetType) then
                _utils.get(target, ["schema", "items", "enum"], [])

            else
                []
        ,
        isSingleValueField: function(target)
            _utils.isWidgetType(target, _widget.getSingleValueWidgets)
        ,
        isSingleSelectionField: function(target)
            _utils.isWidgetType(target, _widget.getSingleSelectionWidgets)
        ,
        isMultipleValueField: function(target)
            _utils.isWidgetType(target, _widget.getMultipleSelectionWidgets)
        ,

        getSetValue: function(res, gaiaCmdTyp, defaultValue=_const.DefaultJsonValue)
            _utils.get(res, [gaiaCmdTyp, "gaiaCmd", "value"], defaultValue),
        getHidden: function(res)
            _calc.getSetValue(res, "hide", false),
        getError: function(res)
            _calc.getSetValue(res, "setError", ""),
        getWarning: function(res)
            _calc.getSetValue(res, "warning", ""),
        getDisabled: function(res)
            _calc.getSetValue(res, "disable", false),
        getDisabledOptions: function(res)
            _calc.getSetValue(res, "disabledOptions", []),
        getValue: function(res, defaultVal=_const.DefaultJsonValue)
            _calc.getSetValue(res, "value", defaultVal),
        getSigningType: function(res)
            _calc.getSetValue(res, "signingType", ["e-sign", "wet-sign"]),
        getSelectedOptions: function(target, res)
            local value = _calc.getValue(res, target.input.value);

            if value == _const.DefaultJsonValue then [] else value
        ,

        getGaiaDefaultResult: function(target)
            {
                commands: {
                    hide: _utils.getDefaultCommand,
                    setError: _utils.getDefaultCommand,
                    warning: _utils.getDefaultCommand,
                    value: _utils.getDefaultCommand,
                    disable: _utils.getDefaultCommand,
                    disabledOptions: _utils.getDefaultCommand,
                    touched: _utils.getDefaultCommand,
                    imported: _utils.getDefaultCommand,
                    autoFilled: _utils.getDefaultCommand,
                    addItem: _utils.getDefaultCommand,
                    removeItem: _utils.getDefaultCommand,
                    signingType: _utils.getDefaultCommand,

                    andThen:: function(cb) cb(self),
                    store:: {
                        precheckOptions: {},
                        rules: {
                            fill: [],
                        },
                    },
                },

                gaiaEvents:
                    std.flatMap(
                        function(commands)
                            _utils.toGaiaEvent(target, commands.gaiaCmd)
                        , std.objectValues(self.commands)
                    ),
            }
        ,

        getExcludedOptions: function(target, rule, options)
            local allOptions = _calc.getAllOptions(target);

            if !rule.condition || std.length(options) == 0 then
                []

            else if rule.typ == _rule.typ.excludeOptions.key then
                local ops = _utils.intersect(rule.options, options);
                local len = std.length(ops);

                if len == 0 then
                    []
                else if len == 1 then
                    _utils.purge(rule.options, [ops[0]])
                else
                    rule.options

            else if rule.typ == _rule.typ.excludeOptionGroups.key then
                local belongGroups = std.filter(
                    function(optionGroup)
                        std.length(_utils.intersect(optionGroup, options)) > 0
                    , rule.optionGroups
                );

                if std.length(belongGroups) > 0 then
                    local exclusiveGroups = _utils.purge(rule.optionGroups, belongGroups);

                    std.flattenArrays(exclusiveGroups)
                else
                    []

            else if rule.typ == _rule.typ.excludeOtherOptions.key then
                if std.length(_utils.intersect(rule.options, options)) > 0 then
                    _utils.purge(allOptions, rule.options)
                else
                    rule.options

            else if rule.typ == _rule.typ.excludeEachOptions.key then
                _utils.purge(allOptions, options)

            else
                []
        ,

        getGlobalExcludedOptions: function(target, rules)
            local excludeRules = _utils.getRulesByType([
                _rule.typ.excludeOptions.key,
                _rule.typ.excludeOptionGroups.key,
                _rule.typ.excludeOtherOptions.key,
                _rule.typ.excludeEachOptions.key,
            ], rules);

            function(options)
                std.foldl(
                    function(res, rule)
                        local ops = _calc.getExcludedOptions(target, rule, options);
                        local hasExclusiveOptions = ops != [];
                        local disabledOps = if rule.disabled then ops else [];
                        local disableRules = if rule.disabled then rules else [];

                        if ops == [] then
                            res { disableRules: res.disableRules + disableRules }
                        else
                            res {
                                options: res.options + ops,
                                disabledOptions: res.disabledOptions + disabledOps,
                                rules: res.rules + [rule],
                                disableRules: res.disableRules + disableRules,
                            }
                    , excludeRules
                    , { options: [], disabledOptions: [], rules: [], disableRules: [] }
                )
        ,

        combineHideRule: function(cmds, rule)
            local value = _calc.getSetValue(cmds, "hide", rule.typ == _rule.typ.show.key);
            local newValue = _calc.combineCondition(value, rule, rule.typ == _rule.typ.show.key, rule.typ == _rule.typ.hide.key);
            local cmd = cmds.hide { gaiaCmd: _gaiaCmd.hide(newValue) };

            cmds { hide: _calc.addRules(cmd, [rule], "combine_hide_rule") }
        ,

        combineSetErrorRule: function(cmds, rule)
            local value = _calc.getError(cmds);
            local newValue = if rule.condition then rule.message else value;
            local cmd =
                cmds.setError { gaiaCmd: _gaiaCmd.setError(newValue) };

            cmds { setError: _calc.addRules(cmd, [rule], "combine_set_error_rule") }
        ,

        combineWarningRule: function(cmds, rule)
            local value = _calc.getWarning(cmds);
            local newValue = if rule.condition then rule.message else value;
            local cmd =
                cmds.warning { gaiaCmd: _gaiaCmd.warning(newValue) };

            cmds { warning: _calc.addRules(cmd, [rule], "combine_warning_rule") }
        ,

        combineDisableRule: function(cmds, rule)
            local value = _calc.getSetValue(cmds, "disable", rule.typ == _rule.typ.activate.key);
            local newValue = _calc.combineCondition(value, rule, rule.typ == _rule.typ.activate.key, rule.typ == _rule.typ.deactivate.key);
            local cmd = cmds.disable { gaiaCmd: _gaiaCmd.disable(newValue) };

            cmds { disable: _calc.addRules(cmd, [rule], "combine_disable_rule") }
        ,

        combineSetPropertyValueRule: function(cmds, rule)
            local valueCmd =
                if rule.condition && rule.propertyName == "value" then
                    _calc.addSideEffects(
                        cmds.value { gaiaCmd: _gaiaCmd.value(rule.value) },
                        [rule],
                        "combine_unsafe_set_property_value_rule"
                    )
                else
                    cmds.value;
            local disableCmd =
                if rule.condition && rule.propertyName == "disable" then
                    _calc.addSideEffects(
                        cmds.disable { gaiaCmd: _gaiaCmd.disable(rule.value) },
                        [rule],
                        "combine_unsafe_set_property_disable_rule"
                    )
                else
                    cmds.disable;
            local touchedCmd =
                if rule.condition && rule.propertyName == "touched" then
                    _calc.addSideEffects(
                        cmds.touched { gaiaCmd: _gaiaCmd.touched(rule.value) },
                        [rule],
                        "combine_unsafe_set_property_touched_rule"
                    )
                else
                    cmds.touched;

            cmds { value: valueCmd, disable: disableCmd, touched: touchedCmd },

        combineDeselectOptionsRule: function(target, cmds, rule)
            if _calc.isMultipleValueField(target) then
                local selectedOptions = _calc.getSelectedOptions(target, cmds);
                local deselectedOptions = if rule.condition then rule.options else [];
                local value = _utils.purge(selectedOptions, deselectedOptions);

                local valueCmd =
                    if value != selectedOptions then
                        _calc.addSideEffects(
                            cmds.value { gaiaCmd: _gaiaCmd.value(value) },
                            [rule],
                            "combine_unsafe_deselect_options_rule"
                        )
                    else
                        cmds.value;

                cmds { value: valueCmd }
            else
                cmds
        ,

        combineSigningTypesRule: function(cmds, rule)
            local value = _calc.getSetValue(cmds, "signingType", null);

            local newValue =
                if rule.condition then
                    rule.singingTypes
                else if value != null then
                    value
                else
                    _main.AnduinSignatureFlow;

            local cmd = cmds.signingType { gaiaCmd: _gaiaCmd.signingType(newValue) };

            cmds { signingType: _calc.addRules(cmd, [rule], "combine_signing_types_rule") }
        ,

        combineEnableFilesRule: function(target, cmds, rule)
            local value = _calc.getValue(cmds, []);
            local files =
                if rule.typ == _rule.typ.enableFiles.key then
                    rule.files
                else if rule.typ == _rule.typ.enableAllFiles.key then
                    _calc.getAllOptions(target)
                else
                    [];

            local newValue =
                if rule.condition then
                    _utils.unique(value + files)
                else
                    local removedFiles = _utils.purge(files, value);
                    _utils.purge(value, removedFiles);

            local cmd = cmds.value { gaiaCmd: _gaiaCmd.value(newValue) };

            cmds { value: _calc.addRules(cmd, [rule], "combine_enable_files_rule") }
        ,

        combineSingleFillRule: function(target, cmds, rule)
            local rules = cmds.store.rules.fill;
            local activeRules = std.filter(function(rule) rule.condition, rules);
            local isDirty = _calc.isDirty(target.input);

            local gaiaCmd =
                if rule.condition then
                    if rule.disabled then
                        _gaiaCmd.value(rule.value)
                    else if isDirty then
                        null
                    else
                        _gaiaCmd.value(rule.value)
                else if !isDirty && rule.orElse == "clear" && activeRules == [] then
                    _gaiaCmd.value(_const.DefaultJsonValue)
                else
                    cmds.value.gaiaCmd;

            local cmd = cmds.value { gaiaCmd: gaiaCmd };

            cmds {
                value: _calc.addRules(cmd, [rule], "combine_single_fill_rule"),
                store:: _utils.set(cmds.store, ["rules", "fill"], function(rules) rules + [rule]),
            }
        ,

        combineRepeatableFillRule: function(target, cmds, rule)
            local rules = cmds.store.rules.fill;
            local activeRules = std.filter(function(rule) rule.condition, rules);
            local currentLength = std.length(target.input.value);
            local isDirty = _calc.isDirty(target.input);

            local syncedValue = {
                local sourceLength = std.length(rule.value),
                local targetMaxLength = target.uiSchema["ui:maxLength"],
                addItem:
                    local newLength = std.min(sourceLength, targetMaxLength);
                    if newLength > currentLength then
                        _gaiaCmd.addItem(newLength - currentLength)
                    else
                        null,
                removeItem:
                    if currentLength > sourceLength then
                        _gaiaCmd.removeItem(std.range(sourceLength, currentLength - 1))
                    else
                        null,
            };

            local gaiaCmd =
                if rule.condition then
                    if rule.disabled then
                        syncedValue
                    else if isDirty then
                        {
                            addItem: null,
                            removeItem: null,
                        }
                    else
                        syncedValue
                else if !isDirty && rule.orElse == "clear" && activeRules == [] then
                    {
                        addItem: null,
                        removeItem: _gaiaCmd.removeItem(std.range(1, currentLength - 1)),
                    }
                else
                    {
                        addItem: cmds.addItem.gaiaCmd,
                        removeItem: cmds.removeItem.gaiaCmd,
                    };

            local addItemCmd = cmds.addItem { gaiaCmd: gaiaCmd.addItem };
            local removeItemCmd = cmds.removeItem { gaiaCmd: gaiaCmd.removeItem };

            cmds {
                addItem: _calc.addRules(addItemCmd, [rule], "combine_repeatable_fill_rule"),
                removeItem: _calc.addRules(removeItemCmd, [rule], "combine_repeatable_fill_rule"),
                store:: _utils.set(cmds.store, ["rules", "fill"], function(rules) rules + [rule]),
            }
        ,

        combineMultipleValueFillRule: function(target, cmds, rule)
            local isDirty = _calc.isDirty(target.input);
            local value = _calc.getValue(cmds, if isDirty then target.input.value else []);
            local options =
                if rule.typ == _rule.typ.fillAllOptions.key || rule.value == _const.AllWidgetOptions then
                    _calc.getAllOptions(target)
                else
                    rule.value;

            local gaiaCmd =
                if rule.condition then
                    if rule.disabled || !isDirty then
                        _gaiaCmd.value(_utils.unique(value + options))
                    else
                        cmds.value.gaiaCmd
                else if !isDirty && rule.orElse == "clear" then
                    local removedOptions = _utils.purge(options, value);
                    _gaiaCmd.value(_utils.purge(value, removedOptions))
                else
                    cmds.value.gaiaCmd;

            local cmd = cmds.value { gaiaCmd: gaiaCmd };

            local precheckOptions = std.foldl(
                function(acc, op)
                    acc { [op]: rule }
                , if rule.condition then options else []
                , cmds.store.precheckOptions
            );

            cmds {
                value: _calc.addRules(cmd, [rule], "combine_multiple_value_fill_rule"),
                store:: { precheckOptions: precheckOptions },
            }
        ,

        combineDisabledOptionsRule: function(target, cmds, rule)
            local value = _calc.getDisabledOptions(cmds);
            local newValue =
                if !rule.condition then
                    value
                else if rule.typ == _rule.typ.deactivateOptions.key then
                    rule.options

                else if rule.typ == _rule.typ.activateOptions.key then
                    local allOptions = _calc.getAllOptions(target);

                    _utils.purge(allOptions, rule.options)

                else
                    value;

            local cmd = cmds.disabledOptions { gaiaCmd: _gaiaCmd.disabledOptions(_utils.unique(newValue)) };

            cmds { disabledOptions: _calc.addRules(cmd, [rule], "combine_disabled_options_rule") }
        ,

        combineAddDisabledOptionsRule: function(cmds, rule)
            local value = _calc.getDisabledOptions(cmds);

            local newValue =
                if rule.typ == _rule.typ.addDeactivatedOptions.key then
                    if rule.condition then
                        value + rule.options
                    else
                        value
                else if rule.typ == _rule.typ.addActivatedOptions.key then
                    if rule.condition then
                        _utils.purge(value, rule.options)
                    else
                        value
                else
                    value;

            local cmd = cmds.disabledOptions { gaiaCmd: _gaiaCmd.disabledOptions(_utils.unique(newValue)) };

            cmds { disabledOptions: _calc.addRules(cmd, [rule], "combine_append_disabled_options_rule") }
        ,

        combineAutoCorrectRule: function(target, cmds, rule)
            local value = _calc.getValue(cmds, target.input.value);
            local correctValue = if value != _const.DefaultJsonValue then rule.getValue(value) else value;
            local newValue = if rule.trimEnabled then _utils.trim(correctValue) else correctValue;

            local cmd =
                if value != newValue then
                    _calc.addRules(
                        cmds.value { gaiaCmd: _gaiaCmd.value(newValue) },
                        [rule],
                        "combine_auto_correct_rule",
                    )
                else
                    cmds.value;

            cmds { value: cmd }
        ,

        combineSideEffectK: function(target) function(cmds)
            local rules = cmds.value.rules;
            local allOptions = _calc.getAllOptions(target);
            local value = _calc.getValue(cmds);

            local cmd =
                if rules == [] || allOptions == [] || value == _const.DefaultJsonValue then
                    cmds.value
                else if _calc.isSingleSelectionField(target) then
                    if !std.member(allOptions, value) then
                        _calc.addSideEffects(
                            cmds.value { gaiaCmd: null }
                            , rules
                            , "ignore_invalid_fill_options"
                        )
                    else
                        cmds.value
                else if _calc.isMultipleValueField(target) then
                    local validValue = _utils.intersect(value, allOptions);
                    if validValue != value then
                        _calc.addSideEffects(
                            cmds.value { gaiaCmd: _gaiaCmd.value(validValue) }
                            , rules
                            , "ignore_invalid_fill_options"
                        )
                    else
                        cmds.value
                else
                    cmds.value;

            cmds { value: cmd }
        ,

        combineSideEffectH: function(target, queryExcludedOptions) function(cmds)
            if _calc.isMultipleValueField(target) then
                local value = _calc.getSelectedOptions(target, cmds);
                local excludingRes = queryExcludedOptions(value);
                local disabledOptions = _calc.getDisabledOptions(cmds);
                local newValue = _utils.unique(disabledOptions + excludingRes.disabledOptions);

                local cmd =
                    if excludingRes.disableRules != [] then
                        _calc.addSideEffects(
                            cmds.disabledOptions { gaiaCmd: _gaiaCmd.disabledOptions(newValue) }
                            , excludingRes.disableRules
                            , "disable_options_if_select_exclusive_options"
                        )
                    else
                        cmds.disabledOptions;

                cmds { disabledOptions: cmd }
            else
                cmds
        ,

        combineSideEffectF2: function(target, queryExcludedOptions, rules) function(cmds)
            if _calc.isMultipleValueField(target) then
                local precheckOptions = cmds.store.precheckOptions;

                local disabledSelectedOptions = std.filter(
                    function(op) precheckOptions[op].disabled
                    , std.objectFields(precheckOptions)
                );
                local disabledRules = std.filter(
                    function(rule) rule.disabled
                    , std.objectValues(precheckOptions)
                );

                local excludingRes = queryExcludedOptions(disabledSelectedOptions);
                local disabledOptions = _calc.getDisabledOptions(cmds);
                local newValue = _utils.unique(disabledOptions + disabledSelectedOptions + excludingRes.options);

                local cmd =
                    if _utils.purge(newValue, disabledOptions) != [] then
                        _calc.addSideEffects(
                            cmds.disabledOptions { gaiaCmd: _gaiaCmd.disabledOptions(newValue) }
                            , disabledRules + excludingRes.rules
                            , "disable_options_by_fill_exclusive_options"
                        )
                    else
                        cmds.disabledOptions;

                cmds { disabledOptions: cmd }
            else
                cmds
        ,

        combineSideEffectF1: function(target) function(cmds)
            local rules = cmds.store.rules.fill;

            if _calc.isSingleValueField(target) && rules != [] then
                local activeRules = std.filter(function(rule) rule.condition, rules);
                local activeRuleOpt = _utils.last(activeRules);
                local isDisabled = _calc.getDisabled(cmds);
                local newValue = (activeRuleOpt != null && activeRuleOpt.disabled) || isDisabled;

                local cmd =
                    _calc.addSideEffects(
                        cmds.disable { gaiaCmd: _gaiaCmd.disable(newValue) }
                        , rules
                        , "disable_by_fill_rule"
                    );

                cmds { disable: cmd }
            else
                cmds
        ,

        combineSideEffectE: function(target, queryExcludedOptions) function(cmds)
            if _calc.isMultipleValueField(target) then
                local selectedOptions = _calc.getSelectedOptions(target, cmds);

                local excludingRes = std.foldr(
                    function(option, res)
                        if std.member(res.options, option) then
                            res
                        else
                            local excludingRes = queryExcludedOptions([option]);
                            local options = res.options + excludingRes.options;
                            local rules = if _utils.intersect(selectedOptions, excludingRes.options) != [] then excludingRes.rules else [];

                            { options: options, rules: res.rules + rules }
                    , selectedOptions
                    , { options: [], rules: [] }
                );
                local value = _utils.purge(selectedOptions, excludingRes.options);

                local cmd =
                    if value != selectedOptions then
                        _calc.addSideEffects(
                            cmds.value { gaiaCmd: _gaiaCmd.value(value) }
                            , excludingRes.rules
                            , "exclusive_rules"
                        )
                    else
                        cmds.value;

                cmds { value: cmd }
            else
                cmds
        ,

        combineSideEffectD: function(target) function(cmds)
            local rules = cmds.disabledOptions.rules;
            local allOptions = _calc.getAllOptions(target);

            if (_calc.isSingleSelectionField(target) || _calc.isMultipleValueField(target)) && rules != [] && allOptions != [] then
                local disabledOptions = _calc.getDisabledOptions(cmds);

                local isDisabled = _calc.getDisabled(cmds);
                local isAllOptionsDisabled = std.length(_utils.purge(allOptions, disabledOptions)) == 0;
                local newValue = isAllOptionsDisabled || isDisabled;

                local cmd =
                    _calc.addSideEffects(
                        cmds.disable { gaiaCmd: _gaiaCmd.disable(newValue) }
                        , rules
                        , "disable_if_all_options_disabled"
                    );

                cmds { disable: cmd }
            else
                cmds
        ,

        combineSideEffectC: function(target) function(cmds)
            local disabledOptions = _calc.getDisabledOptions(cmds);
            local rules = cmds.disabledOptions.rules;

            local cmd =
                if disabledOptions == [] then
                    cmds.value
                else if _calc.isSingleValueField(target) && std.member(disabledOptions, _calc.getValue(cmds, target.input.value)) then
                    _calc.addSideEffects(
                        cmds.value { gaiaCmd: _gaiaCmd.value(_const.DefaultJsonValue) }
                        , rules
                        , "clear_if_selecting_disabled_option"
                    )
                else if _calc.isMultipleValueField(target) then
                    local selectedOptions = _calc.getSelectedOptions(target, cmds);
                    local value = _utils.purge(selectedOptions, disabledOptions);

                    if value != selectedOptions then
                        _calc.addSideEffects(
                            cmds.value { gaiaCmd: _gaiaCmd.value(value) }
                            , rules
                            , "clear_if_selecting_disabled_option"
                        )
                    else
                        cmds.value
                else
                    cmds.value;

            cmds { value: cmd }
        ,

        combineSideEffectB: function(target) function(cmds)
            local isDisabled = _calc.getDisabled(cmds);

            if isDisabled then
                local valueCmd = _calc.addSideEffects(
                    cmds.value { gaiaCmd: _gaiaCmd.value(_const.DefaultJsonValue) }
                    , cmds.disable.rules
                    , "clear_if_disable"
                );

                cmds { value: valueCmd }
            else
                cmds
        ,

        combineSideEffectA: function(target) function(cmds)
            local isDisabledByParent = target.input.disableByParent;

            if isDisabledByParent then
                local valueCmd = _calc.addSideEffects(
                    cmds.value { gaiaCmd: _gaiaCmd.value(_const.DefaultJsonValue) },
                    [],
                    "clear_if_disable_by_parent",
                );

                cmds { value: valueCmd }
            else
                cmds
        ,

        toGaiaCommand: function(target, initRules)
            local rules = std.mapWithIndex(function(index, rule) rule { order: index }, initRules);

            local res = _calc.getGaiaDefaultResult(target) { ctx: target.input, uiSchema: target.uiSchema, schema: target.schema };
            local queryExcludedOptions = _calc.getGlobalExcludedOptions(target, rules);

            local commands =
                res.commands
                .andThen(
                    function(cmds)
                        std.foldl(
                            function(acc, rule)
                                if rule.typ == _rule.typ.hide.key || rule.typ == _rule.typ.show.key then
                                    _calc.combineHideRule(acc, rule)

                                else
                                    acc
                            , rules
                            , cmds
                        )
                )
                .andThen(
                    function(cmds)
                        local isHidden = _calc.getHidden(cmds) || target.input.hideByParent;

                        if isHidden then
                            cmds
                        else
                            cmds
                            .andThen(
                                function(cmds)
                                    std.foldl(
                                        function(acc, rule)
                                            if rule.typ == _rule.typ.deactivate.key || rule.typ == _rule.typ.activate.key then
                                                _calc.combineDisableRule(acc, rule)

                                            else if rule.typ == _rule.typ.deactivateOptions.key || rule.typ == _rule.typ.activateOptions.key then
                                                _calc.combineDisabledOptionsRule(target, acc, rule)

                                            else if rule.typ == _rule.typ.setWarning.key then
                                                _calc.combineWarningRule(acc, rule)

                                            else if rule.typ == _rule.typ.setError.key then
                                                _calc.combineSetErrorRule(acc, rule)

                                            else if rule.typ == _rule.typ.enableFiles.key || rule.typ == _rule.typ.enableAllFiles.key then
                                                _calc.combineEnableFilesRule(target, acc, rule)

                                            else if rule.typ == _rule.typ.setSigningTypes.key then
                                                _calc.combineSigningTypesRule(acc, rule)

                                            else
                                                acc
                                        , rules
                                        , cmds
                                    )
                            )
                            .andThen(
                                function(cmds)
                                    std.foldl(
                                        function(acc, rule)
                                            if rule.typ == _rule.typ.addDeactivatedOptions.key
                                               || rule.typ == _rule.typ.addActivatedOptions.key then
                                                _calc.combineAddDisabledOptionsRule(acc, rule)

                                            else
                                                acc
                                        , rules
                                        , cmds
                                    )
                            )
                            .andThen(
                                function(cmds)
                                    local isDisabled = _calc.getDisabled(cmds) || target.input.disableByParent;

                                    std.foldl(
                                        function(acc, rule)
                                            if isDisabled then
                                                acc
                                            else if rule.typ == _rule.typ.fill.key || rule.typ == _rule.typ.fillAllOptions.key then
                                                if _calc.isSingleValueField(target) then
                                                    _calc.combineSingleFillRule(target, acc, rule)

                                                else if _calc.isMultipleValueField(target) then
                                                    _calc.combineMultipleValueFillRule(target, acc, rule)

                                                else if _utils.isWidgetType(target, [_widgetKey.Repeatable]) then
                                                    _calc.combineRepeatableFillRule(target, acc, rule)

                                                else
                                                    acc
                                            else
                                                acc
                                        , rules
                                        , cmds
                                    )
                            )
                            // List of side effects are resolved in order:
                            // A: if field is disabled by parent, [clear] value
                            // B: if field is disabled by rule, [clear] value
                            // C: if selected option is disabled, [clear] the options
                            // D: if all options are disabled, [disable]
                            // E: [multiple value] apply exclude logic on [value]
                            // F1: [single value] if fill and disable, [disable]
                            // F2: [multiple value] if fill and disable, [disabledOptions] which are exclusive with pre-checked options
                            // H: [multiple value] [disabledOptions] when its exclusive options are checked
                            // I: [single value - textbox], [value] will be auto-corrected
                            // K: [option widgets] [value] ignored invalid pre-filled options
                            .andThen(_calc.combineSideEffectA(target))
                            .andThen(_calc.combineSideEffectB(target))
                            .andThen(_calc.combineSideEffectC(target))
                            .andThen(_calc.combineSideEffectD(target))
                            .andThen(_calc.combineSideEffectE(target, queryExcludedOptions))
                            .andThen(_calc.combineSideEffectF1(target))
                            .andThen(_calc.combineSideEffectF2(target, queryExcludedOptions, rules))
                            .andThen(_calc.combineSideEffectH(target, queryExcludedOptions))
                            .andThen(_calc.combineSideEffectK(target))
                            .andThen(
                                // Side Effect I
                                function(cmds)
                                    std.foldl(
                                        function(acc, rule)
                                            if rule.typ == _rule.typ.autoCorrect.key then
                                                _calc.combineAutoCorrectRule(target, acc, rule)
                                            else
                                                acc
                                        , rules
                                        , cmds
                                    )
                            )
                            .andThen(
                                function(cmds)
                                    std.foldl(
                                        function(acc, rule)
                                            if rule.typ == _rule.typ.unsafeSetValue.key then
                                                _calc.combineSetPropertyValueRule(acc, rule)
                                            else if rule.typ == _rule.typ.unsafeDeselectOptions.key then
                                                _calc.combineDeselectOptionsRule(target, acc, rule)
                                            else
                                                acc
                                        , rules
                                        , cmds
                                    )
                            )

                );

            res { commands: commands }
        ,

        getSyncKeyRules: function(fakeTarget, fakeTargetCmds, rawRules)
            local allKeys = std.objectFields(fakeTarget.targets);

            local isParentDeactivated = _calc.getDisabled(fakeTargetCmds);
            local isParentHidden = _calc.getHidden(fakeTargetCmds);

            local encodedDisabledOptions = _calc.getDisabledOptions(fakeTargetCmds);
            local encodedSelectedOptions = _calc.getSelectedOptions(fakeTarget, fakeTargetCmds);
            local encodedDeactivatedOptions = _utils.purge(encodedDisabledOptions, encodedSelectedOptions);
            local encodedDisabledSelectedOptions = _utils.intersect(encodedSelectedOptions, encodedDisabledOptions);
            local encodedEnabledSelectedOptions = _utils.purge(encodedSelectedOptions, encodedDisabledOptions);
            local encodedDeselectedOptions = _utils.purge(fakeTarget.input.value, encodedSelectedOptions);

            local keyFillRules = std.foldl(
                function(acc, rawRule)
                    local keyValues = _fakeCheckboxUtils.resolveKeyValues(rawRule.value);

                    std.foldl(
                        function(acc, key)
                            local keyRules = _utils.get(acc, [key], []);
                            local rule = rawRule { value: keyValues[key] };

                            acc { [key]: keyRules + [rule] }
                        , std.objectFields(keyValues)
                        , acc
                    )
                , _utils.getRulesByType([_rule.typ.fill.key], rawRules)
                , {}
            );

            std.foldl(
                function(res, key)
                    local target = fakeTarget.targets[key];
                    local isOptionType = _calc.isMultipleValueField(target);
                    local getFieldOptions = _fakeCheckboxUtils.getFieldOptions(key);
                    local includeField = _fakeCheckboxUtils.includeField(key);

                    local parentRules = [
                        _rule.deactivateImpl(_utils.identity)(isParentDeactivated),
                        _rule.hideImpl(_utils.identity)(isParentHidden),
                        _rule.unsafeSetValueImpl(_utils.identity)("touched", false),
                    ];

                    local rules =
                        if isOptionType then
                            local disabledSelectedOptions = getFieldOptions(encodedDisabledSelectedOptions);
                            local enabledSelectedOptions = getFieldOptions(encodedEnabledSelectedOptions);
                            local deactivatedOptions = getFieldOptions(encodedDeactivatedOptions);
                            local deselectedOptions = getFieldOptions(encodedDeselectedOptions);

                            [
                                _rule.fillImpl(_utils.identity)(disabledSelectedOptions, disabled=true),
                                _rule.fillImpl(_utils.identity)(enabledSelectedOptions),
                                _rule.addDeactivatedOptionsImpl(_utils.identity)(deactivatedOptions),
                                _rule.unsafeDeselectOptionsImpl(_utils.identity)(deselectedOptions),
                            ]
                        else
                            local isDeactivated = includeField(encodedDeactivatedOptions);
                            local isSelected = includeField(encodedSelectedOptions);
                            local isDeselected = includeField(encodedDeselectedOptions);
                            local isDisabledFill = includeField(encodedDisabledSelectedOptions);

                            local fillCmds = std.map(
                                function(rule)
                                    rule { condition: rule.condition && isSelected, disabled: rule.disabled && isDisabledFill }
                                , _utils.get(keyFillRules, [key], [])
                            );

                            fillCmds
                            + [
                                _rule.deactivateImpl(_utils.identity)(isDeactivated),
                                _rule.unsafeSetValueImpl(_utils.identity)("value", _const.DefaultJsonValue, isDeselected),
                            ];

                    res { [key]: parentRules + rules }
                , allKeys
                , {}
            ),

        combineFakeCheckboxRules: function(fakeTarget, rawRules, keyRules, debug)
            local syncTouchedRule = _rule.unsafeSetValueImpl(_utils.identity)("touched", fakeTarget.input.touched);
            local rules = _fakeCheckboxUtils.getEncodedRules(fakeTarget, rawRules) + [syncTouchedRule];

            local isValidArguments =
                _validate.onRules(fakeTarget, rules)
                && _validate.onApplicableWidgets(fakeTarget, rules)
                && _validate.onKeysRules(keyRules);

            local fakeTargetRes =
                if isValidArguments then _calc.toGaiaCommand(fakeTarget, rules) else null;
            local fakeTargetCmds = fakeTargetRes.commands;
            local allKeys = std.objectFields(fakeTarget.targets);

            local syncRules = _calc.getSyncKeyRules(fakeTarget, fakeTargetCmds, rawRules);

            local allKeyRules = std.foldl(
                function(res, sync)
                    res { [sync.key]: sync.rules + res[sync.key] }
                , keyRules
                , syncRules
            );

            local keyTargetsRes = std.foldl(
                function(res, key)
                    local rules = allKeyRules[key];
                    local target = fakeTarget.targets[key];

                    res { [key]: _calc.init(target.input { touched: fakeTarget.input.touched }, target.output, rules) }
                , allKeys
                , {}
            );

            if debug then
                { fakeTarget: fakeTargetRes, targets: keyTargetsRes }
            else
                std.flatMap(function(key) keyTargetsRes[key].gaiaEvents, allKeys)
                + fakeTargetRes.gaiaEvents,

        init: function(ctx, output, rules)
            local targetObj = _utils.getTargetObject(ctx, output);
            local isValidArguments =
                _validate.onRules(targetObj, rules)
                && _validate.onApplicableWidgets(targetObj, rules);

            if !isValidArguments then
                {}

            else
                _calc.toGaiaCommand(targetObj, rules),
    },

    fakeCheckbox: {
        local ref = self,

        targetObj: {
            input: null,
            output: null,
            targets: {},
        },

        utils: {
            resolveKeyValues: function(ruleValue)
                local errorMessage = "Given value: " + std.toString(ruleValue);

                if std.isObject(ruleValue) then
                    ruleValue
                else if std.isArray(ruleValue) then
                    std.foldl(
                        function(acc, key)
                            acc { [key]: _const.AllWidgetOptions }
                        , ruleValue
                        , {}
                    )
                else
                    error "Invalid value. Given value type is not supported for FakeCheckbox. " + errorMessage,

            getEncodedRules: function(fakeCheckbox, rules)
                local encodeValue = function(rawValue, isValueFormat=false)
                    local keyValues = _fakeCheckboxUtils.resolveKeyValues(rawValue);

                    std.map(
                        function(key)
                            _fakeCheckboxUtils.encodeTargetOptions(fakeCheckbox)(key, function(_) keyValues[key], isValueFormat)
                        , std.objectFields(keyValues)
                    );

                std.map(
                    function(rule)
                        if std.member(_rule.getRulesWithValue, rule.typ) then
                            rule { value: std.flattenArrays(encodeValue(rule.value, isValueFormat=true)) }
                        else if std.member(_rule.getRulesWithOptions, rule.typ) then
                            if _rule.typ.excludeOptions.key == rule.typ then
                                rule {
                                    optionGroups: encodeValue(rule.options),
                                    typ: _rule.typ.excludeOptionGroups.key,
                                }
                            else
                                rule { options: std.flattenArrays(encodeValue(rule.options)) }

                        else if std.member(_rule.getRulesWithOptionGroups, rule.typ) then
                            rule { optionGroups: std.map(function(options) std.flattenArrays(encodeValue(options)), rule.optionGroups) }
                        else
                            rule
                    , rules
                ),

            getFieldOptions: function(key) function(encodedOptions)
                local decodedOptions = std.map(_fakeCheckboxUtils.decodeOption, encodedOptions);

                local keyOptions = std.filter(
                    function(decodedOption)
                        decodedOption.fakeCheckboxKey == key && decodedOption.isOption
                    , decodedOptions
                );

                std.map(function(decodedOption) decodedOption.optionId, keyOptions),

            includeField: function(key) function(encodedOptions)
                local decodedOptions = std.map(_fakeCheckboxUtils.decodeOption, encodedOptions);

                std.filter(
                    function(decodedOption)
                        decodedOption.fakeCheckboxKey == key && !decodedOption.isOption
                    , decodedOptions
                ) != [],

            encodeTargetOptions: function(fakeCheckbox) function(targetKey, getValue, isValueFormat=false)
                local target = fakeCheckbox.targets[targetKey];
                local rawValue = getValue(target);
                local value = if rawValue == _const.AllWidgetOptions then _calc.getAllOptions(target) else rawValue;
                local isOptionType = _calc.isMultipleValueField(target);
                local isEmpty = _utils.isEmptyValue({ value: value });

                if isOptionType && !std.isArray(value) then
                    error targetKey + "'s value must array because of its widget type. Given: " + std.toString(value)
                else if _calc.isMultipleValueField(target) then
                    if isEmpty then
                        []
                    else
                        std.map(
                            function(option)
                                _fakeCheckboxUtils.encodeOption(targetKey, option)
                            , value
                        )

                else
                    if isEmpty && isValueFormat then
                        []
                    else
                        [_fakeCheckboxUtils.encodeOption(targetKey, null)],

            encodeOption: function(fakeCheckboxKey, optionId=null)
                local tpe = if optionId != null then "option" else "field";
                local optionPart = if optionId != null then [optionId] else [];

                std.join("&", [fakeCheckboxKey, tpe] + optionPart),

            decodeOption: function(encodedString)
                local parts = std.splitLimit(encodedString, "&", 2);

                {
                    fakeCheckboxKey: parts[0],
                    isOption: parts[1] == "option",
                    optionId: if std.length(parts) > 2 then parts[2] else null,
                },
        },

        addKey:: function(key, input, output)
            local targetObj = ref.targetObj {
                targets: ref.targetObj.targets { [key]: _utils.getTargetObject(input, output) },
            };

            ref { targetObj: targetObj }
        ,

        init:: function(fakeOutput, fakeInput)
            local keys = std.objectFields(ref.targetObj.targets);
            local encodeTargetOptions = _fakeCheckboxUtils.encodeTargetOptions(ref.targetObj);

            local allOptions = std.flatMap(
                function(key)
                    encodeTargetOptions(key, _calc.getAllOptions)
                , keys
            );

            local touchedKey = _utils.last(std.filter(
                function(key)
                    ref.targetObj.targets[key].input.touched
                , keys
            ));

            local value = std.foldl(
                function(res, key)
                    local selectedOptions =
                        encodeTargetOptions(key, function(target) target.input.value, isValueFormat=true);

                    if key == touchedKey then
                        res + selectedOptions
                    else
                        selectedOptions + res
                , keys
                , []
            );

            local isTouched = std.foldl(
                function(res, key)
                    res || ref.targetObj.targets[key].input.touched
                , keys
                , false
            );

            local isImported = std.foldl(
                function(res, key)
                    res || ref.targetObj.targets[key].input.imported
                , keys
                , false
            );
            local isAutoFilled = std.foldl(
                function(res, key)
                    res || ref.targetObj.targets[key].input.autoFilled
                , keys
                , false
            );

            ref.targetObj {
                input: _const.DefaultInputValue {
                    value: value,
                    touched: isTouched || fakeInput.touched,
                    imported: isImported,
                    autoFilled: isAutoFilled,
                    disable: fakeInput.disable,
                    hide: fakeInput.hide,
                    "error": fakeInput["error"],
                    warning: fakeInput.warning,
                    disabledOptions: null,
                },
                schema: { items: { enum: allOptions } },
                uiSchema: { "ui:widget": _widgetKey.FakeMultipleCheckbox },
                output: fakeOutput,
                fakeInput: fakeInput,
            },
    },
}
