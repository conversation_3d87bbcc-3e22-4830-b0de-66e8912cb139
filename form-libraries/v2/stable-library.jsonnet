{
  getState: function(results, prefillState)
    local init = {
      disabledOptions: [],
      clear: [],
      prefill: [],
      exclusives: [],
      disableAll: false,
      prefillState: prefillState,
    };
    local _ = self;

    local combine = function(value, obj)
      local prefilled = if std.objectHas(prefillState, obj.key) && prefillState[obj.key] == "prefilled" then true else false;
      local cleared = if std.objectHas(prefillState, obj.key) && prefillState[obj.key] == "cleared" then true else false;
      local condition = obj.condition; // prefill condition

      local prefillValue = if !prefilled && condition then value.prefill + obj.prefill else value.prefill;

      // handle clear value for prefill method
      local clearValue = if prefilled && !condition then value.clear + obj.prefill else value.clear;

      if obj.type == 'disable_and_clear'
      then
        {
          disabledOptions: value.disabledOptions + obj.disabledOptions,
          clear: value.clear + obj.clear,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll,
          prefillState: value.prefillState
        }
      else if obj.type == 'disable_and_prefill'
      then
        {
          disabledOptions: value.disabledOptions + obj.disabledOptions,
          clear: clearValue,
          prefill: prefillValue,
          exclusives: value.exclusives,
          disableAll: value.disableAll,
          prefillState: if !prefilled && condition && obj.key != "" then std.mergePatch(value.prefillState, {[obj.key]: 'prefilled'}) else if prefilled && !condition && obj.key != "" then std.mergePatch(value.prefillState, {[obj.key]: 'cleared'}) else value.prefillState
        }
      else if obj.type == 'prefill_only'
      then 
        {
          disabledOptions: value.disabledOptions,
          clear: clearValue,
          prefill: prefillValue,
          exclusives: value.exclusives,
          disableAll: value.disableAll,
          prefillState: if !prefilled && condition && obj.key != "" then std.mergePatch(value.prefillState, {[obj.key]: 'prefilled'}) else if prefilled && !condition && obj.key != "" then std.mergePatch(value.prefillState, {[obj.key]: 'cleared'}) else value.prefillState
        }
      else if obj.type == 'clear_prefill'
      then
        {
          disabledOptions: value.disabledOptions,
          clear: clearValue,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll,
          prefillState: if clearValue != value.clear && obj.key != "" then std.mergePatch(value.prefillState, {[obj.key]: 'cleared'}) else value.prefillState
        }
      else if obj.type == 'exclusive'
        then {
          disabledOptions: value.disabledOptions,
          clear: value.clear,
          prefill: value.prefill,
          exclusives: value.exclusives + [obj.exclusive],
          disableAll: value.disableAll,
          prefillState: value.prefillState
        }
      else if obj.type == 'disable_all'
        then {
          disabledOptions: value.disabledOptions,
          clear: value.clear,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: obj.disableAll,
          prefillState: value.prefillState
        }
      else value;

    std.foldl(combine, results, init),
  purge: function(arr, elements) std.filter(function(ele) !std.member(elements, ele), arr),
  containsAny: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,
  endsWith: function(arr, ele) if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele,
  endsWithOneOf: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) arr[std.length(arr) - 1] == item, elements)) > 0,
  intersect: function(arr1, arr2)
    std.filter(function(op) std.member(arr1, op), arr2),
  strContains: function(string, substr)
    std.length(std.findSubstr(substr, string)) > 0,
  fullyFlattenArrays: function(arr)
    local f = function(result, elm)
      if std.isArray(elm) then
        if std.length(elm) > 0 then
          result + std.foldl(f, elm, [])
        else
          result
      else
        result + [elm];
    std.foldl(f, arr, []),
  setError: function(target, message) target.setError(message),
  setWarning: function(target, message) target.warning(message),
  setSigningType: function(document, type) document.signingType(type),
  setESignOnly: function(document) 
    local _ = self;
    _.setSigningType(document, ['e-sign']),
  setWetSignOnly: function(document) 
    local _ = self;
    _.setSigningType(document, ['wet-sign']),
  setESignAndWetSign: function(document) 
    local _ = self;
    _.setSigningType(document, ['e-sign', 'wet-sign']),
  setQESAndWetSign: function(document) 
    local _ = self;
    _.setSigningType(document, ['qes', 'wet-sign']),
  showIf: function(target, condition)
    local _ = self;
    _.hideIf(target, !condition),
  hideIf: function(target, condition) 
    local _ = self;
    local hideIfCond = function(t)
        _.hideIf(t, condition);

    if std.isArray(target) then
      std.flattenArrays(std.map(hideIfCond, target))
    else
      target.hide(condition),
  clearIf: function(target, condition)
    local _ = self;
    local clearIfCond = function(t)
        _.clearIf(t, condition);

    if std.isArray(target) then
      std.flattenArrays(std.map(clearIfCond, target))
    else
      if condition then target.clear() else [],
  enableIf: function(target, condition, childLogicArr) 
    local _ = self;
    local clearChildren = if !condition then std.mapWithIndex(function(index, item) item.clear(), childLogicArr) else [];
    std.flattenArrays(clearChildren) + target.disable(!condition),
  disableIf: function(target, condition, childLogicArr) 
    local _ = self;
    local clearChildren = if condition then std.mapWithIndex(function(index, item) item.clear(), childLogicArr) else [];
    std.flattenArrays(clearChildren) + target.disable(condition),
  fill: function(target, value)
    local _ = self;
    local fillWithValue = function(t)
        _.fill(t, value);

    if std.isArray(target) then
      std.flattenArrays(std.map(fillWithValue, target))
    else
      target.value(value),
  fillAndDisableIf: function(target, value, condition, targetsToClear=[target])
    local _ = self;
    (if condition then _.fill(target, value) else [])
    + _.disableIf(target, condition, [])
    + _.clearIf(targetsToClear, !condition),
  setDisabledOptions: function(target, options) target.disabledOptions(options),
  getOrElse: function(value, default) if value == null then default else value,
  safeInt: function(s)
    local numStr = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(s)));
    if numStr == "" then 0 else std.parseInt(numStr),
  getDaysInMonth: function(mm, yy)
    local m = self.safeInt(mm);
    local y = self.safeInt(yy);
    local MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if !(m > 0 && m <= 12) then
      0
    else if m != 2 then
      MONTH_DAYS[m - 1]
    else
      local isLeap = ((y % 4 == 0) && (y % 100 != 0)) || (y % 400 == 0);
      if isLeap then 29 else 28,
  a_checkboxDisableAndClear: function(disabledOptions, condition)
    {
      type: 'disable_and_clear',
      disabledOptions: if condition then disabledOptions else [],
      clear: if condition then disabledOptions else [],
    },
  a_checkboxDisableAndPrefill: function(disabledOptions, prefillOptions, clearOptions, condition, key="")
    {
      type: 'disable_and_prefill',
      disabledOptions: if condition then disabledOptions else [],
      prefill: prefillOptions,
      clear: if condition then clearOptions else [],
      key: key,
      condition: condition
    },
  a_checkboxPrefillWithoutDisable: function(prefillOptions, condition, key="")
    {
      type: 'prefill_only',
      prefill: prefillOptions,
      key: key,
      condition: condition
    },
  a_checkboxClearPrefill: function(options, condition, key="")
    {
      type: 'clear_prefill',
      clear: if condition then options else [],
      key: key
    },
  a_checkboxExclusive: function(arr1, arr2)
    {
      type: 'exclusive',
      exclusive: [arr1, arr2]
    },
  a_checkboxDisableAll: function(condition)
    {
      type: 'disable_all',
      disableAll: condition
    },
  a_checkboxMutuallyExclusive: function(options)
    local _ = self;
    local optCount = std.length(options);
    local ruleForIndex = function(index, opt)
      if index + 1 < optCount then
        _.a_checkboxExclusive([options[index]], options[index+1:optCount:1])
      else
        null;

    local notNull = function(x) x != null;
    local rules = std.mapWithIndex(ruleForIndex, options);

    std.filter(notNull, rules),
  applyCheckbox: function(data)
    local _ = self;
    local target = data.target;
    local value = data.value;
    local rules = _.fullyFlattenArrays(data.rules);
    local prefillState = if std.objectHas(data, "prefillState") && data.prefillState != "" then data.prefillState else "{}";
    local prefillStateLogic = if std.objectHas(data, "prefillStateLogic") then data.prefillStateLogic else null;

    local handleExclusive = function(v, r)
      local arr1 = r[0];
      local arr2 = r[1];

      if _.endsWithOneOf(v, arr1) then _.purge(v, arr2)
      else if _.endsWithOneOf(v, arr2) then _.purge(v, arr1)
      else v;

    local addPrefill = function(val, arr)
      std.foldl(function(v, e) if std.member(v, e) then v else v + [e], arr, val);

    local state = _.getState(rules, std.parseJson(prefillState));

    local valueForPrefill = addPrefill(value, state.prefill);
    local valueForClearDisable = _.purge(valueForPrefill, state.clear);
    local valueForExclusive = std.foldl(handleExclusive, state.exclusives, valueForClearDisable);
    local ruleForDisableAll = if state.disableAll
      then
        target.value([])
        + target.disable(true)
      else target.disable(false);
    
    local setPrefillState = if prefillStateLogic != null then prefillStateLogic.value(std.toString(state.prefillState)) else [];

    target.disabledOptions(state.disabledOptions)
    + target.value(valueForExclusive)
    + ruleForDisableAll
    + setPrefillState,
  a_radioDisableAndClear: function(disabledOptions, condition)
    {
      type: 'disable_and_clear',
      disabledOptions: if condition then disabledOptions else [],
      clear: if condition then disabledOptions else [],
    },
  a_radioDisableAndPrefill: function(disabledOptions, prefillOption, condition, key="")
    {
      type: 'disable_and_prefill',
      disabledOptions: if condition then disabledOptions else [],
      prefill: prefillOption,
      clear: [],
      key: key,
      condition: condition
    },
  a_radioPrefillWithoutDisable: function(prefillOption, condition, key="")
    {
      type: 'prefill_only',
      prefill: if condition then [prefillOption] else [],
      key: key,
      condition: condition
    },
  a_radioClearPrefill: function(options, condition, key="")
    {
      type: 'clear_prefill',
      clear: if condition then options else [],
      key: key
    },
  a_radioDisableAll: function(condition)
    {
      type: 'disable_all',
      disableAll: condition
    },
  applyRadio: function(data)
    local target = data.target;
    local value = data.value;
    local rules = data.rules;
    local prefillState = if std.objectHas(data, "prefillState") && data.prefillState != "" then data.prefillState else "{}";
    local prefillStateLogic = if std.objectHas(data, "prefillStateLogic") then data.prefillStateLogic else null;

    local _ = self;

    local state = _.getState(rules, std.parseJson(prefillState));

    local prefillValue = if state.prefill != [] then state.prefill[std.length(state.prefill) - 1] else value;
    local clearValue = if std.member(state.clear, prefillValue) then '' else prefillValue;
    local ruleForDisableAll = if state.disableAll
      then
        target.value('')
        + target.disable(true)
      else target.disable(false);
    
    local setPrefillState = if prefillStateLogic != null then prefillStateLogic.value(std.toString(state.prefillState)) else [];

    target.disabledOptions(state.disabledOptions)
    + target.value(clearValue)
    + ruleForDisableAll
    + setPrefillState,
  showEither: function(targetForTrue, targetForFalse, condition)
    local _ = self;
    _.showIf(targetForTrue, condition)
    + _.hideIf(targetForFalse, condition),
  merge: function(target, separator, parts)
    local combine = std.filter(function(str) str != "", parts);
    target.value(std.join(separator, combine)),
  validateDate: function(target, value)
    if atd.isValidDate(value) && atd.compareToday(value) > 0 then
        target.setError('Must not be later than today.')
    else
        target.setError(null),
  validateYear: function(target, value)
    local _ = self;
    
    if atd.compareYear(_.safeInt(value)) > 0 then 
        target.setError('Must not be later than the current year.') 
    else 
        target.setError(null),
  validateCommitment: function(target, value)
    local floor = std.floor(value);
    local valid = std.isNumber(value);

    if valid && value == 0 then
      target.setError('Must be greater than $0.')
    else if valid && value > floor then
      target.setError("Must not include cents.")
    else
      target.setError(null),
  validateFiscalYear: function(target, month, day)
    local max = 
      if month == "February" then
          28
      else if std.member(["April", "June", "September", "November"], month) then
          30
      else
          31;
    if std.isNumber(day) && (day <= 0 || day > max) then
        target.setError("Day must be valid.")
    else
        target.setError(""),
  validateMinNumber: function(target, value, min)
    local valid = std.isNumber(value);

    if valid && value <= min then
      target.setError(std.format('Must be greater than %d.', min)) 
    else 
      target.setError(null),
  validatePercentage: function(target, value)
    local valid = std.isNumber(value);

    if valid && value == 0 then
      target.setError('Must be greater than 0%.')
    else
      target.setError(null),
  validateMinPercentage: function(target, value, min)
    local valid = std.isNumber(value);

    if valid && value <= min then
      target.setError(std.format('Must be greater than %d%%.', min))
    else
      target.setError(null),
  validateMinMoney: function(target, value, min)
    local valid = std.isNumber(value);

    if valid && value <= min then
      target.setError(std.format('Must be greater than $%d.', min)) 
    else 
      target.setError(null),
  validateTotalProfits: function(target, value, percent_alias, message='The total share of profits must not exceed 100%.')
    local _ = self;
    local f = function(x, y) x + _.getOrElse(y, 0);
    local v = function(element) element[percent_alias].value;
    
    local sum = std.foldl(f, std.map(v, value), 0);
    
    if sum > 100 then 
      target.setError(message) 
    else 
      target.setError(null),
  checkAllBoxes: function(target, schemaObject)
    local _ = self;
    _.fill(target, schemaObject.items.enum),
  showFileGroupWithAllFiles: self.checkAllBoxes,
  showFileGroup: function(targetAlias, fileArray)
    local _ = self;
    local files = std.map(function(m) if m['condition'] then [m['file']] else [], fileArray);
    _.fill(targetAlias, std.flattenArrays(files)),
  showFileGroupWithSameCondition: function(targetAlias, files, condition)
    local _ = self;
    _.fill(targetAlias, if condition then files else []),
  validateAndPrefillPercentageWithXDecimals: function(x, inputAliasInsideQuote, inputAliasValue, hiddenMappingAliasInsideQuote)
    local _ = self;
    local commaToDot = function(s) std.strReplace(s, ',', '.');
    local strToNumber = function(s) std.join('', std.filter(function(c) std.member('**********.', c), std.stringChars(s)));
    local preTreatment = commaToDot(inputAliasValue);
    local numInStr = strToNumber(preTreatment);
    local numInArray = std.split(numInStr, '.');
    local decimal = numInArray[0];
    local fraction = if std.length(numInArray) > 1 then numInArray[1] else '';

    local startOrEndWithDot = std.startsWith(preTreatment, '.') || std.endsWith(preTreatment, '.');
    local containStr = std.length(preTreatment) != std.length(numInStr);
    local greaterThanXDecimals = std.length(fraction) > x;
    local containMoreThan1Dot = std.count(std.stringChars(numInStr), '.') > 1;
    local commonErr = startOrEndWithDot || containStr || greaterThanXDecimals || containMoreThan1Dot;

    local decimalInNum = _.safeInt(decimal);
    local fractionInNum = _.safeInt(fraction);
    local greaterThan100 = decimalInNum > 100 || (decimalInNum == 100 && fractionInNum > 0);
    local equalTo0 = (decimalInNum == 0 && fractionInNum == 0);

    local err = if inputAliasValue == '' then 'This field is required'
    else if commonErr then std.format('Please enter a valid percentage with up to %d decimals', x)
    else if greaterThan100 then 'Must not be greater than 100%'
    else if equalTo0 then 'Must be greater than 0%'
    else '';

    local fractionAfterTreatmenting = std.rstripChars(fraction, '0');
    local val = if fractionAfterTreatmenting != '' then decimalInNum + '.' + fractionAfterTreatmenting else decimalInNum;

    inputAliasInsideQuote.setError(err) + hiddenMappingAliasInsideQuote.value(val + '%'),
  applyRepeatable: function(func, repeatableValue) std.flattenArrays(std.mapWithIndex(func, repeatableValue)),
  prefillValueBetweenTwoRepeatable: function(sourceValue, destinationValue, destinationLogic, mappingFunc)
    local _ = self;
    local destLen = std.length(destinationValue);
    local sourceLen = std.length(sourceValue);

    local processPrefill = function()
      local func = function(index, element)
          // mappings for prefill values
          local mappings = mappingFunc(element);

          // check row of destination
          local createRow = if destLen < index+1 then destinationLogic.addItem() else [];

          // check row for remove when length of destination greater than source
          // local checkLength = if sourceLen < destLen then destinationLogic.removeItem(destLen - 1) else [];

          // copy values
          local copyFunc = function(map)
              local fromValue = map.value;
              local toLogic = map.to;

              toLogic.valueAtIndex(index, fromValue);
          
          local copyValues = std.flattenArrays(std.map(copyFunc, mappings));

          createRow
          + copyValues;

      _.applyRepeatable(func, sourceValue);

    if destLen > 0 && sourceLen > 0 then processPrefill() else [],
  isDirty: function(field)
    field.touched || field.imported || field.autoFilled,
  nb_validate_country_code: function(countryValue, countryCodeLogic)

    local isUS = countryValue == "United States";
    
    local e1 = countryCodeLogic.value(if isUS then "+1" else "");
    local e2 = countryCodeLogic.disable(isUS);

    e1 + e2,
  nb_validate_area_code: function(countryValue, areaCodeValue, areaCodeLogic)

    local isUS = countryValue == 'United States';

    local areaCodeError = if isUS && areaCodeValue != "" && std.length(areaCodeValue) != 3 
    then "The area code must be 3 digits" 
    else "";

    areaCodeLogic.setError(areaCodeError),
  nb_validate_phone_number: function(countryValue, phoneValue, phoneLogic)

    local isUS = countryValue == 'United States';

    local areaCodeError = if isUS && phoneValue != "" && std.length(phoneValue) != 7 
    then "The phone number must be 7 digits excluding dashes" 
    else "";

    phoneLogic.setError(areaCodeError),
  nb_validate_date_field: function(dateValue, dateLogic)

    local parts = std.split(dateValue, "/");

    local safeInt = function(s)
        local numStr = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(s)));
        if numStr == "" then 0 else std.parseInt(numStr);

    local MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    local getDaysInMonth = function(mm, yy)
        local m = safeInt(mm);
        local y = safeInt(yy);

        if !(m > 0 && m <= 12) then
            0
        else if m != 2 then
            MONTH_DAYS[m - 1]
        else
            local isLeap = ((y % 4 == 0) && (y % 100 != 0)) || (y % 400 == 0);

            if isLeap then 29 else 28
    ;

    local formatDate = function(mm, dd, yyyy)
        local m = if std.length(mm) == 1 then "0" + mm else mm;
        local d = if std.length(dd) == 1 then "0" + dd else dd;

        yyyy + "-" + m + "-" + d
    ;

    local isStringOfNumber = function(str)
        std.filter(function(c) !std.member("**********", c), std.stringChars(str)) == []
    ;

    local e1 = 
        if dateValue == "" then
            []
        else if std.length(parts) != 3 then
            dateLogic.setError("Must be in valid format MM/DD/YYYY")
        else if !isStringOfNumber(parts[0]) || (std.length(parts[0]) != 2 && std.length(parts[0]) != 1) then
            dateLogic.setError("Valid month contains 1 or 2 digits.")
        else if !isStringOfNumber(parts[2]) || (std.length(parts[2]) != 4) then
            dateLogic.setError("Valid year contains 4 digits.")
        else if !isStringOfNumber(parts[1]) || (std.length(parts[1]) != 2 && std.length(parts[1]) != 1) then
            dateLogic.setError("Valid day contains 1 or 2 digits.")
        else if !(safeInt(parts[0]) > 0 && safeInt(parts[0]) <= 12) then
            dateLogic.setError("Date must be valid.")
        else if !(safeInt(parts[2]) >= 1000 && safeInt(parts[2]) <= 9999) then
            dateLogic.setError("Date must be valid.")
        else if !(safeInt(parts[1]) > 0 && safeInt(parts[1]) <= getDaysInMonth(parts[0], parts[2])) then
            dateLogic.setError("Date must be valid.")
        else if atd.compareToday(formatDate(parts[0], parts[1], parts[2])) > 0 then 
            dateLogic.setError('Must not be later than today.')
        else if atd.diffYear(formatDate(parts[0], parts[1], parts[2])) > -18 then 
            dateLogic.setError('You must be at least 18 years of age to complete this form.')
        else
            dateLogic.setError(null);
    e1,
  nb_brokerage_account_number: function(accountValue, accountLogic, message='Please use a valid format (8 numeric)')
    local safeInt = function(s) 
        local numStr = std.join('', std.filter(function(c) std.member('**********', c), std.stringChars(s)));
        numStr;
    
    local value = accountValue;
    local newValue = safeInt(value);
    local enough = std.length(newValue) == 8 || std.length(newValue) == 0;

    local e1 =
        if !enough then
            accountLogic.setError(message)
        else 
            accountLogic.setError(null);

    local e2 = if newValue != value 
        then accountLogic.value(newValue) 
        else [];

    e1 + e2,
  nb_validate_nfs_number: function(nfsValue, nfsLogic)
    local value = std.toString(nfsValue);
    
    local enough = std.length(std.toString(value)) == 9; // TODO: delete this hack

    local specialChars = "\"\';:<>?,./|}{[]~`!@#$%^&*()_+=- ";

    local refined = std.join('', std.filter(function(c) !std.member(specialChars, c), std.stringChars(value)));

    local newValue = refined;

    local e1 = if !enough then 
        nfsLogic.setError("Please use a valid format (9 alphanumeric)") 

    else 
        nfsLogic.setError(null);

    local e2 = if newValue != value then nfsLogic.value(newValue) else [];

    e1 + e2,
  nb_validate_ITIN: function(itinValue, itinLogic)
    local nums = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(itinValue)));

    local part1 = if std.length(nums) >= 0 + 1 then std.substr(nums, 0, 3) else std.substr(nums, 0, std.min(3, std.length(nums) - 0));
    local part2 = if std.length(nums) >= 3 + 1 then std.substr(nums, 3, 2) else std.substr(nums, 3, std.min(2, std.length(nums) - 3));
    local part3 = if std.length(nums) >= 5 + 1 then std.substr(nums, 5, std.length(nums)-5) else "";

    local overwrite = std.join("-", std.filter(function(x) x != "", [part1, part2, part3]));

    local e2 = itinLogic.value(overwrite);

    local e1 = 
        if itinValue == "" then
            []
        else if std.length(nums) != 9 then
            itinLogic.setError("ITIN must contain 9 digits.")

        else if nums[0] != "9" then
            itinLogic.setError("ITIN must begin with number 9.")

        else
            itinLogic.setError(null);

    e1 + itinLogic.value(overwrite),
  nb_validate_aba: function(abaValue, abaLogic)
    local enough = std.length(std.toString(abaValue)) == 9; // TODO: delete this hack

    local specialChars = "\"\';:<>?,./|}{[]~`!@#$%^&*()_+=- ";

    local refined = std.filter(function(c) !std.member(specialChars, c), std.stringChars(abaValue));
    local removedSpace = std.strReplaceAll(abaValue, " ", "");

    local alphaNum = std.length(refined) == std.length(abaValue);

    local e1  = if (!enough || !alphaNum) && abaValue != "" then 
        abaLogic.setError("Please use a valid format (9 numeric)")

    else 
        abaLogic.setError(null);

    local e2 = abaLogic.value(removedSpace);

    e1 + e2,
  nb_validate_GNumber: function(number, target)

    local isValidGNumber = function(num)
        local numStr = std.join('', std.filter(function(c) std.member('**********', c), std.stringChars(num)));
        
        if std.length(num) == 8 && std.length(numStr) == 8 
        then
            true
        else
            false;

    if number != "" && !isValidGNumber(number)
    then
        target.setError("The input is not valid format of 8 numbers.")
    else 
        target.setError(""),

  // Deprecated functions
  wrap: function(s) if s == '' then '' else s + ' ',
  radioShowIf: function(value, option, target)
    local selected = value == option;
    target.hide(!selected),
  untouch: function(target)
    target.touched(false),
  multipleUntouch: function(targets)
    local _ = self;
    local f = function(target)
      _.untouch(target);

    std.flattenArrays(std.map(f, targets)),
  prefillTextbox: function(sourceValue, targetAlias, targetValue, targetTouched, condition)
    if targetTouched  || (sourceValue != targetValue && targetValue != '') then []
    else if condition then targetAlias.value(sourceValue)
    else [],
  hideRepeatable: function(target, index, condition) target.hideAtIndex(index, condition),
  disableRepeatable: function(target, index, condition) target.disableAtIndex(index, condition),
  disableAndClearRepeatable: function(target, condition)
    target.clear()
    + target.disable(condition),
  fillRepeatable: function(target, index, value) target.valueAtIndex(index, value),
  showMultipleIf: function(targets, condition)
    local _ = self;
    local f = function(target)
      _.showIf(target, condition);
    std.flattenArrays(std.map(f, targets)),
  hideMultipleIf: function(targets, condition)
    local _ = self;
    local f = function(target)
      _.hideIf(target, condition);
    std.flattenArrays(std.map(f, targets)),
  showUSState: function(countryValue, usTarget, nonUSTarget)
    local isUS = countryValue == "United States";
    usTarget.hide(!isUS)
    + nonUSTarget.hide(isUS || countryValue == ""),
  getAllOptions: function(schemaObject)
    local type = schemaObject.type;
    if type == 'array' then schemaObject.items.enum 
    else if type == 'string' then schemaObject.enum 
    else null,
}
