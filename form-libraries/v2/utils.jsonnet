{
    // Library Document:
    // https://www.notion.so/anduin/FormBuilder-Library-utils-1c93f653b1df80c68305f51dc14785da

    // ---- CONSTANT ----
    EmptyValue: "DEFAULT_EMPTY_VALUE",

    // ---- UNIFY VALUE ----

    forceNum: function(value, default=0)
        if std.isNumber(value) then
            value
        else if std.isString(value) then
            self.parseNumber(value, default)
        else
            default,

    isEmpty: function(value)
        if std.type(value) == "array" then
            std.length(value) == 0
        else if std.type(value) == "null" then
            true
        else if std.type(value) == "string" then
            value == ""
        else
            false,

    trim: function(str)
        // Non-breaking space (\u00A0) is intentionally represented here.
        // Please do not change without understanding the context [here](https://github.com/anduintransaction/gandalf/pull/30#pullrequestreview-2617218189)
        std.stripChars(str, " \t\n\f\r\u0085 "),

    // ---- ARRAY UTILITY ----

    purge: function(arr, elements)
        std.filter(function(ele) !std.member(elements, ele), arr),

    containsAny: function(arr, elements)
        if std.length(arr) == 0 then
            false
        else
            std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,

    endsWith:
        function(arr, ele)
            if std.length(arr) == 0 then
                false
            else
                arr[std.length(arr) - 1] == ele,

    endsWithOneOf:
        function(arr, elements)
            if std.length(arr) == 0 then
                false
            else
                std.length(
                    std.filter(function(item) arr[std.length(arr) - 1] == item, elements)
                ) > 0,

    intersect: function(arr1, arr2)
        std.filter(function(op) std.member(arr1, op), arr2),

    last: function(arr, default="")
        local len = std.length(arr);

        if len == 0 then default else arr[len - 1],

    join: function(spliter, items)
        local nonEmptyItems = std.filter(function(item) !self.isEmpty(item), items);

        local unifiedItems = std.map(
            function(item) std.toString(item),
            nonEmptyItems
        );

        std.join(spliter, unifiedItems),

    unique: function(arr)
        std.foldl(
            function(res, item)
                if std.member(res, item) then
                    res
                else
                    res + [item]
            , arr
            , []
        ),

    // ---- STRING UTILITY ----

    stringContains: function(string, substr)
        std.length(std.findSubstr(substr, string)) > 0,

    isAlphanumericString: function(value)
        if value == "" then
            true
        else
            local invalidChars = std.filter(function(c) !std.member("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", c), std.stringChars(value));

            invalidChars == [],

    isDigitString: function(value)
        if value == "" then
            true
        else
            local invalidChars = std.filter(function(c) !std.member("0123456789.", c), std.stringChars(value));

            invalidChars == [],

    parseNumber: function(s, default=0)
        local numStr = std.join("", std.filter(function(c) std.member("0123456789.", c), std.stringChars(s)));
        local parts = std.split(numStr, ".");

        if !self.isEmpty(numStr) && std.length(parts) <= 2 then  // Allow at most one decimal point
            std.parseJson(numStr)
        else
            default,

    // ---- OBJECT UTILITY ----

    get: function(obj, parts, defaultVal=null)
        local key = parts[0];
        local rest = parts[1:];

        if parts == [] then
            obj
        else if obj != null &&
                ((std.type(obj) == "object" && std.objectHas(obj, key)) ||
                 (std.type(obj) == "array" && std.isNumber(key) && key >= 0 && key < std.length(obj))) then
            self.get(obj[key], rest, defaultVal)
        else
            defaultVal,

    set: function(obj, parts, updateFunc, defaultVal=null)
        if parts == [] then
            updateFunc(obj)
        else
            local key = parts[0];
            local rest = parts[1:];
            local value =
                if std.objectHas(obj, key) then
                    obj[key]
                else
                    if rest == [] then defaultVal else {};
            local newValue =
                if rest == [] then
                    updateFunc(value)
                else
                    self.set(value, rest, updateFunc, defaultVal);

            obj { [key]: newValue },

    // ---- Form Schema ----

    isDirty: function(field)
        field.touched || field.imported || field.autoFilled,

    getAllOptions: function(schemaObject)
        local type = self.get(schemaObject, ["type"]);

        if type == "array" then
            local itemsEnum = self.get(schemaObject, ["items", "enum"], []);
            itemsEnum
        else if type == "string" then
            local enumValues = self.get(schemaObject, ["enum"], []);
            enumValues
        else
            [],

    // ---- CHECKLIST UTILITY ----

    getLabel:
        function(labelMappings)
            function(value, exceptionalOptions, exceptionalOptionValues)
                if self.isEmpty(value) then
                    ""
                else if std.member(exceptionalOptions, value) then
                    local index = std.find(value, exceptionalOptions)[0];

                    exceptionalOptionValues[index]
                else
                    local label = self.get(labelMappings, [value], null);

                    if label == null then
                        value
                    else
                        label,

    getDaysInMonth: function(m, y)
        local MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        if !(m > 0 && m <= 12) then
            0
        else if m != 2 then
            MONTH_DAYS[m - 1]
        else
            local isLeap = ((y % 4 == 0) && (y % 100 != 0)) || (y % 400 == 0);
            if isLeap then 29 else 28,

    getFiscalDaysInMonth: function(month)
        if month == "February" then
            28
        else if std.member(["April", "June", "September", "November"], month) then
            30
        else
            31,

    getFiscalEndDate: function(startMonth, startDay)
        local MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

        local invalid = self.isEmpty(startMonth) || self.isEmpty(startDay);

        if invalid then
            {
                endMonth: "",
                endDay: null,
            }
        else
            local idxes = std.find(startMonth, MONTHS);
            local idx = if std.length(idxes) >= 1 then idxes[0] else 0;

            local month =
                if idx - 1 < 0 then
                    MONTHS[11]
                else
                    MONTHS[idx - 1]
            ;

            local endMonth =
                if startDay - 1 == 0 then
                    month
                else
                    startMonth
            ;
            local endDay =
                if startDay - 1 <= 0 then
                    self.getFiscalDaysInMonth(month)
                else
                    startDay - 1
            ;

            {
                endMonth: endMonth,
                endDay: endDay,
            },

}
