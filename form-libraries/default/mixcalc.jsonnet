{
    getError: error "Upgrading to latest version of `utils` is required",

    local _main = self,
    local _rule = _main.rule,
    local _collector = _rule.collector,

    // ------ Public methods ------

    unit: _collector,
    show: _collector.show,
    hide: _collector.hide,
    deactivate: _collector.deactivate,
    activate: _collector.activate,
    setError: _collector.setError,
    setWarning: _collector.setWarning,
    fill: _collector.fill,
    fillAllOptions: _collector.fillAllOptions,
    deactivateOptions: _collector.deactivateOptions,
    activateOptions: _collector.activateOptions,
    addDeactivatedOptions: _collector.addDeactivatedOptions,
    addActivatedOptions: _collector.addActivatedOptions,
    excludeOptions: _collector.excludeOptions,
    excludeOptionGroups: _collector.excludeOptionGroups,
    excludeKeyOptionGroups: _collector.excludeKeyOptionGroups,
    excludeOtherOptions: _collector.excludeOtherOptions,
    excludeEachOptions: _collector.excludeEachOptions,
    enableFiles: _collector.enableFiles,
    enableAllFiles: _collector.enableAllFiles,
    setSigningTypes: _collector.setSigningTypes,
    autoCorrect: _collector.autoCorrect,
    resetValue: _collector.resetValue,

    rule: {
        collector: {
            local ref = self,

            rules: [],

            append: function(rule)
                ref {
                    rules: ref.rules + [rule],
                },

            show: _rule.showImpl(ref.append),
            hide: _rule.hideImpl(ref.append),
            deactivate: _rule.deactivateImpl(ref.append),
            activate: _rule.activateImpl(ref.append),
            setError: _rule.setErrorImpl(ref.append),
            setWarning: _rule.setWarningImpl(ref.append),
            fill: _rule.fillImpl(ref.append),
            fillAllOptions: _rule.fillAllOptionsImpl(ref.append),
            deactivateOptions: _rule.deactivateOptionsImpl(ref.append),
            activateOptions: _rule.activateOptionsImpl(ref.append),
            addDeactivatedOptions: _rule.addDeactivatedOptionsImpl(ref.append),
            addActivatedOptions: _rule.addActivatedOptionsImpl(ref.append),
            excludeOptions: _rule.excludeOptionsImpl(ref.append),
            excludeOptionGroups: _rule.excludeOptionGroupsImpl(ref.append),
            excludeKeyOptionGroups: _rule.excludeKeyOptionGroupsImpl(ref.append),
            excludeOtherOptions: _rule.excludeOtherOptionsImpl(ref.append),
            excludeEachOptions: _rule.excludeEachOptionsImpl(ref.append),
            enableFiles: _rule.enableFilesImpl(ref.append),
            enableAllFiles: _rule.enableAllFilesImpl(ref.append),
            setSigningTypes: _rule.setSigningTypesImpl(ref.append),
            autoCorrect: _rule.autoCorrectImpl(ref.append),
            resetValue: _rule.resetValueImpl(ref.append),

            apply: function(output, ctx, debug) self.getError,

            applyRepeatable: function(output, ctx, debug) function(onRow) self.getError,

            applyKey: function(key) self.getError,

            applyFakeCheckbox: function(targetObj, keyRules, debug) self.getError,
        },

        // ------ Show/Hide ------

        showImpl: function(converter)
            function(condition) self.getError,

        hideImpl: function(converter)
            function(condition) self.getError,

        // ------ Activate/Deactivate ------

        deactivateImpl: function(converter)
            function(condition) self.getError,

        activateImpl: function(converter) self.getError,

        // ------ Warning/Error ------

        setErrorImpl: function(converter)
            function(message, condition) self.getError,

        setWarningImpl: function(converter)
            function(message, condition) self.getError,

        // ------ Pre-fill value/Pre-check options ------

        fillImpl: function(converter)
            function(value, condition, disabled, orElse="clear") self.getError,

        fillAllOptionsImpl: function(converter)
            function(condition, disabled, orElse="clear") self.getError,

        // ------ Activate/Deactivate Options ------

        deactivateOptionsImpl: function(converter)
            function(options, condition) self.getError,

        activateOptionsImpl: function(converter)
            function(options, condition) self.getError,

        addDeactivatedOptionsImpl: function(converter)
            function(options, condition) self.getError,

        addActivatedOptionsImpl: function(converter)
            function(options, condition) self.getError,

        // ------ Exclude Between Options ------

        excludeOptionsImpl: function(converter)
            function(options, condition, disabled) self.getError,

        excludeOptionGroupsImpl: function(converter)
            function(
                group1,
                group2,
                group3=[],
                group4=[],
                group5=[],
                group6=[],
                group7=[],
                group8=[],
                condition,
                disabled
            ) self.getError,
        excludeKeyOptionGroupsImpl: function(converter)
            function(
                group1,
                group2,
                group3={},
                group4={},
                group5={},
                group6={},
                group7={},
                group8={},
                condition,
                disabled
            ) self.getError,

        excludeOtherOptionsImpl: function(converter)
            function(options, condition, disabled) self.getError,

        excludeEachOptionsImpl: function(converter)
            function(condition, disabled) self.getError,

        // ------ Supporting Documents ------

        enableFilesImpl: function(converter)
            function(files, condition) self.getError,

        enableAllFilesImpl: function(converter)
            function(condition) self.getError,

        // ------ E-sign/Wet-sign ------

        setSigningTypesImpl: function(converter)
            function(singingTypes, condition) self.getError,
        // ------ Others ------

        autoCorrectImpl: function(converter)
            function(getValue, trimEnabled) self.getError,

        resetValueImpl: function(converter)
            function(condition) self.getError,
    },

}
