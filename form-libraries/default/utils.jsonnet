{
    getError: error "Upgrading to latest version of `utils` is required",

    EmptyValue: self.getError,

    forceNum: function(value, default = 0) self.getError,
    isEmpty: function(value) self.getError,
    trim: function(str) self.getError,
    purge: function(arr, elements) self.getError,
    containsAny: function(arr, elements) self.getError,
    intersect: function(arr1, arr2) self.getError,
    last: function(arr, default="") self.getError,
    join: function(spliter, items) self.getError,
    unique: function(arr) self.getError,
    stringContains: function(string, substr) self.getError,
    isAlphanumericString: function(value) self.getError,
    isDigitString: function(value) self.getError,
    parseNumber: function(s, default=0) self.getError,
    get: function(obj, parts, defaultVal=null) self.getError,
    set: function(obj, parts, updateFunc, defaultVal=null) self.getError,
    isDirty: function(field) self.getError,
    getLabel: 
        function(labelMappings)
            function(value, exceptionalOptions, exceptionalOptionValues)
                self.getError,
    getAllOptions: function(schemaObject) self.getError,
    getDaysInMonth: function(mm, yy) self.getError,
    getFiscalDaysInMonth: function(month) self.getError,
    getFiscalEndDate: function(startMonth, startDay) self.getError,

}
