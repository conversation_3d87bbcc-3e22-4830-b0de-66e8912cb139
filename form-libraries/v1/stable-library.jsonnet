{
  getState: function(results)
    local init = {
      disabledOptions: [],
      clear: [],
      prefill: [],
      exclusives: [],
      disableAll: false,
    };

    local combine = function(value, obj)
      if obj.type == 'disable_and_clear'
      then
        {
          disabledOptions: value.disabledOptions + obj.disabledOptions,
          clear: value.clear + obj.clear,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll
        }
      else if obj.type == 'disable_and_prefill'
      then
        {
          disabledOptions: value.disabledOptions + obj.disabledOptions,
          clear: value.clear + obj.clear,
          prefill: value.prefill + obj.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll
        }
      else if obj.type == 'prefill_only'
      then 
        {
          disabledOptions: value.disabledOptions,
          clear: value.clear,
          prefill: value.prefill + obj.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll
        }
      else if obj.type == 'clear_prefill'
      then
        {
          disabledOptions: value.disabledOptions,
          clear: value.clear + obj.clear,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: value.disableAll
        }
      else if obj.type == 'exclusive'
        then {
          disabledOptions: value.disabledOptions,
          clear: value.clear,
          prefill: value.prefill,
          exclusives: value.exclusives + [obj.exclusive],
          disableAll: value.disableAll
        }
      else if obj.type == 'disable_all'
        then {
          disabledOptions: value.disabledOptions,
          clear: value.clear,
          prefill: value.prefill,
          exclusives: value.exclusives,
          disableAll: obj.disableAll
        }
      else value;

    std.foldl(combine, results, init),
  purge: function(arr, elements) std.filter(function(ele) !std.member(elements, ele), arr),
  contains: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,
  endsWith: function(arr, ele) if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele,
  endsWithOneOf: function(arr, elements) if std.length(arr) == 0 then false else std.length(std.filter(function(item) arr[std.length(arr) - 1] == item, elements)) > 0,
  intersect: function(arr1, arr2)
    std.filter(function(op) std.member(arr1, op), arr2),
  showIf: function(target, condition) atd.add(target, 'hide', !condition),
  hideIf: function(target, condition) atd.add(target, 'hide', condition),
  showMultipleIf: function(targets, condition)
    local _ = self;

    local f = function(target)
      _.showIf(target, condition);

    std.flattenArrays(std.map(f, targets)),
  hideMultipleIf: function(targets, condition)
    local _ = self;

    local f = function(target)
      _.hideIf(target, condition);

    std.flattenArrays(std.map(f, targets)),
  enableIf: function(target, condition, childArr) local clearChildren = if !condition then std.mapWithIndex(function(index, value) atd.add(value, 'value', ''), childArr) else []; atd.add(target, 'disable', !condition) + std.flattenArrays(clearChildren),
  disableIf: function(target, condition, childArr) local clearChildren = if condition then std.mapWithIndex(function(index, value) atd.add(value, 'value', ''), childArr) else []; atd.add(target, 'disable', condition) + std.flattenArrays(clearChildren),
  fill: function(target, value) atd.add(target, 'value', value),
  wrap: function(s) if s == '' then '' else s + ' ',
  getOrElse: function(value, default) if value == null then default else value,
  safeInt: function(s)
    local numStr = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(s)));
    if numStr == "" then 0 else std.parseInt(numStr),
  getDayOfMonth: function(mm, yy)
    local m = self.safeInt(mm);
    local y = self.safeInt(yy);
    local MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if !(m > 0 && m <= 12) then
      0
    else if m != 2 then
      MONTH_DAYS[m - 1]
    else
      local isLeap = ((y % 4 == 0) && (y % 100 != 0)) || (y % 400 == 0);
      if isLeap then 29 else 28,
  a_checkboxDisableAndClear: function(disabledOptions, condition)
    {
      type: 'disable_and_clear',
      disabledOptions: if condition then disabledOptions else [],
      clear: if condition then disabledOptions else [],
    },
  a_checkboxDisableAndPrefill: function(disabledOptions, prefillOptions, clearOptions, condition)
    {
      type: 'disable_and_prefill',
      disabledOptions: if condition then disabledOptions else [],
      prefill: if condition then prefillOptions else [],
      clear: if condition then clearOptions else [],
    },
  a_checkboxPrefillWithoutDisable: function(prefillOptions, condition)
    {
      type: 'prefill_only',
      prefill: if condition then prefillOptions else [],
    },
  a_checkboxClearPrefill: function(options, condition)
    {
      type: 'clear_prefill',
      clear: if condition then options else [],
    },
  a_checkboxExclusive: function(arr1, arr2)
    {
      type: 'exclusive',
      exclusive: [arr1, arr2]
    },
  a_checkboxDisableAll: function(condition)
    {
      type: 'disable_all',
      disableAll: condition
    },
  applyCheckbox: function(data)
    local target = data.target;
    local value = data.value;
    local rules = data.rules;
    local _ = self;

    local handleExclusive = function(v, r)
      local arr1 = r[0];
      local arr2 = r[1];

      if _.endsWithOneOf(v, arr1) then _.purge(v, arr2)
      else if _.endsWithOneOf(v, arr2) then _.purge(v, arr1)
      else v;

    local addPrefill = function(val, arr)
      std.foldl(function(v, e) if std.member(v, e) then v else v + [e], arr, val);

    local state = _.getState(rules);

    local valueForPrefill = addPrefill(value, state.prefill);
    local valueForClearDisable = _.purge(valueForPrefill, state.clear);
    local valueForExclusive = std.foldl(handleExclusive, state.exclusives, valueForClearDisable);
    local ruleForDisableAll = if state.disableAll
      then
        atd.add(target, 'value', [])
        + atd.add(target, 'disable', true)
      else atd.add(target, 'disable', false);
    
    atd.add(target, 'disabledOptions', state.disabledOptions)
    + atd.add(target, 'value', valueForExclusive)
    + ruleForDisableAll,
  a_radioDisableAndClear: function(disabledOptions, condition)
    {
      type: 'disable_and_clear',
      disabledOptions: if condition then disabledOptions else [],
      clear: if condition then disabledOptions else [],
    },
  a_radioDisableAndPrefill: function(disabledOptions, prefillOption, condition)
    {
      type: 'disable_and_prefill',
      disabledOptions: if condition then disabledOptions else [],
      prefill: if condition then [prefillOption] else [],
      clear: []
    },
  a_radioPrefillWithoutDisable: function(prefillOptions, condition)
    {
        type: 'prefill_only',
        prefill: if condition then prefillOptions else [],
    },
  a_radioClearPrefill: function(options, condition)
    {
      type: 'clear_prefill',
      clear: if condition then options else [],
    },
  a_radioDisableAll: function(condition)
    {
      type: 'disable_all',
      disableAll: condition
    },
  applyRadio: function(data)
    local target = data.target;
    local value = data.value;
    local rules = data.rules;
    local _ = self;

    local state = _.getState(rules);

    local prefillValue = if state.prefill != [] then state.prefill[0] else value;
    local clearValue = if std.member(state.clear, prefillValue) then '' else prefillValue;
    local ruleForDisableAll = if state.disableAll
      then
        atd.add(target, 'value', '')
        + atd.add(target, 'disable', true)
      else atd.add(target, 'disable', false);
    
    atd.add(target, 'disabledOptions', state.disabledOptions)
    + atd.add(target, 'value', clearValue)
    + ruleForDisableAll,
  showUSState: function(country, us, nonUS)
    local isUS = country == "United States";
    atd.add(us, "hide", !isUS)
    + atd.add(nonUS, "hide", isUS || country == ""),
  merge: function(target, separator, parts)
    local combine = std.filter(function(str) str != "", parts);
    atd.add(target, 'value', std.join(separator, combine)),
  radioShowIf: function(value, option, target)
    local selected = value == option;
    atd.add(target, "hide", !selected),
  validateDate: function(target, value)
    if atd.compareToday(value) > 0 then
        atd.add(target, 'error', 'Must not be later than today.')
    else
        atd.add(target, 'error', null),
  validateYear: function(target, value)
    local _ = self;
    
    if atd.compareYear(_.safeInt(value)) > 0 then 
        atd.add(target, 'error', 'Must not be later than the current year.') 
    else 
        atd.add(target, 'error', null),
  validateCommitment: function(target, value)
    local floor = std.floor(value);
    local valid = std.isNumber(value);

    if valid && value == 0 then
      atd.add(target, 'error', 'Must be greater than $0.')
    else if valid && value > floor then
      atd.add(target, 'error', "Must not include cents.")
    else
      atd.add(target, 'error', null),
  validateFiscalYear: function(target, month, day)
    local max = 
      if month == "February" then
          28
      else if std.member(["April", "June", "September", "November"], month) then
          30
      else
          31;
    if std.isNumber(day) && (day <= 0 || day > max) then
        atd.add(target, "error", "Day must be valid.")
    else
        atd.add(target, "error", ""),
  validateMinNumber: function(target, value, min)
    local valid = std.isNumber(value);

    if valid && value <= min then
      atd.add(target, 'error', std.format('Must be greater than %d.', min)) 
    else 
      atd.add(target, 'error', null),
  validatePercentage: function(target, value)
    local valid = std.isNumber(value);

    if valid && value == 0 then
      atd.add(target, 'error', 'Must be greater than 0%.')
    else
      atd.add(target, 'error', null),
  validateMinMoney: function(target, value, min)
    local valid = std.isNumber(value);

    if valid && value <= min then
      atd.add(target, 'error', std.format('Must be greater than $%d.', min)) 
    else 
      atd.add(target, 'error', null),
  validateTotalProfits: function(target, value, percent_alias)
    local _ = self;
    local f = function(x, y) x + _.getOrElse(y, 0);
    local v = function(element) element[percent_alias].value;
    
    local sum = std.foldl(f, std.map(v, value), 0);
    
    if sum > 100 then 
      atd.add(target, 'error', 'The total share of profits must not exceed 100%.') 
    else 
      atd.add(target, 'error', null),
  getConstantValue: function()
    {
      defaultText: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : false,
        "disableByParent" : true,
        "value" : "",
        "touched" : false
      },
      defaultNumber: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : false,
        "disableByParent" : true,
        "value" : null,
        "touched" : false
      },
      defaultCopyText: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : false,
        "disableByParent" : false,
        "value" : "",
        "touched" : false
      },
      defaultCopyNumber: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : false,
        "disableByParent" : false,
        "value" : null,
        "touched" : false
      },
      disabledText: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : true,
        "disableByParent" : true,
        "value" : "",
        "touched" : false
      },
      disabledNumber: {
        "hide" : false,
        "hideByParent" : false,
        "disable" : true,
        "disableByParent" : true,
        "value" : null,
        "touched" : false
      }
    },
  hideRepeatable: function(repeatable, target, index, condition) atd.add(repeatable, ['value', index, target, 'hide'], condition),
  disableRepeatable: function(repeatable, target, index, condition) atd.add(repeatable, ['value', index, target, 'disable'], condition),
  disableAndClearRepeatable: function(target, value, condition)
    local _ = self;
    local constants = _.getConstantValue();

    local row = if std.length(value) > 0 then value[0] else {};

    local getDefaultValue = function(val)
        if val.value == null || std.isNumber(val.value) then constants.defaultNumber else constants.defaultText;

    local defaultRow = std.mapWithKey(function(key, val) getDefaultValue(val), row);

    (if condition && defaultRow != {} then _.fill(target, [defaultRow]) else [])
    + atd.add(target, "disable", condition),
  fillRepeatable: function(repeatable, target, index, value) atd.add(repeatable, ['value', index, target, 'value'], value),
  applyRepeatable: function(func, repeatableValue) std.flattenArrays(std.mapWithIndex(func, repeatableValue)),
  prefillValueBetweenTwoRepeatable: function(sourceValue, destinationValue, destinationAlias, mappings)
    local _ = self;
    local destLen = std.length(destinationValue);
    local sourceLen = std.length(sourceValue);

    local processPrefill = function()
        local constants = _.getConstantValue();
        
        local row = if std.length(destinationValue) > 0 then destinationValue[0] else {};

        local getDefaultValue = function(val)
            if val.value == null || std.isNumber(val.value) then constants.defaultCopyNumber else constants.defaultCopyText;

        local emptyRowForDestination = std.mapWithKey(function(key, val) getDefaultValue(val), row);

        local f = function(index, element)

        local mapping = function(map)
            local val = element[map.from].value;
            atd.add(destinationAlias, ['value', index, map.to, 'value'], val);

        local e1 = if destLen <= index then atd.add(destinationAlias, 'value', destinationValue + [emptyRowForDestination]) else [];

        local e2 = std.flattenArrays(std.map(mapping, mappings));

        e1 + e2;

        _.applyRepeatable(f, sourceValue);

    if destLen > 0 && sourceLen > 0 then processPrefill() else [],
  untouch: function(target)
    atd.add(target, 'touched', false),
  multipleUntouch: function(targets)
    local _ = self;
    local f = function(target)
      _.untouch(target);

    std.flattenArrays(std.map(f, targets)),

  commaToDot: function(s) std.strReplace(s, ',', '.'),
  strToNumber: function(s) std.join('', std.filter(function(c) std.member('**********.', c), std.stringChars(s))),
  validateAndPrefillPercentageWithXDecimals: function(x, inputAliasInsideQuote, inputAliasValue, hiddenMappingAliasInsideQuote)
    local _ = self;
    local preTreatment = _.commaToDot(inputAliasValue);
    local numInStr = _.strToNumber(preTreatment);
    local numInArray = std.split(numInStr, '.');
    local decimal = numInArray[0];
    local fraction = if std.length(numInArray) > 1 then numInArray[1] else '';

    local startOrEndWithDot = std.startsWith(preTreatment, '.') || std.endsWith(preTreatment, '.');
    local containStr = std.length(preTreatment) != std.length(numInStr);
    local greaterThanXDecimals = std.length(fraction) > x;
    local containMoreThan1Dot = std.count(std.stringChars(numInStr), '.') > 1;
    local commonErr = startOrEndWithDot || containStr || greaterThanXDecimals || containMoreThan1Dot;

    local decimalInNum = _.safeInt(decimal);
    local fractionInNum = _.safeInt(fraction);
    local greaterThan100 = decimalInNum > 100 || (decimalInNum == 100 && fractionInNum > 0);
    local equalTo0 = (decimalInNum == 0 && fractionInNum == 0);

    local err = if inputAliasValue == '' then 'This field is required'
    else if commonErr then std.format('Please enter a valid percentage with up to %d decimals', x)
    else if greaterThan100 then 'Must not be greater than 100%'
    else if equalTo0 then 'Must be greater than 0%'
    else '';

    local fractionAfterTreatmenting = std.rstripChars(fraction, '0');
    local val = if fractionAfterTreatmenting != '' then decimalInNum + '.' + fractionAfterTreatmenting else decimalInNum;

    atd.add(inputAliasInsideQuote, 'error', err) + atd.add(hiddenMappingAliasInsideQuote, 'value', val + '%'),

  prefillTextbox: function(sourceValue, targetAlias, targetValue, targetTouched, condition)
    if targetTouched  || (sourceValue != targetValue && targetValue != '') then []
    else if condition then atd.add(targetAlias, 'value', sourceValue)
    else [],
  showFileGroup: function(targetAlias, fileArray)
    local _ = self;
    local files = std.map(function(m) if m['condition'] then [m['file']] else [], fileArray);
    _.fill(targetAlias, std.flattenArrays(files)),
  showFileGroupWithSameCondition: function(targetAlias, files, condition)
    local _ = self;
    _.fill(targetAlias, if condition then files else []),
  getEmptyRow: function(repeatableValue)
    local _ = self;
    local constants = _.getConstantValue();
    local row = if std.length(repeatableValue) > 0 then repeatableValue[0] else {};

    local getDefaultValue = function(val)
        if val.value == null || std.isNumber(val.value) then constants.defaultCopyNumber else constants.defaultCopyText;

    local emptyRow = std.mapWithKey(function(key, val) getDefaultValue(val), row);

    emptyRow,
  nb_validate_country_code: function(countryValue, target)

    local isUS = countryValue == "United States";
    
    local e1 = atd.add(target, 'value', if isUS then "+1" else "");
    local e2 = atd.add(target, 'disable', isUS);

    e1 + e2,
  nb_validate_area_code: function(countryValue, areaCodeValue, target)

    local isUS = countryValue == 'United States';

    local areaCodeError = if isUS && areaCodeValue != "" && std.length(areaCodeValue) != 3 
    then "The area code must be 3 digits" 
    else "";

    atd.add(target, 'error', areaCodeError),
  nb_validate_phone_number: function(countryValue, phoneValue, target)

    local isUS = countryValue == 'United States';

    local areaCodeError = if isUS && phoneValue != "" && std.length(phoneValue) != 7 
    then "The phone number must be 7 digits excluding dashes" 
    else "";

    atd.add(target, 'error', areaCodeError),
  nb_validate_date_field: function(dateValue, target)

    local parts = std.split(dateValue, "/");

    local safeInt = function(s)
        local numStr = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(s)));
        if numStr == "" then 0 else std.parseInt(numStr);

    local MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    local getDayOfMonth = function(mm, yy)
        local m = safeInt(mm);
        local y = safeInt(yy);

        if !(m > 0 && m <= 12) then
            0
        else if m != 2 then
            MONTH_DAYS[m - 1]
        else
            local isLeap = ((y % 4 == 0) && (y % 100 != 0)) || (y % 400 == 0);

            if isLeap then 29 else 28
    ;

    local formatDate = function(mm, dd, yyyy)
        local m = if std.length(mm) == 1 then "0" + mm else mm;
        local d = if std.length(dd) == 1 then "0" + dd else dd;

        yyyy + "-" + m + "-" + d
    ;

    local isStringOfNumber = function(str)
        std.filter(function(c) !std.member("**********", c), std.stringChars(str)) == []
    ;

    local e1 = 
        if dateValue == "" then
            []
        else if std.length(parts) != 3 then
            atd.add(target, "error", "Must be in valid format MM/DD/YYYY")
        else if !isStringOfNumber(parts[0]) || (std.length(parts[0]) != 2 && std.length(parts[0]) != 1) then
            atd.add(target, "error", "Valid month contains 1 or 2 digits.")
        else if !isStringOfNumber(parts[2]) || (std.length(parts[2]) != 4) then
            atd.add(target, "error", "Valid year contains 4 digits.")
        else if !isStringOfNumber(parts[1]) || (std.length(parts[1]) != 2 && std.length(parts[1]) != 1) then
            atd.add(target, "error", "Valid day contains 1 or 2 digits.")
        else if !(safeInt(parts[0]) > 0 && safeInt(parts[0]) <= 12) then
            atd.add(target, "error", "Date must be valid.")
        else if !(safeInt(parts[2]) >= 1000 && safeInt(parts[2]) <= 9999) then
            atd.add(target, "error", "Date must be valid.")
        else if !(safeInt(parts[1]) > 0 && safeInt(parts[1]) <= getDayOfMonth(parts[0], parts[2])) then
            atd.add(target, "error", "Date must be valid.")
        else if atd.compareToday(formatDate(parts[0], parts[1], parts[2])) > 0 then 
            atd.add(target, "error", 'Must not be later than today.')
        else if atd.diffYear(formatDate(parts[0], parts[1], parts[2])) > -18 then 
            atd.add(target, "error", 'You must be at least 18 years of age to complete this form.')
        else
            atd.add(target, "error", null);
    e1,
  nb_brokerage_account_number: function(accountValue, target, message='Please use a valid format (8 numeric)')
    local safeInt = function(s) 
        local numStr = std.join('', std.filter(function(c) std.member('**********', c), std.stringChars(s)));
        numStr;
    
    local value = accountValue;
    local newValue = safeInt(value);
    local enough = std.length(newValue) == 8 || std.length(newValue) == 0;

    local e1 =
        if !enough then
            atd.add(target, "error", message)
        else 
            atd.add(target, "error", null);

    local e2 = if newValue != value 
        then atd.add(target, "value", newValue) 
        else [];

    e1 + e2,
  nb_validate_nfs_number: function(nfsValue, target)
    local value = std.toString(nfsValue);
    
    local enough = std.length(std.toString(value)) == 9; // TODO: delete this hack

    local specialChars = "\"\';:<>?,./|}{[]~`!@#$%^&*()_+=- ";

    local refined = std.join('', std.filter(function(c) !std.member(specialChars, c), std.stringChars(value)));

    local newValue = refined;

    local e1 = if !enough then 
        atd.add(target, "error", "Please use a valid format (9 alphanumeric)") 

    else 
        atd.add(target, "error", null);

    local e2 = if newValue != value then atd.add(target, "value", newValue) else [];

    e1 + e2,
  nb_validate_ITIN: function(itinValue, target)
    local nums = std.join("", std.filter(function(c) std.member("**********", c), std.stringChars(itinValue)));

    local part1 = if std.length(nums) >= 0 + 1 then std.substr(nums, 0, 3) else std.substr(nums, 0, std.min(3, std.length(nums) - 0));
    local part2 = if std.length(nums) >= 3 + 1 then std.substr(nums, 3, 2) else std.substr(nums, 3, std.min(2, std.length(nums) - 3));
    local part3 = if std.length(nums) >= 5 + 1 then std.substr(nums, 5, std.length(nums)-5) else "";

    local overwrite = std.join("-", std.filter(function(x) x != "", [part1, part2, part3]));

    local e2 = atd.add(target, "value", overwrite);

    local e1 = 
        if itinValue == "" then
            []
        else if std.length(nums) != 9 then
            atd.add(target, "error", "ITIN must contain 9 digits.")

        else if nums[0] != "9" then
            atd.add(target, "error", "ITIN must begin with number 9.")

        else
            atd.add(target, "error", null);

    e1 + atd.add(target, "value", overwrite),
  nb_validate_aba: function(abaValue, target)
    local enough = std.length(std.toString(abaValue)) == 9; // TODO: delete this hack

    local specialChars = "\"\';:<>?,./|}{[]~`!@#$%^&*()_+=- ";

    local refined = std.filter(function(c) !std.member(specialChars, c), std.stringChars(abaValue));
    local removedSpace = std.strReplaceAll(abaValue, " ", "");

    local alphaNum = std.length(refined) == std.length(abaValue);

    local e1  = if (!enough || !alphaNum) && abaValue != "" then 
        atd.add(target, "error", "Please use a valid format (9 numeric)")

    else 
        atd.add(target, "error", null);

    local e2 = atd.add(target, "value", removedSpace);

    e1 + e2,
  nb_validate_GNumber: function(number, target)

    local isValidGNumber = function(num)
        local numStr = std.join('', std.filter(function(c) std.member('**********', c), std.stringChars(num)));
        
        if std.length(num) == 8 && std.length(numStr) == 8 
        then
            true
        else
            false;

    if number != "" && !isValidGNumber(number)
    then
        atd.add(target, "error", "The input is not valid format of 8 numbers.")
    else 
        atd.add(target, "error", ""),
}