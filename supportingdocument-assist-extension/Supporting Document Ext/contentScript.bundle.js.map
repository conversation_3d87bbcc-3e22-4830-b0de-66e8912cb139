{"version": 3, "sources": ["../webpack/bootstrap", "contentScript/taxform_config.ts", "contentScript/eu_taxform_config.ts", "contentScript/button_config.ts", "contentScript/supportingDocumentAssist.ts", "contentScript/index.ts"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "W8_EXP", "W8_BEN", "W8_IMY", "W8_FILES", "W9_FILES", "W8_NIO_FILES", "EU_W8_FILES", "EU_W9_FILES", "W8_BUTTON", "assign", "document", "createElement", "id", "className", "innerHTML", "W9_BUTTON", "NIO_LABEL", "generateNioInnerHtml", "isNIO", "TAX_FORMS", "Map", "isEUPortal", "checkIfEUPortal", "window", "location", "origin", "startsWith", "addAssistButton", "modalContainers", "querySelectorAll", "length", "table", "closest", "console", "error", "rows", "index", "for<PERSON>ach", "row", "cell", "children", "targetElement", "rootElement", "querySelector", "buttonW8", "cloneNode", "buttonW9", "addEventListener", "event", "mappingAssistClicked", "stopPropagation", "append<PERSON><PERSON><PERSON>", "updateAssistButton", "target", "HTMLInputElement", "type", "button", "tdElement", "fileName", "isW9Button", "includes", "formFiles", "isW9", "toggleClass", "removeClass", "addClass", "classList", "remove", "add", "contains", "updatedForms", "filter", "item", "some", "removeItem", "formId", "delete", "set", "forms", "push", "bearerToken", "mergeArrays", "arr1", "arr2", "map", "_ref", "existingEntry", "formVersionIds", "formVersionId", "Array", "from", "entries", "async", "currentTabHref", "href", "isValidPortalDomain", "innerText", "taxFormGroups", "keys", "taxForms", "form", "clear", "generateTaxFormsToArray", "supportingFormsButton", "backupSupportingFormsButtonInnerHTML", "_button$innerHTML", "trim", "setTimeout", "fundsubId", "parts", "split", "alert", "get<PERSON><PERSON><PERSON>", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "portalData", "json", "getResponse", "fundSubGeneralInfo", "versionSettings", "fundSubFormVersion", "response", "taxforms", "additionalTaxForms", "additionalTaxFormVersions", "res", "log", "status", "localStorage", "getItem"], "mappings": "aACE,IAAIA,EAAmB,CAAC,EAGxB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,CAAC,GAUX,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,OACf,CAIAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,GAEhE,EAGAZ,EAAoBkB,EAAI,SAAShB,GACX,qBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,GACvD,EAOArB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,EAAM,EAAEC,KAAK,KAAMD,IAC9I,OAAOF,CACR,EAGAzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,OAAG,EAClD,WAA8B,OAAOA,CAAQ,EAE9C,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,CACR,EAGAZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,EAAW,EAGpH/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,0CClFrD,IAAIC,EAAS,CAET,OAAU,uBACV,OAAU,cAcVC,EAAS,CAET,OAAU,uBACV,OAAU,cAEVC,EAAS,CAET,OAAU,uBACV,OAAU,cAqBHC,EAAW,CAACF,EAlBT,CAEV,OAAU,uBACV,OAAU,cA1BD,CAET,OAAU,uBACV,OAAU,cAsCkCD,EAAQE,GAC7CE,EAAW,CAPb,CAEL,OAAU,uBACV,OAAU,eAKHC,EAAe,CAACJ,EAdT,CAEd,OAAU,uBACV,OAAU,cA1BG,CAEb,OAAU,uBACV,OAAU,cAkC8CD,EAAQE,GCGzDI,EAAc,CAhCZ,CAET,OAAU,uBACV,OAAU,cASA,CAEV,OAAU,uBACV,OAAU,cA7BD,CAET,OAAU,uBACV,OAAU,cARD,CAET,OAAU,uBACV,OAAU,cAsBD,CAET,OAAU,uBACV,OAAU,eAwBHC,EAAc,CAPhB,CAEL,OAAU,uBACV,OAAU,eChDP,MAGMC,EAAY9B,OAAO+B,OAAOC,SAASC,cAAc,UAAW,CAACC,GAH1C,qBAIhCJ,EAAUK,UAAY,yCAEtBL,EAAUM,UAAY,MAEf,MAAMC,EAAYrC,OAAO+B,OAAOC,SAASC,cAAc,UAAW,CAACC,GAP1C,qBAQhCG,EAAUF,UAAY,wCAEtBE,EAAUD,UAAY,MAGtB,MASaE,EAAYtC,OAAO+B,OAAOC,SAASC,cAAc,SAAU,CAACC,GAAI,cAItE,SAASK,EAAqBC,GACjC,MAAO,oDAAoDA,EAd3C,g4BAGE,6mBAYtB,CALAF,EAAUH,UAAY,oCACtBG,EAAUF,UAAYG,GAAqB,GCrB3C,IAAIE,EAA8B,IAAIC,IAClCF,GAAiB,EAEjBG,GAAsB,EAG1B,SAASC,IAEL,OADeC,OAAOC,SAASC,OACjBC,WAAW,oBAC7B,CAaO,SAASC,IAEZN,EAAaC,IAEb,MAAMM,EAAkBlB,SAASmB,iBAAiB,oCAClD,GAAID,EAAgBE,OAAS,EAAG,CAC5B,IAAIC,EAAQH,EAAgB,GAAGI,QAAQ,SACvC,IAAKD,EAED,YADAE,QAAQC,MAAM,mBAGlB,IAAIC,EAAOJ,EAAMF,iBAAiB,YAC9BO,EAAgB,EAEpBD,EAAKE,SAASC,IACV,IAAIC,EAAOD,EAAIE,SAAS,GACnBD,EA2BV,SAA4BE,EAA4BL,GAC3D,IAAIM,EAEJ,GADAA,EAAcD,EAAcE,cAAc,kBACrCD,EAED,YADAT,QAAQC,MAAM,0BAIlB,GAAIQ,EAAYC,cAAc,0BAC1B,OAEJ,MAAMC,EAAWpC,EAAUqC,WAAU,GAC/BC,EAAW/B,EAAU8B,WAAU,GACrCD,EAAShC,GAAK,oBAAoBwB,IAClCU,EAASlC,GAAK,oBAAoBwB,IAElCQ,EAASG,iBAAiB,SAAUC,IAChCC,EAAqBD,EAAON,GAC5BM,EAAME,iBAAiB,IAE3BJ,EAASC,iBAAiB,SAAUC,IAChCC,EAAqBD,EAAON,GAC5BM,EAAME,iBAAiB,IAE3BR,EAAYS,YAAYP,GACxBF,EAAYS,YAAYL,EAC5B,CAjDYM,CAAmBb,EAAqBH,KAHpCH,QAAQC,MAAM,iBAG8B,GAExD,CAEA,IAAIb,EAAY,CACQX,SAASmB,iBAAiB,0CAC9BC,OAAS,GACrBd,EAAU+B,iBAAiB,SAAUC,IAE7BA,EAAMK,kBAAkBC,kBAA0C,aAAtBN,EAAMK,OAAOE,OAG7DrC,GAASA,EACTF,EAAUF,UAAYG,EAAqBC,GAC3C8B,EAAME,kBAAiB,GAKnC,CAEJ,CA8BA,SAASD,EAAqBD,EAAmBN,GAC7C,IAAKA,EAAa,OAElB,MAAMc,EAAUR,EAAMK,OAAuBrB,QAAQ,UACrD,IAAKwB,EAED,YADAvB,QAAQC,MAAM,6BAIlB,MAAMuB,EAAYf,EAAYV,QAAQ,MAChC0B,EAAoB,OAATD,QAAS,IAATA,OAAS,EAATA,EAAWjB,SAAS,GAAGA,SAAS,GAAG1B,UACpD,IAAK4C,EAAU,OAEf,MAAMC,EAAaH,EAAO5C,GAAGgD,SAAS,oBAChCC,GA/FeC,EA+FaH,EA9FlCtC,EAAaC,IAETwC,EACOzC,EAAad,EAAcH,EAE3Bc,EAAQb,EAAgBgB,EAAaf,EAAcH,GANlE,IAAyB2D,EAiGrB,IAAKD,EAED,YADA5B,QAAQC,MAAM,0BAIlB,MAAM6B,EAAcA,CAACC,EAAqBC,KACtCT,EAAOU,UAAUC,OAAOH,GACxBR,EAAOU,UAAUE,IAAIH,EAAS,EAGlC,GAAIT,EAAOU,UAAUG,SAAS,iBAAmBb,EAAOU,UAAUG,SAAS,eAAgB,CACvFN,EAAYP,EAAOU,UAAUG,SAAS,gBAAkB,eAAiB,cACrEb,EAAOU,UAAUG,SAAS,gBAAkB,eAAiB,eAEjE,MACMC,GADQnD,EAAUtC,IAAI6E,IAAa,IACda,QAAQC,IAAeX,EAAUY,MAAKC,GAAcF,EAAKG,SAAWD,EAAWC,WAClF,IAAxBL,EAAaxC,OAAeX,EAAUyD,OAAOlB,GAAYvC,EAAU0D,IAAInB,EAAUY,EAErF,KAAO,CACHP,EAAYP,EAAOU,UAAUG,SAAS,gBAAkB,eAAiB,cACrEb,EAAOU,UAAUG,SAAS,gBAAkB,eAAiB,eAEjE,MAAMS,EAAQ3D,EAAUtC,IAAI6E,IAAa,GACzCoB,EAAMC,QAAQlB,GACd1C,EAAU0D,IAAInB,EAAUoB,EAC5B,CACJ,CC5GA,IAGIE,EAA6B,KA6IjC,SAASC,EAAYC,EAAsBC,GAEvC,MAAMC,EAAM,IAAIhE,IACZ8D,EAAKE,KAAIC,IAAA,IAAE9F,EAAKN,GAAMoG,EAAA,MAAK,CAAC9F,EAAK,IAAIN,GAAO,KAIhD,IAAK,MAAOM,EAAKN,KAAUkG,EAAM,CAC7B,MAAMG,EAAgBF,EAAIvG,IAAIU,GAE1B+F,GAEKA,EAAcC,iBACfD,EAAcC,eAAiB,IAGnCtG,EAAMsG,eAAelD,SAASmD,IACrBF,EAAcC,eAAed,MAAMD,GAASA,EAAKG,SAAWa,EAAcb,UAC3EW,EAAcC,eAAeR,KAAKS,EACtC,KAIJJ,EAAIP,IAAItF,EAAK,IAAIN,GAEzB,CAGA,OAAOwG,MAAMC,KAAKN,EAAIO,UAC1B,CAlKAjF,SAASqC,iBAAiB,SAAS6C,UAC/B,IAAIpE,EAAWD,OAAOC,SAClBqE,EAAiBrE,EAASsE,KAC1BrE,EAASD,EAASC,OAGtB,GAXJ,SAA6BA,GACzB,MAAkB,8BAAXA,GACHA,EAAOC,WAAW,oBAC1B,CAQQqE,CAAoBtE,GAAS,CAC7B,MAAM4B,EAASL,EAAMK,OACrB,GAAIA,GAA+B,SAArBA,EAAO2C,UAAsB,CACvC,IAAIC,ED0FT,SAAiCxE,GAEpCJ,EAAaI,EAAOC,WAAW,qBAE/B,IAAIoD,EAAQW,MAAMC,KAAKvE,EAAU+E,QAC7BC,EAAWV,MAAMC,KAAKZ,EAAMM,KAAKgB,GAC1B,CACHA,EACA,CACI,eAAkBjF,EAAUtC,IAAIuH,QAM5C,OAFAjF,EAAUkF,QACVnF,GAAQ,EACDiF,CACX,CC1GgCG,CAAwB7E,GACxC8E,EAA4C,KAC5CC,EAAsD,KAC1D,KAAIP,EAAcnE,OAAS,GAevB,OAf0B,CAC1B,MAAM0B,EAAS9C,SAASiC,cAAc,8BACzB,IAAD8D,EAAZ,GAAIjD,EACoB,QAApBiD,EAAIjD,EAAO1C,iBAAS,IAAA2F,GAAhBA,EAAkBC,OAAO9C,SAAS,sBAClC2C,EAAwB/C,EACxBgD,EAAuCD,EAAsBzF,WAE7DyF,IAEAA,EAAsBP,UAAY,YAClCO,EAAsBrC,UAAUE,IAAI,YAIhD,CAGAuC,YAAWf,UACP,GAAIK,EAAcnE,OAAS,EAAG,CAE1B,IAAI8E,EAAgC,GAEpC,GAAIf,EAAgB,CAEhB,MAAMgB,EAAQhB,EAAeiB,MAAM,KACnCF,EAAYC,EAAMA,EAAM/E,OAAS,EACrC,CACA,IAAK8E,EAED,YADAG,MAAM,mCAKV,MAAMC,QAAkBC,MAAM,GAAGxF,iDAAuD,CACpFyF,OAAQ,OACRC,QAAS,CACL,eAAgB,mBAEhB,cAAiB,UAAUnC,KAE/BoC,KAAMC,KAAKC,UAAUV,KAEpBI,EAAUO,IACXR,MAAM,mCAGV,MAAMS,QAAmBR,EAAUS,OAG7BC,QAAoBT,MAAM,GAAGxF,oDAA0D,CACzFyF,OAAQ,OACRC,QAAS,CACL,eAAgB,mBAEhB,cAAiB,UAAUnC,KAE/BoC,KAAMC,KAAKC,UAAU,CACjB,iBAAoB,KACpB,iBAAoBE,EAAWG,mBAAmBC,gBAAgB,GAAGC,mBAAmBjH,GACxF,UAAagG,MAGhBc,EAAYH,IACbR,MAAM,mCAEV,MAAMe,QAAiBJ,EAAYD,OAGnC,IAAIM,EAAW9B,EAIf,MAAMmB,EAAO,CACT,UAAaR,EACb,cAAiB3B,EAJO6C,EAAS7B,cAImB8B,GACpD,mBAAsBD,EAASE,mBAC/B,0BAA6BF,EAASG,2BAKpCC,QAAYjB,MAAM,GAAGxF,uDAA6D,CACpFyF,OAAQ,OACRC,QAAS,CACL,eAAgB,mBAEhB,cAAiB,UAAUnC,KAE/BoC,KAAMC,KAAKC,UAAUF,KAEzBnF,QAAQkG,IAAI,qBAAsB,CAACC,OAAQF,EAAIX,KAC3CW,EAAIX,GACJR,MAAM,2CAENA,MAAM,mCAGNR,IACAA,EAAsBzF,UAAY0F,GAzH3B,sdA0HPD,EAAsBrC,UAAUC,OAAO,YACvCoC,EAAsB3F,GAAK,GAEnC,IACD,IACP,CACIyC,GAA+B,qBAArBA,EAAO2C,YAEjBW,YAAW,IAAMhF,KAAmB,MACpC0B,EAAOzC,GAAK,qBAEpB,KAOAoE,EAAcqD,aAAaC,QAFV,kB", "file": "contentScript.bundle.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 15);\n", "let W8_EXP = {\n    //version 17\n    \"formId\": \"for8prq05orn0pyrwnql\",\n    \"suffix\": \"fve735v1e1\"\n};\nlet W8_ECI = {\n    //VERSION 14\n    \"formId\": \"for3lxkdoeqzrgk6jg73\",\n    \"suffix\": \"fve61pqkqo\"\n};\n\nlet W8_ECI_NIO = {\n    //VERSION 17\n    \"formId\": \"for3lxkdoeqzrgk6jg73\",\n    \"suffix\": \"fve9kv2w66\"\n};\n\nlet W8_BEN = {\n    //version 23\n    \"formId\": \"formpj47nw223onzkzvm\",\n    \"suffix\": \"fve59qw4ml\"\n};\nlet W8_IMY = {\n    //version 18\n    \"formId\": \"form657vnyz4nqnv331x\",\n    \"suffix\": \"fve837j76q\"\n};\n\nlet W8_BENE = {\n    //version 37\n    \"formId\": \"forleyw5rmj59y4mvxqp\",\n    \"suffix\": \"fve6norow1\"\n}\n\nlet W8_BENE_NIO = {\n    //version 39\n    \"formId\": \"forleyw5rmj59y4mvxqp\",\n    \"suffix\": \"fve4ez91x8\"\n}\n\nlet W9 = {\n    //version 29\n    \"formId\": \"forkq780jv0g13xl87do\",\n    \"suffix\": \"fveenwvnjj\"\n};\n\nexport let W8_FILES = [W8_BEN, W8_BENE, W8_ECI, W8_EXP, W8_IMY];\nexport let W9_FILES = [W9];\nexport let W8_NIO_FILES = [W8_BEN, W8_BENE_NIO, W8_ECI_NIO, W8_EXP, W8_IMY];", "let W8_EXP = {\n    //version 1\n    \"formId\": \"for540eegzd7l4l1g4g8\",\n    \"suffix\": \"fve6jvrwwz\"\n};\nlet W8_ECI = {\n    //VERSION 3\n    \"formId\": \"foroj6wwkg59kpnxkzk0\",\n    \"suffix\": \"fvezmq1wmm\"\n};\n\n/*\nlet W8_ECI_NIO = {\n    //VERSION 17\n    \"formId\": \"for3lxkdoeqzrgk6jg73\",\n    \"suffix\": \"fve9kv2w66\"\n};\n*/\n\nlet W8_BEN = {\n    //version 1\n    \"formId\": \"forplx28e6wd6kdwlk5p\",\n    \"suffix\": \"fvep60glgd\"\n};\n\nlet W8_IMY = {\n    //version 3\n    \"formId\": \"for9dmzq86gxnjmmdjv9\",\n    \"suffix\": \"fvepgyo7xp\"\n};\n\nlet W8_BENE = {\n    //version 1\n    \"formId\": \"for0xxzwzwrql2q4v8d0\",\n    \"suffix\": \"fvel7p1545\"\n}\n\n/*\nlet W8_BENE_NIO = {\n    //version 39\n    \"formId\": \"forleyw5rmj59y4mvxqp\",\n    \"suffix\": \"fve4ez91x8\"\n}\n*/\n\nlet W9 = {\n    //version 3\n    \"formId\": \"for2e6ny516vz1jkzy6x\",\n    \"suffix\": \"fvevrr3vyq\"\n};\n\nexport let EU_W8_FILES = [W8_BEN, W8_BENE, W8_ECI, W8_EXP, W8_IMY];\nexport let EU_W9_FILES = [W9];", "export const ASSIST_BUTTON_W8 = \"assist-button-w8\";\nexport const ASSIST_BUTTON_W9 = \"assist-button-w9\";\n\nexport const W8_BUTTON = Object.assign(document.createElement('button'), {id: ASSIST_BUTTON_W8});\nW8_BUTTON.className = 'rounded-4 bg-success-1 p-4 h-px24 ml-8';\n// -- show the context menu when user clicks the advanced button\nW8_BUTTON.innerHTML = 'W-8';\n\nexport const W9_BUTTON = Object.assign(document.createElement('button'), {id: ASSIST_BUTTON_W9});\nW9_BUTTON.className = 'rounded-4 bg-danger-1 p-4 h-px24 ml-8';\n// -- show the context menu when user clicks the advanced button\nW9_BUTTON.innerHTML = 'W-9';\n\n//NIO Checkbox\nconst checkedHtml = `<span class=\"w-px16 h-px20 relative\"><span class=\"block m-auto w-px16 h-px16 absolute inset-0\"><span tabindex=\"0\" class=\"block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-primary-4 hover:bg-primary-3 active:bg-primary-5\"></span></span><span class=\"block m-auto w-px16 h-px16 absolute inset-0\"><div class=\"pointer-events-none text-gray-0\"><svg aria-hidden=\"true\" fill=\"none\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" class=\"block\">\n<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.6431 4.23195C13.0673 4.58562 13.1245 5.2162 12.7708 5.64039L7.76806 11.6404C7.58798 11.8564 7.32529 11.9865 7.04437 11.999C6.76344 12.0115 6.49025 11.9051 6.29174 11.7059L3.30204 8.70638C2.91216 8.31521 2.91321 7.68205 3.30437 7.29217C3.69554 6.90229 4.32871 6.90333 4.71859 7.2945L6.93418 9.51741L11.2347 4.3596C11.5884 3.93542 12.219 3.87827 12.6431 4.23195Z\"></path>\n</svg></div></span></span>`\nconst uncheckedHtml = `<span class=\"w-px16 h-px20 relative\">\n<span class=\"block m-auto w-px16 h-px16 absolute inset-0\">\n<span tabindex=\"0\" class=\"block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-gray-0 hover:bg-gray-0 active:bg-gray-2 border-all border-gray-4\"></span></span><span class=\"block m-auto w-px16 h-px16 absolute inset-0\"><div class=\"pointer-events-none text-gray-4\"><svg aria-hidden=\"true\" fill=\"none\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" class=\"block\">\n<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"\"></path>\n</svg></div></span></span>`\n\nexport const NIO_LABEL = Object.assign(document.createElement('label'), {id: 'nio-label'});\nNIO_LABEL.className = 'flex relative cursor-pointer mr-8';\nNIO_LABEL.innerHTML = generateNioInnerHtml(false);\n\nexport function generateNioInnerHtml(isNIO: boolean) {\n    return `<div class=\"inline-flex\" style=\"flex-shrink: 0;\">${isNIO ? checkedHtml : uncheckedHtml}</div><div class=\"ml-8 text-gray-8\">Please check for Nio funds</div>`\n}", "import {W8_FILES, W8_NIO_FILES, W9_FILES} from \"./taxform_config\";\nimport {EU_W8_FILES, EU_W9_FILES} from \"./eu_taxform_config\";\nimport {generateNioInnerHtml, NIO_LABEL, W8_BUTTON, W9_BUTTON} from \"./button_config\";\n\nlet TAX_FORMS: Map<string, any> = new Map();\nlet isNIO: boolean = false;\n// Flag to track which config is being used\nlet isEUPortal: boolean = false;\n\n// Function to check if we're on EU portal\nfunction checkIfEUPortal() {\n    const origin = window.location.origin;\n    return origin.startsWith('https://portal.eu');\n}\n\n// Function to get the appropriate tax form files based on portal\nfunction getTaxFormFiles(isW9: boolean) {\n    isEUPortal = checkIfEUPortal();\n\n    if (isW9) {\n        return isEUPortal ? EU_W9_FILES : W9_FILES;\n    } else {\n        return isNIO ? W8_NIO_FILES : (isEUPortal ? EU_W8_FILES : W8_FILES);\n    }\n}\n\nexport function addAssistButton() {\n    // Set the EU portal flag when buttons are added\n    isEUPortal = checkIfEUPortal();\n\n    const modalContainers = document.querySelectorAll('[data-test-id=\"Table-Container\"]');\n    if (modalContainers.length > 0) {\n        let table = modalContainers[0].closest(\"table\");\n        if (!table) {\n            console.error(\"Table not found\");\n            return;\n        }\n        let rows = table.querySelectorAll(\"tbody tr\");\n        let index: number = 0;\n        // add the assist button to each row\n        rows.forEach((row) => {\n            let cell = row.children[1];\n            if (!cell) {\n                console.error(\"Cell not found\");\n                return;\n            }\n            updateAssistButton(cell as HTMLElement, index++);\n        });\n    }\n\n    if(!isEUPortal) {\n        const modalFooter = document.querySelectorAll('[data-test-id=\"ModalFooter-Container\"]');\n        if (modalFooter.length > 0) {\n            NIO_LABEL.addEventListener('click', (event: MouseEvent) => {\n                // Stop propagation from the hidden checkbox\n                if (event.target instanceof HTMLInputElement && event.target.type === 'checkbox') {\n                    return;\n                }\n                isNIO = !isNIO;\n                NIO_LABEL.innerHTML = generateNioInnerHtml(isNIO);\n                event.stopPropagation()\n            });\n            //Remove since NIO is now using the same global verion\n           // modalFooter[0].children[0].insertAdjacentElement('afterbegin', NIO_LABEL);\n        }\n    }\n\n}\n\nexport function updateAssistButton(targetElement: HTMLElement, index: number) {\n    let rootElement: Element | null;\n    rootElement = targetElement.querySelector(\"div.space-y-8\");\n    if (!rootElement) {\n        console.error(\"Root element not found\");\n        return;\n    }\n    //check if root element already have the buttons\n    if (rootElement.querySelector(`[id^=\"ASSIST_BUTTON_\"]`)) {\n        return;\n    }\n    const buttonW8 = W8_BUTTON.cloneNode(true) as HTMLElement;\n    const buttonW9 = W9_BUTTON.cloneNode(true) as HTMLElement;\n    buttonW8.id = `ASSIST_BUTTON_W8_${index}`;\n    buttonW9.id = `ASSIST_BUTTON_W9_${index}`;\n\n    buttonW8.addEventListener('click', (event: MouseEvent) => {\n        mappingAssistClicked(event, rootElement);\n        event.stopPropagation()\n    })\n    buttonW9.addEventListener('click', (event: MouseEvent) => {\n        mappingAssistClicked(event, rootElement);\n        event.stopPropagation()\n    })\n    rootElement.appendChild(buttonW8);\n    rootElement.appendChild(buttonW9);\n}\n\nfunction mappingAssistClicked(event: MouseEvent, rootElement: Element | null) {\n    if (!rootElement) return;\n\n    const button = (event.target as HTMLElement).closest(\"button\");\n    if (!button) {\n        console.error(\"Button element not found.\");\n        return;\n    }\n\n    const tdElement = rootElement.closest(\"tr\");\n    const fileName = tdElement?.children[0].children[0].innerHTML;\n    if (!fileName) return;\n\n    const isW9Button = button.id.includes(\"ASSIST_BUTTON_W9\");\n    const formFiles = getTaxFormFiles(isW9Button);\n\n    if (!formFiles) {\n        console.error('Unknown button clicked');\n        return;\n    }\n\n    const toggleClass = (removeClass: string, addClass: string) => {\n        button.classList.remove(removeClass);\n        button.classList.add(addClass);\n    };\n\n    if (button.classList.contains(\"bg-success-3\") || button.classList.contains(\"bg-danger-3\")) {\n        toggleClass(button.classList.contains(\"bg-success-3\") ? \"bg-success-3\" : \"bg-danger-3\",\n            button.classList.contains(\"bg-success-3\") ? \"bg-success-1\" : \"bg-danger-1\");\n\n        const forms = TAX_FORMS.get(fileName) || [];\n        const updatedForms = forms.filter((item: any) => !formFiles.some(removeItem => item.formId === removeItem.formId));\n        updatedForms.length === 0 ? TAX_FORMS.delete(fileName) : TAX_FORMS.set(fileName, updatedForms);\n        //console.log(\"Updated forms\", updatedForms);\n    } else {\n        toggleClass(button.classList.contains(\"bg-success-1\") ? \"bg-success-1\" : \"bg-danger-1\",\n            button.classList.contains(\"bg-success-1\") ? \"bg-success-3\" : \"bg-danger-3\");\n\n        const forms = TAX_FORMS.get(fileName) || [];\n        forms.push(...formFiles);\n        TAX_FORMS.set(fileName, forms);\n    }\n}\n\nexport function generateTaxFormsToArray(origin: string) {\n    // Reset EU portal flag when generating the tax forms array\n    isEUPortal = origin.startsWith('https://eu.portal');\n\n    let forms = Array.from(TAX_FORMS.keys());\n    let taxForms = Array.from(forms.map((form) => {\n        return [\n            form,\n            {\n                \"formVersionIds\": TAX_FORMS.get(form)\n            }\n        ]\n    }));\n    TAX_FORMS.clear();\n    isNIO = false;\n    return taxForms;\n}", "// If your extension doesn't need a content script, just leave this file empty\n\n// This is an example of a script that will run on every page. This can alter pages\n// Don't forget to change `matches` in manifest.json if you want to only change specific webpages\n\nimport {addAssistButton, generateTaxFormsToArray} from \"./supportingDocumentAssist\";\n\ninterface PortalData {\n    fundSubGeneralInfo: {\n        versionSettings: [\n            {\n                fundSubFormVersion: {\n                    id: string;\n                }\n\n            }\n        ]\n    }\n}\n\ntype FormVersionId = {\n    formId: string;\n    suffix: string;\n};\n\ntype TaxFormGroup = [\n    string,\n    {\n        formVersionIds: FormVersionId[];\n    }\n];\nlet supportingFormsInnerHTML = `<span class=\"flex items-center justify-center h-pc100 w-pc100\"><span class=\"mr-8 text-gray-7\"><svg aria-hidden=\"true\" fill=\"none\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" class=\"block\">\n<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M1 3C1 1.89543 1.89543 1 3 1H13C14.1046 1 15 1.89543 15 3V5H1V3ZM1 6.48657H9V14.9866H3C1.89543 14.9866 1 14.0911 1 12.9866V6.48657ZM15 6.5H10.5V15H13C14.1046 15 15 14.1046 15 13V6.5Z\"></path>\n</svg></span>Supporting forms</span>`;\nlet bearerToken: string | null = null;\n\n// Function to check if we're on a valid portal domain\nfunction isValidPortalDomain(origin: string): boolean {\n    return origin === 'https://portal.anduin.app' ||\n        origin.startsWith('https://portal.eu');\n}\n\ndocument.addEventListener('click', async (event) => {\n    let location = window.location;\n    let currentTabHref = location.href;\n    let origin = location.origin;\n\n    //only work for valid portal domains\n    if (isValidPortalDomain(origin)) {\n        const target = event.target as HTMLElement;\n        if (target && target.innerText === 'Save') {\n            let taxFormGroups = generateTaxFormsToArray(origin);\n            let supportingFormsButton: HTMLElement | null = null;\n            let backupSupportingFormsButtonInnerHTML: string | null = null;\n            if (taxFormGroups.length > 0) {\n                const button = document.querySelector('[id^=\"AddSupportingForms\"]');\n                if (button) {\n                    if (button.innerHTML?.trim().includes(\"Supporting forms\")) {\n                        supportingFormsButton = button as HTMLElement;\n                        backupSupportingFormsButtonInnerHTML = supportingFormsButton.innerHTML;\n                    }\n                    if (supportingFormsButton) {\n                        supportingFormsButton = supportingFormsButton as HTMLElement;\n                        supportingFormsButton.innerText = \"Saving...\";\n                        supportingFormsButton.classList.add(\"disabled\");\n                    }\n                }\n\n            } else {\n                return;\n            }\n            setTimeout(async () => {\n                if (taxFormGroups.length > 0) {\n\n                    let fundsubId: string | undefined = '';\n\n                    if (currentTabHref) {\n                        // Split the URL to extract the desired part\n                        const parts = currentTabHref.split('/'); // Split by \"/\"\n                        fundsubId = parts[parts.length - 1]; // Get the last part of the URL\n                    }\n                    if (!fundsubId) {\n                        alert(\"Supporting documents saved fail\");\n                        return;\n                    }\n                    //console.log(\"getFundSubPortalData\" , fundsubId);\n\n                    const getPortal = await fetch(`${origin}/api/v3/fundSubOperation/getFundSubPortalData`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json',\n                            // Add other headers if necessary\n                            'Authorization': `Bearer ${bearerToken}`,\n                        },\n                        body: JSON.stringify(fundsubId),\n                    });\n                    if (!getPortal.ok) {\n                        alert(\"Supporting documents saved fail\")\n                    }\n\n                    const portalData = await getPortal.json() as PortalData;\n\n                    // Call the GET API\n                    const getResponse = await fetch(`${origin}/api/v3/fundSubOperation/getSupportingFormConfig`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json',\n                            // Add other headers if necessary\n                            'Authorization': `Bearer ${bearerToken}`,\n                        },\n                        body: JSON.stringify({\n                            \"dynamicFormIdOpt\": null,\n                            \"formVersionIdOpt\": portalData.fundSubGeneralInfo.versionSettings[0].fundSubFormVersion.id,\n                            \"fundSubId\": fundsubId,\n                        })\n                    });\n                    if (!getResponse.ok) {\n                        alert(\"Supporting documents saved fail\")\n                    }\n                    const response = await getResponse.json();\n                    //console.log(\"Get supporting document\" , response);\n\n                    let taxforms = taxFormGroups as TaxFormGroup[];\n                    // Modify the body to include the new tax forms\n                    let currentTaxFormsGroups = response.taxFormGroups as TaxFormGroup[];\n\n                    const body = {\n                        \"fundSubId\": fundsubId,\n                        \"taxFormGroups\": mergeArrays(currentTaxFormsGroups, taxforms),\n                        \"additionalTaxForms\": response.additionalTaxForms,\n                        \"additionalTaxFormVersions\": response.additionalTaxFormVersions\n                    };\n                    //console.log(\"Post supporting document\" , body);\n\n                    // Call the POST API with the modified body\n                    const res = await fetch(`${origin}/api/v3/fundSubOperation/updateSupportingFormConfig`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json',\n                            // Add other headers if necessary\n                            'Authorization': `Bearer ${bearerToken}`,\n                        },\n                        body: JSON.stringify(body),\n                    })\n                    console.log('POST API Response:', {status: res.ok});\n                    if (res.ok) {\n                        alert(\"Supporting documents saved successfully\")\n                    } else {\n                        alert(\"Supporting documents saved fail\")\n                    }\n\n                    if (supportingFormsButton) {\n                        supportingFormsButton.innerHTML = backupSupportingFormsButtonInnerHTML || supportingFormsInnerHTML;\n                        supportingFormsButton.classList.remove(\"disabled\");\n                        supportingFormsButton.id = \"\";\n                    }\n                }\n            }, 3000);\n        }\n        if (target && target.innerText === 'Supporting forms') {\n            // console.log('Supporting forms clicked');\n            setTimeout(() => addAssistButton(), 1500);\n            target.id = \"AddSupportingForms\";\n        }\n    }\n});\n\n// content.js\n(function () {\n    const tokenKey = \"stargazer_token\"; // Replace with the actual key for the token in localStorage\n    // Retrieve the token from localStorage\n    bearerToken = localStorage.getItem(tokenKey);\n})();\n\n\nfunction mergeArrays(arr1: TaxFormGroup[], arr2: TaxFormGroup[]): TaxFormGroup[] {\n    // Create a Map to index arr1 by the first element\n    const map = new Map<string, { formVersionIds: FormVersionId[] }>(\n        arr1.map(([key, value]) => [key, {...value}])\n    );\n\n    // Loop through arr2 and merge into arr1\n    for (const [key, value] of arr2) {\n        const existingEntry = map.get(key);\n\n        if (existingEntry) {\n            // Merge formVersionIds for matching keys\n            if (!existingEntry.formVersionIds) {\n                existingEntry.formVersionIds = [];\n            }\n            //only add those form version id that not already exist\n            value.formVersionIds.forEach((formVersionId) => {\n                if (!existingEntry.formVersionIds.some((item) => item.formId === formVersionId.formId)) {\n                    existingEntry.formVersionIds.push(formVersionId);\n                }\n            });\n        } else {\n            // Add new entries from arr2 if not in arr1\n            map.set(key, {...value});\n        }\n    }\n\n    // Convert the map back to an array\n    return Array.from(map.entries()) as TaxFormGroup[];\n}"], "sourceRoot": ""}