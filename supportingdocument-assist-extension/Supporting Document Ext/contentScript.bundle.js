!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=15)}({15:function(e,t,n){"use strict";n.r(t);let o={formId:"for8prq05orn0pyrwnql",suffix:"fve735v1e1"},r={formId:"formpj47nw223onzkzvm",suffix:"fve59qw4ml"},s={formId:"form657vnyz4nqnv331x",suffix:"fve837j76q"},i=[r,{formId:"forleyw5rmj59y4mvxqp",suffix:"fve6norow1"},{formId:"for3lxkdoeqzrgk6jg73",suffix:"fve61pqkqo"},o,s],a=[{formId:"forkq780jv0g13xl87do",suffix:"fveenwvnjj"}],l=[r,{formId:"forleyw5rmj59y4mvxqp",suffix:"fve4ez91x8"},{formId:"for3lxkdoeqzrgk6jg73",suffix:"fve9kv2w66"},o,s],d=[{formId:"forplx28e6wd6kdwlk5p",suffix:"fvep60glgd"},{formId:"for0xxzwzwrql2q4v8d0",suffix:"fvel7p1545"},{formId:"foroj6wwkg59kpnxkzk0",suffix:"fvezmq1wmm"},{formId:"for540eegzd7l4l1g4g8",suffix:"fve6jvrwwz"},{formId:"for9dmzq86gxnjmmdjv9",suffix:"fvepgyo7xp"}],c=[{formId:"for2e6ny516vz1jkzy6x",suffix:"fvevrr3vyq"}];const u=Object.assign(document.createElement("button"),{id:"assist-button-w8"});u.className="rounded-4 bg-success-1 p-4 h-px24 ml-8",u.innerHTML="W-8";const f=Object.assign(document.createElement("button"),{id:"assist-button-w9"});f.className="rounded-4 bg-danger-1 p-4 h-px24 ml-8",f.innerHTML="W-9";const p=Object.assign(document.createElement("label"),{id:"nio-label"});function g(e){return`<div class="inline-flex" style="flex-shrink: 0;">${e?'<span class="w-px16 h-px20 relative"><span class="block m-auto w-px16 h-px16 absolute inset-0"><span tabindex="0" class="block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-primary-4 hover:bg-primary-3 active:bg-primary-5"></span></span><span class="block m-auto w-px16 h-px16 absolute inset-0"><div class="pointer-events-none text-gray-0"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">\n<path fill="currentColor" fill-rule="evenodd" d="M12.6431 4.23195C13.0673 4.58562 13.1245 5.2162 12.7708 5.64039L7.76806 11.6404C7.58798 11.8564 7.32529 11.9865 7.04437 11.999C6.76344 12.0115 6.49025 11.9051 6.29174 11.7059L3.30204 8.70638C2.91216 8.31521 2.91321 7.68205 3.30437 7.29217C3.69554 6.90229 4.32871 6.90333 4.71859 7.2945L6.93418 9.51741L11.2347 4.3596C11.5884 3.93542 12.219 3.87827 12.6431 4.23195Z"></path>\n</svg></div></span></span>':'<span class="w-px16 h-px20 relative">\n<span class="block m-auto w-px16 h-px16 absolute inset-0">\n<span tabindex="0" class="block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-gray-0 hover:bg-gray-0 active:bg-gray-2 border-all border-gray-4"></span></span><span class="block m-auto w-px16 h-px16 absolute inset-0"><div class="pointer-events-none text-gray-4"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">\n<path fill="currentColor" fill-rule="evenodd" d=""></path>\n</svg></div></span></span>'}</div><div class="ml-8 text-gray-8">Please check for Nio funds</div>`}p.className="flex relative cursor-pointer mr-8",p.innerHTML=g(!1);let m=new Map,v=!1,b=!1;function x(){return window.location.origin.startsWith("https://portal.eu")}function h(){b=x();const e=document.querySelectorAll('[data-test-id="Table-Container"]');if(e.length>0){let t=e[0].closest("table");if(!t)return void console.error("Table not found");let n=t.querySelectorAll("tbody tr"),o=0;n.forEach((e=>{let t=e.children[1];t?function(e,t){let n;if(n=e.querySelector("div.space-y-8"),!n)return void console.error("Root element not found");if(n.querySelector('[id^="ASSIST_BUTTON_"]'))return;const o=u.cloneNode(!0),r=f.cloneNode(!0);o.id=`ASSIST_BUTTON_W8_${t}`,r.id=`ASSIST_BUTTON_W9_${t}`,o.addEventListener("click",(e=>{y(e,n),e.stopPropagation()})),r.addEventListener("click",(e=>{y(e,n),e.stopPropagation()})),n.appendChild(o),n.appendChild(r)}(t,o++):console.error("Cell not found")}))}if(!b){document.querySelectorAll('[data-test-id="ModalFooter-Container"]').length>0&&p.addEventListener("click",(e=>{e.target instanceof HTMLInputElement&&"checkbox"===e.target.type||(v=!v,p.innerHTML=g(v),e.stopPropagation())}))}}function y(e,t){if(!t)return;const n=e.target.closest("button");if(!n)return void console.error("Button element not found.");const o=t.closest("tr"),r=null===o||void 0===o?void 0:o.children[0].children[0].innerHTML;if(!r)return;const s=n.id.includes("ASSIST_BUTTON_W9"),u=(f=s,b=x(),f?b?c:a:v?l:b?d:i);var f;if(!u)return void console.error("Unknown button clicked");const p=(e,t)=>{n.classList.remove(e),n.classList.add(t)};if(n.classList.contains("bg-success-3")||n.classList.contains("bg-danger-3")){p(n.classList.contains("bg-success-3")?"bg-success-3":"bg-danger-3",n.classList.contains("bg-success-3")?"bg-success-1":"bg-danger-1");const e=(m.get(r)||[]).filter((e=>!u.some((t=>e.formId===t.formId))));0===e.length?m.delete(r):m.set(r,e)}else{p(n.classList.contains("bg-success-1")?"bg-success-1":"bg-danger-1",n.classList.contains("bg-success-1")?"bg-success-3":"bg-danger-3");const e=m.get(r)||[];e.push(...u),m.set(r,e)}}let S=null;function w(e,t){const n=new Map(e.map((e=>{let[t,n]=e;return[t,{...n}]})));for(const[o,r]of t){const e=n.get(o);e?(e.formVersionIds||(e.formVersionIds=[]),r.formVersionIds.forEach((t=>{e.formVersionIds.some((e=>e.formId===t.formId))||e.formVersionIds.push(t)}))):n.set(o,{...r})}return Array.from(n.entries())}document.addEventListener("click",(async e=>{let t=window.location,n=t.href,o=t.origin;if(function(e){return"https://portal.anduin.app"===e||e.startsWith("https://portal.eu")}(o)){const t=e.target;if(t&&"Save"===t.innerText){let e=function(e){b=e.startsWith("https://eu.portal");let t=Array.from(m.keys()),n=Array.from(t.map((e=>[e,{formVersionIds:m.get(e)}])));return m.clear(),v=!1,n}(o),t=null,s=null;if(!(e.length>0))return;{const e=document.querySelector('[id^="AddSupportingForms"]');var r;if(e)null!==(r=e.innerHTML)&&void 0!==r&&r.trim().includes("Supporting forms")&&(t=e,s=t.innerHTML),t&&(t.innerText="Saving...",t.classList.add("disabled"))}setTimeout((async()=>{if(e.length>0){let r="";if(n){const e=n.split("/");r=e[e.length-1]}if(!r)return void alert("Supporting documents saved fail");const i=await fetch(`${o}/api/v3/fundSubOperation/getFundSubPortalData`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${S}`},body:JSON.stringify(r)});i.ok||alert("Supporting documents saved fail");const a=await i.json(),l=await fetch(`${o}/api/v3/fundSubOperation/getSupportingFormConfig`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${S}`},body:JSON.stringify({dynamicFormIdOpt:null,formVersionIdOpt:a.fundSubGeneralInfo.versionSettings[0].fundSubFormVersion.id,fundSubId:r})});l.ok||alert("Supporting documents saved fail");const d=await l.json();let c=e;const u={fundSubId:r,taxFormGroups:w(d.taxFormGroups,c),additionalTaxForms:d.additionalTaxForms,additionalTaxFormVersions:d.additionalTaxFormVersions},f=await fetch(`${o}/api/v3/fundSubOperation/updateSupportingFormConfig`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${S}`},body:JSON.stringify(u)});console.log("POST API Response:",{status:f.ok}),f.ok?alert("Supporting documents saved successfully"):alert("Supporting documents saved fail"),t&&(t.innerHTML=s||'<span class="flex items-center justify-center h-pc100 w-pc100"><span class="mr-8 text-gray-7"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">\n<path fill="currentColor" fill-rule="evenodd" d="M1 3C1 1.89543 1.89543 1 3 1H13C14.1046 1 15 1.89543 15 3V5H1V3ZM1 6.48657H9V14.9866H3C1.89543 14.9866 1 14.0911 1 12.9866V6.48657ZM15 6.5H10.5V15H13C14.1046 15 15 14.1046 15 13V6.5Z"></path>\n</svg></span>Supporting forms</span>',t.classList.remove("disabled"),t.id="")}}),3e3)}t&&"Supporting forms"===t.innerText&&(setTimeout((()=>h()),1500),t.id="AddSupportingForms")}})),S=localStorage.getItem("stargazer_token")}});
//# sourceMappingURL=contentScript.bundle.js.map