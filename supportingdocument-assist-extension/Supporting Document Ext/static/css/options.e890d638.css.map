{"version": 3, "sources": ["webpack://src/options/Options.css"], "names": [], "mappings": "AAAA,KACE,QACF,CAEA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YACE,wBAAyB,CACzB,gBAAiB,CACjB,YAAa,CACb,YAAa,CACb,qBAAsB,CACtB,kBAAmB,CACnB,sBAAuB,CACvB,4BAA6B,CAC7B,UACF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,qBACE,WACF,CAEA,OACE,iBACF,CAEA,OACE,kBACF,CAEA,OACE,mBACF,CAEA,OACE,iBACF,CAEA,OACE,kBACF", "file": "options.e890d638.css", "sourcesContent": ["body {\n  margin: 0;\n}\n\n.App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  padding: 30px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.upload-notification {\n  color: green;\n}\n\n.pl-20 {\n  padding-left: 20px;\n}\n\n.pr-20 {\n  padding-right: 20px;\n}\n\n.pb-10 {\n  padding-bottom: 10px;\n}\n\n.mr-10 {\n  margin-right: 10px;\n}\n\n.mb-10 {\n  margin-bottom: 10px;\n}"]}