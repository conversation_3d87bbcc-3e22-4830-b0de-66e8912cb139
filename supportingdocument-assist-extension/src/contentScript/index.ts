// If your extension doesn't need a content script, just leave this file empty

// This is an example of a script that will run on every page. This can alter pages
// Don't forget to change `matches` in manifest.json if you want to only change specific webpages

import {addAssistButton, generateTaxFormsToArray} from "./supportingDocumentAssist";

interface PortalData {
    fundSubGeneralInfo: {
        versionSettings: [
            {
                fundSubFormVersion: {
                    id: string;
                }

            }
        ]
    }
}

type FormVersionId = {
    formId: string;
    suffix: string;
};

type TaxFormGroup = [
    string,
    {
        formVersionIds: FormVersionId[];
    }
];
let supportingFormsInnerHTML = `<span class="flex items-center justify-center h-pc100 w-pc100"><span class="mr-8 text-gray-7"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">
<path fill="currentColor" fill-rule="evenodd" d="M1 3C1 1.89543 1.89543 1 3 1H13C14.1046 1 15 1.89543 15 3V5H1V3ZM1 6.48657H9V14.9866H3C1.89543 14.9866 1 14.0911 1 12.9866V6.48657ZM15 6.5H10.5V15H13C14.1046 15 15 14.1046 15 13V6.5Z"></path>
</svg></span>Supporting forms</span>`;
let bearerToken: string | null = null;

// Function to check if we're on a valid portal domain
function isValidPortalDomain(origin: string): boolean {
    return origin === 'https://portal.anduin.app' ||
        origin.startsWith('https://portal.eu');
}

document.addEventListener('click', async (event) => {
    let location = window.location;
    let currentTabHref = location.href;
    let origin = location.origin;

    //only work for valid portal domains
    if (isValidPortalDomain(origin)) {
        const target = event.target as HTMLElement;
        if (target && target.innerText === 'Save') {
            let taxFormGroups = generateTaxFormsToArray(origin);
            let supportingFormsButton: HTMLElement | null = null;
            let backupSupportingFormsButtonInnerHTML: string | null = null;
            if (taxFormGroups.length > 0) {
                const button = document.querySelector('[id^="AddSupportingForms"]');
                if (button) {
                    if (button.innerHTML?.trim().includes("Supporting forms")) {
                        supportingFormsButton = button as HTMLElement;
                        backupSupportingFormsButtonInnerHTML = supportingFormsButton.innerHTML;
                    }
                    if (supportingFormsButton) {
                        supportingFormsButton = supportingFormsButton as HTMLElement;
                        supportingFormsButton.innerText = "Saving...";
                        supportingFormsButton.classList.add("disabled");
                    }
                }

            } else {
                return;
            }
            setTimeout(async () => {
                if (taxFormGroups.length > 0) {

                    let fundsubId: string | undefined = '';

                    if (currentTabHref) {
                        // Split the URL to extract the desired part
                        const parts = currentTabHref.split('/'); // Split by "/"
                        fundsubId = parts[parts.length - 1]; // Get the last part of the URL
                    }
                    if (!fundsubId) {
                        alert("Supporting documents saved fail");
                        return;
                    }
                    //console.log("getFundSubPortalData" , fundsubId);

                    const getPortal = await fetch(`${origin}/api/v3/fundSubOperation/getFundSubPortalData`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            // Add other headers if necessary
                            'Authorization': `Bearer ${bearerToken}`,
                        },
                        body: JSON.stringify(fundsubId),
                    });
                    if (!getPortal.ok) {
                        alert("Supporting documents saved fail")
                    }

                    const portalData = await getPortal.json() as PortalData;

                    // Call the GET API
                    const getResponse = await fetch(`${origin}/api/v3/fundSubOperation/getSupportingFormConfig`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            // Add other headers if necessary
                            'Authorization': `Bearer ${bearerToken}`,
                        },
                        body: JSON.stringify({
                            "dynamicFormIdOpt": null,
                            "formVersionIdOpt": portalData.fundSubGeneralInfo.versionSettings[0].fundSubFormVersion.id,
                            "fundSubId": fundsubId,
                        })
                    });
                    if (!getResponse.ok) {
                        alert("Supporting documents saved fail")
                    }
                    const response = await getResponse.json();
                    //console.log("Get supporting document" , response);

                    let taxforms = taxFormGroups as TaxFormGroup[];
                    // Modify the body to include the new tax forms
                    let currentTaxFormsGroups = response.taxFormGroups as TaxFormGroup[];

                    const body = {
                        "fundSubId": fundsubId,
                        "taxFormGroups": mergeArrays(currentTaxFormsGroups, taxforms),
                        "additionalTaxForms": response.additionalTaxForms,
                        "additionalTaxFormVersions": response.additionalTaxFormVersions
                    };
                    //console.log("Post supporting document" , body);

                    // Call the POST API with the modified body
                    const res = await fetch(`${origin}/api/v3/fundSubOperation/updateSupportingFormConfig`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            // Add other headers if necessary
                            'Authorization': `Bearer ${bearerToken}`,
                        },
                        body: JSON.stringify(body),
                    })
                    console.log('POST API Response:', {status: res.ok});
                    if (res.ok) {
                        alert("Supporting documents saved successfully")
                    } else {
                        alert("Supporting documents saved fail")
                    }

                    if (supportingFormsButton) {
                        supportingFormsButton.innerHTML = backupSupportingFormsButtonInnerHTML || supportingFormsInnerHTML;
                        supportingFormsButton.classList.remove("disabled");
                        supportingFormsButton.id = "";
                    }
                }
            }, 3000);
        }
        if (target && target.innerText === 'Supporting forms') {
            // console.log('Supporting forms clicked');
            setTimeout(() => addAssistButton(), 1500);
            target.id = "AddSupportingForms";
        }
    }
});

// content.js
(function () {
    const tokenKey = "stargazer_token"; // Replace with the actual key for the token in localStorage
    // Retrieve the token from localStorage
    bearerToken = localStorage.getItem(tokenKey);
})();


function mergeArrays(arr1: TaxFormGroup[], arr2: TaxFormGroup[]): TaxFormGroup[] {
    // Create a Map to index arr1 by the first element
    const map = new Map<string, { formVersionIds: FormVersionId[] }>(
        arr1.map(([key, value]) => [key, {...value}])
    );

    // Loop through arr2 and merge into arr1
    for (const [key, value] of arr2) {
        const existingEntry = map.get(key);

        if (existingEntry) {
            // Merge formVersionIds for matching keys
            if (!existingEntry.formVersionIds) {
                existingEntry.formVersionIds = [];
            }
            //only add those form version id that not already exist
            value.formVersionIds.forEach((formVersionId) => {
                if (!existingEntry.formVersionIds.some((item) => item.formId === formVersionId.formId)) {
                    existingEntry.formVersionIds.push(formVersionId);
                }
            });
        } else {
            // Add new entries from arr2 if not in arr1
            map.set(key, {...value});
        }
    }

    // Convert the map back to an array
    return Array.from(map.entries()) as TaxFormGroup[];
}