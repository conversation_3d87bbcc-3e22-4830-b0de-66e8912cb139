export const ASSIST_BUTTON_W8 = "assist-button-w8";
export const ASSIST_BUTTON_W9 = "assist-button-w9";

export const W8_BUTTON = Object.assign(document.createElement('button'), {id: ASSIST_BUTTON_W8});
W8_BUTTON.className = 'rounded-4 bg-success-1 p-4 h-px24 ml-8';
// -- show the context menu when user clicks the advanced button
W8_BUTTON.innerHTML = 'W-8';

export const W9_BUTTON = Object.assign(document.createElement('button'), {id: ASSIST_BUTTON_W9});
W9_BUTTON.className = 'rounded-4 bg-danger-1 p-4 h-px24 ml-8';
// -- show the context menu when user clicks the advanced button
W9_BUTTON.innerHTML = 'W-9';

//NIO Checkbox
const checkedHtml = `<span class="w-px16 h-px20 relative"><span class="block m-auto w-px16 h-px16 absolute inset-0"><span tabindex="0" class="block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-primary-4 hover:bg-primary-3 active:bg-primary-5"></span></span><span class="block m-auto w-px16 h-px16 absolute inset-0"><div class="pointer-events-none text-gray-0"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">
<path fill="currentColor" fill-rule="evenodd" d="M12.6431 4.23195C13.0673 4.58562 13.1245 5.2162 12.7708 5.64039L7.76806 11.6404C7.58798 11.8564 7.32529 11.9865 7.04437 11.999C6.76344 12.0115 6.49025 11.9051 6.29174 11.7059L3.30204 8.70638C2.91216 8.31521 2.91321 7.68205 3.30437 7.29217C3.69554 6.90229 4.32871 6.90333 4.71859 7.2945L6.93418 9.51741L11.2347 4.3596C11.5884 3.93542 12.219 3.87827 12.6431 4.23195Z"></path>
</svg></div></span></span>`
const uncheckedHtml = `<span class="w-px16 h-px20 relative">
<span class="block m-auto w-px16 h-px16 absolute inset-0">
<span tabindex="0" class="block w-px16 h-px16 rounded-3 focus:outline-light trns-o bg-gray-0 hover:bg-gray-0 active:bg-gray-2 border-all border-gray-4"></span></span><span class="block m-auto w-px16 h-px16 absolute inset-0"><div class="pointer-events-none text-gray-4"><svg aria-hidden="true" fill="none" width="16" height="16" viewBox="0 0 16 16" class="block">
<path fill="currentColor" fill-rule="evenodd" d=""></path>
</svg></div></span></span>`

export const NIO_LABEL = Object.assign(document.createElement('label'), {id: 'nio-label'});
NIO_LABEL.className = 'flex relative cursor-pointer mr-8';
NIO_LABEL.innerHTML = generateNioInnerHtml(false);

export function generateNioInnerHtml(isNIO: boolean) {
    return `<div class="inline-flex" style="flex-shrink: 0;">${isNIO ? checkedHtml : uncheckedHtml}</div><div class="ml-8 text-gray-8">Please check for Nio funds</div>`
}