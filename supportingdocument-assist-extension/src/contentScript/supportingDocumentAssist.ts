import {W8_FILES, W8_NIO_FILES, W9_FILES} from "./taxform_config";
import {EU_W8_FILES, EU_W9_FILES} from "./eu_taxform_config";
import {generateNioInnerHtml, NIO_LABEL, W8_BUTTON, W9_BUTTON} from "./button_config";

let TAX_FORMS: Map<string, any> = new Map();
let isNIO: boolean = false;
// Flag to track which config is being used
let isEUPortal: boolean = false;

// Function to check if we're on EU portal
function checkIfEUPortal() {
    const origin = window.location.origin;
    return origin.startsWith('https://portal.eu');
}

// Function to get the appropriate tax form files based on portal
function getTaxFormFiles(isW9: boolean) {
    isEUPortal = checkIfEUPortal();

    if (isW9) {
        return isEUPortal ? EU_W9_FILES : W9_FILES;
    } else {
        return isNIO ? W8_NIO_FILES : (isEUPortal ? EU_W8_FILES : W8_FILES);
    }
}

export function addAssistButton() {
    // Set the EU portal flag when buttons are added
    isEUPortal = checkIfEUPortal();

    const modalContainers = document.querySelectorAll('[data-test-id="Table-Container"]');
    if (modalContainers.length > 0) {
        let table = modalContainers[0].closest("table");
        if (!table) {
            console.error("Table not found");
            return;
        }
        let rows = table.querySelectorAll("tbody tr");
        let index: number = 0;
        // add the assist button to each row
        rows.forEach((row) => {
            let cell = row.children[1];
            if (!cell) {
                console.error("Cell not found");
                return;
            }
            updateAssistButton(cell as HTMLElement, index++);
        });
    }

    if(!isEUPortal) {
        const modalFooter = document.querySelectorAll('[data-test-id="ModalFooter-Container"]');
        if (modalFooter.length > 0) {
            NIO_LABEL.addEventListener('click', (event: MouseEvent) => {
                // Stop propagation from the hidden checkbox
                if (event.target instanceof HTMLInputElement && event.target.type === 'checkbox') {
                    return;
                }
                isNIO = !isNIO;
                NIO_LABEL.innerHTML = generateNioInnerHtml(isNIO);
                event.stopPropagation()
            });
            //Remove since NIO is now using the same global verion
           // modalFooter[0].children[0].insertAdjacentElement('afterbegin', NIO_LABEL);
        }
    }

}

export function updateAssistButton(targetElement: HTMLElement, index: number) {
    let rootElement: Element | null;
    rootElement = targetElement.querySelector("div.space-y-8");
    if (!rootElement) {
        console.error("Root element not found");
        return;
    }
    //check if root element already have the buttons
    if (rootElement.querySelector(`[id^="ASSIST_BUTTON_"]`)) {
        return;
    }
    const buttonW8 = W8_BUTTON.cloneNode(true) as HTMLElement;
    const buttonW9 = W9_BUTTON.cloneNode(true) as HTMLElement;
    buttonW8.id = `ASSIST_BUTTON_W8_${index}`;
    buttonW9.id = `ASSIST_BUTTON_W9_${index}`;

    buttonW8.addEventListener('click', (event: MouseEvent) => {
        mappingAssistClicked(event, rootElement);
        event.stopPropagation()
    })
    buttonW9.addEventListener('click', (event: MouseEvent) => {
        mappingAssistClicked(event, rootElement);
        event.stopPropagation()
    })
    rootElement.appendChild(buttonW8);
    rootElement.appendChild(buttonW9);
}

function mappingAssistClicked(event: MouseEvent, rootElement: Element | null) {
    if (!rootElement) return;

    const button = (event.target as HTMLElement).closest("button");
    if (!button) {
        console.error("Button element not found.");
        return;
    }

    const tdElement = rootElement.closest("tr");
    const fileName = tdElement?.children[0].children[0].innerHTML;
    if (!fileName) return;

    const isW9Button = button.id.includes("ASSIST_BUTTON_W9");
    const formFiles = getTaxFormFiles(isW9Button);

    if (!formFiles) {
        console.error('Unknown button clicked');
        return;
    }

    const toggleClass = (removeClass: string, addClass: string) => {
        button.classList.remove(removeClass);
        button.classList.add(addClass);
    };

    if (button.classList.contains("bg-success-3") || button.classList.contains("bg-danger-3")) {
        toggleClass(button.classList.contains("bg-success-3") ? "bg-success-3" : "bg-danger-3",
            button.classList.contains("bg-success-3") ? "bg-success-1" : "bg-danger-1");

        const forms = TAX_FORMS.get(fileName) || [];
        const updatedForms = forms.filter((item: any) => !formFiles.some(removeItem => item.formId === removeItem.formId));
        updatedForms.length === 0 ? TAX_FORMS.delete(fileName) : TAX_FORMS.set(fileName, updatedForms);
        //console.log("Updated forms", updatedForms);
    } else {
        toggleClass(button.classList.contains("bg-success-1") ? "bg-success-1" : "bg-danger-1",
            button.classList.contains("bg-success-1") ? "bg-success-3" : "bg-danger-3");

        const forms = TAX_FORMS.get(fileName) || [];
        forms.push(...formFiles);
        TAX_FORMS.set(fileName, forms);
    }
}

export function generateTaxFormsToArray(origin: string) {
    // Reset EU portal flag when generating the tax forms array
    isEUPortal = origin.startsWith('https://eu.portal');

    let forms = Array.from(TAX_FORMS.keys());
    let taxForms = Array.from(forms.map((form) => {
        return [
            form,
            {
                "formVersionIds": TAX_FORMS.get(form)
            }
        ]
    }));
    TAX_FORMS.clear();
    isNIO = false;
    return taxForms;
}