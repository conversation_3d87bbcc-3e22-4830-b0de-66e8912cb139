const fs = require('fs')
const { exec } = require("child_process");

const purge = (arr, elements) =>
    arr.filter(ele => !(elements.indexOf(ele) > -1))

const intersect = (ops1, ops2) =>
    ops2.filter(op => (ops1.indexOf(op) > -1))

const toIds = (parent) => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])

    if (!children) {
        return []
    }

    return Object
        .entries(children)
        .map(
            ([k, v]) => {
                return [k, ...toIds(v)]
            }
        )
        .flat()
}
const toIdsWithoutProperties = (children) => {
    if (JSON.stringify(children) == "{}") {
        return []
    }

    return Object
        .entries(children)
        .map(
            ([k, v]) => {
                return [k, ...toIdsWithoutProperties(v)]
            }
        )
        .flat()
}

const formatSchema = comps => comps.map(comp => {
    if (comp["enum"] && comp["enum"].length > 30) {
        comp["enum"].length = 3
    }

    return comp
})

const toSchemaObj = (parent, raw = false) => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])

    if (!children) {
        const removedName = { ...parent }

        delete removedName["name"]

        return removedName
    }

    const formattedChildren = raw ? [...children] : formatSchema([...children])

    const convertedChildren = Object.fromEntries(
        formattedChildren.map(
            child => {
                const removedName = { ...child }

                delete removedName["name"]

                return [child.name, { ...toSchemaObj(removedName) }]
            }
        )
    )

    const newParent = { ...parent }

    if (newParent["properties"]) {
        newParent["properties"] = convertedChildren
    }
    if (newParent["items"] && newParent["items"]["properties"]) {
        newParent["items"]["properties"] = convertedChildren
    }

    return newParent
}
const formatComment = (txt, limit) => {
    const line = txt.replaceAll("\n", " ")
    let cut = ""

    if (line.length <= limit) {
        cut = line
    } else {
        cut = line.substring(0, limit) + "..."
    }
    return cut
}

const readJsonFile = (p) =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(p, 'utf8', (err, data) => {
                if (err) {
                    reject(err)
                }

                try {
                    resolve(JSON.parse(data))
                } catch (e) {
                    reject(`${e}: ${p}`)
                }
            })
        }
    )


function sortByKey(array, key) {
    return array.sort(function (a, b) {
        var x = a[key];
        var y = b[key];
        return ((x < y) ? -1 : ((x > y) ? 1 : 0));
    });
}

const clone = (obj) => JSON.parse(JSON.stringify(obj))


const executeCommandPromise = (command) => new Promise((resolve, reject) => {
    exec(command, (err, stdout, _) => {
        if (err) {
            reject(err)
        } else {
            resolve(stdout)
        }
    });
})

module.exports = {
    purge,
    intersect,
    clone,
    toIds,
    toSchemaObj,
    formatComment,
    readJsonFile,
    toIdsWithoutProperties,
    sortByKey,
    executeCommandPromise
}