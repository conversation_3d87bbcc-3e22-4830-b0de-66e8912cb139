// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Return corect 1`] = `
Array [
  "sectiona_accreditedinvestor",
  "header43",
  "header44",
  "content2",
  "paragraph",
  "pdf1",
  "paragraph1",
  "accreditednaturalpersons",
  "accreditedentities",
  "opt1",
  "opt2",
  "attention",
  "pdf",
  "sectionb_qualifiedclientstatus",
  "header45",
  "header46",
  "content3",
  "paragraph2",
  "fornaturalpersons",
  "forentities3",
  "engageininvesting",
  "ifyes",
  "paragraph9",
  "networthatleast_b_entities_sectionb_part2",
  "engagedortrading_b_entities_sectionb_part2",
  "sectionb_qualifiedclientstatus1",
  "header47",
  "header48",
  "content4",
  "paragraph3",
  "pdf2",
  "qualifiedpurchasernaturalpersons",
  "forentities1",
  "a",
  "qualifiedpurchaserentities",
  "b_forentities_sectionc_part2",
  "c_forentities_sectionc_part2",
  "d_forentities_sectionc_part2",
  "paragraph4",
  "subscriberisnotaqualifiedpurchaser_forentities_sectionc_part2",
  "section",
  "header50",
  "header49",
  "paragraph13",
  "a1",
  "a_sectioniv",
  "foropt3",
  "nameandcrd_1to3_restrictedperson_sectiond_part2",
  "relationship_1to3_restrictedperson_sectiond_part2",
  "foropt5",
  "foropt9",
  "paragraph15",
  "section2",
  "b",
  "section1",
  "multiplecheckbox",
  "specify_1to3_coveredperson_sectiond_part2",
  "section21",
  "one_unaffiliatedprivatefunds_sectiond_part2",
  "showifyesabove",
  "two_unaffiliatedprivatefunds_sectiond_part2",
  "specifytwo_unaffiliatedprivatefunds_sectiond_part2",
  "section3",
  "c",
  "section11",
  "multiplecheckbox3",
  "section22",
  "multiplecheckbox4",
  "specify_5130only_exemptedentitystatus_sectiond_part2",
  "messageforindina",
  "messageforentityna",
]
`;
