const { toIds } = require("./basic")

it('Return corect', () => {
    const input = {
        "type": "object",
        "title": "Part B - Subscriber Qualification",
        "properties": {
            "sectiona_accreditedinvestor": {
                "type": "object",
                "title": "Section I - Accredited Investor",
                "properties": {
                    "header43": {
                        "type": "null",
                        "title": "Header"
                    },
                    "header44": {
                        "type": "null",
                        "title": "Header 23"
                    },
                    "content2": {
                        "type": "object",
                        "title": "Content",
                        "properties": {
                            "paragraph": {
                                "type": "null",
                                "title": "Paragraph"
                            },
                            "pdf1": {
                                "type": "null",
                                "title": "Pdf 1"
                            },
                            "paragraph1": {
                                "type": "null",
                                "title": "Paragraph 1"
                            },
                            "accreditednaturalpersons": {
                                "type": "array",
                                "title": "accredited - natural persons",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "a_naturalpersons_sectiona_part2",
                                        "b_naturalpersons_sectiona_part2",
                                        "c_naturalpersons_sectiona_part2",
                                        "d_naturalpersons_sectiona_part2",
                                        "e_naturalpersons_sectiona_part2",
                                        "f_naturalpersons_sectiona_part2"
                                    ]
                                }
                            },
                            "accreditedentities": {
                                "type": "array",
                                "title": "accredited - entities",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "a_forentities_sectiona_part2",
                                        "b_forentities_sectiona_part2",
                                        "c_forentities_sectiona_part2",
                                        "d_forentities_sectiona_part2",
                                        "e_forentities_sectiona_part2",
                                        "f_forentities_sectiona_part2",
                                        "g_forentities_sectiona_part2",
                                        "h_forentities_sectiona_part2",
                                        "i_forentities_sectiona_part2",
                                        "j_forentities_sectiona_part2",
                                        "k_forentities_sectiona_part2"
                                    ]
                                }
                            },
                            "opt1": {
                                "type": "string",
                                "title": "Opt 1",
                                "enum": [
                                    "corporationaentityform_forentities_sectiona_part2",
                                    "partnershipentityform_forentities_sectiona_part2",
                                    "llpentityform_forentities_sectiona_part2",
                                    "businesstrustentityform_forentities_sectiona_part2",
                                    "taxexemptorganizationentityform_forentities_sectiona_part2"
                                ]
                            },
                            "opt2": {
                                "type": "array",
                                "title": "Opt 2",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "excess5milsubscribercondition_d_forentities_sectiona_part2",
                                        "investmentdecisionsubscribercondition_d_forentities_sectiona_part2",
                                        "selfdirectedplansubscribercondition_d_forentities_sectiona_part2"
                                    ]
                                }
                            },
                            "attention": {
                                "type": "null",
                                "title": "Attention"
                            },
                            "pdf": {
                                "type": "null",
                                "title": "Pdf"
                            }
                        }
                    }
                }
            },
            "sectionb_qualifiedclientstatus": {
                "type": "object",
                "title": "Section II - Qualified Client Status",
                "properties": {
                    "header45": {
                        "type": "null",
                        "title": "Header"
                    },
                    "header46": {
                        "type": "null",
                        "title": "Header 23"
                    },
                    "content3": {
                        "type": "object",
                        "title": "Content",
                        "properties": {
                            "paragraph2": {
                                "type": "null",
                                "title": "Paragraph 2"
                            },
                            "fornaturalpersons": {
                                "type": "array",
                                "title": "For Natural persons",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "a_naturalperson_sectionb_part2",
                                        "b_naturalperson_sectionb_part2"
                                    ]
                                }
                            },
                            "forentities3": {
                                "type": "array",
                                "title": "For entities",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "a_entities_sectionb_part2",
                                        "b_entities_sectionb_part2"
                                    ]
                                }
                            },
                            "engageininvesting": {
                                "type": "string",
                                "title": "Y/N",
                                "enum": [
                                    "yes",
                                    "no"
                                ]
                            },
                            "ifyes": {
                                "type": "object",
                                "title": "If Yes",
                                "properties": {
                                    "paragraph9": {
                                        "type": "null",
                                        "title": "Paragraph 9"
                                    },
                                    "networthatleast_b_entities_sectionb_part2": {
                                        "type": "string",
                                        "title": "Networthatleast _b _entities _sectionb _part 2",
                                        "enum": [
                                            "yesnetworthatleast_b_entities_sectionb_part2",
                                            "nonetworthatleast_b_entities_sectionb_part2"
                                        ]
                                    },
                                    "engagedortrading_b_entities_sectionb_part2": {
                                        "type": "string",
                                        "title": "Engagedortrading _b _entities _sectionb _part 2",
                                        "enum": [
                                            "yesengagedortrading_b_entities_sectionb_part2",
                                            "noengagedortrading_b_entities_sectionb_part2"
                                        ]
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "sectionb_qualifiedclientstatus1": {
                "type": "object",
                "title": "Section III - Qualified Puchaser Status",
                "properties": {
                    "header47": {
                        "type": "null",
                        "title": "Header"
                    },
                    "header48": {
                        "type": "null",
                        "title": "Header 23"
                    },
                    "content4": {
                        "type": "object",
                        "title": "Content",
                        "properties": {
                            "paragraph3": {
                                "type": "null",
                                "title": "Paragraph 3"
                            },
                            "pdf2": {
                                "type": "null",
                                "title": "Pdf 1"
                            },
                            "qualifiedpurchasernaturalpersons": {
                                "type": "array",
                                "title": "Qualified purchaser - Natural persons",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "afornaturalperson_sectionc_part2",
                                        "bfornaturalperson_sectionc_part2"
                                    ]
                                }
                            },
                            "forentities1": {
                                "type": "object",
                                "title": "For entities",
                                "properties": {
                                    "a": {
                                        "type": "null",
                                        "title": "a."
                                    },
                                    "qualifiedpurchaserentities": {
                                        "type": "array",
                                        "title": "Qualified purchaser - Entities",
                                        "items": {
                                            "type": "string",
                                            "enum": [
                                                "a1_forentities_sectionc_part2",
                                                "a2_forentities_sectionc_part2",
                                                "a3_forentities_sectionc_part2",
                                                "a4_forentities_sectionc_part2",
                                                "a5_forentities_sectionc_part2"
                                            ]
                                        }
                                    },
                                    "b_forentities_sectionc_part2": {
                                        "type": "string",
                                        "title": "B _forentities _sectionc _part 2",
                                        "enum": [
                                            "yesb_forentities_sectionc_part2",
                                            "nob_forentities_sectionc_part2"
                                        ]
                                    },
                                    "c_forentities_sectionc_part2": {
                                        "type": "string",
                                        "title": "C _forentities _sectionc _part 2",
                                        "enum": [
                                            "yesc_forentities_sectionc_part2",
                                            "noc_forentities_sectionc_part2"
                                        ]
                                    },
                                    "d_forentities_sectionc_part2": {
                                        "type": "string",
                                        "title": "D _forentities _sectionc _part 2",
                                        "enum": [
                                            "yesd_forentities_sectionc_part2",
                                            "nod_forentities_sectionc_part2"
                                        ]
                                    },
                                    "paragraph4": {
                                        "type": "null",
                                        "title": "Paragraph 4"
                                    }
                                }
                            },
                            "subscriberisnotaqualifiedpurchaser_forentities_sectionc_part2": {
                                "type": "array",
                                "title": "Subscriberisnotaqualifiedpurchaser _forentities _sectionc _part 2",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "subscriberisnotaqualifiedpurchaser_forentities_sectionc_part2"
                                    ]
                                }
                            }
                        }
                    }
                }
            },
            "section": {
                "type": "object",
                "title": "Section IV - Fund Investments in FINRA",
                "properties": {
                    "header50": {
                        "type": "null",
                        "title": "Header"
                    },
                    "header49": {
                        "type": "null",
                        "title": "Header 23"
                    },
                    "paragraph13": {
                        "type": "null",
                        "title": "Paragraph 13"
                    },
                    "a1": {
                        "type": "object",
                        "title": "A.",
                        "properties": {
                            "a_sectioniv": {
                                "type": "array",
                                "title": "section 1",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "one_restrictedperson_sectiond_part2",
                                        "two_restrictedperson_sectiond_part2",
                                        "three_restrictedperson_sectiond_part2",
                                        "four_restrictedperson_sectiond_part2",
                                        "five_restrictedperson_sectiond_part2",
                                        "six_restrictedperson_sectiond_part2",
                                        "seven_restrictedperson_sectiond_part2",
                                        "eight_restrictedperson_sectiond_part2",
                                        "nine_restrictedperson_sectiond_part2",
                                        "ten_restrictedperson_sectiond_part2",
                                        "eleven_restrictedperson_sectiond_part2",
                                        "twelve_restrictedperson_sectiond_part2"
                                    ]
                                }
                            },
                            "foropt3": {
                                "type": "object",
                                "title": "For opt 3",
                                "properties": {
                                    "nameandcrd_1to3_restrictedperson_sectiond_part2": {
                                        "type": "string",
                                        "title": "Nameandcrd _1to 3_restrictedperson _sectiond _part 2"
                                    },
                                    "relationship_1to3_restrictedperson_sectiond_part2": {
                                        "type": "number",
                                        "title": "Relationship _1to 3_restrictedperson _sectiond _part 2"
                                    }
                                }
                            },
                            "foropt5": {
                                "type": "string",
                                "title": "For opt 5"
                            },
                            "foropt9": {
                                "type": "string",
                                "title": "For opt 9"
                            },
                            "paragraph15": {
                                "type": "null",
                                "title": "Paragraph 15"
                            },
                            "section2": {
                                "type": "array",
                                "title": "section 2",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "unrestrictedperson_sectiond_part2"
                                    ]
                                }
                            }
                        }
                    },
                    "b": {
                        "type": "object",
                        "title": "B.",
                        "properties": {
                            "section1": {
                                "type": "object",
                                "title": "Section 1",
                                "properties": {
                                    "multiplecheckbox": {
                                        "type": "array",
                                        "title": "Multiplecheckbox",
                                        "items": {
                                            "type": "string",
                                            "enum": [
                                                "one_coveredperson_sectiond_part2",
                                                "two_coveredperson_sectiond_part2",
                                                "three_coveredperson_sectiond_part2"
                                            ]
                                        }
                                    },
                                    "specify_1to3_coveredperson_sectiond_part2": {
                                        "type": "string",
                                        "title": "Specify _1to 3_coveredperson _sectiond _part 2"
                                    }
                                }
                            },
                            "section21": {
                                "type": "object",
                                "title": "Section 2",
                                "properties": {
                                    "one_unaffiliatedprivatefunds_sectiond_part2": {
                                        "type": "string",
                                        "title": "One _unaffiliatedprivatefunds _sectiond _part 2",
                                        "enum": [
                                            "yesone_unaffiliatedprivatefunds_sectiond_part2",
                                            "noone_unaffiliatedprivatefunds_sectiond_part2"
                                        ]
                                    },
                                    "showifyesabove": {
                                        "type": "null",
                                        "title": "Show if Yes above"
                                    },
                                    "two_unaffiliatedprivatefunds_sectiond_part2": {
                                        "type": "string",
                                        "title": "Two _unaffiliatedprivatefunds _sectiond _part 2",
                                        "enum": [
                                            "yestwo_unaffiliatedprivatefunds_sectiond_part2",
                                            "notwo_unaffiliatedprivatefunds_sectiond_part2"
                                        ]
                                    },
                                    "specifytwo_unaffiliatedprivatefunds_sectiond_part2": {
                                        "type": "number",
                                        "title": "Specifytwo _unaffiliatedprivatefunds _sectiond _part 2"
                                    }
                                }
                            },
                            "section3": {
                                "type": "array",
                                "title": "Section 3",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "notcoveredperson_sectiond_part2"
                                    ]
                                }
                            }
                        }
                    },
                    "c": {
                        "type": "object",
                        "title": "C.",
                        "properties": {
                            "section11": {
                                "type": "object",
                                "title": "Section 1",
                                "properties": {
                                    "multiplecheckbox3": {
                                        "type": "array",
                                        "title": "Multiplecheckbox 3",
                                        "items": {
                                            "type": "string",
                                            "enum": [
                                                "one_exemptedentitystatus_sectiond_part2",
                                                "two_exemptedentitystatus_sectiond_part2",
                                                "three_exemptedentitystatus_sectiond_part2",
                                                "four_exemptedentitystatus_sectiond_part2",
                                                "five_exemptedentitystatus_sectiond_part2",
                                                "six_exemptedentitystatus_sectiond_part2",
                                                "seven_exemptedentitystatus_sectiond_part2",
                                                "eight_exemptedentitystatus_sectiond_part2",
                                                "nine_exemptedentitystatus_sectiond_part2",
                                                "ten_exemptedentitystatus_sectiond_part2"
                                            ]
                                        }
                                    }
                                }
                            },
                            "section22": {
                                "type": "object",
                                "title": "Section 2",
                                "properties": {
                                    "multiplecheckbox4": {
                                        "type": "array",
                                        "title": "Multiplecheckbox 4",
                                        "items": {
                                            "type": "string",
                                            "enum": [
                                                "one_5130only_exemptedentitystatus_sectiond_part2",
                                                "two_5130only_exemptedentitystatus_sectiond_part2"
                                            ]
                                        }
                                    },
                                    "specify_5130only_exemptedentitystatus_sectiond_part2": {
                                        "type": "number",
                                        "title": "Specify _5130only _exemptedentitystatus _sectiond _part 2"
                                    }
                                }
                            }
                        }
                    },
                    "messageforindina": {
                        "type": "null",
                        "title": "Message for Indi - NA"
                    },
                    "messageforentityna": {
                        "type": "null",
                        "title": "Message for Entity - NA"
                    }
                }
            }
        }
    }

    expect(toIds(input)).toMatchSnapshot();
})