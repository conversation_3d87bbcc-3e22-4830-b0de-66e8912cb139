const findAll = (regexPattern, sourceString) => {
    let output = []
    let match
        // make sure the pattern has the global flag
    let regexPatternWithGlobal = RegExp(regexPattern, [...new Set("g" + regexPattern.flags)].join(""))
    while (match = regexPatternWithGlobal.exec(sourceString)) {
        // get rid of the string copy
        delete match.input
            // store the match data
        output.push(match)
    }
    return output.flat()
}


const toCompareString = (str) => {
    if (!str) {
        return ""
    }
    const subs = findAll(/[a-z0-9]+/g, str.toLowerCase())

    return subs.join("")
}

module.exports = { findAll, toCompareString }