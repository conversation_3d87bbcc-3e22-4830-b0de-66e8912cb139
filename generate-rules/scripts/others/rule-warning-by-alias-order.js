/**
 * All the rules are supposed to have input aliases occurring before the output ones
 * For example, there is a prefill rule between address blocks at page 1 and page 2, the address on page 1 should
 * be taken to prefill to page 2, not vice versa. Because rule always support common filling order which is from top to the bottom
 * 
 * In most of the cases of rule looping, it happens because it exists a rule including an output alias occuring before the input ones
 * 
 * This file to throw out all rule which possible involving in rule looping.
 */

const FOLDER_NAME = process.argv[2];

const INPUT = require(`../../data/${FOLDER_NAME}/extract/input.json`)
const S1 = require(`../../data/${FOLDER_NAME}/analyze/step1.json`)
const RULES = require(`../../data/${FOLDER_NAME}/extract/rules.json`)

const ORDER = Object.fromEntries(Object.values(S1).flat().map(v => [v["id"], v["order"]]))
const HIDDEN = Object.fromEntries(Object.values(S1).flat().map(v => [v["id"], v["hidden"]]))

const getMaxOrderAlias = aliases => aliases.reduce((result, ele) => ORDER[ele] ?? -1 > ORDER[result] ?? -1 ? ele : result, aliases[0] ?? '')
const getMinOrderAlias = aliases => aliases.reduce((result, ele) => ORDER[ele] ?? -1 < ORDER[result] ?? -1 ? ele : result, aliases[0] ?? '')

INPUT.map((rule, i) => {
    const maxInputAlias = getMaxOrderAlias(rule["input"].filter(x => !HIDDEN[x]))
    const minOutputAlias = getMinOrderAlias(Object.keys(rule["output"]).filter(x => !HIDDEN[x]))
    const minSuspectedOuputAlias = getMinOrderAlias(rule["suspectedAlias"].filter(x => !HIDDEN[x]))

    const maxInputAliasOrder = ORDER[maxInputAlias]
    const minOutputAliasOrder = ORDER[minOutputAlias]
    const minSuspectedOuputAliasOrder = ORDER[minSuspectedOuputAlias]

    if (maxInputAliasOrder > minOutputAliasOrder) {
        console.log(`\n${RULES[i]["name"]}\n- Input: ${maxInputAlias}\n- Output: ${minOutputAlias} `)
    }
    if (maxInputAliasOrder > minSuspectedOuputAliasOrder) {
        console.log(`\n[Suspected] ${RULES[i]["name"]}\n- Input: ${maxInputAlias}\n- Output: ${minSuspectedOuputAlias} `)
    }
})