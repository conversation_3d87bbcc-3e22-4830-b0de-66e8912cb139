/**
 * This file will generated a new form JSON at the same path as original file
 * 
 * This JSON form will contain a formatted UI schema object where redundant UI keys are removed and sorted with schema order
 */

const fs = require('fs');
const _ = require('lodash');

const USAGE_STRING = 'Usage: node ./scripts/preprocessing/step0-preparation.js FILE_PATH';

const processUserInput = () => {
    const FILE_PATH = process.argv[2];

    if (!FILE_PATH) {
        return Promise.reject(USAGE_STRING);
    }

    console.log(`\n\nExtracting data with given data`)
    console.log(`FILE: ${FILE_PATH}`)

    return Promise.resolve({
        INPUT_PATH: FILE_PATH,
    });
};


const calculateOuput = (data) => {
    const { INPUT_PATH } = data

    // output
    const OUPUT_PATH = INPUT_PATH.replace(".json", "_formatted_ui.json")

    const promises = {
        ...data,
        OUPUT_PATH
    };
    return Promise.resolve(promises);
}

const readJSONFile = (data) => {
    const { INPUT_PATH } = data

    return new Promise(
        (resolve, reject) => {
            fs.readFile(INPUT_PATH, 'utf8', (err, json) => {
                if (err) {
                    reject(err)
                }

                resolve({
                    ...data,
                    form: JSON.parse(json)
                })
            })
        }
    )
}

const { toIds, toSchemaObj } = require("../../utils/basic")

const writeData = (data) => {
    const { form, OUPUT_PATH } = data

    const schema = _.get(form, ["form", "namespaceFormSchemaMap", "main", "schema"], {})
    const uiShema = _.get(form, ["form", "namespaceFormSchemaMap", "main", "uiSchema"], {})

    const aliases = toIds(toSchemaObj(schema))
    const removedInvalidAlias = Object.fromEntries(
        aliases.map(alias => [alias, uiShema[alias]])
    )

    form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"] = removedInvalidAlias

    return new Promise(
        (resolve, reject) => {
            fs.writeFile(OUPUT_PATH, JSON.stringify(form, null, 4), (e) => {
                if (e) {
                    reject(e)
                    return
                }
            })

            console.log(`Write file ${OUPUT_PATH} successfully`)
            resolve()
        }
    )
}

processUserInput()
    .then(calculateOuput)
    .then(readJSONFile)
    .then(writeData)
    .catch((e) => {
        console.log(e)
    })