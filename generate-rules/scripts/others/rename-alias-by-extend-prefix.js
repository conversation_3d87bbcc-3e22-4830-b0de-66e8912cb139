/**
 * This step should be applied before adding rules because it will rename all aliases in the form
 * 
 * USAGE: In case that we want to copy content from a form to the other but afraid that alias that has the same name
 * will be automatically renamed after pasting. This tool will get this problem out by add a prefix to all aliases in the form.
 */

const fs = require('fs');
const _ = require('lodash');
const FOLDER = process.argv[2]
const PREFIX = process.argv[3]
const JSON_PATH = process.argv[4]

if (!FOLDER || !PREFIX) {
    console.log("USAGE: node rename-alias-by-extend-prefix.js [FODLER_NAME] [PREFIX] [JSON_PATH]")
}

const OUPUT_PATH = `./data/${FOLDER}/extract/form-of-prefixed-alias.json`

const renameSchema = parent => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])
    
    if (!children) {
        return parent
    }

    const renamedChildren = Object.fromEntries(Object.entries(children).map(([k, v]) => ([k, renameSchema(v)])))
    
    const renamedAlias = Object.fromEntries(Object.entries(renamedChildren).map(([k, v]) => ([PREFIX + k, v])))

    let renamedParent = parent
    if (parent["items"] && parent["items"]["properties"]) {
        renamedParent = _.set(parent, ["items", "properties"], renamedAlias)
    } else if (parent["properties"]) {
        renamedParent = _.set(parent, ["properties"], renamedAlias)
    }

    return renamedParent
}

const readJSONFile = () => {
    return new Promise(
        (resolve, reject) => {
            fs.readFile(JSON_PATH, 'utf8', (err, json) => {
                if (err) {
                    reject(err)
                }

                resolve({
                    form: JSON.parse(json)
                })
            })
        }
    )
}

const writeData = (data) => {
    const { form } = data

    const schema = _.get(form, ["form", "namespaceFormSchemaMap", "main", "schema"], {})
    const uiShema = _.get(form, ["form", "namespaceFormSchemaMap", "main", "uiSchema"], {})

    form["form"]["namespaceFormSchemaMap"]["main"]["schema"] = renameSchema(schema)
    form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"] = Object.fromEntries(Object.entries(uiShema).map(([k, v]) => ([PREFIX + k, v])))

    return new Promise(
        (resolve, reject) => {
            fs.writeFile(OUPUT_PATH, JSON.stringify(form, null, 4), (e) => {
                if (e) {
                    reject(e)
                    return
                }
            })

            console.log(`Write file successfully`)
            console.log(OUPUT_PATH)
            resolve()
        }
    )
}

readJSONFile()
    .then(writeData)
    .catch((e) => {
        console.log(e)
    })