// This tool receive a list of form ID from file ./data/analyze-embedded-files.json
// and generate statistic data for given form
// Only form listed in folder ./data/ is applicable to access

const _ = require('lodash');
const fs = require('fs');

const formIds = require('../../data/analyze-embedded-files.json');

const isRadio = (widget) => widget == "Radio"
const isMultipleCheckbox = (widget) => widget == "MultipleCheckbox"
const isSignature = (widget) => widget == "Signature"


const countRadio = (fields) => {
    return fields.filter(f => isRadio(f["widgetType"])).length
}
const countCheckbox = (fields) => {
    return fields.filter(f => isMultipleCheckbox(f["widgetType"])).length
}
const countSignature = (fields) => {
    return fields.filter(f => isSignature(f["widgetType"])).length
}
const countOther = (fields) => {
    return fields.filter(f =>
        !isRadio(f["widgetType"])
        && !isMultipleCheckbox(f["widgetType"])
        && !isSignature(f["widgetType"])
    ).length
}
const countRadioOption = (fields) => {
    return fields.filter(f => isRadio(f["widgetType"]))
        .map(f => _.get(f, ["widget", "ui:multipleOption", "options"]))
        .flat()
        .length
}
const countCheckboxOption = (fields) => {
    return fields.filter(f => isMultipleCheckbox(f["widgetType"]))
        .map(f => _.get(f, ["widget", "ui:multipleOption", "options"]))
        .flat()
        .length
}

const analyzedObjs = []

formIds.map((id) => {

    try {

        const embeddedJsonPath = `./data/${id}/extract/embedded_pdf.json`
        const embeddedJson = require(embeddedJsonPath)

        embeddedJson.map(([fileID, { name, fields }]) => {
            const analyzedObj = {
                fileID,
                name
            }

            analyzedObj["countRadio"] = countRadio(fields)
            analyzedObj["countCheckbox"] = countCheckbox(fields)
            analyzedObj["countSignature"] = countSignature(fields)
            analyzedObj["countOther"] = countOther(fields)
            analyzedObj["countRadioOption"] = countRadioOption(fields)
            analyzedObj["countCheckboxOption"] = countCheckboxOption(fields)
            analyzedObj["formID"] = id

            analyzedObjs.push(analyzedObj)
        })

    } catch (e) { console.log(e) }
})
function convertToCSV(arr) {
    const array = [Object.keys(arr[0])].concat(arr)

    return array.map(it => {
        return Object.values(it).map(x => `"${x.toString()}"`).toString()
    }).join('\n')
}
const analyzedCSV = convertToCSV(analyzedObjs)
fs.writeFile("./data/analyze-embedded.csv", analyzedCSV, (e) => {
    if (e) {
        reject(e)
    }

    console.log("Write file successfully")
})