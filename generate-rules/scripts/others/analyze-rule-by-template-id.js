/**
 * This file will generate analyzed result
 * which count number of rules that is manually written
 * 
 * Instruction:
 * 1. Make sure you are on `generate-rules` folder 
 * 2. Run extract script: 
 *      sh scripts/extract.sh [FORM_NAME] [PATH_TO_JSON_FORM]/form.json
 * 3. Run analyze script: 
 *      node ./scripts/others/analyze-rule-by-template-id.js [FORM_NAME]
 */

const fs = require('fs');
const _ = require('lodash');

const USAGE_STRING = 'Usage: node ./scripts/others/analyze-rule-by-template-id.js [FOLDER_NAME]';

const toOutputPath = folderName => `./data/${folderName}/analyze/rule-analyzed-result.txt`
const toInputPath = folderName => `./data/${folderName}/extract/rules.json`

const processUserInput = () => {
    const FOLDER_NAME = process.argv[2];

    if (!FOLDER_NAME) {
        return Promise.reject(USAGE_STRING);
    }

    console.log(`\n\nExtracting data with given data`)
    console.log(`FOLDER_NAME: ${FOLDER_NAME}`)

    return Promise.resolve({
        FOLDER_NAME,
    });
};


const calculatePaths = (data) => {
    const { FOLDER_NAME } = data

    const INPUT_PATH = toInputPath(FOLDER_NAME)
    const OUPUT_PATH = toOutputPath(FOLDER_NAME)

    const promises = {
        ...data,
        INPUT_PATH,
        OUPUT_PATH
    };
    return Promise.resolve(promises);
}

const readJSONFile = (data) => {
    const { INPUT_PATH } = data

    return new Promise(
        (resolve, reject) => {
            console.log(`Reading JSON at file: ${INPUT_PATH}`)
            fs.readFile(INPUT_PATH, 'utf8', (err, json) => {
                if (err) {
                    reject(err)
                }

                resolve({
                    ...data,
                    rules: JSON.parse(json)
                })
            })
        }
    )
}


const writeData = (data) => {
    const { rules, OUPUT_PATH } = data

    let result = ''

    const manualRules = rules.filter(rule => !rule.templateId)

    result += `Total rules: ${rules.length}\n`
    result += `Generated rules: ${rules.length - manualRules.length}\n`
    result += `Manual added rules: ${manualRules.length}\n`
    result += `Percent of manual added rules: ${manualRules.length / rules.length * 100}\n\n`

    result += `List of manual added rules:\n`
    manualRules.forEach(rule => {
        result += `\t${rule.name}\n`
    });


    return new Promise(
        (resolve, reject) => {
            fs.writeFile(OUPUT_PATH, result, (e) => {
                if (e) {
                    reject(e)
                    return
                }
            })

            console.log(`Write file ${OUPUT_PATH} successfully`)
            resolve()
        }
    )
}

processUserInput()
    .then(calculatePaths)
    .then(readJSONFile)
    .then(writeData)
    .catch((e) => {
        console.log(e)
    })