// This tool is to converting old widget of Country, State and Zipcode to latest one
// File UPDATE_ALIAS_PATH must have following structure
// {
//     "states": [
//         "state_alias1",
//         ...
//     ],
//     "countries": [
//         "country_alias1",
//         ...
//     ],
//     "usZipCodes": [
//         "zipcode_alias1",
//         ...
//     ]
// }

const fs = require('fs')


const { purge } = require("../../utils/basic")

const COUNTRY_OPTIONS = [
    "Afghanistan",
    "Aland Islands",
    "Albania",
    "Algeria",
    "American Samoa",
    "Andorra",
    "Angola",
    "Anguilla",
    "Antarctica",
    "Antigua and Barbuda",
    "Argentina",
    "Armenia",
    "Aruba",
    "Australia",
    "Austria",
    "Azerbaijan",
    "Bahamas",
    "Bahrain",
    "Bangladesh",
    "Barbados",
    "Belarus",
    "Belgium",
    "Belize",
    "Benin",
    "Bermuda",
    "Bhutan",
    "Bolivia",
    "Bosnia and Herzegovina",
    "Botswana",
    "Brazil",
    "British Indian Ocean Territory",
    "Brunei Darussalam",
    "Bulgaria",
    "Burkina Faso",
    "Burundi",
    "Cambodia",
    "Cameroon",
    "Canada",
    "Cape Verde",
    "Cayman Islands",
    "Central African Republic",
    "Chad",
    "Chile",
    "China",
    "Christmas Island",
    "Cocos (Keeling) Islands",
    "Colombia",
    "Comoros",
    "Congo",
    "Congo, The Democratic Republic of the Congo",
    "Cook Islands",
    "Costa Rica",
    "Cote d'Ivoire",
    "Croatia",
    "Cuba",
    "Cyprus",
    "Czech Republic",
    "Denmark",
    "Djibouti",
    "Dominica",
    "Dominican Republic",
    "Ecuador",
    "Egypt",
    "El Salvador",
    "Equatorial Guinea",
    "Eritrea",
    "Estonia",
    "Ethiopia",
    "Falkland Islands (Malvinas)",
    "Faroe Islands",
    "Fiji",
    "Finland",
    "France",
    "French Guiana",
    "French Polynesia",
    "Gabon",
    "Gambia",
    "Georgia",
    "Germany",
    "Ghana",
    "Gibraltar",
    "Greece",
    "Greenland",
    "Grenada",
    "Guadeloupe",
    "Guam",
    "Guatemala",
    "Guernsey",
    "Guinea",
    "Guinea-Bissau",
    "Guyana",
    "Haiti",
    "Holy See (Vatican City State)",
    "Honduras",
    "Hong Kong",
    "Hungary",
    "Iceland",
    "India",
    "Indonesia",
    "Iran, Islamic Republic of Persian Gulf",
    "Iraq",
    "Ireland",
    "Isle of Man",
    "Israel",
    "Italy",
    "Jamaica",
    "Japan",
    "Jersey",
    "Jordan",
    "Kazakhstan",
    "Kenya",
    "Kiribati",
    "Korea, Democratic People's Republic of Korea",
    "Korea, Republic of South Korea",
    "Kosovo",
    "Kuwait",
    "Kyrgyzstan",
    "Laos",
    "Latvia",
    "Lebanon",
    "Lesotho",
    "Liberia",
    "Libyan Arab Jamahiriya",
    "Liechtenstein",
    "Lithuania",
    "Luxembourg",
    "Macao",
    "Macedonia",
    "Madagascar",
    "Malawi",
    "Malaysia",
    "Maldives",
    "Mali",
    "Malta",
    "Marshall Islands",
    "Martinique",
    "Mauritania",
    "Mauritius",
    "Mayotte",
    "Mexico",
    "Micronesia, Federated States of Micronesia",
    "Moldova",
    "Monaco",
    "Mongolia",
    "Montenegro",
    "Montserrat",
    "Morocco",
    "Mozambique",
    "Myanmar",
    "Namibia",
    "Nauru",
    "Nepal",
    "Netherlands",
    "Netherlands Antilles",
    "New Caledonia",
    "New Zealand",
    "Nicaragua",
    "Niger",
    "Nigeria",
    "Niue",
    "Norfolk Island",
    "Northern Mariana Islands",
    "Norway",
    "Oman",
    "Pakistan",
    "Palau",
    "Palestinian Territory, Occupied",
    "Panama",
    "Papua New Guinea",
    "Paraguay",
    "Peru",
    "Philippines",
    "Pitcairn",
    "Poland",
    "Portugal",
    "Puerto Rico",
    "Qatar",
    "Romania",
    "Russia",
    "Rwanda",
    "Reunion",
    "Saint Barthelemy",
    "Saint Helena, Ascension and Tristan Da Cunha",
    "Saint Kitts and Nevis",
    "Saint Lucia",
    "Saint Martin",
    "Saint Pierre and Miquelon",
    "Saint Vincent and the Grenadines",
    "Samoa",
    "San Marino",
    "Sao Tome and Principe",
    "Saudi Arabia",
    "Senegal",
    "Serbia",
    "Seychelles",
    "Sierra Leone",
    "Singapore",
    "Slovakia",
    "Slovenia",
    "Solomon Islands",
    "Somalia",
    "South Africa",
    "South Georgia and the South Sandwich Islands",
    "Spain",
    "Sri Lanka",
    "Sudan",
    "Suriname",
    "Svalbard and Jan Mayen",
    "Swaziland",
    "Sweden",
    "Switzerland",
    "Syrian Arab Republic",
    "Taiwan",
    "Tajikistan",
    "Tanzania, United Republic of Tanzania",
    "Thailand",
    "Timor-Leste",
    "Togo",
    "Tokelau",
    "Tonga",
    "Trinidad and Tobago",
    "Tunisia",
    "Turkey",
    "Turkmenistan",
    "Turks and Caicos Islands",
    "Tuvalu",
    "Uganda",
    "Ukraine",
    "United Arab Emirates",
    "United Kingdom",
    "United States",
    "Uruguay",
    "Uzbekistan",
    "Vanuatu",
    "Venezuela, Bolivarian Republic of Venezuela",
    "Vietnam",
    "Virgin Islands, British",
    "Virgin Islands, U.S.",
    "Wallis and Futuna",
    "Yemen",
    "Zambia",
    "Zimbabwe"
]

/**
 * STATE
 * Current: 
 *      - "enum" : [...
 * Expect:
 * 
 * Current:             
 *      - "ui:multipleOption" : {
 *      - "ui:widget" : "Dropdown"
 * Expect:
 *      + "ui:subdivisionOf" : "US",
 *      + "ui:widget" : "State"
 */
const formatStateSchema = (obj) => {
    const result = { ...obj }

    delete result["enum"]

    return result
}
const formatStateUiSchema = (obj) => {
    const result = { ...obj }

    delete result["ui:multipleOption"]
    delete result["ui:widget"]

    result["ui:subdivisionOf"] = "US"
    result["ui:widget"] = "State"

    return result
}

/**
 * COUNTRY
 * Current: 
 *      - "enum" : [...
 * Expect:
 * 
 * Current:             
 *      - "ui:multipleOption" : {
 *      - "ui:widget" : "Dropdown"
 * Expect:
 *      + "ui:widget" : "Country"
 */

const formatCountrySchema = (obj) => {
    const result = { ...obj }

    const deletedCountries = purge(COUNTRY_OPTIONS, result["enum"] || [])

    if (deletedCountries.length > 0) {
        console.log("\n\nWarning: shouldn't replace alias " + obj.name)
        console.log("Deleted countries: " + deletedCountries)
    }

    delete result["enum"]

    return result
}
const formatCountryUiSchema = (obj) => {
    const result = { ...obj }

    delete result["ui:multipleOption"]
    delete result["ui:widget"]

    result["ui:widget"] = "Country"

    return result
}

/**
 * US ZIP CODE
 * Current:
 * 
 * Expect:
 *      + "format" : "USZIP"
 * 
 * Current:
 *      - "ui:widget" : "Phone"
 * Expect:
 *      + "ui:customFormat" : "USZIP",
 *      + "ui:placeholder" : "Enter 5 or 9 digits",
 *      + "ui:validationError" : "Please enter a valid ZIP code",
 *      + "ui:widget" : "CustomFormat
 */


const formatUsZipCodeSchema = (obj) => {
    const result = { ...obj }

    result["format"] = "USZIP"

    return result
}
const formatUsZipCodeUiSchema = (obj) => {
    const result = { ...obj }

    delete result["ui:widget"]

    result["ui:customFormat"] = "USZIP"
    result["ui:placeholder"] = "Enter 5 or 9 digits"
    result["ui:validationError"] = "Please enter a valid ZIP code"
    result["ui:widget"] = "CustomFormat"

    return result
}

const formatSchema = (parent, { states, countries, usZipCodes }) => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])

    if (!children) {
        if (states.indexOf(parent["name"]) > -1) {
            return formatStateSchema(parent)
        } else if (countries.indexOf(parent["name"]) > -1) {
            return formatCountrySchema(parent)
        } else if (usZipCodes.indexOf(parent["name"]) > -1) {
            return formatUsZipCodeSchema(parent)
        } else {
            return parent
        }
    }

    const formattedChildren = children.map(child => formatSchema(child, { states, countries, usZipCodes }))

    const newParent = { ...parent }

    if (newParent["properties"]) {
        newParent["properties"] = formattedChildren
    }
    if (newParent["items"] && newParent["items"]["properties"]) {
        newParent["items"]["properties"] = formattedChildren
    }

    return newParent
}

const formatUiSchema = (items, { states, countries, usZipCodes }) => Object.fromEntries(
    Object.entries(items).map(([k, ui]) => {
        if (states.indexOf(k) > -1) {
            return [k, formatStateUiSchema(ui)]
        } else if (countries.indexOf(k) > -1) {
            return [k, formatCountryUiSchema(ui)]
        } else if (usZipCodes.indexOf(k) > -1) {
            return [k, formatUsZipCodeUiSchema(ui)]
        } else {
            return [k, ui]
        }
    })
)

const USAGE_STRING = 'Usage: node format-address-using-new-widget.js PATH_TO_JSON_FILE PATH_TO_UPDATE_ALIAS_LIST';

const processUserInput = () => {
    const PATH_TO_JSON_FILE = process.argv[2];
    const PATH_TO_UPDATE_ALIAS_LIST = process.argv[3];

    if (!PATH_TO_JSON_FILE || !PATH_TO_UPDATE_ALIAS_LIST) {
        return Promise.reject(USAGE_STRING);
    }

    console.log(`\n\nExtracting data with given data`)
    console.log(`JSON PATH: ${PATH_TO_JSON_FILE}`)
    console.log(`UPDATE ALIAS PATH: ${PATH_TO_UPDATE_ALIAS_LIST}`)

    return Promise.resolve({
        JSON_PATH: PATH_TO_JSON_FILE,
        UPDATE_ALIAS_PATH: PATH_TO_UPDATE_ALIAS_LIST
    });
};

const readJSONFile = (path) => {
    return new Promise(
        (resolve, reject) => {
            fs.readFile(path, 'utf8', (err, json) => {
                if (err) {
                    reject(err)
                }

                resolve({ data: JSON.parse(json), path })
            })
        }
    )
}

processUserInput()
    .then(({ JSON_PATH, UPDATE_ALIAS_PATH }) =>
        Promise.all([
            readJSONFile(JSON_PATH),
            readJSONFile(UPDATE_ALIAS_PATH)
        ])
    )
    .then(([formData, updateAliasData]) => {
        const { data: form, path: formPath } = formData
        const { data: updatedAlias } = updateAliasData


        const schema = form["form"]["namespaceFormSchemaMap"]["main"]["schema"]
        const uiSchema = form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"]

        form["form"]["namespaceFormSchemaMap"]["main"]["schema"] = formatSchema(schema, updatedAlias)
        form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"] = formatUiSchema(uiSchema, updatedAlias)


        fs.writeFile(formPath.replace(".json", "-formatted.json"), JSON.stringify(form, null, 4), (e) => {
            if (!e) {
                console.log("Write file successfully!")
            } else {
                console.error(e)
            }
        })
    })

