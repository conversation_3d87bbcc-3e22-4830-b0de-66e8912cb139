const fs = require('fs')

const INPUT_FOLDER = process.argv[2]
const OUTPUT = process.argv[3]
const FILE_EXT = process.argv[4]
const VERSION = process.argv[5]

const readJsonnetFile = (fileName) =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(`${INPUT_FOLDER}/${VERSION}/${fileName}`, 'utf8', (e, data) => {
                if (e) {
                    reject(e)
                }

                const name = fileName.replace(FILE_EXT, "")

                resolve([
                    name, data
                ])
            })
        })

const readDescriptionFile = () =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(`${INPUT_FOLDER}/_description.json`, 'utf8', (e, data) => {
                if (e) {
                    reject(e)
                }

                resolve(JSON.parse(data))
            })
        })

const readJsonnetFiles = (paths) => {
    const promises = paths
        .map(readJsonnetFile)

    return Promise.all(promises)
}
const readFilePath = () =>
    new Promise(
        (resolve, reject) => {
            fs.readdir(`${INPUT_FOLDER}/${VERSION}`, (err, files) => {
                if (err) {
                    reject(err)
                    return
                }

                const jsonnetPaths = files.filter(dir => dir.endsWith(FILE_EXT))

                resolve(jsonnetPaths);
            });
        })

const writeJSON = ([description, rules]) => {
    const all = Object.fromEntries(rules)
    const result = {
        "description": description,
        "all": all,
    }
    return new Promise(
        (_, reject) => {
            fs.writeFile(OUTPUT, JSON.stringify(
                result, null, 4
            ), (e) => {
                if (e) {
                    reject(e)
                }

                console.log("Write file successfully")
            })
        })
}
readFilePath()
    .then((data) => Promise.all([
        readDescriptionFile(),
        readJsonnetFiles(data)
    ]))
    .then(writeJSON)
    .catch((e) => {
        console.log(e)
    })