const fs = require('fs')
const { executeCommandPromise } = require('../../utils/basic')

const INPUT_FOLDER = process.argv[2]
const OUTPUT_JSON = process.argv[3]
const VERSION = process.argv[4]
const FILE_EXT = ".jsonnet"

const readJsonnetFile = (fileName) =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(`${INPUT_FOLDER}/${VERSION}/${fileName}`, 'utf8', (e, data) => {
                if (e) {
                    reject(e)
                }

                const name = fileName.replace(FILE_EXT, "")

                resolve([
                    name, data
                ])
            })
        })

const readIndexFile = () =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(`${INPUT_FOLDER}/_index.json`, 'utf8', (e, data) => {
                if (e) {
                    reject(e)
                }

                resolve(JSON.parse(data))
            })
        })

const readJsonnetFiles = (paths) => {
    const promises = paths
        .map(readJsonnetFile)

    return Promise.all(promises)
}
const readFilePath = () =>
    new Promise(
        (resolve, reject) => {
            fs.readdir(`${INPUT_FOLDER}/${VERSION}`, (err, files) => {
                if (err) {
                    reject(err)
                    return
                }

                const jsonnetPaths = files.filter(dir => dir.endsWith(FILE_EXT))

                resolve(jsonnetPaths);
            });
        })


const writeJsonnetFile = ([names, rules, gitVersion]) => {
    const all = Object.fromEntries(rules)
    const obj = names
        .filter(name => {
            if (!all[name]) {
                console.error("Cannot find rule with key " + name + "in folder" + `${INPUT_FOLDER}/${VERSION}`)
            }
            return all[name]
        })
        .map(name => `${name}: ${all[name]}`)
    const result = `{${obj.join(",\n\n")}}`

    // Currently, Jsonnet doesn't support to minify Jsonnet scripts such as remove comments and spaces from
    // This following script is considered a temporary solution
    const minifiedResult = result
        .replace(/(\/\/.*?\n)|(\/\*(.|\n|\r\n)*?\*\/)/g, "")
        .replace(/\r\n|\n/g, "")
        .replace(/\s+/g, " ")

    const comment = 
        "// Generate Rule Import Library is generated as following instruction\n"
        + "// https://github.com/anduintransaction/gandalf/blob/master/generate-rules/rules/lib-import/README.md\n"
        + `// Version: ${gitVersion}\n`

    const resultVars = names
        .filter(name => all[name])
        .map(name => `local ${name} = ${all[name].replaceAll("self.", "")};`)
    return new Promise(
        (_, reject) => {
            fs.writeFile(OUTPUT_JSON.replace("json", "jsonnet"),comment + result, (e) => {
                if (e) {
                    reject(e)
                }

                console.log("Write file successfully")
            })
            fs.writeFile(OUTPUT_JSON.replace(".json", "-minified.jsonnet"), comment + minifiedResult, (e) => {
                if (e) {
                    reject(e)
                }

                console.log("Write file successfully")
            })
            fs.writeFile(OUTPUT_JSON.replace(".json", "-debug.text"), comment + resultVars.join("\n\n"), (e) => {
                if (e) {
                    reject(e)
                }

                console.log("Write file successfully")
            })
            fs.writeFile(OUTPUT_JSON, JSON.stringify({ all: comment + result }, null, 4), (e) => {
                if (e) {
                    reject(e)
                }

                console.log("Write file successfully")
            })
        })
}
readFilePath()
    .then((data) => Promise.all([
        readIndexFile(),
        readJsonnetFiles(data),
        executeCommandPromise("git rev-parse --short HEAD")
    ]))
    .then(writeJsonnetFile)
    .catch((e) => {
        console.log(e)
    })