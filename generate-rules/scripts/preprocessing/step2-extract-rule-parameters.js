/**
 * This step will extract following data from orginal form JSON
 * 1. schema
 * 2. uiSchema: sorted based on alias order in schema 
 * 3. rules
 * 4. embedded_pdf
 * 
 * Generating these files have to be the first step any further calculating 
 */

const FOLDER = process.argv[2]

if (!FOLDER) {
    console.log("USAGE: node extract-rule-input.js [FODLER_NAME]")
}

const fs = require('fs')
const _ = require('lodash')
const { COMPONENT_ALIAS_INPUT } = require("../analyze/config")
const { toIdsWithoutProperties } = require("../../utils/basic")

const CONSTANTS = require("./constants")
const { removeComments, extractFirstFunctions, ALL_ACCESSIBLE_ATTRIBUTES } = require("./utils")
const regexUtils = require("../../utils/regex")
const matchRecursive = require("match-recursive")

const RULE_PATH = CONSTANTS.IN_STEP1_RULE(FOLDER)
const OUTPUT_PATH = CONSTANTS.STEP2(FOLDER)
const COMPONENT_ALIAS = require(COMPONENT_ALIAS_INPUT)

const ALIASES = toIdsWithoutProperties(COMPONENT_ALIAS)

const rules = require(RULE_PATH)
const STRING_REGEX = /\w+/g

const findAll = (regexPattern, sourceString) => {
    let output = []
    let match
    // make sure the pattern has the global flag
    let regexPatternWithGlobal = RegExp(regexPattern, [...new Set("g" + regexPattern.flags)].join(""))
    while (match = regexPatternWithGlobal.exec(sourceString)) {
        // get rid of the string copy
        delete match.input
        // store the match data
        output.push(match)
    }
    return output
}

const removedComments = rules
    .map((r) => {
        const { value } = r
        return { ...r, value: removeComments(value) }
    })

const ruleInput = removedComments
    .map(r => {
        const { value } = r

        const funcLine = extractFirstFunctions(value)

        return funcLine
            .replace("function(", "")
            .replace(")", "")
            .split(",")
    })

const toAddStatements = (value) => {
    const parts = value.split(new RegExp(`\\batd\\b.\\badd\\b`, "g"))

    return parts.map((p, i) => {
        if (i == 0) { return null }

        const matches = matchRecursive(p, "(...)")
        const firstMatch = matches.length ? matches[0] : typeof matches == "string" ? matches : ""

        return firstMatch
    }).filter(x => x)
}
const isRepeatable = (addParams) => {
    const nonSpace = addParams.replaceAll(/\s?\n?/g, '')
    const splitByComma = nonSpace.split(",")
    const isRepeatable = splitByComma[1].startsWith("[")

    return isRepeatable
}

// TODO: Calculate for repeatable case
const ruleRepeatableOuput = removedComments
    .map(({ value, name }) => {
        const addStatements = toAddStatements(value).filter(match => isRepeatable(match))
        const repeatableAtr = {}
        const childOutput = {}

        addStatements
            .filter(x => x)
            .map(statement => {
                const words = regexUtils.findAll(STRING_REGEX, statement)

                // 0 => output, 1 => attribute, 2 => index, 3 => child_alias, 4 => attribute
                const output = words[0]
                const attribute = words[1]
                const index = words[2]
                const child_alias = words[3]
                const child_attribute = words[4]

                if (ALIASES.indexOf(output) == -1) {
                    console.log(`\n[REPEATABLE] Ignore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *output* alias: ${output}`)

                    return null
                }

                if (ALL_ACCESSIBLE_ATTRIBUTES.indexOf(attribute) == -1) {
                    console.log(`\n[REPEATABLE] Ignore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *attribute*: ${attribute}`)

                    return null
                }

                if (ALIASES.indexOf(child_alias) == -1) {
                    console.log(`\n[REPEATABLE] Ignore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *child_alias*: ${child_alias}`)

                    return null
                }

                if (ALL_ACCESSIBLE_ATTRIBUTES.indexOf(child_attribute) == -1) {
                    console.log(`\n[REPEATABLE] Ignore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *child_attribute*: ${child_attribute}`)

                    return null
                }

                if (words.length < 5) {
                    console.log("\nExpected at least 5 words in add statement");
                    console.log(`Actual: ${words.length} for statement ${statement}`);
                    console.log(`Rule: ${name}`);
                    console.log(`Words: ${words}`);
                    return null
                }

                if (!repeatableAtr[words[0]]) {
                    repeatableAtr[words[0]] = []
                }
                if (repeatableAtr[words[0]].indexOf(words[1]) == -1) {
                    repeatableAtr[words[0]].push(words[1])
                }
                if (!childOutput[words[3]]) {
                    childOutput[words[3]] = []
                }
                if (childOutput[words[3]].indexOf(words[4]) == -1) {
                    childOutput[words[3]].push(words[4])
                }
            })

        return [repeatableAtr, childOutput]
    })
const ruleOuput = removedComments
    .map(({ value, name }) => {
        const all = {}
        const addStatements = toAddStatements(value).filter(match => !isRepeatable(match))

        addStatements
            .map((ele) => {
                const words = ele.match(STRING_REGEX).filter(x => x)

                // 2 => output, 3 => attribute
                const output = words[0]
                const attribute = words[1]

                if (ALIASES.indexOf(output) == -1) {
                    console.log(`\nIgnore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *output* alias: ${output}`)

                    return null
                }

                if (ALL_ACCESSIBLE_ATTRIBUTES.indexOf(attribute) == -1) {
                    console.log(`\nIgnore add statement syntax at Rule: ${name}`)
                    console.log(`Unknown *attribute*: ${attribute}`)

                    return null
                }

                if (all[words[0]]) {
                    if (all[words[0]].indexOf(words[1]) > -1) {

                    } else {
                        all[words[0]].push(words[1])
                    }
                } else {
                    all[words[0]] = [words[1]]
                }
                return []
            })

        return all
    })

const result = removedComments.map(({ value, name }, idx) => {
    const item = {}
    item["input"] = ruleInput[idx]

    const [repeatableOuput, childOutput] = ruleRepeatableOuput[idx]

    item["output"] = { ...ruleOuput[idx], ...repeatableOuput }

    if (Object.keys(childOutput).length > 0) {
        item["repeatableOuput"] = childOutput
    }

    const allOutputs = [...Object.keys(repeatableOuput), ...Object.keys(ruleOuput[idx]), ...Object.keys(childOutput)]

    const extractedStringLookLikeOutput = ALIASES
        .filter(alias => allOutputs.indexOf(alias) === -1)
        .filter(alias => {
            const strings = findAll(new RegExp(`[\'|\"]${alias}[\'|\"]`, "g"), value)

            return strings.length > 0
        })

    item["suspectedAlias"] = _.uniq(extractedStringLookLikeOutput)

    if (item["suspectedAlias"].length > 0) {
        console.log(`\nSuspected alias detected at rule ${name}`)
        console.log(item["suspectedAlias"])
    }

    item["strings"] = _.uniq(findAll(/(["'])(?:(?=(\\?))\2.)*?\1/g, value).map(items => items[0]))

    return item
})

fs.writeFile(OUTPUT_PATH, JSON.stringify(result, null, 4), (e) => {
    if (e) {
        console.log(e)
    }

    console.log("Write file succesfully!")
})