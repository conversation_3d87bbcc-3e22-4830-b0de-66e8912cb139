/**
 * This step will extract following data from orginal form JSON
 * 1. schema
 * 2. uiSchema: sorted based on alias order in schema 
 * 3. rules
 * 4. embedded_pdf
 * 
 * Generating these files have to be the first step any further calculating 
 */

const fs = require('fs');
const { toStringFromJsonRules } = require('./utils');
const { toIds, toSchemaObj, clone } = require("../../utils/basic");

const USAGE_STRING = 'Usage: node step1-extract.js FOLDER_NAME FILE_NAME';

const processUserInput = () => {
    const FOLDER_NAME = process.argv[2];
    const FILE_PATH = process.argv[3];

    if (!FOLDER_NAME || !FILE_PATH) {
        return Promise.reject(USAGE_STRING);
    }

    console.log(`\n\nExtracting data with given data`)
    console.log(`FOLDER: ${FOLDER_NAME}`)
    console.log(`FILE: ${FILE_PATH}`)

    return Promise.resolve({
        FOLDER_NAME,
        INPUT_PATH: FILE_PATH
    });
};


const calculateOuput = (data) => {
    const { FOLDER_NAME } = data

    // output
    const OUPUT_PATH = `./data/${FOLDER_NAME}/extract/`

    const promises = {
        ...data,
        OUPUT_PATH
    };
    return Promise.resolve(promises);
}

const toSchemaAlias = (parent) => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])
    if (!children) {
        return {}
    }

    return Object.fromEntries(
        Object.entries(children).map(
            ([k, v]) => {
                return [k, toSchemaAlias(v)]
            }
        )
    )
}

const readJSONFile = (data) => {
    const { INPUT_PATH } = data

    return new Promise(
        (resolve, reject) => {
            fs.readFile(INPUT_PATH, 'utf8', (err, json) => {
                if (err) {
                    reject(err)
                }

                resolve({
                    ...data,
                    form: JSON.parse(json)
                })
            })
        }
    )
}

const formatUiComps = (comps) => {
    return Object.fromEntries(
        Object.entries(comps).map(([k, comp]) => {
            delete comp["ui:marginBottom"]
            delete comp["ui:css"]
            delete comp["ui:width"]
            delete comp["ui:indentation"]
            delete comp["ui:dynamicLayout"]
            delete comp["ui:height"]
            delete comp["ui:fixedLayout"]
            delete comp["ui:enabledComment"]
            delete comp["ui:maxLength"]
            delete comp["ui:dateFormat"]
            delete comp["ui:viewInModal"]
            delete comp["ui:tooltipForDisabledState"]

            // This step is tackling old version of country & state dropdown where all of their options are listed in schema
            // If the form using new version of country & state dropdown, we can eliminate this check.
            if (comp["ui:multipleOption"] && comp["ui:multipleOption"]["options"].length > 30) {
                comp["ui:multipleOption"]["options"].length = 3
            }

            return [k, comp]
        }))
}


const sortUiSchema = (aliases, uiSchema) => Object.fromEntries(
    aliases.map(alias => [alias, uiSchema[alias]])
)

const extractData = (data) => {
    const { form } = data

    const initSchemaObj = form["form"]["namespaceFormSchemaMap"]["main"]["schema"]
    const schemaObj = toSchemaObj(clone(initSchemaObj))
    const rawSchemaObj = toSchemaObj(clone(initSchemaObj), true)

    const aliases = toIds(schemaObj)

    const uiSchemaObj = form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"]

    const sortedRawUiSchema = sortUiSchema(aliases, uiSchemaObj)

    const schema = JSON.stringify(schemaObj, null, 4)
    const rawSchema = JSON.stringify(rawSchemaObj, null, 4)
    const uiSchema = JSON.stringify(formatUiComps(clone(sortedRawUiSchema)), null, 4)
    const rawUiSchema = JSON.stringify(clone(sortedRawUiSchema), null, 4)
    const jsonRules = form["form"]["rules"]
    const rules = toStringFromJsonRules(jsonRules)
    const schemaAlias = JSON.stringify(toSchemaAlias(schemaObj), null, 4)
    const embeddedPdf = JSON.stringify(form["uploadedPdf"], null, 4)

    return Promise.resolve({
        ...data,
        schema,
        rawSchema,
        uiSchema,
        rawUiSchema,
        jsonRules: JSON.stringify(jsonRules, null, 4),
        rules,
        schemaAlias,
        embeddedPdf
    });
}

const writeData = (data) => {
    const {
        OUPUT_PATH,
        schema,
        rawSchema,
        uiSchema,
        rawUiSchema,
        jsonRules,
        rules,
        schemaAlias,
        embeddedPdf
    } = data
    const UI = `${OUPUT_PATH}ui.json`
    const RAW_UI = `${OUPUT_PATH}raw_ui.json`
    const COMPONENTS = `${OUPUT_PATH}components.json`
    const RAW_COMPONENTS = `${OUPUT_PATH}raw_components.json`
    const RULES = `${OUPUT_PATH}rules_.lua`
    const JSON_RULES = `${OUPUT_PATH}rules.json`
    const COMPONENTS_ALIAS = `${OUPUT_PATH}components_alias.json`
    const EMBEDDED_PDF = `${OUPUT_PATH}embedded_pdf.json`

    const all = [
        [uiSchema, UI],
        [rawUiSchema, RAW_UI],
        [schema, COMPONENTS],
        [rawSchema, RAW_COMPONENTS],
        [rules, RULES],
        [schemaAlias, COMPONENTS_ALIAS],
        [jsonRules, JSON_RULES],
        [embeddedPdf, EMBEDDED_PDF],
    ]
    const promises = all.map(([extractedData, dir]) => {
        return new Promise(
            (resolve, reject) => {
                fs.writeFile(dir, extractedData, (e) => {
                    if (e) {
                        reject(e)
                        return
                    }
                })

                console.log(`Write file ${dir} successfully`)
                resolve()
            }
        )
    })
    return Promise.all(promises);
}

processUserInput()
    .then(calculateOuput)
    .then(readJSONFile)
    .then(extractData)
    .then(writeData)
    .catch((e) => {
        console.log(e)
    })