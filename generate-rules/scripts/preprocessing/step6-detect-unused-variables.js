const FOLDER = process.argv[2]

if (!FOLDER) {
    console.log("USAGE: node step6.js [FODLER_NAME]")
}
const CONSTANTS = require("./constants")

const RULE_PATH = CONSTANTS.IN_STEP1_RULE(FOLDER);
const OUPUT_PATH = `./data/${FOLDER}/extract/unused-variable.json`;
const fs = require('fs');

const { extractUnusedVariables } = require("./utils")

const rules = require(RULE_PATH)


const unusedVariables = rules
    .map(rule => extractUnusedVariables(rule, []))
    .filter(([_, v]) => {
        return v && v.length > 0
    })

fs.writeFile(OUPUT_PATH,
    JSON.stringify(
        Object.fromEntries(unusedVariables), null, 4
    ),
    (e) => {
        if (e) {
            console.log(e)
            return
        }

        console.log("Write file successfully")
    })