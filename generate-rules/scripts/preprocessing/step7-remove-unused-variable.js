const FOLDER = process.argv[2]
const FILE_PATH = process.argv[3]

if (!FOLDER) {
    console.log("USAGE: node step7.js [FODLER_NAME] [PATH_TO_JSON_FORM]")
}

const CONSTANTS = require("./constants")

const RULE_PATH = CONSTANTS.IN_STEP1_RULE(FOLDER);
const UNUSED_VAR_PATH = `../../data/${FOLDER}/extract/unused-variable.json`;

const OUPUT_PATH = `./data/${FOLDER}/extract/step7-clean-form.json`;
const CLEANED_RULE_PATH = `./data/${FOLDER}/extract/rules__cleaned.lua`;

const fs = require('fs');

const { findAll } = require("../../utils/regex");
const {
    verifyFunctionInput,
    cleanRedundantSpace,
    toStringFromJsonRules,
    COMMENT_LINE,
} = require("./utils");

const VARIABLE_DECLARE_LINE = name => new RegExp(`[ ]*local[\\s]*${name}[\\s]*=[^\;]*;`)
const FUNCTION_DECLARE_LINE = name => new RegExp(`[ ]*local[\\s]*${name}[\\s]*=[\\s]*function`)

const removeComments = (value) => {
    return value
        .replaceAll(COMMENT_LINE, "")
}

const cleanRules = (rules, unusedVars) =>
    rules.map(rule => {
        const { value, name } = rule

        const unused = unusedVars[name]

        if (!unused) {
            const cleanedRule = cleanRedundantSpace(removeComments(value))

            return { ...rule, value: cleanedRule }
        }


        const unusedDeclarations = unused
            // Ignore unused function declaration because we cannot remove it precisely
            .filter(varName => {
                const rgx = FUNCTION_DECLARE_LINE(varName)
                const extractStatements = findAll(rgx, value)

                return extractStatements.length == 0
            })
            .map(varName => {
                const rgx = VARIABLE_DECLARE_LINE(varName)
                const extractStatements = findAll(rgx, value)

                if (extractStatements.length > 1) {
                    console.log("Something go south. There up to 2 declarations in rule")
                    console.log(value)
                    return null
                }

                return extractStatements[0]
            })

        let cleanedRule = value

        unusedDeclarations.map(statement => {
            cleanedRule = cleanedRule.replace(statement, "")
        })


        const verifiedInput = verifyFunctionInput(cleanedRule, name)

        const removedComments = removeComments(verifiedInput)

        const cleanedRedundantSpace = cleanRedundantSpace(removedComments)

        return { ...rule, value: cleanedRedundantSpace }
    })

const readForm = () =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(FILE_PATH, 'utf8', (err, data) => {
                if (err) {
                    reject(err)
                }

                resolve(JSON.parse(data))
            })
        }
    )

// if the rule is like just "function() []", it should be removed
const isEmtptyRule = (value) => 
    findAll(/\w+/, value).length < 2


const writeForm = (form) => {
    const rules = require(RULE_PATH)
    const unusedVars = require(UNUSED_VAR_PATH)

    const cleanedRules = 
        cleanRules(rules, unusedVars)
        .filter(rule => !isEmtptyRule(rule.value))

    form["form"]["rules"] = cleanedRules

    const writteRules = toStringFromJsonRules(cleanedRules)

    fs.writeFile(OUPUT_PATH, JSON.stringify(form, null, 4), (e) => {
        if (e) {
            reject(e)
        }

        console.log("Write file successfully")
        console.log(OUPUT_PATH)
    })

    fs.writeFile(CLEANED_RULE_PATH, writteRules, (e) => {
        if (e) {
            reject(e)
        }

        console.log("Write file successfully")
        console.log(CLEANED_RULE_PATH)
    })
}


readForm()
    .then(writeForm)
    .catch(e => console.log(e))