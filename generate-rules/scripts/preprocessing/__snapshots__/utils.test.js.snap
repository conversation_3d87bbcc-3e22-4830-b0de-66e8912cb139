// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Extract correct result after replacing add statement with empty value 1`] = `
"function(investortype)

local op1 = investortype.value == \\"individual\\";

[] /* deleted alias: alias1 */

+ alias2.add(\\"hide\\", !op1)"
`;

exports[`Extract correct result after replacing add statement with empty value 2`] = `
"function(investortype)

local op1 = investortype.value == \\"individual\\";

alias1.add(\\"hide\\", !op1)
+ [] /* deleted alias: alias2 */
"
`;

exports[`Extract correct unused variables 1`] = `
Array [
  "random name 1",
  Array [],
]
`;

exports[`Extract correct unused variables 2`] = `
Array [
  "random name 2",
  Array [
    "x",
  ],
]
`;

exports[`Extract correct unused variables 3`] = `
Array [
  "random name 2",
  Array [
    "y",
    "x",
  ],
]
`;

exports[`Extract correct unused variables 4`] = `
Array [
  "random name 2",
  Array [
    "isSingleMemberLLC",
    "isLP",
    "isGP",
    "isMultiMemberLLC",
    "isPublicCorporation",
    "isPrivateCorporation",
    "isNonProfit",
  ],
]
`;

exports[`Extract correct unused variables 5`] = `
Array [
  "random name 2",
  Array [
    "isIRA_401",
    "isTrusts",
    "isSingleMemberLLC",
    "isLP",
    "isGP",
    "isMultiMemberLLC",
    "isPublicCorporation",
    "isPrivateCorporation",
    "isNonProfit",
    "isEntity",
    "isIndividual",
    "isRevocable_LivingTrust",
    "isIrrevocableTrust",
    "isGrantorTrust",
    "isOtherTrust",
    "isOther",
  ],
]
`;
