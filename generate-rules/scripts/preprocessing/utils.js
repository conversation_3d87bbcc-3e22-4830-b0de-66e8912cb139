const { purge } = require("../../utils/basic")
const { findAll } = require("../../utils/regex")
const fs = require('fs');
const _l = require('lodash');
const matchRecursive = require("match-recursive")

const VARIABLE_DECLARE_LINE = /[ ]*local[ ]*[_a-zA-Z][_a-zA-Z0-9]*[ ]*=[^\;]*;/
const USE_VARIABLE_LINE = name => new RegExp(`${name}[ ]*[^=]*`, "g")
const VARIABLE = /\b[_a-zA-Z][_a-zA-Z0-9]*\b/
const FUNCTION_DECLARE_LINE = /[ ]*function[ ]*\([^(\(|;|=)]*\)/
const STRING = /'[^']*'|"[^"]*"/g
const COMMENT_LINE = /\/\/.*/g
const VARIABLE_DECLARE_LINE_BY_NAME = name => new RegExp(`[ ]*local[ ]*${name}[ ]*=[^\;]*;`)
const REDUNDANT_COLONS_1 = /,[\s,]*,/g
const REDUNDANT_COLONS_2 = /function\([\s,]*/g
const ALL_ACCESSIBLE_ATTRIBUTES = ["value", "disable", "disableByParent", "hide", "hideByParent", "touched", "error", "warning", "widgetError", "widgetWarning", "disabledOptions", "signingType"]
const ACCESS_VALUE_LINE = name => new RegExp(`${name}\.(${ALL_ACCESSIBLE_ATTRIBUTES.join("|")})`, "g")

const UNIQUE_STRING = "this_unique_string_is_for_replace_alias__"

const WHITE_LIST_VARIABLES = [
    "split"
]


const verifyFunctionInput = (rule, name) => {
    // const FUNCTION_DECLARE_LINE = /[ ]*function[ ]*\([^(\(|;|=)]*\)/

    const functionMatches = findAll(FUNCTION_DECLARE_LINE, rule)

    if (!functionMatches[0]) {
        console.error("Cannot find any function in rule ", name)
    }

    const initFunction = functionMatches[0]

    const inputs = findAll(VARIABLE, initFunction).filter(x => x != "function")

    const redundantInputs = inputs.filter(input => {
        return findAll(ACCESS_VALUE_LINE(input), rule).length == 0
    })

    if (redundantInputs.length == 0) {
        return rule
    }

    let filteredRedundantInputs = initFunction
    redundantInputs.map(input => {
        filteredRedundantInputs = filteredRedundantInputs.replace(input, "")
    })
    const filteredRedudantColons = filteredRedundantInputs
        .replace(REDUNDANT_COLONS_2, "function(\n")
        .replaceAll(REDUNDANT_COLONS_1, ",")

    return rule.replace(initFunction, filteredRedudantColons)
}

const cleanRedundantSpace = (value) => {
    return value
        .replace(/\n\s*\n/g, "\n\n")
        .replace(/\n\n+/g, "\n\n")
}
const removeComments = (value) => {
    const commentMatches = findAll(COMMENT_LINE, value)
    let filteredComments = value

    commentMatches.map(match => {
        filteredComments = filteredComments.replace(match, "")
    })

    return filteredComments
}

const removeFirstFunctions = (value, name) => {
    const functionMatches = findAll(FUNCTION_DECLARE_LINE, value)

    if (!functionMatches[0]) {
        console.error("Cannot find any function in rule ", name)

        return value
    }

    const filteredFunction = value.replace(functionMatches[0], "")

    return filteredFunction
}
const extractFirstFunctions = (value, name) => {
    const functionMatches = findAll(FUNCTION_DECLARE_LINE, value)

    if (!functionMatches[0]) {
        console.error("Cannot find any function in rule ", name)

        return value
    }

    return functionMatches[0].replace(/\s*/g, '')
}


const verifyVariables = (vars, name) => {
    vars.reduce((arr, v) => {
        if (arr.indexOf(v) > -1) {
            console.log("\nA variable is declared twice")
            console.log("Rule name :", name)
            console.log("Variable :", v)

            return arr
        } else {
            return [...arr, v]
        }
    }, [])
}

const extractUnusedVariables = (rule, lastDectectedVariables) => {
    const { value, name } = rule

    const filteredFunction = removeFirstFunctions(value, name)

    let filteredComments = removeComments(filteredFunction)

    let removedLastDetectVariableDeclaration = filteredComments
    lastDectectedVariables.map(varName => {
        const rgx = VARIABLE_DECLARE_LINE_BY_NAME(varName)

        removedLastDetectVariableDeclaration = removedLastDetectVariableDeclaration.replace(rgx, "")
    })
    const declarations = findAll(VARIABLE_DECLARE_LINE, removedLastDetectVariableDeclaration)

    const variables = declarations.map(declare => {
        const matches = findAll(VARIABLE, declare)

        return matches[1]
    })

    verifyVariables(variables, name)

    const filteredString = removedLastDetectVariableDeclaration.replace(STRING, "")

    const usedVariables = variables.filter(variable => {
        const rgx = USE_VARIABLE_LINE(variable)
        const extract = findAll(rgx, filteredString)
        const matched = extract && extract[1]

        return matched
    })

    const filtteredVariables = usedVariables.concat(WHITE_LIST_VARIABLES)
    const unusedVariables = purge(variables, filtteredVariables)

    if (unusedVariables.length == 0) {
        return [name, unusedVariables.concat(lastDectectedVariables)]
    }

    return extractUnusedVariables(rule, lastDectectedVariables.concat(unusedVariables))
}

const replaceAddStatement = (str, alias) => {
    const removedAlias = str
        .replaceAll(new RegExp(`atd[\\s]*\\.[\\s]*add[\\s]*\\([\\s]*["']\\b${alias}\\b["']`, "g"), `[] /* atd.add("${alias}"`)

    const parts = removedAlias.split("/* atd.add")

    return parts.map((p, i) => {
        if (i == 0) {
            return p
        }

        const matches = matchRecursive(p, "(...)")

        const firstMatch = matches.length ? matches[0] : typeof matches == "string" ? matches : ""

        if (!firstMatch) {
            console.log("Invalid add statement")
            console.log('p :>> ', p);
            console.log('matches :>> ', matches);
        }
        return p.replace(`(${firstMatch})`, `/* deleted alias: ${alias} */`)
    })
        .join("")
}

const removeInvalidOutputs = (rules, availableAliases, extractedParames, scopeId, mapping, curAlias, curOptionDict = {}) => {
    let allInvalidinputIds = []
    const updatedRules = rules.map((r, i) => {
        const { value, name } = r

        const removedComments = removeComments(value)

        const inputIds = extractedParames[i]["input"]
        const outputIds = Object.keys(extractedParames[i]["output"])

        const validIds = ([...inputIds, ...outputIds]).filter(id => availableAliases.indexOf(id) > -1)
        const validIputIds = inputIds.filter(id => availableAliases.indexOf(id) > -1)
        const validOutputIds = outputIds.filter(id => availableAliases.indexOf(id) > -1)
        const invalidOutputIds = outputIds.filter(id => availableAliases.indexOf(id) == -1)
        const invalidInputIds = inputIds.filter(id => availableAliases.indexOf(id) == -1)

        if (validIds.length == 0 || (validIds.length == 0 && validOutputIds.length == 0)) {
            return null
        } else {
            // console.log('name :>> ', name);
            // console.log('validIputIds :>> ', validIputIds);
            // console.log('validOutputIds :>> ', validOutputIds);
            // console.log('invalidOutputIds :>> ', invalidOutputIds);
        }

        allInvalidinputIds = [...allInvalidinputIds, ...invalidInputIds]

        const removedOutputIds = validIputIds.length == 0 ? invalidOutputIds : []
        const replacedOutputIds = validIputIds.length == 0 ? [] : invalidOutputIds

        allInvalidinputIds = [...allInvalidinputIds, ...replacedOutputIds]

        // replace output with empty statement
        let replacedOutput = removedComments
        removedOutputIds.forEach(id => {
            replacedOutput = replaceAddStatement(replacedOutput, id)
        })

        // replace input with new alias
        let replacedInput = replacedOutput
        let replacedIds = ([...invalidInputIds, ...replacedOutputIds])

        replacedIds.forEach(id => {
            replacedInput = replacedInput.replaceAll(new RegExp(`\\b${id}\\b`, "g"), scopeId + "__" + id)
        })

        // replace alias based on mapping dict
        // TODO: Fix me
        let posibleConflictStrings = [...ALL_ACCESSIBLE_ATTRIBUTES, ...(inputIds.map(id => curOptionDict[id] || []).flat())]
        let replaceByMapping = replacedInput
        Object.entries(mapping)
            .reverse()
            .forEach(([k, v]) => {
                if (posibleConflictStrings.indexOf(k) > -1) {
                    console.log(`\nNot safe to replace: ${k} => ${v}\nRule: ${name}`)
                    return
                }

                replaceByMapping = replaceByMapping.replaceAll(new RegExp(`\\b${k}\\b`, "g"), UNIQUE_STRING + v)
            })
        
        replaceByMapping = replaceByMapping.replaceAll(UNIQUE_STRING, "")

        let cleanedSpace = cleanRedundantSpace(replaceByMapping)
        let verifiedInput = verifyFunctionInput(cleanedSpace, name)

        let newValue = verifiedInput


        return [{ ...r, value: newValue, name: `${name}` }, r]
    }).filter(x => x)

    allInvalidinputIds = allInvalidinputIds
        .filter(x => x)
        .reduce(
            (arr, item) => arr.indexOf(item) == -1 ? [...arr, item] : arr, []
        )

    return {
        newRules: updatedRules.map(x => x[0]),
        invalidInputIds: allInvalidinputIds,
        originalRules: updatedRules.map(x => x[1])
    }
}

const DEFAULT_SCHEMA = {
    "type": "null",
    "title": "Unknown"
}
const DEFAULT_SCHEMA_UI = {
    "ui:formattedText": "Unknown",
    "ui:widget": "Paragraph"
}

const deleteMapping = (ui) => {
    const cloned = { ...ui }

    if (cloned["ui:pdfMapping"]) {
        cloned["ui:pdfMapping"] = []
    }

    const options = _l.get(cloned, ["ui:multipleOption", "options"], [])
    if (options.length > 0) {
        cloned["ui:multipleOption"]["options"] = options.map(([k, v]) => {
            const op = { ...v, "pdfMapping": "" }

            return [k, op]
        })
    }

    return cloned
}

const toReplacedAliasesUI = (invalidIds, scopeId, step0, ui) => {
    const unknowIds = []

    const components = invalidIds.map(id => {
        const meta = step0["meta"][id]

        if (!meta) {
            unknowIds.push(id)
            return [scopeId + "__" + id, DEFAULT_SCHEMA]
        }

        return [scopeId + "__" + id, meta]
    })
    const uiSchema = invalidIds.map(id => {
        if (unknowIds.indexOf(id) > -1) {
            return [scopeId + "__" + id, DEFAULT_SCHEMA_UI]
        }

        const deletedMapping = deleteMapping(ui[id])

        return [scopeId + "__" + id, deletedMapping]
    })

    return {
        components: Object.fromEntries(components),
        ui: Object.fromEntries(uiSchema)
    }
}

const toStringFromJsonRules = (rules) => {
    return rules.map(r => {
        const { value, name } = r
        let str = `------------  ${name}  ------------\n`

        str += value

        return str
    }).join("\n\n")
}

module.exports = {
    extractUnusedVariables,
    toStringFromJsonRules,
    removeComments,
    removeFirstFunctions,
    extractFirstFunctions,
    removeInvalidOutputs,
    replaceAddStatement,
    toReplacedAliasesUI,
    verifyFunctionInput,
    cleanRedundantSpace,
    VARIABLE_DECLARE_LINE,
    USE_VARIABLE_LINE,
    VARIABLE,
    FUNCTION_DECLARE_LINE,
    STRING,
    COMMENT_LINE,
    VARIABLE_DECLARE_LINE_BY_NAME,
    ACCESS_VALUE_LINE,
    ALL_ACCESSIBLE_ATTRIBUTES,
}