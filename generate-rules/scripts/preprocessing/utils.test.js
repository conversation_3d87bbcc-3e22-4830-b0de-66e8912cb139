const { extractUnusedVariables, replaceAddStatement } = require("./utils")

it('Extract correct unused variables', () => {
    const input = { value: "function(comp) local x = 1; x + 1", name: "random name 1" }

    expect(extractUnusedVariables(input, [])).toMatchSnapshot()
})
it('Extract correct unused variables', () => {
    const input = { value: "function(comp) local x = 1; []", name: "random name 2" }

    expect(extractUnusedVariables(input, [])).toMatchSnapshot()
})
it('Extract correct unused variables', () => {
    const input = { value: "function(comp) local x = 1; local y = x + 1; []", name: "random name 2" }

    expect(extractUnusedVariables(input, [])).toMatchSnapshot()
})
it('Extract correct unused variables', () => {
    const input = {
        value: "function(investortype)\n\n// Individuals\nlocal isIndividual = investortype.value == \"investortype_individual\";\n\n// IRA, 401k, Retirement Plans\nlocal isIRA_401 = investortype.value == \"investortype_ira\";\n\n// Trusts\nlocal isRevocable_LivingTrust = investortype.value == \"investortype_reveocabletrust\";\nlocal isIrrevocableTrust = investortype.value == \"irrevocabletrustinvestortype\";\nlocal isGrantorTrust = investortype.value == \"grantortrustinvestortype\";\nlocal isOtherTrust = investortype.value == \"investortype_othertypeoftrust\";\nlocal isTrusts = isRevocable_LivingTrust || isIrrevocableTrust || isGrantorTrust || isOtherTrust;\n\n// Single Member LLCs and  Limited Partnerships\nlocal isSingleMemberLLC = investortype.value == \"singlememberllcinvestortype\";\nlocal isLP = investortype.value == \"investortype_limitedpartnership\";\n\n\n// Other Parnterships and Corporations, and LLCs\nlocal isGP = investortype.value == \"investortype_generalpartnership\";\nlocal isMultiMemberLLC = investortype.value == \"multimemberllcinvestortype\";\nlocal isPublicCorporation = investortype.value == \"publiccorporationinvestortype\";\nlocal isPrivateCorporation = investortype.value == \"privatecorporationinvestortype\";\nlocal isNonProfit = investortype.value == \"nonprofitentityinvestortype\";\n\n\n// Other\nlocal isOther = investortype.value == \"investortype_othertypeoforg\";\n\nlocal isEntity = investortype.value != \"\" && !isIndividual;\n\nlocal indiPrefill = \n    if isIndividual then\n        radio2.add(\"value\", \"no\")\n        + questiona.add(\"value\", \"no\")\n        + questionb.add(\"value\", \"no\")\n        + questionc.add(\"value\", \"no\")\n        + radio.add(\"value\", \"no\")\n        + multiplecheckbox9.add(\"value\", [\"op12\"])\n\n    else\n        []\n    ;\n\nindividualsandiras.add(\"hide\", !isIndividual && !isIRA_401)\n    + individualsignatures.add(\"hide\", !isIndividual)\n    + subscriber.add(\"hide\", !isIndividual)\n    + subscriber2.add(\"hide\", !isIndividual)\n    + witnessmustbedifferentfromtheinvestor.add(\"hide\", !isIndividual)\n    + witnessmustbedifferentfromtheinvestor2.add(\"hide\", !isIndividual)\n    + individualparagraph.add(\"hide\", !isIndividual)\n    + forindividuals1.add(\"hide\", !isIndividual)\n    + taxresidencystateandcountry_individual_investorinformation.add(\"hide\", !isIndividual)\n    + areyoujointtenantsortenantsincommon.add(\"hide\", !isIndividual)\n    + areyoumarriedandresidinginacommunitypropertystate.add(\"hide\", !isIndividual)\n\n    + investortype_othertypeoftrust_specify.add(\"hide\", !isOtherTrust)\n    + areyouadomesticgrantortrust.add(\"hide\", !isTrusts)\n\n    + entitysignatures.add(\"hide\", !isEntity)\n    + entitysubscriber.add(\"hide\", !isEntity)\n    + entitysubscriber1.add(\"hide\", !isEntity)\n    + entitysubscribersname.add(\"hide\", !isEntity)\n    + forindividuals2.add(\"hide\", !isEntity)\n\n    + investortype_othertypeoforg_specify.add(\"hide\", !isOther)\n\n    + custodiancheckbox.add(\"hide\", !isIRA_401)\n\n    + individualcrs.add(\"hide\", !isIndividual)\n    + crsforindividuals.add(\"hide\", !isIndividual)\n    + reasonsfornothavingtinindividual.add(\"hide\", !isIndividual)\n    + individualsignatures1.add(\"hide\", !isIndividual)\n    + entittycrs.add(\"hide\", !isEntity)\n    + questionsforentities.add(\"disable\", !isEntity)\n    + crsforentities.add(\"hide\", !isEntity)\n    + reasonsfornothavingtinentities.add(\"hide\", !isEntity)\n    + entitysignatures1.add(\"hide\", ! isEntity)\n    + specialenties.add(\"hide\", ! isEntity)\n    + secondauthorizedsignatory.add(\"hide\", ! isEntity)\n    + messageforsection8.add(\"hide\", !isIndividual)\n\n    + multiplecheckbox9.add(\"disable\", isIndividual)\n    + radio.add(\"disable\", !isEntity)\n\n    + events.add(\"value\", \"investortype\")\n    + question9.add(\"disable\", isIRA_401)\n    + indiPrefill\n",
        name: "random name 2"
    }

    expect(extractUnusedVariables(input, [])).toMatchSnapshot()
})
it('Extract correct unused variables', () => {
    const input = {
        value: "function(investortype)\n\n// Individuals\nlocal isIndividual = investortype.value == \"investortype_individual\";\n\n// IRA, 401k, Retirement Plans\nlocal isIRA_401 = investortype.value == \"investortype_ira\";\n\n// Trusts\nlocal isRevocable_LivingTrust = investortype.value == \"investortype_reveocabletrust\";\nlocal isIrrevocableTrust = investortype.value == \"irrevocabletrustinvestortype\";\nlocal isGrantorTrust = investortype.value == \"grantortrustinvestortype\";\nlocal isOtherTrust = investortype.value == \"investortype_othertypeoftrust\";\nlocal isTrusts = isRevocable_LivingTrust || isIrrevocableTrust || isGrantorTrust || isOtherTrust;\n\n// Single Member LLCs and  Limited Partnerships\nlocal isSingleMemberLLC = investortype.value == \"singlememberllcinvestortype\";\nlocal isLP = investortype.value == \"investortype_limitedpartnership\";\n\n\n// Other Parnterships and Corporations, and LLCs\nlocal isGP = investortype.value == \"investortype_generalpartnership\";\nlocal isMultiMemberLLC = investortype.value == \"multimemberllcinvestortype\";\nlocal isPublicCorporation = investortype.value == \"publiccorporationinvestortype\";\nlocal isPrivateCorporation = investortype.value == \"privatecorporationinvestortype\";\nlocal isNonProfit = investortype.value == \"nonprofitentityinvestortype\";\n\n\n// Other\nlocal isOther = investortype.value == \"investortype_othertypeoforg\";\n\nlocal isEntity = investortype.value != \"\" && !isIndividual; []",
        name: "random name 2"
    }

    expect(extractUnusedVariables(input, [])).toMatchSnapshot()
})
it('Extract correct result after replacing add statement with empty value', () => {
    const input = "function(investortype)\n\nlocal op1 = investortype.value == \"individual\";\n\nalias1.add(\"hide\", !op1)\n+ alias2.add(\"hide\", !op1)"

    expect(replaceAddStatement(input, "alias1")).toMatchSnapshot()
    expect(replaceAddStatement(input, "alias2")).toMatchSnapshot()
})