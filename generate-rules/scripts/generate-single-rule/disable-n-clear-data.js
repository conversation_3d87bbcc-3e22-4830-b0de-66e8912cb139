const FOLDER = process.argv[2]
const PARENT_ID = process.argv[3]
const LOGIC = process.argv[4]

if (!FOLDER || !PARENT_ID) {
    console.log("USAGE: node disable-n-clear-data.jss [FODLER_NAME] [PARENT_ID] [LOGIC](optional)")
}

const { findAll } = require('../../utils/regex')
const { toIdsWithoutProperties } = require('../../utils/basic')

const { DEFAULT_VALUE_BY_TYPES } = require("../analyze/constants")
const s0 = require(`../../data/${FOLDER}/analyze/step0.json`)
const ui = require(`../../data/${FOLDER}/extract/ui.json`)
const componentAlias = require(`../../data/${FOLDER}/extract/components_alias.json`)

const _ = require('lodash')

let inputIds = findAll(/\b\w+\b\.value/, LOGIC)
    .map(getStm => getStm.replace(".value", ""))
    .join(", ")

let result = `function(${inputIds})\n\nlocal disable = ${LOGIC || "true"};\n\n`

const path = s0.dict[PARENT_ID]
const parent = _.get(componentAlias, [...path, PARENT_ID])
const disabledIds = JSON.stringify(parent) == "{}" ? [PARENT_ID] : [...toIdsWithoutProperties(parent), PARENT_ID];

const disableRepeatableIds = disabledIds.map(repeatableId => {
    const subIds = Object.entries(s0.repeatable)
        .filter(([_, v]) => v == repeatableId)
        .map(([k, _]) => k)

    return [repeatableId, subIds]
}).filter(([_, subIds]) => subIds.length > 0)

const repeatableIds = disableRepeatableIds.map(([k, v]) => [...v, k]).flat()

const disableNonRepeatableInputIds = disabledIds.filter(disabledId => {
    const childType = s0.meta[disabledId].type

    const isRepeatable =
        repeatableIds.indexOf(disabledId) > -1

    if (!isRepeatable && (
        childType == "string" ||
        childType == "array" ||
        childType == "number"
    )) {
        return true
    }

    return false
})

// generate default value for repeatable

const toDefaulValObj = (valType, isRequired) => {
    const obj = {
        "hide": false,
        "hideByParent": false,
        "disable": false,
        "disableByParent": false,
        "touched": false
    }

    obj["value"] = DEFAULT_VALUE_BY_TYPES[valType]

    if (isRequired) {
        obj["widgetError"] = "This field is required"
    }

    return obj
}

const toRepeatableVarName = (id) => `${id}DefaultVal`

result += disableRepeatableIds.map(([id, subIds]) => {
    const val = subIds.map(subId => {
        const childType = s0.meta[subId].type
        const isRequired = !!ui[subId]["ui:required"]

        return [subId, toDefaulValObj(childType, isRequired)]
    })

    const varName = toRepeatableVarName(id)

    return `local ${varName} = [${JSON.stringify(Object.fromEntries(val), null, 4)}];`
}).join('\n') + "\n\n"

result += `atd.add("${PARENT_ID}", "disable", disable)\n\n+ `


result += disableNonRepeatableInputIds.map(disabledId => {
    const childType = s0.meta[disabledId].type
    const defaultVal = JSON.stringify(DEFAULT_VALUE_BY_TYPES[childType])

    return `( if disable then atd.add("${disabledId}", "value", ${defaultVal}) else [] )`
}).filter(x => x).join("\n+ ")

if (disableRepeatableIds.length > 0) {
    result += disableRepeatableIds.map(([id, _]) => {
        const varName = toRepeatableVarName(id)

        return `( if disable then atd.add("${id}", "value", ${varName}) else [] )\n+ atd.add("${id}", "disable", disable)`
    }).join('\n+\n')
}


console.log(result)