const FOLDER = process.argv[2]
const FROM_PARENT_ID = process.argv[3]
const TO_PARENT_ID = process.argv[4]

if (!FOLDER || !FROM_PARENT_ID) {
    console.log("USAGE: node prefill.js [FODLER_NAME] [FROM_PARENT_ID] [TO_PARENT_ID]")
}

const { findAll } = require('../../utils/regex')
const { toIdsWithoutProperties } = require('../../utils/basic')

const { DEFAULT_VALUE_BY_TYPES } = require("../analyze/constants")
const s0 = require(`../../data/${FOLDER}/analyze/step0.json`)
const ui = require(`../../data/${FOLDER}/extract/ui.json`)
const componentAlias = require(`../../data/${FOLDER}/extract/components_alias.json`)

const _ = require('lodash')

let filterFields = ids => ids.filter(id => {
    const childType = s0.meta[id].type

    return (
        childType == "string" ||
        childType == "array" ||
        childType == "number"
    )
})

const fromPath = s0.dict[FROM_PARENT_ID]
const fromParent = _.get(componentAlias, [...fromPath, FROM_PARENT_ID])
const fromIds = filterFields(JSON.stringify(fromParent) == "{}" ? [FROM_PARENT_ID] : toIdsWithoutProperties(fromParent))

const toPath = s0.dict[TO_PARENT_ID]
const toParent = _.get(componentAlias, [...toPath, TO_PARENT_ID])
const toIds = filterFields(JSON.stringify(toParent) == "{}" ? [TO_PARENT_ID] : toIdsWithoutProperties(toParent))

let result = `function(\n\t${fromIds.join("\n\t, ")}\n)\n\n`

// TODO: make it more flexible
if (fromIds.length != toIds.length) {
    throw new Error("Cannot prefill because number of fields is not the same.")
}

result += fromIds.map((inputId, i) => `atd.add("${toIds[i]}", "value", ${inputId}.value)`).join("\n+ ")

console.log(result)