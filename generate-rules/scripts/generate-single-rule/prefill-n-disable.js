const FOLDER = process.argv[2]
const FROM_PARENT_ID = process.argv[3]
const TO_PARENT_ID = process.argv[4]

if (!FOLDER || !FROM_PARENT_ID) {
    console.log("USAGE: node prefill.js [FODLER_NAME] [FROM_PARENT_ID] [TO_PARENT_ID]")
}

const { toIdsWithoutProperties } = require('../../utils/basic')

const s0 = require(`../../data/${FOLDER}/analyze/step0.json`)
const s1 = require(`../../data/${FOLDER}/analyze/step1.json`)
const componentAlias = require(`../../data/${FOLDER}/extract/components_alias.json`)

const _ = require('lodash')

const HIDDEN_IDS = Object.values(s1).flat().filter(item => item["hidden"]).map(item => item.id)

let filterFields = ids => ids.filter(id => {
    const childType = s0.meta[id].type

    return (
        childType == "string" ||
        childType == "array" ||
        childType == "number"
    ) && HIDDEN_IDS.indexOf(id) == -1
})

const fromPath = s0.dict[FROM_PARENT_ID] || []
const fromParent = _.get(componentAlias, [...fromPath, FROM_PARENT_ID]) || {}
const fromIds = filterFields(JSON.stringify(fromParent) == "{}" ? [FROM_PARENT_ID] : toIdsWithoutProperties(fromParent))

const toPath = s0.dict[TO_PARENT_ID] || []
const toParent = _.get(componentAlias, [...toPath, TO_PARENT_ID]) || {}
const toIds = filterFields(JSON.stringify(toParent) == "{}" ? [TO_PARENT_ID] : toIdsWithoutProperties(toParent))

let result = `function(\n\t${fromIds.join("\n\t, ")}\n)\n\n`

if (!(fromIds.length == toIds.length || toIds.length == 1)) {
    throw new Error(`Cannot prefill because number of fields is not the same.\nFrom Ids: ${fromIds}\nTo Ids: ${toIds}`)
}

result += `atd.add("${TO_PARENT_ID}", "disable", true)\n\n`

if (fromIds.length == toIds.length) {
    result += "+ " + fromIds.map((inputId, i) => `atd.add("${toIds[i]}", "value", ${inputId}.value)`).join("\n+ ")
} else {
    result += "+ " + `atd.add("${toIds[0]}", "value", \n\t${fromIds.map(inputId => `${inputId}.value`).join("\n\t+ ")}\n)`
}

console.log(result)