# Generate customized rules


## Supported rules

1. Disable & uncheck options

    - Applicable widget type: Radio, Checkbox
    - Command line:
        ```
        node ./scripts/generate-single-rule/disable-n-clear-data.js [FOLDER] [ALIAS] [USE_MAPPING]
        ```
        - FOLDER: available form name in folder `./data`
        - ALIAS: a radio or checkbox alias
        - USE_MAPPING: 0 or 1
    - Examples:
        - Example 1:
            - Command line:
                ```
                node disable-n-uncheck-options.js folder-name multiplecheckbox
                ```
            - Output:
                ```
                function(multiplecheckbox)

                local all = ["op1", "op2"];

                local purge = function(arr, elements)
                    std.filter(function(ele) !std.member(elements, ele), arr);

                local disabledOps = [];

                ...

                local e1 = 
                    // This condition is to avoid rule looping
                    if intersect(uncheckOps + disabledOps, multiplecheckbox.value) != [] then 
                        atd.add("multiplecheckbox", "value", value)
                    else
                        [];

                local e2 = atd.add("multiplecheckbox", "disabledOptions", disabledOps);

                e1 + e2

                ```
        - Example 2:
            - Command line:
                ```
                node disable-n-uncheck-options.js folder-name radio
                ```
            - Output:
                ```
                function(radio)

                local purge = function(arr, elements)
                    std.filter(function(ele) !std.member(elements, ele), arr);

                local disabledOps = [];

                // Always clear the data of a disabled field which has its disabled situation depending on a condition
                local value = if std.member(disabledOps, radio.value) then "" else radio.value;
                local e1 = atd.add("radio", "value", value);
                local e2 = atd.add("radio", "disabledOptions", disabledOps);

                e1 + e2
                ```

2. Disable & clear data on a field or a group of fields

    - This script will collect all aliases in given field alias in case it's a group or repeatable and generate disable statement with proper default value for sub-children
    - Applicable widget: Every field (group, textbox, etc.) including Repeatable
    - Examples:
        - Example 1:
            - Command line:
                ```
                node disable-n-clear-data.js folder-name textbox
                ```
            - Output:
                ```
                function()

                local disable = true;

                atd.add("textbox", "disable", disable)

                + ( if disable then atd.add("textbox", "value", "") else [] )
                ```
        - Example 2:
            - Command line:
                ```
                node disable-n-clear-data.js folder-name repeatable
                ```
            - Output:
                ```
                function()

                local disable = true;

                local repeatableDefaultVal = [{
                    "textbox": {
                        "hide": false,
                        "hideByParent": false,
                        "disable": false,
                        "disableByParent": false,
                        "touched": false,
                        "value": ""
                    },
                    "group": {
                        "hide": false,
                        "hideByParent": false,
                        "disable": false,
                        "disableByParent": false,
                        "touched": false,
                        "value": {}
                    },
                    "paragraph": {
                        "hide": false,
                        "hideByParent": false,
                        "disable": false,
                        "disableByParent": false,
                        "touched": false,
                        "value": null
                    },
                    ...
                }];

                atd.add("repeatable", "disable", disable)

                + ( if disable then atd.add("repeatable", "value", repeatableDefaultVal) else [] )
                + atd.add("repeatable", "disable", disable)
                ```

3. Prefill

    - Get data from a group or a field and prefill them to destination
    - Applicable Widget: Group and all input fields
    - Command line:
        ```
        node prefill.js [FODLER_NAME] [FROM_PARENT_ID] [TO_PARENT_ID]
        ```
    - FROM_PARENT_ID and TO_PARENT_ID have to be aliases that have the same schema structure or widget types. For example, if FROM_PARENT_ID is a textbox, TO_PARENT_ID has to be the same. If FROM_PARENT_ID is a group of 2 textboxes and 3 radio, TO_PARENT_ID has to be the same
    - Examples:
        - Command line:
            ```
            node prefill-n-disable.js form-name group1 group2
            ```
        - Output:
            ```
            function(
                country
                , numberandstreet
                , city
                , us_state
                , us_zipcode
                , nonus_state
                , nonus_zipcode
            )

            atd.add("country1", "value", country.value)
            + atd.add("numberandstreet1", "value", numberandstreet.value)
            + atd.add("city1", "value", city.value)
            + atd.add("us_state1", "value", us_state.value)
            + atd.add("us_zipcode1", "value", us_zipcode.value)
            + atd.add("nonus_state1", "value", nonus_state.value)
            + atd.add("nonus_zipcode1", "value", nonus_zipcode.value)
            ```

4. Prefill & disable

    - The same as Prefill
    - Command line:
        ```
        node prefill.js [FODLER_NAME] [FROM_PARENT_ID] [TO_PARENT_ID]
        ```
    - Examples:
        - Command line
            ```
            node prefill-n-disable.js form-name textbox1 textbox2
            ```
        - Output:
            ```
            function(
                textbox1
            )

            atd.add("textbox2", "disable", true)

            + atd.add("textbox2", "value", textbox1.value)
            ```

