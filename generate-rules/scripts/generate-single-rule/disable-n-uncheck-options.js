const FOLDER = process.argv[2]
const ALIAS = process.argv[3]
const USE_MAPPING = process.argv[4]


if (!FOLDER || !ALIAS) {
    console.log("USAGE: node disable-n-clear-data.js [FOLDER] [ALIAS] [USE_MAPPING]")
    console.log("TYPE is 'checkbox' or 'radio'")
}

const s0 = require(`../../data/${FOLDER}/analyze/step0.json`)
const COMMON_RULES = require('../../data/common-rules.json')

const _ = require("lodash")

const ops = _.get(s0, ["options", ALIAS], [])

if (ops.length == 0) {
    console.log(`Found no option at alias ${ALIAS} in folder ${FOLDER}`)
}
const meta = _.get(s0, ["meta", ALIAS], null)

if (!meta) {
    console.log(`Found no meta data at alias ${ALIAS} in folder ${FOLDER}`)
}

const TYPE = meta["type"] == "string" ? "radio" : meta["type"] == "array" ? "checkbox" : ""

if (!TYPE) {
    console.log(`TYPE is not 'checkbox' or either 'radio'`)
}

let rule = ""

const all = USE_MAPPING != "1" ? `local all = [${ops.map(op => `"${op}"`).join(", ")}];` : `local all = [${ops.map((_, i) => `"${i + 1}"`).join(", ")}];`;
const mapping = `local MAPPING = {\n${ops.map((op, i) => `\t"${i + 1}": "${op}",`).join("\n")}\n};`

if (TYPE == "checkbox") {
    if (USE_MAPPING == "1") {
        rule = COMMON_RULES["all"]["Checkbox_Mapping__UncheckAndDisableOptions"]
    } else {
        rule = COMMON_RULES["all"]["Checkbox__UncheckAndDisableOptions"]
    }
} else if (TYPE == "radio") {
    if (USE_MAPPING == "1") {
        rule = COMMON_RULES["all"]["Radio_Mapping__DisableOptions"]
    } else {
        rule = COMMON_RULES["all"]["Radio__DisableOptions"]
    }
} else {
    throw new Error("input is not correct")
}

rule = rule.replaceAll("alias", ALIAS)
rule = rule.replaceAll("local MAPPING = {};", mapping)
rule = rule.replaceAll("local all = [];", all)


console.log(rule)