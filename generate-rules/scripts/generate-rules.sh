#!/bin/sh

#$1 FORM_NAME
#$2 PATH_TO_JSON_FILE
#$3 VERSION

mkdir ./data/$1/analyze/rules

version="v1"

node ./scripts/others/generate-rule-templates.js "./rules/common-rules/" "./data/common-rules.json" ".jsonnet" $version

node ./scripts/others/generate-rule-templates.js "./rules/generating-templates" "./data/rule-templates.json" ".template" $version

node ./scripts/others/lib-import.js "./rules/lib-import" "./data/lib-import.json" $version

node ./scripts/analyze/step3.js $1 $2

if [[ $? -ne 0 ]]; then
    echo "MAKE SURE YOU EXECUTED EXTRACT.SH SCRIPT BEFORE GENERATING RULES"
    exit 1
fi
