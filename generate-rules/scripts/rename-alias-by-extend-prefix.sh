#!/bin/sh

#$1 FORM_NAME
#$2 PREFIX
#$3 PATH_TO_JSON_FILE

mkdir ./data
mkdir ./data/$1
mkdir ./data/$1/extract
mkdir ./data/$1/analyze

node ./scripts/preprocessing/step0-preparation.js $1 $3

if [[ $? -ne 0 ]]; then
    exit 1
fi

STEP0_OUTPUT=./data/$1/before-extract.json

sh ./scripts/extract.sh $1 $STEP0_OUTPUT

if [[ $? -ne 0 ]]; then
    exit 1
fi

node ./scripts/others/rename-alias-by-extend-prefix.js $1 $2 $STEP0_OUTPUT
