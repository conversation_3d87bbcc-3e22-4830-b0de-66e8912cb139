const CUR_FOLDER = process.argv[2]
const CUR_ALIAS = process.argv[3]
const ORIGIN_FOLDER = process.argv[4]
const ORIGIN_ALIAS = process.argv[5]
const JSON_FORM = process.argv[6]
const TAG = process.argv[7]

const REPLACEMENT_PAGE_ALIAS = "alias_replacement"
const CLONE_WHOLE_FORM = "whole_form"

const OUTPUT = `./data/${CUR_FOLDER}/clone/form-${ORIGIN_FOLDER}.json`

const fs = require('fs')
const _ = require('lodash')

console.log('CUR_FOLDER :>> ', CUR_FOLDER);
console.log('CUR_ALIAS :>> ', CUR_ALIAS);
console.log('ORIGIN_FOLDER :>> ', ORIGIN_FOLDER);
console.log('ORIGIN_ALIAS :>> ', ORIGIN_ALIAS);
console.log('JSON_FORM :>> ', JSON_FORM);

const { toIdsWithoutProperties, sortByKey } = require('../../utils/basic')
const CUR_STEP0 = require(`../../data/${CUR_FOLDER}/analyze/step0.json`)
const CUR_STEP1 = require(`../../data/${CUR_FOLDER}/analyze/step1.json`)
const CUR_FORM_TREE = require(`../../data/${CUR_FOLDER}/extract/components_alias.json`)
const ORIGIN_STEP0 = require(`../../data/${ORIGIN_FOLDER}/analyze/step0.json`)
const ORIGIN_STEP1 = require(`../../data/${ORIGIN_FOLDER}/analyze/step1.json`)
const ORIGIN_FORM_TREE = require(`../../data/${ORIGIN_FOLDER}/extract/components_alias.json`)
const { WIDGET_PAGE } = require('../analyze/constants')
const cloneWholeForm = TAG === CLONE_WHOLE_FORM

const curPath = cloneWholeForm ? [] : [...CUR_STEP0.dict[CUR_ALIAS], CUR_ALIAS]
const curContent = cloneWholeForm ? CUR_FORM_TREE : _.get(CUR_FORM_TREE, curPath)
if (!curContent) {
    console.log('Empty curFormTree :>> ', curPath);
}

const originPath = cloneWholeForm ? [] : [...ORIGIN_STEP0.dict[ORIGIN_ALIAS], ORIGIN_ALIAS]
const originContent = cloneWholeForm ? ORIGIN_FORM_TREE : _.get(ORIGIN_FORM_TREE, originPath)
if (!originContent) {
    console.log('Empty originFormTree :>> ', originPath);
}

const curAlias = toIdsWithoutProperties(curContent)
const originAlias = toIdsWithoutProperties(originContent)

const toComparedFields = (step1, aliases) => {
    const sorted = sortByKey(
        Object.entries(step1)
        .map(([k, vs]) => vs.map(v => ({...v, type: k })))
        .flat()
        .filter(item => aliases.indexOf(item.id) > -1), "order"
    )

    return sorted.map((f) => {
        const formmated = {}

        formmated["alias"] = f.id
        formmated["type"] = f.type

        if (f["ui:formattedText"]) {
            formmated["text"] = f["ui:formattedText"]
        }
        if (f["ui:multipleOption"] && f["ui:multipleOption"].length > 0) {
            formmated["options"] = f["ui:multipleOption"].map((op) => ({
                "option_id": op.id,
                "text": op.formattedText,
            }))
        }
        if (f["ui:supportingFileGroup"]) {
            formmated["options"] = Object.entries(f["ui:supportingFileGroup"]["files"]).map(([k, op]) => ({
                "option_id": k,
                "text": op.description,
            }))
        }
        if (f.type == WIDGET_PAGE) {
            formmated["title"] = f["title"]
        }
        if (f["ui:heading"]) {
            formmated["heading"] = f["ui:heading"]
        }
        if (f["ui:subtitle"]) {
            formmated["subtitle"] = f["ui:subtitle"]
        }

        return formmated
    })
}

const curComps = toComparedFields(CUR_STEP1, curAlias)
const originComps = toComparedFields(ORIGIN_STEP1, originAlias)

fs.writeFile(`./data/${CUR_FOLDER}/clone/compare/current.json`, JSON.stringify(curComps, null, 4), () => {})
fs.writeFile(`./data/${CUR_FOLDER}/clone/compare/origin.json`, JSON.stringify(originComps, null, 4), () => {})