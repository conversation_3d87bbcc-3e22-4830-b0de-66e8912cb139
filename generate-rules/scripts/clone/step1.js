const CUR_FOLDER = process.argv[2]
const CUR_ALIAS = process.argv[3]
const ORIGIN_FOLDER = process.argv[4]
const ORIGIN_ALIAS = process.argv[5]
const TAG = process.argv[6]

const CLONE_WHOLE_FORM = "whole_form"
const cloneWholeForm = TAG === CLONE_WHOLE_FORM

const fs = require('fs')
const _ = require('lodash')

const { toIdsWithoutProperties, purge } = require('../../utils/basic')
const { findAll } = require('../../utils/regex')
const CUR_STEP0 = require(`../../data/${CUR_FOLDER}/analyze/step0.json`)
const CUR_FORM_TREE = require(`../../data/${CUR_FOLDER}/extract/components_alias.json`)
const ORIGIN_STEP0 = require(`../../data/${ORIGIN_FOLDER}/analyze/step0.json`)
const ORIGIN_FORM_TREE = require(`../../data/${ORIGIN_FOLDER}/extract/components_alias.json`)

const INIT = { "cloned": [], "mapped": {}, "removed": [] }

const readFile = (filePath) =>
    new Promise(
        (resolve, reject) => {
            fs.readFile(filePath, 'utf8', (err, data) => {
                if (err) {
                    reject(err)
                }

                resolve(data)
            })
        }
    )
Promise.all(
    [
        readFile(`./data/${CUR_FOLDER}/clone/compare/origin.json`),
        readFile(`./data/${CUR_FOLDER}/clone/compare/current.json`)
    ]
)
.then(([origin, current]) => {
    const originLines = origin.split("\n")
    const currentLines = current.split("\n")
    const map = {}

    for (let i = 0; i <  originLines.length; i++) {
        const leftWords = findAll(/\w+/, originLines[i])
        const rightWords = findAll(/\w+/, currentLines[i])

        if (!leftWords[0] && !rightWords[0]) {
            continue
        }

        if (leftWords[0] != rightWords[0]) {
            console.error(`Line ${i + 1} not match:\nLeft: ${originLines[i]}\nRight: ${currentLines[i]}`)

            return
        }

        if (leftWords[0] == "alias") {
            map[leftWords[1]] = rightWords[1]
        }
    }

    const allMappedOriginAliases = Object.keys(map)
    const allMappedCurrentAliases = Object.values(map)

    const originPath = cloneWholeForm ? [] : [...ORIGIN_STEP0.dict[ORIGIN_ALIAS], ORIGIN_ALIAS]
    const currentPath = cloneWholeForm ? [] : [...CUR_STEP0.dict[CUR_ALIAS], CUR_ALIAS]
    const originContent = cloneWholeForm ? ORIGIN_FORM_TREE : _.get(ORIGIN_FORM_TREE, originPath)
    const currentContent = cloneWholeForm ? CUR_FORM_TREE : _.get(CUR_FORM_TREE, currentPath)

    if (!originContent) {
        console.log('Empty originFormTree :>> ', originPath);
    }
    const allOriginAliases = toIdsWithoutProperties(originContent)
    const allCurrentAliases = toIdsWithoutProperties(currentContent)

    const removed = purge(allOriginAliases, allMappedOriginAliases)
    const added = purge(allCurrentAliases, allMappedCurrentAliases)
    const mapped = Object.fromEntries(Object.entries(map).filter(([k, v]) => k != v))
    
    INIT["mapped"] = mapped
    INIT["removed"] = removed
    INIT["added"] = added

    fs.writeFile(`./data/${CUR_FOLDER}/clone/mapping-form-${ORIGIN_FOLDER}.json`, JSON.stringify(INIT, null, 4), () => {})
})
