// USAGE: node ./scripts/clone/clone-rules-for-a-section.js [CUR_FOLDER] [CUR_ALIAS] [ORIGIN_FOLDER] [ORIGIN_ALIAS]

// Param 1: Name of form 1 where you is currently working on
// Param 2: Page alias or group of content is cloned
// Param 3: Name of form 2 which is original one
// Param 4: Page alias on original form and corresponding to Param 2
// Param 5: Path to current form JSON
// Param 6: Path to json file that contains mapping info of aliases btw 2 forms

const CUR_FOLDER = process.argv[2]
const CUR_ALIAS = process.argv[3]
const ORIGIN_FOLDER = process.argv[4]
const ORIGIN_ALIAS = process.argv[5]
const JSON_FORM = process.argv[6]
const TAG = process.argv[7]

// if (!CUR_FOLDER ||
//     !CUR_ALIAS ||
//     !ORIGIN_FOLDER ||
//     !ORIGIN_ALIAS ||
//     !JSON_FORM
// ) {
//     console.log("USAGE: node ./scripts/clone/clone-rules-for-a-section.js [CUR_FOLDER] [CUR_ALIAS] [ORIGIN_FOLDER] [ORIGIN_ALIAS]")
//     console.log("-> Param 1: Name of form 1 where you is currently working on")
//     console.log("-> Param 2: Page alias or group of content is cloned")
//     console.log("-> Param 3: Name of form 2 which is original one")
//     console.log("-> Param 4: Page alias on original form and corresponding to Param 2")
//     console.log("-> Param 5: Path to current form JSON")
//     console.log("-> aram 6: Path to json file that contains mapping info of aliases btw 2 forms")
//     throw Error("Invalid Paramenters are given")
// }

console.log('CUR_FOLDER :>> ', CUR_FOLDER);
console.log('CUR_ALIAS :>> ', CUR_ALIAS);
console.log('ORIGIN_FOLDER :>> ', ORIGIN_FOLDER);
console.log('ORIGIN_ALIAS :>> ', ORIGIN_ALIAS);
console.log('JSON_FORM :>> ', JSON_FORM);

const REPLACEMENT_PAGE_ALIAS = "alias_replacement"
const CLONE_WHOLE_FORM = "whole_form"

const OUTPUT = `./data/${CUR_FOLDER}/clone/form-${ORIGIN_FOLDER}.json`
const RULE_OUTPUT = `./data/${CUR_FOLDER}/clone/rules-${ORIGIN_FOLDER}-current.lua`
const ORIGIN_RULE_OUTPUT = `./data/${CUR_FOLDER}/clone/rules-${ORIGIN_FOLDER}-origin.lua`

const fs = require('fs')
const _ = require('lodash')

const { readJsonFile, toIdsWithoutProperties, purge } = require('../../utils/basic')
const { removeInvalidOutputs, toReplacedAliasesUI } = require('../preprocessing/utils')
const CUR_STEP0 = require(`../../data/${CUR_FOLDER}/analyze/step0.json`)
const CUR_STEP1 = require(`../../data/${CUR_FOLDER}/analyze/step1.json`)
const CUR_FORM_TREE = require(`../../data/${CUR_FOLDER}/extract/components_alias.json`)
const ORIGIN_STEP0 = require(`../../data/${ORIGIN_FOLDER}/analyze/step0.json`)
const ORIGIN_STEP1 = require(`../../data/${ORIGIN_FOLDER}/analyze/step1.json`)
const ORIGIN_FORM_TREE = require(`../../data/${ORIGIN_FOLDER}/extract/components_alias.json`)
const ORIGIN_RULE_PARAMS = require(`../../data/${ORIGIN_FOLDER}/extract/input.json`)
const ORIGIN_UI = require(`../../data/${ORIGIN_FOLDER}/extract/ui.json`)
const logicSupportSchema = require("../analyze/logicSupportSchema.json")
const { findAll } = require('../../utils/regex')
const cloneWholeForm = TAG === CLONE_WHOLE_FORM


const { CLONE_STAGES } = require("./config")
const { ADDITIONAL_INGORE_ALIAS } = require('./constants')

const IGNORE_ALIAS = [...Object.keys(logicSupportSchema.ui), ...ADDITIONAL_INGORE_ALIAS]
const SCOPE_ID = cloneWholeForm ? findAll(/\w+/g, ORIGIN_FOLDER).join("") : findAll(/\w+/g, ORIGIN_ALIAS).join("")

const toString = (rules) => {
    return rules.map(r => {
        const { value, name } = r
        let str = `------------  ${name}  ------------\n`

        str += value

        return str
    }).join("\n\n")
}

const cloneInternalRules = CUR_FOLDER == ORIGIN_FOLDER;

Promise.all([
        readJsonFile(JSON_FORM),
        readJsonFile(`./data/${ORIGIN_FOLDER}/extract/rules.json`),
        readJsonFile(`./data/${CUR_FOLDER}/clone/mapping-form-${ORIGIN_FOLDER}.json`),
    ])
    .then(([form, rules, { mapped, removed }]) => {
        const curPath = cloneWholeForm ? [] : [...CUR_STEP0.dict[CUR_ALIAS], CUR_ALIAS]
        const curContent = cloneWholeForm ? CUR_FORM_TREE : _.get(CUR_FORM_TREE, curPath)
        if (!curContent) {
            console.log('Empty curFormTree :>> ', curPath);
        }
        if (!cloneWholeForm && !cloneInternalRules) {
            mapped[ORIGIN_ALIAS] = CUR_ALIAS
            console.log('mapped[ORIGIN_ALIAS] = CUR_ALIAS :>> ');
        }

        let clonedAliases = []

        const originPath = cloneWholeForm ? [] : [...ORIGIN_STEP0.dict[ORIGIN_ALIAS], ORIGIN_ALIAS]
        const originContent = cloneWholeForm ? ORIGIN_FORM_TREE : _.get(ORIGIN_FORM_TREE, originPath)
        if (!originContent) {
            console.log('Empty originFormTree :>> ', originPath);
        }
        clonedAliases = purge([...toIdsWithoutProperties(originContent), Object.keys(mapped), ...IGNORE_ALIAS], [...removed])

        // const curAlias = [...toIdsWithoutProperties(curContent), ...IGNORE_ALIAS]
        const originAlias = cloneWholeForm ? [...clonedAliases] : cloneInternalRules ? [...clonedAliases] : [...clonedAliases, ORIGIN_ALIAS]

        const { newRules, invalidInputIds , originalRules} = removeInvalidOutputs(rules, purge(originAlias, IGNORE_ALIAS), ORIGIN_RULE_PARAMS, SCOPE_ID, mapped, SCOPE_ID, CUR_STEP0["options"])
        const groupId = SCOPE_ID + "_" + REPLACEMENT_PAGE_ALIAS

        const newReplacementChildren = toReplacedAliasesUI(invalidInputIds, SCOPE_ID, ORIGIN_STEP0, ORIGIN_UI)

        const newReplacementComponents = {
            [groupId]: { // could be dup
                "type": "object",
                "title": cloneWholeForm ? SCOPE_ID : _.get(ORIGIN_STEP0, ["titles", ORIGIN_ALIAS], SCOPE_ID),
                "properties": newReplacementChildren["components"]
            }
        }

        form["form"]["rules"] = [...form["form"]["rules"], ...newRules]

        const replacementPageChildren = _.get(form, ["form", "namespaceFormSchemaMap", "main", "schema", "properties", REPLACEMENT_PAGE_ALIAS, "properties"], null)
        const formSchemaUI = _.get(form, ["form", "namespaceFormSchemaMap", "main", "uiSchema"], {})

        if (replacementPageChildren) {
            form["form"]["namespaceFormSchemaMap"]["main"]["schema"]["properties"][REPLACEMENT_PAGE_ALIAS]["properties"] = {...replacementPageChildren, ...newReplacementComponents }
            form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"] = {...formSchemaUI, ...newReplacementChildren["ui"] }
        } else {
            form["form"]["namespaceFormSchemaMap"]["main"]["schema"]["properties"][REPLACEMENT_PAGE_ALIAS] = {
                "type": "object",
                "title": "Alias Replacement",
                "properties": newReplacementComponents
            }
        }

        const rootUISchema = {
            [REPLACEMENT_PAGE_ALIAS]: {
                "ui:invisible": true,
                "ui:widget": "Page"
            },
            [groupId]: {
                "ui:marginBottom": "12px",
                "ui:widget": "Group"
            }
        }
        form["form"]["namespaceFormSchemaMap"]["main"]["uiSchema"] = {...formSchemaUI, ...newReplacementChildren["ui"], ...rootUISchema }

        fs.writeFile(OUTPUT, JSON.stringify(form, null, 4), (e) => {
            if (e) {
                console.log('e :>> ', e);
                return
            }

            console.log('Write file successfully');
            console.log(OUTPUT);
        })
        fs.writeFile(RULE_OUTPUT, toString(newRules), (e) => {
            if (e) {
                console.log('e :>> ', e);
                return
            }

            console.log('Write file successfully');
            console.log(RULE_OUTPUT);
        })
        fs.writeFile(ORIGIN_RULE_OUTPUT, toString(originalRules), (e) => {
            if (e) {
                console.log('e :>> ', e);
                return
            }

            console.log('Write file successfully');
            console.log(ORIGIN_RULE_OUTPUT);
        })
    })
    .catch((e) => {
        if (e) {
            console.log('e :>> ', e);
            return
        }
    })