ORIGIN_FORM='ORIGIN_FORM'
CURRENT_FORM='CURRENT_FORM'
ORIGIN_ALIAS='ORIGIN_ALIAS'
CURRENT_ALIAS='CURRENT_ALIAS'
CURRENT_FORM_PATH='CURRENT_FORM_PATH'
IF_WHOLE_FORM=true

WHOLE_FORM_TAG=if [$IF_WHOLE_FORM = true] ; then echo 'whole_form'; else echo 'na' ; fi

# STEP 1: prepare neccessary data for cloning 

node ./scripts/preprocessing/step2-extract-rule-parameters.js $ORIGIN_FORM
mkdir ./data/$CURRENT_FORM/clone
touch ./data/$CURRENT_FORM/clone/mapping-form-$ORIGIN_FORM.json
mkdir ./data/$CURRENT_FORM/clone/compare
echo '{ "cloned": [], "mapped": { }, "removed": [] }' > ./data/$CURRENT_FORM/clone/mapping-form-$ORIGIN_FORM.json

# STEP 2: Extract data for comparision between original & current data
node ./scripts/clone/step0.js $CURRENT_FORM $CURRENT_ALIAS $ORIGIN_FORM $ORIGIN_ALIAS $CURRENT_FORM_PATH $WHOLE_FORM_TAG
git diff --no-index ./data/$CURRENT_FORM/clone/compare/origin.json ./data/$CURRENT_FORM/clone/compare/current.json > ./data/$CURRENT_FORM/clone/compare/diff.diff
# after done with this step, fill data at file mapping

node ./scripts/clone/step1.js $CURRENT_FORM $CURRENT_ALIAS $ORIGIN_FORM $ORIGIN_ALIAS $WHOLE_FORM_TAG

node ./scripts/clone/step2.js $CURRENT_FORM $ORIGIN_FORM

# STEP 3: Generate cloned rules & UI content
# This step will be enhanced if we found a safe way to detect rule input & output via syntax
# node ./scripts/clone/step9.js $CURRENT_FORM $CURRENT_ALIAS $ORIGIN_FORM $ORIGIN_ALIAS $CURRENT_FORM_PATH $WHOLE_FORM_TAG
