#!/bin/sh

#$1 FORM_NAME

# THE EXRTACT SCRIPT NEED RUNNING BEFORE EXECUTING THIS

touch ./data/log.txt

echo "\n\nTHE EXRTACT SCRIPT NEED RUNNING BEFORE EXECUTING THIS" >> ./data/log.txt
echo "[TIME]: " >> ./data/log.txt
date >> ./data/log.txt

rm -rf ./data/$1/analyze/pages__json
rm -rf ./data/$1/analyze/pages__text

mkdir ./data/$1/analyze/pages__json
mkdir ./data/$1/analyze/pages__text

node ./scripts/analyze/step2.js $1 nothing >> ./data/log.txt

if [[ $? -ne 0 ]]; then
    exit 1
fi
