const WIDGET_SIGNATURE = "Signature"
const WIDGET_PDF = "Pdf"
const WIDGET_FILE = "File"
const WIDGET_RADIO = "Radio"
const WIDGET_TEXTAREA = "TextArea"
const WIDGET_EMAIL = "Email"
const WIDGET_DATE = "Date"
const WIDGET_PHONE = "Phone"
const WIDGET_CUSTOMFORMAT = "CustomFormat"
const WIDGET_INTEGER = "Integer"
const WIDGET_FLOAT = "Float"
const WIDGET_PERCENTAGE = "Percentage"
const WIDGET_MONEY = "Money"
const WIDGET_MULTIPLECHECKBOX = "MultipleCheckbox"
const WIDGET_FILEGROUP = "FileGroup"
const WIDGET_DROPDOWN = "Dropdown"
const WIDGET_PAGE = "Page"
const WIDGET_PARAGRAPH = "Paragraph"
const WIDGET_GROUP = "Group"
const WIDGET_HEADER = "Header"
const WIDGET_TEXTBOX = "TextBox"
const WIDGET_ANCHORLINK = "AnchorLink"
const WIDGET_REPEATABLE = "Repeatable"
const WIDGET_COUNTRY = "Country"
const WIDGET_STATE = "State"

const COMMENT_LENGTH_LIMIT = 120
const OPTION_NUMBER_THRESOLD_FOR_MAPPING = 3

const WIDGET_TYPES = [
    WIDGET_INTEGER,
    WIDGET_FLOAT,
    WIDGET_MONEY,
    WIDGET_TEXTBOX,
    WIDGET_PERCENTAGE,
    WIDGET_RADIO,
    WIDGET_MULTIPLECHECKBOX,
    WIDGET_DATE,
    WIDGET_FILEGROUP,
    WIDGET_DROPDOWN,
    WIDGET_SIGNATURE,
    WIDGET_REPEATABLE,

    // not usually use
    WIDGET_PAGE,
    WIDGET_CUSTOMFORMAT,
    WIDGET_PHONE,
    WIDGET_ANCHORLINK,
    WIDGET_PARAGRAPH,
    WIDGET_GROUP,
    WIDGET_HEADER,
    WIDGET_TEXTAREA,
    WIDGET_EMAIL,
    WIDGET_PDF,
    WIDGET_FILE,

    WIDGET_COUNTRY,
    WIDGET_STATE,
]


// Alias
const LOGIC_SUPPORT_PAGE = "logic_support"
const INVESTOR_TYPE = "shortcut";

// Useful Statements
const IS_ENTITY_STATEMENT = { alias: "shortcut", cmd: "local isEntity = shortcut.value == 'entity';" }
const IS_INDIVIDUAL_STATEMENT = { alias: "shortcut", cmd: "local isIndividual = shortcut.value == 'indi';" }
const IS_TRUST_STATEMENT = { alias: "is_trust", cmd: "local isTrust = is_trust.value == 'yes';" }
const IS_IRA_STATEMENT = { alias: "is_ira", cmd: "local isIRA = is_ira.value == 'yes';" }
const IS_LLC_STATEMENT = { alias: "is_llc", cmd: "local isLLC = is_llc.value == 'yes';" }
const IS_JOINT_STATEMENT = { alias: "is_joint", cmd: "local isJoint = is_joint.value == 'yes';" }

const ALIAS = "alias"
const STATE_ALIAS = "state_alias"
const ZIPCODE_ALIAS = "zipcode_alias"
const COUNTRY_ALIAS = "country_alias"
const US_GROUP = "us_group"
const NON_US_GROUP = "non_us_group"
const PERCENT_ALIAS = "percent_alias"
const REPEATABLE_ALIAS = "repeatable_alias"
const FIRSTNAME_ALIAS = "firstname"
const MIDDLENAME_ALIAS = "middlename"
const LASTNAME_ALIAS = "lastname"
const SUFFIX_ALIAS = "suffix"
const INVESTMENT_ENTITY_ALIAS = "investment_entity_fs_setup"
const MERGED_INDIVIDUAL_NAME_ALIAS = "merged_individual_name"

const DEFAULT_VALUE_BY_TYPES = {
    "string": "",
    "array": [],
    "object": {},
    "null": null,
    "number": null,
}

const GENERATE_RULE_LIB_NAME = "gr";

module.exports = {
    WIDGET_TYPES,
    WIDGET_INTEGER,
    WIDGET_FLOAT,
    WIDGET_MONEY,
    WIDGET_TEXTBOX,
    WIDGET_PERCENTAGE,
    WIDGET_RADIO,
    WIDGET_MULTIPLECHECKBOX,
    WIDGET_DATE,
    WIDGET_FILEGROUP,
    WIDGET_DROPDOWN,
    WIDGET_SIGNATURE,
    WIDGET_REPEATABLE,

    WIDGET_PAGE,
    WIDGET_PARAGRAPH,
    WIDGET_GROUP,
    WIDGET_HEADER,
    WIDGET_TEXTBOX,
    WIDGET_ANCHORLINK,
    WIDGET_REPEATABLE,

    WIDGET_COUNTRY,
    WIDGET_STATE,

    LOGIC_SUPPORT_PAGE,
    INVESTOR_TYPE,
    IS_ENTITY_STATEMENT,
    IS_INDIVIDUAL_STATEMENT,
    IS_TRUST_STATEMENT,
    IS_LLC_STATEMENT,
    IS_JOINT_STATEMENT,
    IS_IRA_STATEMENT,

    ALIAS,
    ZIPCODE_ALIAS,
    STATE_ALIAS,
    COUNTRY_ALIAS,
    PERCENT_ALIAS,
    REPEATABLE_ALIAS,
    US_GROUP,
    NON_US_GROUP,

    COMMENT_LENGTH_LIMIT,
    OPTION_NUMBER_THRESOLD_FOR_MAPPING,
    DEFAULT_VALUE_BY_TYPES,

    FIRSTNAME_ALIAS,
    MIDDLENAME_ALIAS,
    LASTNAME_ALIAS,
    SUFFIX_ALIAS,
    INVESTMENT_ENTITY_ALIAS,
    MERGED_INDIVIDUAL_NAME_ALIAS,

    GENERATE_RULE_LIB_NAME,
}