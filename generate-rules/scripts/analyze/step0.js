const {
    outputStep0File,
    COMPONENT_INPUT
} = require("./config")

console.log("\nREADING FILE " + COMPONENT_INPUT)
const schema = require(COMPONENT_INPUT)

const aliasDict = {}
const options = {}
const titles = {}
const repeatable = {}
const meta = {}

const optimize = (parent, path, parentId, repeatableId) => {
    const children = parent["properties"] || (parent["items"] && parent["items"]["properties"])

    if (parent["items"] && parent["items"]["enum"]) {
        options[parentId] = parent["items"]["enum"]
    }
    if (parent["enum"]) {
        options[parentId] = parent["enum"]
    }

    titles[parentId] = parent["title"]

    if (repeatableId) {
       repeatable[parentId] = repeatableId
    }

    if (!children) {
        meta[parentId] = {...parent }

        return {}
    } else {
        meta[parentId] = { "type": parent["type"], "title": parent["title"] }
    }

    const isRepeatable =
        parent["type"] == "array" &&
        parent["items"] &&
        parent["items"]["type"] &&
        parent["items"]["type"] == "object"
    const repeatableProof = repeatableId ? repeatableId : isRepeatable ? parentId : ""

    if (isRepeatable) {
        meta[parentId] = {...parent }
    }

    return Object.fromEntries(
        Object.entries(children).map(
            ([k, v]) => {
                const next = [...path, k]

                aliasDict[k] = path

                return [k, optimize(v, next, k, repeatableProof)]
            }
        )
    )

}

optimize(schema, [], "")


const fs = require('fs')
fs.writeFile(outputStep0File, JSON.stringify({
    dict: aliasDict,
    options,
    titles,
    meta,
   repeatable
}, null, 4), () => {
    console.log("Write file successfully")
})