const {
    outputStep3Folder,
    outputStep1File,
    outputStep0File,
    COMPONENT_ALIAS_INPUT,
    NEW_ALIAS_INPUT,
    outputStep3File,
    UI_INPUT,
} = require("./config")
const {
    ALIAS,
    FIRSTNAME_ALIAS,
    MIDDLENAME_ALIAS,
    LASTNAME_ALIAS,
    SUFFIX_ALIAS,
    STATE_ALIAS,
    COUNTRY_ALIAS,
    REPEATABLE_ALIAS,
    COMMENT_LENGTH_LIMIT,
    US_GROUP,
    NON_US_GROUP,
    DEFAULT_VALUE_BY_TYPES,
    INVESTMENT_ENTITY_ALIAS,

    WIDGET_DROPDOWN,
    WIDGET_TEXTBOX,
    WIDGET_STATE,
    WIDGET_COUNTRY,
    WIDGET_MULTIPLECHECKBOX,
    WIDGET_RADIO,
    WIDGET_FILEGROUP,
    WIDGET_DATE,
    WIDGET_MONEY,
    WIDGET_INTEGER,
    WIDGET_PERCENTAGE,
    WIDGET_PHONE,
    MERGED_INDIVIDUAL_NAME_ALIAS,
    WIDGET_PARAGRAPH,
} = require("./constants")

const fs = require('fs')
const ejs = require('ejs');
const _ = require('lodash')

const COMMON_RULES = require('../../data/common-rules.json')
const RULE_TEMPLATES = require('../../data/rule-templates.json')
const { toCompareString } = require('../../utils/regex')
const { toIdsWithoutProperties } = require("../../utils/basic")
let newWords = []

try {
    const { added } = require(NEW_ALIAS_INPUT)

    newWords = added
} catch (error) {
    // console.log(error)
}

const step0 = require("../." + outputStep0File)
const alias = require(COMPONENT_ALIAS_INPUT)
const step1 = require("../." + outputStep1File)
const UI = require(UI_INPUT)
const existAlias = toIdsWithoutProperties(alias)

const VALID_IDS = Object.keys(step0.dict)

const orderDict = Object.fromEntries(
    Object.entries(step1)
        .map(([k, vs]) => {
            return vs.map(v => [v["order"], { ...v, type: k }])
        })
        .flat()
)


const rules = []
let i = 1

const writeRules = (tags, value, id, description = "") => {
    const tag = tags.map(t => "[" + t + "]")
    const name = tag.join("") + " Rule " + i
    const fileDir = `${outputStep3Folder}/${name} -- ${id}.lua`

    rules.push({
        value,
        name,
        defaultNamespace: "main",
        description
    })
    fs.writeFile(fileDir, value, () => { })
    i++
}

const ruleMultipleCheckbox = (fields) => {
    const oneOption = fields.filter(field => field.total == 1)
    const manyOption = fields.filter(field => field.total > 1)

    ruleInternalCheckbox(manyOption)

    oneOption.forEach(field => {
        // for required checkbox, show/hide below field will be redundant
        const text = field["ui:formattedText"] || ''
        const firstOpText = _.get(field, ["ui:multipleOption", 0, "formattedText"]) || ''
        const comparedtxt = getCompareOptionText(_.get(field, ["ui:multipleOption", 0]))
        const valid = comparedtxt.includes("check") && comparedtxt.includes("box") && comparedtxt.includes("if")

        if (field["ui:required"] && (text.includes("*") || firstOpText.includes("*")) || !valid) {
            return
        }
        let path = step0.dict[field.id]
        let tags = ["Show if check"]

        const repeatable = isRepeatable(field.id)

        if (repeatable) {
            tags.push("Not Support Repeatable")
        }

        let parent = _.get(alias, path)

        if (!parent) {
            console.error("\nError: Cannot find parent object")
            console.error("Alias : ", field.id)
            console.error("Path : ", path)
        }

        let children = Object.keys(parent)
        let order = children.indexOf(field.id)

        const fieldsBelowId = children[order + 1]

        if (fieldsBelowId) {
            const { rule, description } = generateRuleFromCommonRule(
                repeatable ? "Show_if_Check__Single_Checkbox" : "Show_if_Check__Single_Checkbox",
                {
                    [REPEATABLE_ALIAS]: repeatable,
                    [ALIAS]: field.id,
                    "fieldbelow": fieldsBelowId
                }
            )

            writeRules([...tags], rule, field.id, description)
        } else {
            tags.push("Invalid")

            const { rule, description } = generateRuleFromTemplate(
                repeatable ? "Show_if_Check__Single_Checkbox" : "Show_if_Check__Single_Checkbox",
                {
                    [REPEATABLE_ALIAS]: repeatable,
                    [ALIAS]: field.id
                }
            )

            writeRules([...tags], rule, field.id, description)
        }
    })
}

const isRepeatable = (id) => {
    return step0["repeatable"][id] || ''
}

const ruleDate = (fields) => {
    fields.map(field => {
        const repeatable = isRepeatable(field.id)
        const tags = ["Validate Date"]

        const { rule, description } = generateRuleFromCommonRule(
            repeatable ? "Repeatable__Validate_Date" : "Validate_Date",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: field.id
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, field.id, description)
    })
}


function sort_by_key(array, key) {
    return array.sort(function (a, b) {
        var x = a[key];
        var y = b[key];
        return ((x < y) ? -1 : ((x > y) ? 1 : 0));
    });
}

const getCompareFormatedText = (field) => {
    if (!field) { return "" }

    return (field["ui:formattedText"] || _.get(field, ["ui:multipleOption", 0, "formattedText"]) || '').toLowerCase()
}
const getCompareOptionText = (field) => {
    if (!field) { return "" }

    return (field["formattedText"] || '').toLowerCase()
}

const toMatchedPattern = (terms, basedIdx) => {
    const nextFields = Array.from(Array(12).keys())
        .map(i => ({ i: basedIdx + i + 1, txt: getCompareFormatedText(orderDict[basedIdx + i + 1]) }))
    let i = 0

    const matched = terms.map((term) => {
        for (let j = i; i < nextFields.length; j++) {
            i = j + 1
            if (!nextFields[j].txt.includes(term)) {
                continue
            }
            return nextFields[j].i
        }

        return ""
    })

    return matched
}

const ruleAddressByDropdown = (fields) => {
    const sorted = sort_by_key(fields, "order")

    sorted.map((f, idx) => {
        const isState = getCompareFormatedText(f).includes("state")
        const isCountry = getCompareFormatedText(f).includes("country")
        const repeatable = isRepeatable(f.id)

        const tags = ["Show by country"]
        const repeatableTag = repeatable ? ["Repeatable"] : []

        if (isCountry) {
            const pattern = ["state", "code", "state", "code"]
            const matchedFields = toMatchedPattern(pattern, f["order"])
                .filter(x => x)
                .map(i => orderDict[i])

            if (matchedFields.length == pattern.length) {
                let usGroup = ""
                let nonusGroup = ""

                if ((matchedFields[0].type == WIDGET_DROPDOWN || matchedFields[0].type == WIDGET_STATE) && matchedFields[2].type == WIDGET_TEXTBOX) {
                    usGroup = _.last(step0.dict[matchedFields[0].id])
                    nonusGroup = _.last(step0.dict[matchedFields[2].id])
                } else if ((matchedFields[2].type == WIDGET_DROPDOWN || matchedFields[2].type == WIDGET_STATE) && matchedFields[0].type == WIDGET_TEXTBOX) {
                    usGroup = _.last(step0.dict[matchedFields[2].id])
                    nonusGroup = _.last(step0.dict[matchedFields[0].id])
                } else {
                    console.log('Rule Dropdown: Unknown stituation for pattern', pattern);
                }

                const { rule, description } = generateRuleFromCommonRule(
                    repeatable ? "Repeatable__Show_if_Select__US" : "Show_if_Select__US",
                    {
                        [REPEATABLE_ALIAS]: repeatable,
                        [NON_US_GROUP]: nonusGroup,
                        [US_GROUP]: usGroup,
                        [COUNTRY_ALIAS]: f.id,
                    }
                )

                writeRules([...tags, ...repeatableTag], rule, f.id, description)

                const pattern2 = ["number and street", "city", "state", "code", "state", "code"]
                const matchedFields2 = toMatchedPattern(pattern2, f["order"])
                    .filter(x => x)
                    .map(i => orderDict[i])

                if (matchedFields2.length >= 6) {
                    const hiddenField = matchedFields2[5] ? orderDict[matchedFields2[5].order + 1] : '';
    
                    const { rule: rule2, description : description2 } = generateRuleFromCommonRule(
                        repeatable ? "Repeatable__Combine_Address" : "Combine_Address",
                        {
                            [REPEATABLE_ALIAS]: repeatable,
                            "numberandstreet": matchedFields2[0].id,
                            "city": matchedFields2[1].id,
                            "state_us": matchedFields2[2].id,
                            "state_nonus": matchedFields2[4].id,
                            "zipcode_us": matchedFields2[3].id,
                            "zipcode_nonus": matchedFields2[5].id,
                            "country": f.id,
                            "alias": hiddenField && hiddenField.hidden ? hiddenField.id : "hidden"
                        }
                    )
    
                    writeRules(["Combine Address", ...repeatableTag], rule2, f.id, description2)
                }
            } else {
                const nextField = fields[idx + 1]
                const isStateNext = nextField ? nextField && getCompareFormatedText(nextField).includes("state") : false
                const isClosed = nextField ? Math.abs(nextField["order"] - f["order"]) < 3 : false

                if (isStateNext && isClosed) {
                    let { rule, description } = generateRuleFromTemplate(
                        repeatable ? "Repeatable__Show_if_Select__US" : "Show_if_Select__US",
                        {
                            [REPEATABLE_ALIAS]: repeatable,
                            [COUNTRY_ALIAS]: f.id,
                        })

                    writeRules([...tags, ...repeatableTag], rule, f.id, description)
                }
            }
        } else if (isState) {
            const nextField = fields[idx + 1]

            const isCountryNext = nextField ? nextField && getCompareFormatedText(nextField).includes("country") : false
            const isClosed = nextField ? Math.abs(nextField["order"] - f["order"]) < 3 : false

            if (isState && isCountryNext && isClosed) {
                const stateId = f.id
                const countryId = nextField.id
                const tags = ["Validate", "State & Country"]

                const { rule, description } = generateRuleFromCommonRule(
                    repeatable ? "Repeatable__Validate_State_and_Country" : "Validate_State_and_Country",
                    {
                        [REPEATABLE_ALIAS]: repeatable,
                        [STATE_ALIAS]: stateId,
                        [COUNTRY_ALIAS]: countryId,
                    }
                )

                writeRules([...tags, ...repeatableTag], rule, stateId, description)
            }
        }
    })
}

const ruleMonthByDropdown = (fields) => {
    fields.map((f) => {
        let path = step0.dict[f.id]
        let parent = _.get(alias, path)
        let children = Object.keys(parent)
        let order = children.indexOf(f.id)

        const nextEleId = children[order + 1]
        const nextEle = UI[nextEleId]

        const isMonth = (f["ui:placeholder"] || "").toLowerCase().includes("month")
        const isDate = nextEle && (
            nextEle["ui:widget"] == WIDGET_INTEGER &&
            nextEle["ui:placeholder"] || "").toLowerCase().includes("date"
        )

        if (!isMonth || !isDate) {
            return
        }

        const repeatable = isRepeatable(f.id)

        const tags = ["Validate Date By Month"]
        const repeatableTag = repeatable ? ["Not Support Repeatable"] : []

        const { rule, description } = generateRuleFromCommonRule(
            repeatable ? "Validate_Day" : "Validate_Day",
            {
                [REPEATABLE_ALIAS]: repeatable,
                "dropdown": f.id,
                "integer": nextEleId,
            }
        )

        writeRules([...tags, ...repeatableTag], rule, nextEleId, description)
    })
}

const ruleCombineName = (fields) => {
    const sorted = sort_by_key(fields, "order")

    sorted.map((f, i) => {
        if (i + 4 >= sorted.length) {
            return
        }

        const isFirstName = getCompareFormatedText(sorted[i]).includes("first name")

        if (!isFirstName) {
            return
        }

        const isSecondName = getCompareFormatedText(sorted[i + 1]).includes("middle name")

        if (!isSecondName) {
            return
        }

        const isLastName = getCompareFormatedText(sorted[i + 2]).includes("last name")

        if (!isLastName) {
            return
        }

        const isSuffixName = getCompareFormatedText(sorted[i + 3]).includes("suffix")

        if (!isSuffixName) {
            return
        }

        const isMergeField = getCompareFormatedText(sorted[i + 4]).includes("hidden") || sorted[i + 4]["ui:invisible"]

        if (!isMergeField) {
            return
        }

        const { rule, description } = generateRuleFromCommonRule("Combine_Name", {
            [FIRSTNAME_ALIAS]: sorted[i].id,
            [MIDDLENAME_ALIAS]: sorted[i + 1].id,
            [LASTNAME_ALIAS]: sorted[i + 2].id,
            [SUFFIX_ALIAS]: sorted[i + 3].id,
            [ALIAS]: sorted[i + 4].id,
        })

        const repeatable = isRepeatable(f.id)
        const tags = ["Combine Name"]

        if (repeatable) {
            tags.push("Not Support Repeatable")
        }

        writeRules(tags, rule, f.id, description)
    })
}

const ruleFieldBelowCheckbox = (fields) => {
    fields.map(f => {
        const ops = f["ui:multipleOption"]
        const repeatable = isRepeatable(f.id)
        const tags = ["Field Below", "Checkbox"]

        if (repeatable) {
            tags.push("Not Support Repeatable")
        }

        if (!ops || !(ops.length > 0)) {
            console.error("No option found for checkbox " + f.id)
            return
        }

        const options = ops.map(({ id }, index) => ({ id, index }))

        const outputs = ops.map((op, idx) => {
            const { formattedText, fieldsBelow = [] } = op

            const comparedTxt = (formattedText || "").replace(/\s/g, '')
            const disabled = comparedTxt.endsWith(":") || comparedTxt.endsWith("or") || comparedTxt.endsWith("and")

            return fieldsBelow.map((field) => {
                const isValid = VALID_IDS.indexOf(field) > -1

                if (!isValid) {
                    const comment = `ERROR: field below with alias ${field} under ${f.id} doesn't exist in form.`
                    console.error(comment)
                    return null
                }

                const isHeader =
                    _.get(UI, [field, "ui:widget"]) == WIDGET_PARAGRAPH
                    && _.get(UI, [field, "ui:wellType"], "Default") == "Default"

                if (isHeader) {
                    const comment = `WARN: alias ${field} under ${f.id} is ignored because it's a header.`
                    console.warn(comment)
                    return null
                }

                const output = {
                    "alias": field,
                    "option_index": idx,
                    "disable_fields": []
                }

                if (disabled) {
                    const path = step0.dict[field]
                    const parent = _.get(alias, [...path, field], {})
                    const disabledIds = [...toIdsWithoutProperties(parent), field];
                    const repeatableFields = Object.keys(step0.repeatable)
                    
                    disabledIds
                        .filter(id => repeatableFields.indexOf(id) == -1)
                        .map(disabledId => {
                            const childType = step0.meta[disabledId].type
                            const defaultVal = JSON.stringify(DEFAULT_VALUE_BY_TYPES[childType])

                            // For now, please ignore repeatable in child object because it's complicated
                            if (
                                childType == "string" ||
                                childType == "array" ||
                                childType == "number"
                            ) {
                                output.disable_fields.push({
                                    "alias": disabledId,
                                    "default_value": defaultVal
                                })
                            }
                        })
                }

                return output
            })
        }).flat().filter(x => x)

        const optionHavingFieldBelow = _.uniq(outputs.map(({ option_index }) => option_index))

        if (optionHavingFieldBelow.length == 0 || optionHavingFieldBelow.length != ops.length) {
            ruleMultipleCheckbox([f])

            if (optionHavingFieldBelow.length == 0) {
                return
            }
        }

        const { rule, description } = generateRuleFromTemplate(
            repeatable ? "Checkbox__FieldBelow" : "Checkbox__FieldBelow",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id,
                options: options.filter(({ index }) => optionHavingFieldBelow.indexOf(index) > -1),
                outputs,
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)
    })
}

const ruleInvestorType = (fields) => {
    fields.map((field, i) => {
        const tags = ["Investor Type"]

        const nextField = fields[i + 1]

        if (!nextField) {
            return
        }

        const comparedTxt = (field["ui:formattedText"] || "").toLowerCase().split(" ")[0] ?? ""
        const comparedNextTxt = (nextField["ui:formattedText"] || "").toLowerCase().split(" ")[0] ?? ""

        if (!comparedTxt.includes("individual") || !comparedNextTxt.includes("entit")) {
            return
        }
        const individualAlias = field.id
        const entityAlias = nextField.id

        const { rule: rule1, description: description1 } = generateRuleFromCommonRule("Radio__Uncheck", {
            "radio1": individualAlias,
            "radio2": entityAlias,
        })

        writeRules([...tags, "Radio Uncheck"], rule1, individualAlias, description1)

        const { rule: rule2, description: description2 } = generateRuleFromCommonRule("Radio__Uncheck", {
            "radio1": entityAlias,
            "radio2": individualAlias,
        })

        writeRules([...tags, "Radio Uncheck"], rule2, entityAlias, description2)

        const { rule: rule3, description: description3 } = generateRuleFromTemplate(
            "Radio__Uncheck__ErrorMessage_InvestorType",
            {
                "individual_alias": individualAlias,
                "entity_alias": entityAlias,
            }

        )

        writeRules(tags, rule3, individualAlias + " -- " + entityAlias, description3)

    })
}

const ruleFieldBelowRadio = (fields) => {
    fields.map((f, index) => {
        const ops = f["ui:multipleOption"]
        const repeatable = isRepeatable(f.id)
        const tags = ["Field Below", "Radio"]

        if (repeatable) {
            tags.push("Not Support Repeatable")
        }

        if (!ops || !(ops.length > 0)) {
            console.error("No option found for radio " + f.id)
            return
        }
        // TODO: remove following duplicate code

        const options = ops.map(({ id }, index) => ({ id, index }))

        const outputs = ops.map((op, idx) => {
            const { formattedText, fieldsBelow = [] } = op

            const comparedTxt = (formattedText || "").replace(/\s/g, '')
            const disabled = comparedTxt.endsWith(":") || comparedTxt.endsWith("or") || comparedTxt.endsWith("and")

            return fieldsBelow.map((field) => {
                const isValid = VALID_IDS.indexOf(field) > -1

                if (!isValid) {
                    const comment = `ERROR: field below with alias ${field} under ${f.id} doesn't exist in form.`
                    console.error(comment)
                    return null
                }

                const isHeader =
                    _.get(UI, [field, "ui:widget"]) == WIDGET_PARAGRAPH
                    && _.get(UI, [field, "ui:wellType"], "Default") == "Default"

                if (isHeader) {
                    const comment = `WARN: alias ${field} under ${f.id} is ignored because it's a header.`
                    console.warn(comment)
                    return null
                }

                const output = {
                    "alias": field,
                    "option_index": idx,
                    "disable_fields": []
                }

                if (disabled) {
                    const path = step0.dict[field]
                    const parent = _.get(alias, [...path, field], {})
                    const disabledIds = [...toIdsWithoutProperties(parent), field];
                    const repeatableFields = Object.keys(step0.repeatable)
                    
                    disabledIds
                        .filter(id => repeatableFields.indexOf(id) == -1)
                        .map(disabledId => {
                            const childType = step0.meta[disabledId].type
                            const defaultVal = JSON.stringify(DEFAULT_VALUE_BY_TYPES[childType])

                            // For now, please ignore repeatable in child object because it's complicated
                            if (
                                childType == "string" ||
                                childType == "array" ||
                                childType == "number"
                            ) {
                                output.disable_fields.push({
                                    "alias": disabledId,
                                    "default_value": defaultVal
                                })
                            }
                        })
                }

                return output
            })
        }).flat().filter(x => x)

        const optionHavingFieldBelow = _.uniq(outputs.map(({ option_index }) => option_index))

        if (optionHavingFieldBelow.length == 0 || optionHavingFieldBelow.length != ops.length) {
            ruleInternalRadio([f], fields, index)

            if (optionHavingFieldBelow.length == 0) {
                return
            }
        }

        const { rule, description } = generateRuleFromTemplate(
            repeatable ? "Radio__FieldBelow" : "Radio__FieldBelow",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id,
                options: options.filter(({ index }) => optionHavingFieldBelow.indexOf(index) > -1),
                outputs,
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)
    })
}

const ruleInternalCheckbox = (fields) => {
    fields.map(f => {
        const ops = f["ui:multipleOption"]
        const repeatable = isRepeatable(f.id)
        const tags = ["Full", "Checkbox"]

        if (repeatable) {
            tags.push("Not Support Repeatable")
        }

        if (!ops || !(ops.length > 0)) {
            return
        }

        const options = ops.map(({ id, formattedText }) => {
            const comment = formatComment(formattedText)
            const comparedId = toCompareString(id)
            const comparedText = toCompareString(formattedText)
            const showComment = comparedId != comparedText

            return {
                id,
                description: showComment ? comment : ""
            }
        })

        const { rule, description } = generateRuleFromTemplate(
            repeatable ? "Checkbox__FullOption" : "Checkbox__FullOption",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id,
                options,
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)
    })
}


const ruleInternalRadio = (fields, allFields, index) => {
    fields
        .filter(f => !f["hidden"])
        .map(f => {
            const ops = f["ui:multipleOption"]
            const repeatable = isRepeatable(f.id)
            const tags = ["Full", "Radio"]

            if (repeatable) {
                tags.push("Not Support Repeatable")
            }

            // TODO: remove following duplicate code

            if (!ops || !(ops.length > 0)) {
                return
            }

            const options = ops.map(({ id, formattedText }) => {
                const comment = formatComment(formattedText)
                const comparedId = toCompareString(id)
                const comparedText = toCompareString(formattedText)
                const showComment = comparedId != comparedText
                return {
                    id,
                    description: showComment ? comment : ""
                }
            })

            const outputs = []

            const yesNoQuestion = ops.length == 2 && ops.reduce((result, { formattedText }) => {
                const comparedText = toCompareString(formattedText).replaceAll(/\W/g, "")

                const yesNo =
                    comparedText == "yes" ||
                    comparedText == "no" ||
                    comparedText == "true" ||
                    comparedText == "false"

                return yesNo && result
            }, true)

            if (yesNoQuestion) {
                const nextEle = orderDict[f["order"] + 1] || {}
                const nextEleWithText =
                    _.head(Array.from(Array(10).keys())
                        .map(i => orderDict[f["order"] + i + 1])
                        .filter(e =>
                        (
                            _.get(e, ["ui:formattedText"], null)
                            || _.get(e, ["ui:multipleOption", 0, "formattedText"], null)
                        )
                        )
                    )

                const comparedText = (nextEleWithText && getCompareFormatedText(nextEleWithText).split(" ").map(x => x.replaceAll(/\W/g, ""))) || ""
                const nextYes = comparedText.includes("yes") || comparedText.includes("true") || (nextEle["title"] || "").includes("yes") || (nextEle["title"] || "").includes("true")
                const nextNo = comparedText.includes("no") || comparedText.includes("false") || (nextEle["title"] || "").includes("no") || (nextEle["title"] || "").includes("false")

                if (nextYes || nextNo) {
                    tags.push("Show by Polar Question")

                    const path = step0.dict[nextEle.id]
                    const parent = _.get(alias, [...path, nextEle.id])
                    const disabledIds = JSON.stringify(parent) == "{}" ? [nextEle.id] : toIdsWithoutProperties(parent);

                    const disable = nextYes ? 0 : 1 // 0: op1, 1: op2

                    const output = {
                        alias: nextEle.id,
                        option_index: disable,
                        disable_fields: []
                    }

                    disabledIds.forEach(disabledId => {
                        const childType = step0.meta[disabledId].type
                        const defaultVal = JSON.stringify(DEFAULT_VALUE_BY_TYPES[childType])

                        // For now, please ignore repeatable in child object because it's complicated
                        if (
                            childType == "string" ||
                            childType == "array" ||
                            childType == "number"
                        ) {
                            output.disable_fields.push({
                                alias: disabledId,
                                reset_value: defaultVal,
                            })
                        }
                    })

                    outputs.push(output)
                }
            }

            // TODO: remove following duplicate code

            const nextField = allFields[index + 1]

            const comparedTxt = (f["ui:formattedText"] || "").toLowerCase().split(" ")[0] ?? ""
            const comparedNextTxt = nextField ? (nextField["ui:formattedText"] || "").toLowerCase().split(" ")[0] ?? "" : ""
            if (nextField && comparedTxt.includes("individual") && comparedNextTxt.includes("entit")) {
                const individualAlias = f.id
                const entityAlias = nextField.id

                const { rule: rule1, description: description1 } = generateRuleFromTemplate("Radio__FullOption__InvestorType_Individual", {
                    "individual_alias": individualAlias,
                    "entity_alias": entityAlias,
                    options,
                })

                const nextOptions = nextField["ui:multipleOption"].map(({ id, formattedText }) => {
                    const comment = formatComment(formattedText)
                    const comparedId = toCompareString(id)
                    const comparedText = toCompareString(formattedText)
                    const showComment = comparedId != comparedText
                    return {
                        id,
                        description: showComment ? comment : ""
                    }
                })

                writeRules([...tags, "Investor Type"], rule1, individualAlias, description1)

                const { rule: rule2, description: description2 } = generateRuleFromTemplate("Radio__FullOption__InvestorType_Entity", {
                    "individual_alias": individualAlias,
                    "entity_alias": entityAlias,
                    options: nextOptions,
                })

                writeRules([...tags, "Investor Type"], rule2, entityAlias, description2)
            } else {
                const { rule, description } = generateRuleFromTemplate(
                    repeatable ? "Radio__FullOption" : "Radio__FullOption",
                    {
                        [REPEATABLE_ALIAS]: repeatable,
                        [ALIAS]: f.id,
                        options,
                        disable_output: outputs[0]
                    }
                )

                const repeatableTag = repeatable ? ["Repeatable"] : []

                writeRules([...tags, ...repeatableTag], rule, f.id, description)
            }
        })
}

const ruleTaxIdRadio = (fields) => {
    fields.map(f => {
        const ops = f["ui:multipleOption"]
        const tags = ["Tax Id"]

        if (!ops || !(ops.length > 0)) {
            return
        }

        const comparedFormatedText = getCompareFormatedText(f)
        const comparedOptionFormatedTexts = ops.map(getCompareOptionText)

        if (comparedFormatedText.includes("taxpayer identification number")) {
            const ssnOption = comparedOptionFormatedTexts.filter(x => x.includes("social security number"))[0]
            const einOption = comparedOptionFormatedTexts.filter(x => x.includes("employer identification number"))[0]

            if (ssnOption || einOption) {
                const { rule, description } = generateRuleFromTemplate(
                    "Radio__DisabledOptions__TinRadio",
                    {
                        "tin_alias": f.id,
                        "ein_option": (einOption || {})["id"] || "ein",
                        "ssn_option": (ssnOption || {})["id"] || "ssn",
                    }

                )

                writeRules(tags, rule, f.id, description)
            }
        }
    })
}

const ruleMoney = (fields) => {
    fields.map(f => {
        const repeatable = isRepeatable(f.id)
        const tags = ["Validate Money"]

        const { rule, description } = generateRuleFromCommonRule(
            repeatable ? "Repeatable__Validate_Money" : "Validate_Money",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)
    })
}

const generateRuleFromCommonRule = (ruleName, replacements = {}) => {
    const template = COMMON_RULES["all"][ruleName] ?? ""
    const description = COMMON_RULES["description"][ruleName] ?? ""

    if (!template) {
        console.error(`Cannot find rule for key ${ruleName}`)
    }

    let rule = `// COMMON RULE ID: ${ruleName}\n` + template

    Object.entries(replacements).forEach(([from, to]) => { rule = rule.replaceAll(from, to) })

    return { rule, description }
}

const generateRuleFromTemplate = (templateName, replacements = {}) => {
    const template = RULE_TEMPLATES["all"][templateName] ?? ""
    const description = RULE_TEMPLATES["description"][templateName] ?? ""

    if (!template) {
        console.error(`Cannot find template for key ${templateName}`)
        return {rule: templateName, description: templateName}
    }

    return { rule: `// TEMPLATE ID: ${templateName}\n` + ejs.render(template, replacements), description }
}

const ruleInteger = (fields) => {
    fields.map(f => {
        const repeatable = isRepeatable(f.id)
        const tags = ["Validate Integer"]

        const isDate = (f["ui:placeholder"] || "").toLowerCase().includes("date")

        if (isDate) {
            return
        }

        const { rule, description } = generateRuleFromCommonRule(
            repeatable ? "Repeatable__Validate_Integer" : "Validate_Integer",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)

    })
}
const rulePercentage = (fields) => {
    fields.map(f => {
        const repeatable = isRepeatable(f.id)
        const tags = ["Validate Percentage"]

        const { rule, description } = generateRuleFromCommonRule(
            repeatable ? "Repeatable__Validate_Percent" : "Validate_Percent",
            {
                [REPEATABLE_ALIAS]: repeatable,
                [ALIAS]: f.id
            }
        )

        const repeatableTag = repeatable ? ["Repeatable"] : []

        writeRules([...tags, ...repeatableTag], rule, f.id, description)
    })
}
const writeAddtionalRuleFromTemplate = (templates) => {
    templates.map(template => {
        const { rule, description } = generateRuleFromTemplate(template.name, template.replacements)

        writeRules(["Logic Support"], rule, "", description)
    })
}

const formatComment = txt => {
    if (!txt) {
        return ""
    }

    const line = txt.replaceAll("\n", " ")
    let cut = ""

    if (line.length <= COMMENT_LENGTH_LIMIT) {
        cut = line
    } else {
        cut = line.substring(0, COMMENT_LENGTH_LIMIT) + "..."
    }
    return cut
}

const ruleFileGroup = (fields) => {
    fields.map(f => {
        const files = _.get(f, ["ui:supportingFileGroup", "files"], [])

        const fileArr = Object.entries(files).map(([k, v]) => {
            const comment = formatComment(v["description"])
            const showComment = toCompareString(k) != toCompareString(v["description"])

            return { option: k, description: showComment ? comment : "" }
        })

        const { rule } = generateRuleFromTemplate(
            "FileGroup__Show_Supporting_Doc",
            {
                files: fileArr,
                filegroup_alias: f.id
            }
        )

        writeRules(["File Group"], rule, f.id)
    })
}

Object.entries(step1).map(([type, fields_]) => {
    const fields =
        newWords.length == 0 ?
            fields_.filter(field => existAlias.indexOf(field.id) > -1) :
            fields_.filter(field => {
                return newWords.indexOf(field.id) > -1 && existAlias.indexOf(field.id) > -1
            })

    const visibleFields = fields.filter(f => !f["hidden"])

    switch (type) {
        case WIDGET_MULTIPLECHECKBOX:
            ruleFieldBelowCheckbox(fields)
            break

        case WIDGET_RADIO:
            ruleFieldBelowRadio(fields)
            ruleInvestorType(fields)
            ruleTaxIdRadio(fields)
            break

        case WIDGET_FILEGROUP:
            ruleFileGroup(fields)
            break


        // Validate Rules
        case WIDGET_DATE:
            ruleDate(visibleFields)
            break

        case WIDGET_DROPDOWN:
            ruleMonthByDropdown(fields)
        case WIDGET_COUNTRY:
            ruleAddressByDropdown(fields)
            break

        case WIDGET_MONEY:
            ruleMoney(visibleFields)
            break

        case WIDGET_INTEGER:
            ruleInteger(visibleFields)
            break

        case WIDGET_PERCENTAGE:
            rulePercentage(visibleFields)
            break

        case WIDGET_TEXTBOX:
            ruleCombineName(fields)
            break

        case WIDGET_PHONE:
            break

        default:
            break
    }

})

writeAddtionalRuleFromTemplate([
    { name: "Combine_Name__Investment_Entity" },
    { name: "Radio__FullOption_Groups" },
    { name: "Radio__Shortcut" },
    { name: "Combine__If_Trust" },
    { name: "Combine__All_Investor_Type" },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_trust" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_ira" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_llc" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_joint" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_natural_person" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_indi_trust" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_entity_trust" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_indi_other" } },
    { name: "Radio__FullOption__InvestorType", replacements: { [ALIAS]: "is_entity_other" } },
])

fs.writeFile(outputStep3File, JSON.stringify(rules), () => {
    console.log("Write file " + outputStep3File + " successfully")
})