const {
    WIDGET_TYPES,
    WIDGET_INTEGER,
    WIDGET_FLOAT,
    WIDGET_MONEY,
    WIDGET_TEXTBOX,
    WIDGET_PERCENTAGE,
    WIDGET_RADIO,
    WIDGET_MULTIPLECHECKBOX,
    WIDGET_DATE,
    WIDGET_DROPDOWN,
} = require("./constants")
const {
    UI_INPUT,
    outputStep1File,
    outputStep0File,
} = require("./config")
const { htmlToText } = require('html-to-text');

const _ = require('lodash')

console.log("\nREADING FILE " + UI_INPUT)
const ui = require(UI_INPUT)

const invisibleComponents = Object.entries(ui)
    .filter(([_, v]) => v["ui:invisible"])
    .map(([k, _]) => k)

const step0 = require("../." + outputStep0File)
const ids = Object.keys(step0.dict)

const result = Object.fromEntries(WIDGET_TYPES.map(type => [type, []]))

const formatComp = (comp, key, type) => {
    delete comp["ui:marginBottom"]
    delete comp["ui:css"]
    delete comp["ui:width"]
    delete comp["ui:widget"]
    delete comp["ui:indentation"]
    delete comp["ui:dynamicLayout"]
    delete comp["ui:height"]
    delete comp["ui:pdfMapping"]
    delete comp["ui:fixedLayout"]
    delete comp["ui:enabledComment"]
    delete comp["ui:maxLength"]
    delete comp["ui:dateFormat"]
    delete comp["ui:viewInModal"]
    delete comp["ui:tooltipForDisabledState"]

    if (comp["ui:formattedText"]) {
        comp["ui:formattedText"] = htmlToText(comp["ui:formattedText"])
    }

    if (comp["ui:multipleOption"] && comp["ui:multipleOption"]["options"] && comp["ui:multipleOption"]["options"].length > 0) {
        const dict = Object.fromEntries(comp["ui:multipleOption"]["options"])
        const options = step0["options"] && step0["options"][key]

        if (!options || !(options.length > 0)) {
            console.error("Cannot find options of key " + key)
        } else {
            comp["ui:multipleOption"] = options.map(id => {
                const v = dict[id] || {}

                if (v["formattedText"]) {
                    v["formattedText"] = htmlToText(v["formattedText"])
                }

                return {...v, id }
            })

            if (comp["ui:multipleOption"].length > 30) {
                comp["ui:multipleOption"].length = 4
            }

            comp["total"] = options.length
        }
    }

    // verify if this field is required
    const formattedText =
        comp["ui:formattedText"] ||
        _.get(comp, ["ui:multipleOption", 0, "formattedText"]) ||
        ""
    const extractRequireChar = formattedText.match(/.*\*.*/)
    const hasRequireChar = extractRequireChar && extractRequireChar[0]
    const consideredType = [
        WIDGET_INTEGER,
        WIDGET_FLOAT,
        WIDGET_MONEY,
        WIDGET_TEXTBOX,
        WIDGET_PERCENTAGE,
        WIDGET_RADIO,
        WIDGET_MULTIPLECHECKBOX,
        WIDGET_DATE,
        WIDGET_DROPDOWN,
    ].indexOf(type) > -1
    comp["warning"] = !!(
        consideredType &&
        (
            (hasRequireChar && !comp["ui:required"]) ||
            (!hasRequireChar && comp["ui:required"])
        )
    )

    comp["title"] = step0.titles[key]

    return comp
}

Object.entries(ui).map(([id, comp]) => {
    const type = comp["ui:widget"]

    if (!result[type]) {
        console.log("Cannot set key : " + type)
        return null
    }

    const order = ids.indexOf(id)

    if (order == -1) {
        console.log("Alias is not exist is form tree : " + id)
        return null
    }

    const isHidden = invisibleComponents.indexOf(id) > -1 || _.intersection(invisibleComponents, step0.dict[id]).length > 0

    const formatted = formatComp({...comp }, id, type)

    result[type].push({...formatted, id, order, hidden: isHidden })
}).filter(x => x)

const resultSortedByOrder = Object.fromEntries(
    Object.entries(result).map(
        ([k, v]) => {
            const sortedValues = v.sort((a, b) => {
                if (a["order"] < b["order"]) { return -1; }
                if (a["order"] > b["order"]) { return 1; }

                return 0;
            })


            return [k, sortedValues]
        }
    ))

const fs = require('fs');

console.log("\nWRITING FILE " + outputStep1File)
fs.writeFile(outputStep1File, JSON.stringify(resultSortedByOrder, null, 4), () => {
    console.log("Write file successfully")
})