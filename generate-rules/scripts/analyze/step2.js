const fs = require('fs')
const _ = require('lodash')
const { toIds } = require('../../utils/basic')
const {
    outputStep1File,
    outputJsonStep2Folder,
    outputTextStep2Folder,
    outputStep0File,
    COMPONENT_INPUT,
    UI_INPUT,
} = require("./config")
const {
    WIDGET_PAGE,
} = require("./constants")
const OUTPUT_FOLDER = outputJsonStep2Folder + "/"
const TEXT_OUTPUT_FOLDER = outputTextStep2Folder + "/"

const UI = require(UI_INPUT)
const COMPONENT = require(COMPONENT_INPUT)
const STEP0 = require("../." + outputStep0File)
const STEP1 = require("../." + outputStep1File)

const pages = _.get(STEP1, [WIDGET_PAGE])
const formattedComps = Object.fromEntries(
    Object.values(STEP1)
    .flat()
    .map(item => [item.id, item])
)
const titles = STEP0["titles"] || {}

const jsonPages = pages
    .map(({ id }) => {
        const path = _.get(STEP0, ["dict", id])
        const propertiesPaths = [...path, id]
            .map(ele => ["properties", ele])
            .flat()
        const pageObj = _.get(COMPONENT, propertiesPaths)
        const ids = toIds(pageObj)
        const name = `page_${id} -- ${titles[id]}`
        const ui = Object.fromEntries(
            ids.map(
                id => [id, formattedComps[id]]
            )
        )

        return [name, { schema: pageObj, ui }]
    })

jsonPages.map(([k, v], idx) => {
    const padNum = _.padStart(idx, 2, "0")
    const slashName = k.replace(/'/g, "\\'").replace('/', ':')
    const path = `${OUTPUT_FOLDER}${padNum} -- ${slashName}.json`
    const textPath = `${TEXT_OUTPUT_FOLDER}${padNum} -- ${slashName}.txt`

    fs.writeFile(path, JSON.stringify(v, null, 4), (err) => {
        if (err) {
            console.log(err)
            return
        }

        console.log("\nWrite file successfully")
        console.log("Path: ", path)
    })

    const collectedText = Object.values(v.ui)
        .map(comp => {
            let txt = ""

            if (comp["ui:formattedText"]) {
                txt += "\n" + comp["ui:formattedText"]
            }
            if (comp["ui:multipleOption"] && comp["ui:multipleOption"].length) {
                comp["ui:multipleOption"].forEach(op => {
                    txt += "\n" + op["formattedText"]
                })
            }
            if (comp["ui:supportingFileGroup"] && comp["ui:supportingFileGroup"]["files"]) {
                txt += "\n" + comp["ui:supportingFileGroup"]["description"]
                Object.values(comp["ui:supportingFileGroup"]["files"]).map(op => {
                    txt += "\n" + op["description"]
                })
            }
            if (comp["ui:embeddedPdf"]) {
                txt += "\n" + comp["ui:embeddedPdf"]
            }
            if (comp["ui:fileName"]) {
                txt += "\n" + comp["ui:fileName"]
            }
            if (comp["ui:heading"]) {
                txt += "\n" + comp["ui:heading"]
            }
            if (comp["ui:subtitle"]) {
                txt += "\n" + comp["ui:subtitle"]
            }

            return txt
        })
        .join("\n")

    fs.writeFile(textPath, collectedText, (err) => {
        if (err) {
            console.log(err)
            return
        }

        console.log("\nWrite file successfully")
        console.log("Path: ", textPath)
    })
})