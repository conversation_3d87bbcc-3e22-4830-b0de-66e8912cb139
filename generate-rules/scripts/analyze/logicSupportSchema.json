{"schema": [{"name": "logic_support", "type": "object", "title": "Logic Support", "properties": [{"name": "logic_support_header", "type": "null", "title": "Header"}, {"name": "is_natural_person", "type": "string", "title": "Is user Natural Person?", "enum": ["yes", "no"]}, {"name": "is_joint", "type": "string", "title": "Is user Joint?", "enum": ["yes", "no"]}, {"name": "is_ira", "type": "string", "title": "Is user IRA?", "enum": ["yes", "no"]}, {"name": "is_llc", "type": "string", "title": "Is user LLC?", "enum": ["yes", "no"]}, {"name": "is_trust", "type": "string", "title": "Is user Trust?", "enum": ["yes", "no"]}, {"name": "is_indi_trust", "type": "string", "title": "Is user Individual Trust?", "enum": ["yes", "no"]}, {"name": "is_entity_trust", "type": "string", "title": "Is user Entity Trust?", "enum": ["yes", "no"]}, {"name": "is_indi_other", "type": "string", "title": "Is user Individual Other?", "enum": ["yes", "no"]}, {"name": "is_entity_other", "type": "string", "title": "Is user Entity Other?", "enum": ["yes", "no"]}, {"name": "shortcut", "type": "string", "title": "Shortcut of Investor Type (Individual vs Entity)", "enum": ["indi", "entity"]}, {"name": "groups", "type": "string", "title": "Groups of investor defined by checklist", "enum": ["g1", "g2", "g3", "g4"]}, {"name": "merged_individual_name", "type": "string", "title": "Merged Individual Name"}, {"name": "investment_entity_fs_setup", "type": "string", "title": "Investment Entity FS Setup"}]}], "ui": {"logic_support": {"ui:invisible": true, "ui:widget": "Page"}, "logic_support_header": {"ui:formattedText": "Header", "ui:heading": "LOGIC SUPPORT", "ui:widget": "Header"}, "is_trust": {"ui:formattedText": "Is user Trust?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_ira": {"ui:formattedText": "Is user IRA?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_llc": {"ui:formattedText": "Is user LLC?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_joint": {"ui:formattedText": "Is user Joint?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_natural_person": {"ui:formattedText": "Is user Natural Person?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_indi_trust": {"ui:formattedText": "Is user Indi Trust?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_entity_trust": {"ui:formattedText": "Is user Entity Trust?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_indi_other": {"ui:formattedText": "Is user Indi Other?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "is_entity_other": {"ui:formattedText": "Is user Entity Other?", "ui:multipleOption": {"options": [["yes", {"pdfMapping": "", "formattedText": "<p>yes</p>"}], ["no", {"pdfMapping": "", "formattedText": "<p>no</p>"}]]}, "ui:required": "This field is required", "ui:widget": "Radio"}, "shortcut": {"ui:formattedText": "Shortcut of Investor Type (Individual vs Entity)", "ui:multipleOption": {"options": [["indi", {"pdfMapping": "", "formattedText": "<p>natural person</p>", "fieldsBelow": []}], ["entity", {"pdfMapping": "", "formattedText": "<p>entity</p>", "fieldsBelow": []}]]}, "ui:widget": "Radio"}, "groups": {"ui:formattedText": "Groups of investor defined by checklist (Indi & Joint, Other Indi, Trust, Other Entites, etc.)", "ui:multipleOption": {"options": [["g1", {"pdfMapping": "", "formattedText": "<p>g1</p>", "fieldsBelow": []}], ["g2", {"pdfMapping": "", "formattedText": "<p>g2</p>", "fieldsBelow": []}], ["g3", {"pdfMapping": "", "formattedText": "<p>g3</p>", "fieldsBelow": []}], ["g4", {"pdfMapping": "", "formattedText": "<p>g4</p>", "fieldsBelow": []}]]}, "ui:widget": "Radio"}, "merged_individual_name": {"ui:formattedText": "Merged Individual Name", "ui:widget": "TextBox"}, "investment_entity_fs_setup": {"ui:formattedText": "Investment Entity FS Setup", "ui:widget": "TextBox"}}}