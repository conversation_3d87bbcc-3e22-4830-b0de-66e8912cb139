const PRINT_DATA = process.argv[4] == "log"
const FOLDER_NAME = process.argv[2]
const FILE_PATH = process.argv[3]

const USAGE_STRING = 'Usage: node step1-extract.js FOLDER_NAME FILE_PATH'

if (!FOLDER_NAME) {
    throw USAGE_STRING
}

const INPUT = "../forms/"

const toExtractInputPath = (name) => `../../data/${name}/extract`

const EXTRACT_INPUT = toExtractInputPath(FOLDER_NAME)

const COMPONENT_INPUT = `${EXTRACT_INPUT}/components.json`
const COMPONENT_ALIAS_INPUT = `${EXTRACT_INPUT}/components_alias.json`
const PDF_INPUT = `${EXTRACT_INPUT}/embedded_pdf.json`
const RULE_JSON_INPUT = `${EXTRACT_INPUT}/rules.json`
const UI_INPUT = `${EXTRACT_INPUT}/ui.json`
const NEW_ALIAS_INPUT = `${EXTRACT_INPUT}/new-alias.json`

const OUTPUT = `./data/${FOLDER_NAME}/analyze`

const outputStep0File = `${OUTPUT}/step0.json`
const outputStep1File = `${OUTPUT}/step1.json`
const outputJsonStep2Folder = `${OUTPUT}/pages__json`
const outputTextStep2Folder = `${OUTPUT}/pages__text`
const outputStep3Folder = `${OUTPUT}/rules`
const outputStep3File = `${OUTPUT}/rules/index.json`
const outputStep9File = `${OUTPUT}/step9.json`

if (PRINT_DATA) {
    console.log("\n\nProcessing with give input")
    console.log("FOLDER NAME :    " + FOLDER_NAME)
    console.log("FILE PATH :      " + FILE_PATH)
    console.log("EXTRACT:         " + EXTRACT_INPUT)
    console.log("COMPONENT:       " + COMPONENT_INPUT)
    console.log("COMPONENT ALIAS: " + COMPONENT_ALIAS_INPUT)
    console.log("PDF:             " + PDF_INPUT)
    console.log("RULE JSON:       " + RULE_JSON_INPUT)
    console.log("UI:              " + UI_INPUT)


    console.log("\n\nOuput will be written in following file")
    console.log("OUTPUT PATH: ", OUTPUT)

    console.log("STEP 0: ", outputStep0File)
    console.log("STEP 1: ", outputStep1File)
    console.log("STEP 3: ", outputStep3Folder)
    console.log("STEP 9: ", outputStep9File)
}

const toStepInputPath = (outputPath) => `../.${outputPath}`

module.exports = {
    FOLDER_NAME,
    FILE_PATH,
    INPUT,
    OUTPUT,
    EXTRACT_INPUT,
    COMPONENT_INPUT,
    COMPONENT_ALIAS_INPUT,
    PDF_INPUT,
    RULE_JSON_INPUT,
    UI_INPUT,
    NEW_ALIAS_INPUT,
    outputStep0File,
    outputStep1File,
    outputStep3Folder,
    outputJsonStep2Folder,
    outputTextStep2Folder,
    outputStep3File,
    outputStep9File,
    toExtractInputPath,
    toStepInputPath,
}