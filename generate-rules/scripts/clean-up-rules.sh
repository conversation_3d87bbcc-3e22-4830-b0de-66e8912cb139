#!/bin/sh

#$1 FORM_NAME
#$2 PATH_TO_JSON_FILE

mkdir ./data
mkdir ./data/$1
mkdir ./data/$1/extract

node ./scripts/preprocessing/step1-extract.js $1 $2

if [[ $? -ne 0 ]]; then
    exit 1
fi

node ./scripts/preprocessing/step2-extract-rule-parameters.js $1

if [[ $? -ne 0 ]]; then
    exit 1
fi

node ./scripts/preprocessing/step6-detect-unused-variables.js $1
if [[ $? -ne 0 ]]; then
    exit 1
fi

node ./scripts/preprocessing/step7-remove-unused-variable.js $1 $2
if [[ $? -ne 0 ]]; then
    exit 1
fi