# Generate Rule Import Library

### Intruction to generate lib import script

Run following command lines at Generate Rule root folder `./gandalf/generate-rules`

1. Generate script with logic syntax v2
    ```
    node ./scripts/others/lib-import.js "./rules/lib-import" "./rules/lib-import/output/v2.json" v2
    ```

2. Format generated jsonnet files
    ```
    jsonnetfmt -i ./rules/lib-import/output/*.jsonnet --indent 4 --max-blank-lines 1 --string-style d
    ```


#### Ouput Files:
1. [v2.jsonnet](./output/v2.jsonnet): This can be used directly on FormBuilder by pasting this file's content to Lib Import tab
2. [v2-minified.jsonnet](./output/v2-minified.jsonnet): The same as v2.jsonnet but was minified to avoid overwriting. This is used to stored on Stargazer code base.
3. [v2-debug.text](./output/v2-debug.text): This is used to paste directly to Logic Editor where rule is written
4. [v2.json](./output/v2.json): This is used to to attached to form.json