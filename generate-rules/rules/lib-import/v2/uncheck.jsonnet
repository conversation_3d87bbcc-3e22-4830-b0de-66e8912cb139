function(items)

    local interactedItems = std.filter(function(item) item.touched, items);

    local uncheckCmds =
        if (std.length(interactedItems) > 0) then
            local nonInteractedItems = std.filter(function(item) !item.touched, items);

            self.touched(
                std.flattenArrays(
                    std.map(function(item) item.targets, interactedItems)
                )
                , false
            )
            + self.clear(
                std.flattenArrays(
                    std.map(function(item) item.targets, nonInteractedItems)
                )
            )
        else
            [];

    local noneItemSelected = std.length(
        std.filter(
            function(value) !self.empty(value),
            std.flatMap(
                function(item) item.values,
                items
            )
        )
    ) == 0;

    { uncheckCmds: uncheckCmds, noneItemSelected: noneItemSelected }
