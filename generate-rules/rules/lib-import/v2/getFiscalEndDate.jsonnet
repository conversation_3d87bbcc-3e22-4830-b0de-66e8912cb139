function(startMonth, startDay)
    local MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

    local invalid = self.empty(startMonth) || self.empty(startDay);

    if invalid then
        {
            endMonth: "",
            endDay: null,
        }
    else
        local idxes = std.find(startMonth, MONTHS);
        local idx = if std.length(idxes) >= 1 then idxes[0] else 0;

        local month =
            if idx - 1 < 0 then
                MONTHS[11]
            else
                MONTHS[idx - 1]
        ;

        local endMonth =
            if startDay - 1 == 0 then
                month
            else
                startMonth
        ;
        local endDay =
            if startDay - 1 <= 0 then
                self.getFiscalDaysInMonth(month)
            else
                startDay - 1
        ;

        {
            endMonth: endMonth,
            endDay: endDay,
        }
