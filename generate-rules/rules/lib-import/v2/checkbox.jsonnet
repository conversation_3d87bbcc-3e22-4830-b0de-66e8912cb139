function(config)

    local targetValue = config.value;
    local configuredDisabledOptions = self.toOptions(config.configuredDisabledOptions);
    local disableAndPrefillOps = self.toOptions(
        std.filter(
            function(op) op.disable
            , config.configuredPrefillOptions
        )
    );
    local uncheckOptionGroups = config.uncheckOptionGroups;

    local endItem = self.last(targetValue);

    local toUncheckOptions = function(groups)
        local uncheckedGroups = std.filter(
            function(options)
                !std.member(options, endItem)
            , groups
        );

        if std.length(uncheckedGroups) < std.length(groups) then
            std.flattenArrays(uncheckedGroups)
        else
            [];

    local disableUncheckedOps =
        std.flattenArrays(
            std.map(
                function(op)
                    local uncheckedOps = std.map(
                        function(pair)
                            if std.member(pair[0], op) then
                                pair[1]
                            else if std.member(pair[1], op) then
                                pair[0]
                            else
                                []
                        , config.uncheckOptionGroups
                    );

                    std.flattenArrays(uncheckedOps)
                , disableAndPrefillOps
            )
        );

    // Make sure no conflict between clearing options and the prefilling ones
    local disabledOps = self.purge(configuredDisabledOptions, disableAndPrefillOps) + disableUncheckedOps;

    local uncheckOps = std.flattenArrays(std.map(toUncheckOptions, uncheckOptionGroups));

    // List of options is prefilled by conditions and triggers
    local prefillOptions = std.set(
        std.flattenArrays(
            std.map(
                function(item) item.ops, config.configuredPrefillOptions
            )
        )
    );

    local prefilledTargetValue =
        std.foldl(
            function(value, op)
                local configs = std.filter(
                    function(item) std.member(item.ops, op)
                    , config.configuredPrefillOptions
                );

                local trigger = std.foldl(
                    function(result, item) result || item.trigger
                    , configs
                    , false
                );

                local condition = std.foldl(
                    function(result, item) result || item.condition
                    , configs
                    , false
                );

                if trigger then
                    if condition then
                        std.set(value + [op])
                    else
                        self.purge(value, [op])
                else
                    value
            , prefillOptions
            , targetValue
        );

    // Always clear data of disabled and uncheck options
    local clearedTargetValue = self.purge(prefilledTargetValue, uncheckOps + disabledOps);

    local disabledOptions = configuredDisabledOptions + disableAndPrefillOps + disableUncheckedOps;

    {
        value: clearedTargetValue,
        disabledOptions: disabledOptions,
    }
