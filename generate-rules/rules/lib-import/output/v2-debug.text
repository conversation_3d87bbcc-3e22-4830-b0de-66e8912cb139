// Generate Rule Import Library is generated as following instruction
// https://github.com/anduintransaction/gandalf/blob/master/generate-rules/rules/lib-import/README.md
// Version: 942b683

local purge = function(arr, elements)

    std.filter(function(ele) !std.member(elements, ele), arr)
;

local intersect = function(arr1, arr2)

    std.filter(function(ele) std.member(arr1, ele), arr2)
;

local endsWith = function(arr, ele)

    if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele
;

local forceNum = function(value)

    if std.isNumber(value) then
        value
    else if std.isString(value) then
        safeInt(value)
    else
        0
;

local forceStr = function(value)

    if value == null then
        ""
    else
        std.toString(value)
;

local last = function(arr)

    local len = std.length(arr);

    if len == 0 then "" else arr[len - 1]
;

local join = function(spliter, items)

    local filtered = std.filter(
        function(item) item != null && item != "",
        std.map(
            function(item) forceStr(item),
            items
        )
    );

    std.join(spliter, filtered)
;

local safeInt = function(s)

    local numStr = std.join("", std.filter(function(c) std.member("0123456789", c), std.stringChars(s)));

    if numStr == "" then 0 else std.parseInt(numStr)
;

local empty = function(value)

    if std.type(value) == "array" then
        std.length(value) == 0
    else if std.type(value) == "null" then
        true
    else if std.type(value) == "string" then
        value == ""
    else
        false
;

local getFiscalDaysInMonth = function(month)

    if month == "February" then
        28
    else if std.member(["April", "June", "September", "November"], month) then
        30
    else
        31
;

local getFiscalEndDate = function(startMonth, startDay)
    local MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

    local invalid = empty(startMonth) || empty(startDay);

    if invalid then
        {
            endMonth: "",
            endDay: null,
        }
    else
        local idxes = std.find(startMonth, MONTHS);
        local idx = if std.length(idxes) >= 1 then idxes[0] else 0;

        local month =
            if idx - 1 < 0 then
                MONTHS[11]
            else
                MONTHS[idx - 1]
        ;

        local endMonth =
            if startDay - 1 == 0 then
                month
            else
                startMonth
        ;
        local endDay =
            if startDay - 1 <= 0 then
                getFiscalDaysInMonth(month)
            else
                startDay - 1
        ;

        {
            endMonth: endMonth,
            endDay: endDay,
        }
;

local yes = function(value)

    value == "yes"
;

local no = function(value)

    value == "no"
;

local clear = function(targets, condition=true)

    local clear = function(target)
        if condition then target.clear() else [];

    if std.isArray(targets) then
        std.flattenArrays(std.map(clear, targets))
    else
        clear(targets)
;

local touched = function(targets, value=true)

    local touched = function(target)
        target.touched(value);

    if std.isArray(targets) then
        std.flattenArrays(std.map(touched, targets))
    else
        touched(targets)
;

local disable = function(targets, condition=true, clearedTargets=[])

    local disable = function(target)
        target.disable(condition);

    local disableCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(disable, targets))
        else
            disable(targets);

    clear(clearedTargets, condition)

    + disableCmds
;

local disabledOptions = function(target, options)

    target.disabledOptions(options)
;

local enable = function(targets, condition=true, clearedTargets=[])

    disable(targets, !condition, clearedTargets)
;

local hide = function(targets, condition=true, clearedTargets=[])

    local hide = function(target)
        target.hide(condition);

    local hideCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(hide, targets))
        else
            hide(targets);

    clear(clearedTargets, condition)

    + hideCmds
;

local setError = function(targets, message, condition=true)

    local setError = function(target)
        target.setError(if condition then message else "");

    local setErrorCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(setError, targets))
        else
            setError(targets);

    setErrorCmds
;

local show = function(targets, condition=true, clearedTargets=[])

    hide(targets, !condition, clearedTargets)
;

local value = function(targets, value, condition=true)

    local valueCmd = function(target)
        if condition then
            target.value(value)
        else
            clear(target);

    local valueCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(valueCmd, targets))
        else
            valueCmd(targets);

    valueCmds
;

local warning = function(target, message)

    target.warning(message)
;

local signingType = function(target, options)

    target.signingType(options)
;

local clearRep = function(targets, index, condition=true)

    local clear = function(target)
        if condition then target.clearAtIndex(index) else [];

    if std.isArray(targets) then
        std.flattenArrays(std.map(clear, targets))
    else
        clear(targets)
;

local touchedRep = function(targets, index, value=true)

    local touched = function(target)
        target.touchedAtIndex(index, value);

    if std.isArray(targets) then
        std.flattenArrays(std.map(touched, targets))
    else
        touched(targets)
;

local disableRep = function(targets, index, condition=true, clearedTargets=[])

    local disable = function(target)
        target.disableAtIndex(index, condition);

    local disableCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(disable, targets))
        else
            disable(targets);

    clearRep(clearedTargets, index, condition)

    + disableCmds
;

local disabledOptionsRep = function(target, index, options)

    target.disabledOptionsAtIndex(index, options)
;

local enableRep = function(targets, index, condition=true, clearedTargets=[])

    disableRep(targets, index, !condition, clearedTargets)
;

local hideRep = function(targets, index, condition=true, clearedTargets=[])

    local hide = function(target)
        target.hideAtIndex(index, condition);

    local hideCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(hide, targets))
        else
            hide(targets);

    clearRep(clearedTargets, index, condition)

    + hideCmds
;

local setErrorRep = function(target, index, message)

    target.setErrorAtIndex(index, message)
;

local showRep = function(targets, index, condition=true, clearedTargets=[])

    hideRep(targets, index, !condition, clearedTargets)
;

local valueRep = function(targets, index, value, condition=true)

    local valueCmd = function(target)
        if condition then
            target.valueAtIndex(index, value)
        else
            clearRep(target, index);

    local valueCmds =
        if std.isArray(targets) then
            std.flattenArrays(std.map(valueCmd, targets))
        else
            valueCmd(targets);

    valueCmds
;

local warningRep = function(target, index, message)

    target.warningAtIndex(index, message)
;

local disabled = function(alias)

    alias.disable || alias.disableByParent
;

local enabled = function(alias)

    !disabled(alias)
;

local hidden = function(alias)

    alias.hide || alias.hideByParent
;

local shown = function(alias)

    !hidden(alias)
;

local toOptions = function(configs)

    local f = function(config)
        if config.condition then config.ops else [];

    std.flattenArrays(std.map(f, configs))
;

local uncheck = function(items)

    local interactedItems = std.filter(function(item) item.touched, items);

    local uncheckCmds =
        if (std.length(interactedItems) > 0) then
            local nonInteractedItems = std.filter(function(item) !item.touched, items);

            touched(
                std.flattenArrays(
                    std.map(function(item) item.targets, interactedItems)
                )
                , false
            )
            + clear(
                std.flattenArrays(
                    std.map(function(item) item.targets, nonInteractedItems)
                )
            )
        else
            [];

    local noneItemSelected = std.length(
        std.filter(
            function(value) !empty(value),
            std.flatMap(
                function(item) item.values,
                items
            )
        )
    ) == 0;

    { uncheckCmds: uncheckCmds, noneItemSelected: noneItemSelected }
;

local checkbox = function(config)

    local targetValue = config.value;
    local configuredDisabledOptions = toOptions(config.configuredDisabledOptions);
    local disableAndPrefillOps = toOptions(
        std.filter(
            function(op) op.disable
            , config.configuredPrefillOptions
        )
    );
    local uncheckOptionGroups = config.uncheckOptionGroups;

    local endItem = last(targetValue);

    local toUncheckOptions = function(groups)
        local uncheckedGroups = std.filter(
            function(options)
                !std.member(options, endItem)
            , groups
        );

        if std.length(uncheckedGroups) < std.length(groups) then
            std.flattenArrays(uncheckedGroups)
        else
            [];

    local disableUncheckedOps =
        std.flattenArrays(
            std.map(
                function(op)
                    local uncheckedOps = std.map(
                        function(pair)
                            if std.member(pair[0], op) then
                                pair[1]
                            else if std.member(pair[1], op) then
                                pair[0]
                            else
                                []
                        , config.uncheckOptionGroups
                    );

                    std.flattenArrays(uncheckedOps)
                , disableAndPrefillOps
            )
        );

    // Make sure no conflict between clearing options and the prefilling ones
    local disabledOps = purge(configuredDisabledOptions, disableAndPrefillOps) + disableUncheckedOps;

    local uncheckOps = std.flattenArrays(std.map(toUncheckOptions, uncheckOptionGroups));

    // List of options is prefilled by conditions and triggers
    local prefillOptions = std.set(
        std.flattenArrays(
            std.map(
                function(item) item.ops, config.configuredPrefillOptions
            )
        )
    );

    local prefilledTargetValue =
        std.foldl(
            function(value, op)
                local configs = std.filter(
                    function(item) std.member(item.ops, op)
                    , config.configuredPrefillOptions
                );

                local trigger = std.foldl(
                    function(result, item) result || item.trigger
                    , configs
                    , false
                );

                local condition = std.foldl(
                    function(result, item) result || item.condition
                    , configs
                    , false
                );

                if trigger then
                    if condition then
                        std.set(value + [op])
                    else
                        purge(value, [op])
                else
                    value
            , prefillOptions
            , targetValue
        );

    // Always clear data of disabled and uncheck options
    local clearedTargetValue = purge(prefilledTargetValue, uncheckOps + disabledOps);

    local disabledOptions = configuredDisabledOptions + disableAndPrefillOps + disableUncheckedOps;

    {
        value: clearedTargetValue,
        disabledOptions: disabledOptions,
    }
;

local radio = function(configs)

    local targetValue = configs.targetValue;
    local disabledOptions = toOptions(configs.configuredDisabledOptions);

    // Always clear the data of a disabled field which has its disabled situation depending on a condition
    local value = if std.member(disabledOptions, targetValue) then "" else targetValue;

    { value: value, disabledOptions: disabledOptions }
;

local fileGroup = function(target, fileArray)

    local files = std.map(function(m) if m.condition then [m.file] else [], fileArray);

    value(target, std.flattenArrays(files))
;

local arrayContainsAny = function(arr, elements)
    if std.length(arr) == 0 then 
        false
    else 
        std.length(std.filter(function(item) std.member(elements, item), arr)) > 0;

local isDirtyField = function(field)
    field.touched || field.imported || field.autoFilled
;