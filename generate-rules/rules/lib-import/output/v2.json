{"all": "// Generate Rule Import Library is generated as following instruction\n// https://github.com/anduintransaction/gandalf/blob/master/generate-rules/rules/lib-import/README.md\n// Version: 942b683\n\n{purge: function(arr, elements)\n\n    std.filter(function(ele) !std.member(elements, ele), arr)\n,\n\nintersect: function(arr1, arr2)\n\n    std.filter(function(ele) std.member(arr1, ele), arr2)\n,\n\nendsWith: function(arr, ele)\n\n    if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele\n,\n\nforceNum: function(value)\n\n    if std.isNumber(value) then\n        value\n    else if std.isString(value) then\n        self.safeInt(value)\n    else\n        0\n,\n\nforceStr: function(value)\n\n    if value == null then\n        \"\"\n    else\n        std.toString(value)\n,\n\nlast: function(arr)\n\n    local len = std.length(arr);\n\n    if len == 0 then \"\" else arr[len - 1]\n,\n\njoin: function(spliter, items)\n\n    local filtered = std.filter(\n        function(item) item != null && item != \"\",\n        std.map(\n            function(item) self.forceStr(item),\n            items\n        )\n    );\n\n    std.join(spliter, filtered)\n,\n\nsafeInt: function(s)\n\n    local numStr = std.join(\"\", std.filter(function(c) std.member(\"0123456789\", c), std.stringChars(s)));\n\n    if numStr == \"\" then 0 else std.parseInt(numStr)\n,\n\nempty: function(value)\n\n    if std.type(value) == \"array\" then\n        std.length(value) == 0\n    else if std.type(value) == \"null\" then\n        true\n    else if std.type(value) == \"string\" then\n        value == \"\"\n    else\n        false\n,\n\ngetFiscalDaysInMonth: function(month)\n\n    if month == \"February\" then\n        28\n    else if std.member([\"April\", \"June\", \"September\", \"November\"], month) then\n        30\n    else\n        31\n,\n\ngetFiscalEndDate: function(startMonth, startDay)\n    local MONTHS = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n\n    local invalid = self.empty(startMonth) || self.empty(startDay);\n\n    if invalid then\n        {\n            endMonth: \"\",\n            endDay: null,\n        }\n    else\n        local idxes = std.find(startMonth, MONTHS);\n        local idx = if std.length(idxes) >= 1 then idxes[0] else 0;\n\n        local month =\n            if idx - 1 < 0 then\n                MONTHS[11]\n            else\n                MONTHS[idx - 1]\n        ;\n\n        local endMonth =\n            if startDay - 1 == 0 then\n                month\n            else\n                startMonth\n        ;\n        local endDay =\n            if startDay - 1 <= 0 then\n                self.getFiscalDaysInMonth(month)\n            else\n                startDay - 1\n        ;\n\n        {\n            endMonth: endMonth,\n            endDay: endDay,\n        }\n,\n\nyes: function(value)\n\n    value == \"yes\"\n,\n\nno: function(value)\n\n    value == \"no\"\n,\n\nclear: function(targets, condition=true)\n\n    local clear = function(target)\n        if condition then target.clear() else [];\n\n    if std.isArray(targets) then\n        std.flattenArrays(std.map(clear, targets))\n    else\n        clear(targets)\n,\n\ntouched: function(targets, value=true)\n\n    local touched = function(target)\n        target.touched(value);\n\n    if std.isArray(targets) then\n        std.flattenArrays(std.map(touched, targets))\n    else\n        touched(targets)\n,\n\ndisable: function(targets, condition=true, clearedTargets=[])\n\n    local disable = function(target)\n        target.disable(condition);\n\n    local disableCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(disable, targets))\n        else\n            disable(targets);\n\n    self.clear(clearedTargets, condition)\n\n    + disableCmds\n,\n\ndisabledOptions: function(target, options)\n\n    target.disabledOptions(options)\n,\n\nenable: function(targets, condition=true, clearedTargets=[])\n\n    self.disable(targets, !condition, clearedTargets)\n,\n\nhide: function(targets, condition=true, clearedTargets=[])\n\n    local hide = function(target)\n        target.hide(condition);\n\n    local hideCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(hide, targets))\n        else\n            hide(targets);\n\n    self.clear(clearedTargets, condition)\n\n    + hideCmds\n,\n\nsetError: function(targets, message, condition=true)\n\n    local setError = function(target)\n        target.setError(if condition then message else \"\");\n\n    local setErrorCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(setError, targets))\n        else\n            setError(targets);\n\n    setErrorCmds\n,\n\nshow: function(targets, condition=true, clearedTargets=[])\n\n    self.hide(targets, !condition, clearedTargets)\n,\n\nvalue: function(targets, value, condition=true)\n\n    local valueCmd = function(target)\n        if condition then\n            target.value(value)\n        else\n            self.clear(target);\n\n    local valueCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(valueCmd, targets))\n        else\n            valueCmd(targets);\n\n    valueCmds\n,\n\nwarning: function(target, message)\n\n    target.warning(message)\n,\n\nsigningType: function(target, options)\n\n    target.signingType(options)\n,\n\nclearRep: function(targets, index, condition=true)\n\n    local clear = function(target)\n        if condition then target.clearAtIndex(index) else [];\n\n    if std.isArray(targets) then\n        std.flattenArrays(std.map(clear, targets))\n    else\n        clear(targets)\n,\n\ntouchedRep: function(targets, index, value=true)\n\n    local touched = function(target)\n        target.touchedAtIndex(index, value);\n\n    if std.isArray(targets) then\n        std.flattenArrays(std.map(touched, targets))\n    else\n        touched(targets)\n,\n\ndisableRep: function(targets, index, condition=true, clearedTargets=[])\n\n    local disable = function(target)\n        target.disableAtIndex(index, condition);\n\n    local disableCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(disable, targets))\n        else\n            disable(targets);\n\n    self.clearRep(clearedTargets, index, condition)\n\n    + disableCmds\n,\n\ndisabledOptionsRep: function(target, index, options)\n\n    target.disabledOptionsAtIndex(index, options)\n,\n\nenableRep: function(targets, index, condition=true, clearedTargets=[])\n\n    self.disableRep(targets, index, !condition, clearedTargets)\n,\n\nhideRep: function(targets, index, condition=true, clearedTargets=[])\n\n    local hide = function(target)\n        target.hideAtIndex(index, condition);\n\n    local hideCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(hide, targets))\n        else\n            hide(targets);\n\n    self.clearRep(clearedTargets, index, condition)\n\n    + hideCmds\n,\n\nsetErrorRep: function(target, index, message)\n\n    target.setErrorAtIndex(index, message)\n,\n\nshowRep: function(targets, index, condition=true, clearedTargets=[])\n\n    self.hideRep(targets, index, !condition, clearedTargets)\n,\n\nvalueRep: function(targets, index, value, condition=true)\n\n    local valueCmd = function(target)\n        if condition then\n            target.valueAtIndex(index, value)\n        else\n            self.clearRep(target, index);\n\n    local valueCmds =\n        if std.isArray(targets) then\n            std.flattenArrays(std.map(valueCmd, targets))\n        else\n            valueCmd(targets);\n\n    valueCmds\n,\n\nwarningRep: function(target, index, message)\n\n    target.warningAtIndex(index, message)\n,\n\ndisabled: function(alias)\n\n    alias.disable || alias.disableByParent\n,\n\nenabled: function(alias)\n\n    !self.disabled(alias)\n,\n\nhidden: function(alias)\n\n    alias.hide || alias.hideByParent\n,\n\nshown: function(alias)\n\n    !self.hidden(alias)\n,\n\ntoOptions: function(configs)\n\n    local f = function(config)\n        if config.condition then config.ops else [];\n\n    std.flattenArrays(std.map(f, configs))\n,\n\nuncheck: function(items)\n\n    local interactedItems = std.filter(function(item) item.touched, items);\n\n    local uncheckCmds =\n        if (std.length(interactedItems) > 0) then\n            local nonInteractedItems = std.filter(function(item) !item.touched, items);\n\n            self.touched(\n                std.flattenArrays(\n                    std.map(function(item) item.targets, interactedItems)\n                )\n                , false\n            )\n            + self.clear(\n                std.flattenArrays(\n                    std.map(function(item) item.targets, nonInteractedItems)\n                )\n            )\n        else\n            [];\n\n    local noneItemSelected = std.length(\n        std.filter(\n            function(value) !self.empty(value),\n            std.flatMap(\n                function(item) item.values,\n                items\n            )\n        )\n    ) == 0;\n\n    { uncheckCmds: uncheckCmds, noneItemSelected: noneItemSelected }\n,\n\ncheckbox: function(config)\n\n    local targetValue = config.value;\n    local configuredDisabledOptions = self.toOptions(config.configuredDisabledOptions);\n    local disableAndPrefillOps = self.toOptions(\n        std.filter(\n            function(op) op.disable\n            , config.configuredPrefillOptions\n        )\n    );\n    local uncheckOptionGroups = config.uncheckOptionGroups;\n\n    local endItem = self.last(targetValue);\n\n    local toUncheckOptions = function(groups)\n        local uncheckedGroups = std.filter(\n            function(options)\n                !std.member(options, endItem)\n            , groups\n        );\n\n        if std.length(uncheckedGroups) < std.length(groups) then\n            std.flattenArrays(uncheckedGroups)\n        else\n            [];\n\n    local disableUncheckedOps =\n        std.flattenArrays(\n            std.map(\n                function(op)\n                    local uncheckedOps = std.map(\n                        function(pair)\n                            if std.member(pair[0], op) then\n                                pair[1]\n                            else if std.member(pair[1], op) then\n                                pair[0]\n                            else\n                                []\n                        , config.uncheckOptionGroups\n                    );\n\n                    std.flattenArrays(uncheckedOps)\n                , disableAndPrefillOps\n            )\n        );\n\n    // Make sure no conflict between clearing options and the prefilling ones\n    local disabledOps = self.purge(configuredDisabledOptions, disableAndPrefillOps) + disableUncheckedOps;\n\n    local uncheckOps = std.flattenArrays(std.map(toUncheckOptions, uncheckOptionGroups));\n\n    // List of options is prefilled by conditions and triggers\n    local prefillOptions = std.set(\n        std.flattenArrays(\n            std.map(\n                function(item) item.ops, config.configuredPrefillOptions\n            )\n        )\n    );\n\n    local prefilledTargetValue =\n        std.foldl(\n            function(value, op)\n                local configs = std.filter(\n                    function(item) std.member(item.ops, op)\n                    , config.configuredPrefillOptions\n                );\n\n                local trigger = std.foldl(\n                    function(result, item) result || item.trigger\n                    , configs\n                    , false\n                );\n\n                local condition = std.foldl(\n                    function(result, item) result || item.condition\n                    , configs\n                    , false\n                );\n\n                if trigger then\n                    if condition then\n                        std.set(value + [op])\n                    else\n                        self.purge(value, [op])\n                else\n                    value\n            , prefillOptions\n            , targetValue\n        );\n\n    // Always clear data of disabled and uncheck options\n    local clearedTargetValue = self.purge(prefilledTargetValue, uncheckOps + disabledOps);\n\n    local disabledOptions = configuredDisabledOptions + disableAndPrefillOps + disableUncheckedOps;\n\n    {\n        value: clearedTargetValue,\n        disabledOptions: disabledOptions,\n    }\n,\n\nradio: function(configs)\n\n    local targetValue = configs.targetValue;\n    local disabledOptions = self.toOptions(configs.configuredDisabledOptions);\n\n    // Always clear the data of a disabled field which has its disabled situation depending on a condition\n    local value = if std.member(disabledOptions, targetValue) then \"\" else targetValue;\n\n    { value: value, disabledOptions: disabledOptions }\n,\n\nfileGroup: function(target, fileArray)\n\n    local files = std.map(function(m) if m.condition then [m.file] else [], fileArray);\n\n    self.value(target, std.flattenArrays(files))\n,\n\narrayContainsAny: function(arr, elements)\n    if std.length(arr) == 0 then \n        false\n    else \n        std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,\n\nisDirtyField: function(field)\n    field.touched || field.imported || field.autoFilled\n}"}