// Generate Rule Import Library is generated as following instruction
// https://github.com/anduintransaction/gandalf/blob/master/generate-rules/rules/lib-import/README.md
// Version: 942b683

{
    purge: function(arr, elements)

        std.filter(function(ele) !std.member(elements, ele), arr)
    ,

    intersect: function(arr1, arr2)

        std.filter(function(ele) std.member(arr1, ele), arr2)
    ,

    endsWith: function(arr, ele)

        if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele
    ,

    forceNum: function(value)

        if std.isNumber(value) then
            value
        else if std.isString(value) then
            self.safeInt(value)
        else
            0
    ,

    forceStr: function(value)

        if value == null then
            ""
        else
            std.toString(value)
    ,

    last: function(arr)

        local len = std.length(arr);

        if len == 0 then "" else arr[len - 1]
    ,

    join: function(spliter, items)

        local filtered = std.filter(
            function(item) item != null && item != "",
            std.map(
                function(item) self.forceStr(item),
                items
            )
        );

        std.join(spliter, filtered)
    ,

    safeInt: function(s)

        local numStr = std.join("", std.filter(function(c) std.member("0123456789", c), std.stringChars(s)));

        if numStr == "" then 0 else std.parseInt(numStr)
    ,

    empty: function(value)

        if std.type(value) == "array" then
            std.length(value) == 0
        else if std.type(value) == "null" then
            true
        else if std.type(value) == "string" then
            value == ""
        else
            false
    ,

    getFiscalDaysInMonth: function(month)

        if month == "February" then
            28
        else if std.member(["April", "June", "September", "November"], month) then
            30
        else
            31
    ,

    getFiscalEndDate: function(startMonth, startDay)
        local MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

        local invalid = self.empty(startMonth) || self.empty(startDay);

        if invalid then
            {
                endMonth: "",
                endDay: null,
            }
        else
            local idxes = std.find(startMonth, MONTHS);
            local idx = if std.length(idxes) >= 1 then idxes[0] else 0;

            local month =
                if idx - 1 < 0 then
                    MONTHS[11]
                else
                    MONTHS[idx - 1]
            ;

            local endMonth =
                if startDay - 1 == 0 then
                    month
                else
                    startMonth
            ;
            local endDay =
                if startDay - 1 <= 0 then
                    self.getFiscalDaysInMonth(month)
                else
                    startDay - 1
            ;

            {
                endMonth: endMonth,
                endDay: endDay,
            }
    ,

    yes: function(value)

        value == "yes"
    ,

    no: function(value)

        value == "no"
    ,

    clear: function(targets, condition=true)

        local clear = function(target)
            if condition then target.clear() else [];

        if std.isArray(targets) then
            std.flattenArrays(std.map(clear, targets))
        else
            clear(targets)
    ,

    touched: function(targets, value=true)

        local touched = function(target)
            target.touched(value);

        if std.isArray(targets) then
            std.flattenArrays(std.map(touched, targets))
        else
            touched(targets)
    ,

    disable: function(targets, condition=true, clearedTargets=[])

        local disable = function(target)
            target.disable(condition);

        local disableCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(disable, targets))
            else
                disable(targets);

        self.clear(clearedTargets, condition)

        + disableCmds
    ,

    disabledOptions: function(target, options)

        target.disabledOptions(options)
    ,

    enable: function(targets, condition=true, clearedTargets=[])

        self.disable(targets, !condition, clearedTargets)
    ,

    hide: function(targets, condition=true, clearedTargets=[])

        local hide = function(target)
            target.hide(condition);

        local hideCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(hide, targets))
            else
                hide(targets);

        self.clear(clearedTargets, condition)

        + hideCmds
    ,

    setError: function(targets, message, condition=true)

        local setError = function(target)
            target.setError(if condition then message else "");

        local setErrorCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(setError, targets))
            else
                setError(targets);

        setErrorCmds
    ,

    show: function(targets, condition=true, clearedTargets=[])

        self.hide(targets, !condition, clearedTargets)
    ,

    value: function(targets, value, condition=true)

        local valueCmd = function(target)
            if condition then
                target.value(value)
            else
                self.clear(target);

        local valueCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(valueCmd, targets))
            else
                valueCmd(targets);

        valueCmds
    ,

    warning: function(target, message)

        target.warning(message)
    ,

    signingType: function(target, options)

        target.signingType(options)
    ,

    clearRep: function(targets, index, condition=true)

        local clear = function(target)
            if condition then target.clearAtIndex(index) else [];

        if std.isArray(targets) then
            std.flattenArrays(std.map(clear, targets))
        else
            clear(targets)
    ,

    touchedRep: function(targets, index, value=true)

        local touched = function(target)
            target.touchedAtIndex(index, value);

        if std.isArray(targets) then
            std.flattenArrays(std.map(touched, targets))
        else
            touched(targets)
    ,

    disableRep: function(targets, index, condition=true, clearedTargets=[])

        local disable = function(target)
            target.disableAtIndex(index, condition);

        local disableCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(disable, targets))
            else
                disable(targets);

        self.clearRep(clearedTargets, index, condition)

        + disableCmds
    ,

    disabledOptionsRep: function(target, index, options)

        target.disabledOptionsAtIndex(index, options)
    ,

    enableRep: function(targets, index, condition=true, clearedTargets=[])

        self.disableRep(targets, index, !condition, clearedTargets)
    ,

    hideRep: function(targets, index, condition=true, clearedTargets=[])

        local hide = function(target)
            target.hideAtIndex(index, condition);

        local hideCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(hide, targets))
            else
                hide(targets);

        self.clearRep(clearedTargets, index, condition)

        + hideCmds
    ,

    setErrorRep: function(target, index, message)

        target.setErrorAtIndex(index, message)
    ,

    showRep: function(targets, index, condition=true, clearedTargets=[])

        self.hideRep(targets, index, !condition, clearedTargets)
    ,

    valueRep: function(targets, index, value, condition=true)

        local valueCmd = function(target)
            if condition then
                target.valueAtIndex(index, value)
            else
                self.clearRep(target, index);

        local valueCmds =
            if std.isArray(targets) then
                std.flattenArrays(std.map(valueCmd, targets))
            else
                valueCmd(targets);

        valueCmds
    ,

    warningRep: function(target, index, message)

        target.warningAtIndex(index, message)
    ,

    disabled: function(alias)

        alias.disable || alias.disableByParent
    ,

    enabled: function(alias)

        !self.disabled(alias)
    ,

    hidden: function(alias)

        alias.hide || alias.hideByParent
    ,

    shown: function(alias)

        !self.hidden(alias)
    ,

    toOptions: function(configs)

        local f = function(config)
            if config.condition then config.ops else [];

        std.flattenArrays(std.map(f, configs))
    ,

    uncheck: function(items)

        local interactedItems = std.filter(function(item) item.touched, items);

        local uncheckCmds =
            if (std.length(interactedItems) > 0) then
                local nonInteractedItems = std.filter(function(item) !item.touched, items);

                self.touched(
                    std.flattenArrays(
                        std.map(function(item) item.targets, interactedItems)
                    )
                    , false
                )
                + self.clear(
                    std.flattenArrays(
                        std.map(function(item) item.targets, nonInteractedItems)
                    )
                )
            else
                [];

        local noneItemSelected = std.length(
            std.filter(
                function(value) !self.empty(value),
                std.flatMap(
                    function(item) item.values,
                    items
                )
            )
        ) == 0;

        { uncheckCmds: uncheckCmds, noneItemSelected: noneItemSelected }
    ,

    checkbox: function(config)

        local targetValue = config.value;
        local configuredDisabledOptions = self.toOptions(config.configuredDisabledOptions);
        local disableAndPrefillOps = self.toOptions(
            std.filter(
                function(op) op.disable
                , config.configuredPrefillOptions
            )
        );
        local uncheckOptionGroups = config.uncheckOptionGroups;

        local endItem = self.last(targetValue);

        local toUncheckOptions = function(groups)
            local uncheckedGroups = std.filter(
                function(options)
                    !std.member(options, endItem)
                , groups
            );

            if std.length(uncheckedGroups) < std.length(groups) then
                std.flattenArrays(uncheckedGroups)
            else
                [];

        local disableUncheckedOps =
            std.flattenArrays(
                std.map(
                    function(op)
                        local uncheckedOps = std.map(
                            function(pair)
                                if std.member(pair[0], op) then
                                    pair[1]
                                else if std.member(pair[1], op) then
                                    pair[0]
                                else
                                    []
                            , config.uncheckOptionGroups
                        );

                        std.flattenArrays(uncheckedOps)
                    , disableAndPrefillOps
                )
            );

        // Make sure no conflict between clearing options and the prefilling ones
        local disabledOps = self.purge(configuredDisabledOptions, disableAndPrefillOps) + disableUncheckedOps;

        local uncheckOps = std.flattenArrays(std.map(toUncheckOptions, uncheckOptionGroups));

        // List of options is prefilled by conditions and triggers
        local prefillOptions = std.set(
            std.flattenArrays(
                std.map(
                    function(item) item.ops, config.configuredPrefillOptions
                )
            )
        );

        local prefilledTargetValue =
            std.foldl(
                function(value, op)
                    local configs = std.filter(
                        function(item) std.member(item.ops, op)
                        , config.configuredPrefillOptions
                    );

                    local trigger = std.foldl(
                        function(result, item) result || item.trigger
                        , configs
                        , false
                    );

                    local condition = std.foldl(
                        function(result, item) result || item.condition
                        , configs
                        , false
                    );

                    if trigger then
                        if condition then
                            std.set(value + [op])
                        else
                            self.purge(value, [op])
                    else
                        value
                , prefillOptions
                , targetValue
            );

        // Always clear data of disabled and uncheck options
        local clearedTargetValue = self.purge(prefilledTargetValue, uncheckOps + disabledOps);

        local disabledOptions = configuredDisabledOptions + disableAndPrefillOps + disableUncheckedOps;

        {
            value: clearedTargetValue,
            disabledOptions: disabledOptions,
        }
    ,

    radio: function(configs)

        local targetValue = configs.targetValue;
        local disabledOptions = self.toOptions(configs.configuredDisabledOptions);

        // Always clear the data of a disabled field which has its disabled situation depending on a condition
        local value = if std.member(disabledOptions, targetValue) then "" else targetValue;

        { value: value, disabledOptions: disabledOptions }
    ,

    fileGroup: function(target, fileArray)

        local files = std.map(function(m) if m.condition then [m.file] else [], fileArray);

        self.value(target, std.flattenArrays(files))
    ,

    arrayContainsAny: function(arr, elements)
        if std.length(arr) == 0 then
            false
        else
            std.length(std.filter(function(item) std.member(elements, item), arr)) > 0,

    isDirtyField: function(field)
        field.touched || field.imported || field.autoFilled,
}
