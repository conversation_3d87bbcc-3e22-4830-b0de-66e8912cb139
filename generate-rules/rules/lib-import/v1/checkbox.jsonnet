function(config)

    local target = config.target;
    local targetValue = config.value;
    local disabledAndClearOps = config.disabledAndClearOps;
    local disableAndPrefillOps = config.disableAndPrefillOps;
    local uncheckPairs = config.uncheckPairs;

    local _ = import "gr";

    local endItem = _.last(targetValue);

    local uncheck = function(pair)
        local arr1 = pair[0];
        local arr2 = pair[1];

        if std.member(arr1, endItem) then
            arr2
        else if std.member(arr2, endItem) then
            arr1
        else
            [];

    // Make sure no conflict between clearing options and the prefilling ones
    local disabledOps = _.purge(disabledAndClearOps, disableAndPrefillOps);

    local uncheckOps = std.flattenArrays(std.map(uncheck, uncheckPairs));

    // Always clear data of disabled and uncheck options
    local value = _.purge(targetValue, uncheckOps + disabledOps);

    local e1 =
        // This condition is to avoid rule looping
        if _.intersect(uncheckOps + disabledOps, targetValue) != [] then
            atd.add(target, "value", value)
        else
            [];

    local e2 = atd.add(target, "disabledOptions", disabledAndClearOps + disableAndPrefillOps);

    e1 + e2
