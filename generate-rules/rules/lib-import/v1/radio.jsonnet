function(configs)

    local target = configs.target;
    local targetValue = configs.targetValue;
    local disabledOps = configs.disabledOps;

    // Always clear the data of a disabled field which has its disabled situation depending on a condition
    local value = if std.member(disabledOps, targetValue) then "" else targetValue;
    local e1 = atd.add(target, "value", value);
    local e2 = atd.add(target, "disabledOptions", disabledOps);

    e1 + e2
