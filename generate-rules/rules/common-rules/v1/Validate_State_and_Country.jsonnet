function(state_alias, country_alias)

local validState = std.isString(state_alias.value) && state_alias.value != "";
local validCountry = std.isString(country_alias.value) && country_alias.value != "";

atd.add("country_alias", "disable", validState)
+ atd.add("state_alias", "disable", country_alias.value != "United States" && validCountry)
+ (if validState then atd.add("country_alias", "value", "United States") else [])