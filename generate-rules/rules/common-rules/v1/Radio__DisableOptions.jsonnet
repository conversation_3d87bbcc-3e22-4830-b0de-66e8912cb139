function(alias)

local purge = function(arr, elements)
    std.filter(function(ele) !std.member(elements, ele), arr);

local disabledOps = [];

// Always clear the data of a disabled field which has its disabled situation depending on a condition
local value = if std.member(disabledOps, alias.value) then "" else alias.value;
local e1 = atd.add("alias", "value", value);
local e2 = atd.add("alias", "disabledOptions", disabledOps);

e1 + e2