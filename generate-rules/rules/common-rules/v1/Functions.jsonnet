function()

// Check if `arr` ends with `ele`
local endsWith = function(arr, ele)
    if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele;

// Remove all element in `arr` that's in `elements`
local purge = function(arr, elements)
    std.filter(function(ele) !std.member(elements, ele), arr);

// Remove all element in `arr` that's not in `elements`
local retain = function(arr, elements)
    std.filter(function(ele) std.member(elements, ele), arr);

// Return intersection of arrays 1 and 2
local intersect = function(arr1, arr2)
    std.filter(function(ele) std.member(arr1, ele), arr2);

// Check if these two arrays is has the same set of elements
// Theses arrays can have repeated elemenents
local diff = function(a, b)
    std.length(purge(a, b)) == 0 && std.length(purge(b, a)) == 0;

[]