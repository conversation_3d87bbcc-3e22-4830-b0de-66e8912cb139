function(alias)

local _ = import 'gr';

local all = [];
local endItem = _.last(all);

// The disabled options described here will be cleared out of checkbox value
local disabledAndClearOps = _.toDisableOps(
    [ { condition: true, ops: [] }
    ]
);

// The disabled options described here will NOT be cleared
// The rule for pre-filling value have to be written separately in another rule to avoid rule looping
local disableAndPrefillOps = [];

// If elements in first array are checked, it will uncheck elements in the second one and vice versa
local uncheckPairs =
    [ 
    // This line is to uncheck op1 & op2 when op3 or op4 is checked and vice versa
    // [ ["op1", "op2"], ["op3", "op4"] ]
    
    // Last item is usually be NOT option 
    // This line is to uncheck NOT option when others are checked and vice versa
    // , [ [ endItem ], _.purge(all, [ endItem ]) ]
    ];

_.checkbox({
    target: 'alias',
    value: alias.value,
    disabledAndClearOps: disabledAndClearOps,
    disableAndPrefillOps: disableAndPrefillOps,
    uncheckPairs: uncheckPairs,
})