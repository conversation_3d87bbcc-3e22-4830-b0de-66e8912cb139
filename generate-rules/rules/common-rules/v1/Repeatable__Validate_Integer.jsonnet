function(repeatable_alias)

local f = function(index, element)
    local val = element.alias.value;
    local valid = std.isNumber(val);

    local message = 
        if valid && val == 0 then 
            "Must be greater than 0." 
        else 
            null;

    atd.add("repeatable_alias", ["value", index, "alias", "error"], message);

local e1 = std.flattenArrays(std.mapWithIndex(f, repeatable_alias.value));

e1
