function(integer, dropdown)

local MONTH_DAY = {
    "January": 31,
    "February": 28,
    "March": 31,
    "April": 30,
    "May": 31,
    "June": 30,
    "July": 31,
    "August": 31,
    "September": 30,
    "October": 31,
    "November": 30,
    "December": 31,
};
local MONTH_INDEX = {
    "January": 1,
    "February": 2,
    "March": 3,
    "April": 4,
    "May": 5,
    "June": 6,
    "July": 7,
    "August": 8,
    "September": 9,
    "October": 10,
    "November": 11,
    "December": 12,
};
local MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

local curMonth = dropdown.value;
local idx = MONTH_INDEX[curMonth] - 1;
local month = 
    if idx - 1 < 0 then
        "December"
    else
        MONTHS[idx - 1]
    ;


local endDay = 
    if integer.value - 1 <= 0 then
        MONTH_DAY[month]
    else 
        integer.value - 1
    ;
local endMonth = 
    if integer.value - 1 == 0 then
        month
    else
        dropdown.value
    ;

local prefill = true;

local e1 = if prefill then
    []
else
    atd.add("dropdown", "value", endMonth)
        + atd.add("integer", "value", endDay);

e1