function(alias)

local _ = import 'gr';

local MAPPING = {};

local all = [];

local map = function(op) MAPPING[op];

local endItem = _.last(alias.value);

local disableOps = function(ops, condition)
    if condition then ops else [];

local disabledAndClearOps = 
    disableOps([], true)
;

local disableAndPrefillOps = [];

// If elements in first array are checked, it will uncheck elements in the second one and vice versa
local uncheckPairs =
    [ [ [ endItem ], _.purge(all, [ endItem ]) ]
    ];

_.checkbox({
    target: 'alias',
    value: alias.value
    disabledAndClearOps: disabledAndClearOps,
    disableAndPrefillOps: disableAndPrefillOps,
    uncheckPairs: uncheckPairs,
})