function(repeatable_alias)

local f = function(index, element)
    local val = element.alias.value;

    local message = 
        if val != "" && atd.compareToday(val) > 0 then
            "Must not be later than today."
        else
            null;

    atd.add("repeatable_alias", ["value", index, "alias", "error"], message);

local e1 = std.flattenArrays(std.mapWithIndex(f, repeatable_alias.value));

e1
