function(alias)

// list of all options

local purge = function(arr, elements)
    std.filter(function(ele) !std.member(elements, ele), arr);

local endsWith = function(arr, ele)
    if std.length(arr) == 0 then false else arr[std.length(arr) - 1] == ele;

local disabledOps = [];

local uncheckOps = [];

// Always clear data of disabled options
local value = purge(alias.value, disabledOps + uncheckOps);

local e1 = atd.add("alias", "value", value);
local e2 = atd.add("alias", "disabledOptions", disabledOps);

e1 + e2