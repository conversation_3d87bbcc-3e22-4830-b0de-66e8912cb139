function(alias)

local MAPPING = {};

local all = [];

local purge = function(arr, elements)
    std.filter(function(ele) !std.member(elements, ele), arr);

// List disabled option ids based on conditions
// ids here is not mapped yet
local ops = [];

local map = function(op) MAPPING[op];
local disabledOps = std.map(map, ops);

// Always clear the data of a disabled field which has its disabled situation depending on a condition
local value = if std.member(disabledOps, alias.value) then "" else alias.value;
local e1 = atd.add("alias", "value", value);
local e2 = atd.add("alias", "disabledOptions", disabledOps);

e1 + e2