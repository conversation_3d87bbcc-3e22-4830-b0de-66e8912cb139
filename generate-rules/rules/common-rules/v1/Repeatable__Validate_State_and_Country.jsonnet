function(repeatable_alias)

local f = function(index, element)
    local _state = element.state_alias.value;
    local _country = element.country_alias.value;
    local validState = std.isString(_state) && _state != "";
    local validCountry = std.isString(_country) && _country != "";

    atd.add("repeatable_alias", ["value", index, "state_alias", "disable"], _country != "United States" && validCountry)
    + atd.add("repeatable_alias", ["value", index, "country_alias", "disable"], validState)
	+ (if validState then atd.add("repeatable_alias", ["value", index, "country_alias", "value"], "United States") else [])
;

local e1 = std.flattenArrays(std.mapWithIndex(f, repeatable_alias.value));

e1