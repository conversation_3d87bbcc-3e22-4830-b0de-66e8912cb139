function(alias)

local parts = std.split(alias.value, "-");

local year = 
    if std.length(parts) != 3 then
        ""
    else
        parts[0]
    ;

local safeInt = function(s)
    local numStr = std.join("", std.filter(function(c) std.member("0123456789", c), std.stringChars(s)));
    if numStr == "" then 0 else std.parseInt(numStr);

local enough = std.length(std.toString(std.parseInt(year))) == 4;

if alias == "" then
    []
else if atd.compareToday(alias.value) > 0 then 
    atd.add("alias", "error", "Must not be later than today.")
else if !enough then
    atd.add("alias", "error", "Must be in valid format")
else
    atd.add("alias", "error", null)