function(repeatable_alias)

local _ = import "gr";

local logic = afs.logic.main;
local logicRep = logic.repeatable_alias.children;

local f = function(index, element)
    local isUS = element.country.value == "United States";
    local isCanada = element.country.value == "Canada";

    local parts = [
        element.numberandstreet.value,
        element.city.value,
        if isUS then
            element.state_us.value
        else if isCanada then
            element.state_canada.value
        else
            element.state_other.value,
        if isUS then
            element.zipcode_us.value
        else if isCanada then
            element.zipcode_canada.value
        else
            element.zipcode_other.value,
        element.country.value,
    ];

    local combine = _.join(", ", parts);

    _.valueRep(logicRep.output, index, combine);

local e1 = std.flattenArrays(std.mapWithIndex(f, repeatable_alias.value));

e1
