function(
    numberandstreet
    , city
    , state_us
    , state_canada
    , state_other
    , zipcode_us
    , zipcode_canada
    , zipcode_other
    , country
)    

local parts = [
    numberandstreet.value
    , city.value
    , state_us.value
    , state_canada.value
    , state_other.value
    , zipcode_us.value
    , zipcode_canada.value
    , zipcode_other.value
    , country.value
];

local combine = _.join(", ", parts);

atd.add("alias", "value", combine)