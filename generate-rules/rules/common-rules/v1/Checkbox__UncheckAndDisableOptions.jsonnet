function(alias)

local all = [];

local purge = function(arr, elements)
    std.filter(function(ele) !std.member(elements, ele), arr);

local disabledOps = [];

// UNCHECK OPTION LOGIC START FROM HERE

local intersect = function(arr1, arr2)
    std.filter(function(ele) std.member(arr1, ele), arr2);

local opNum = std.length(alias.value);
local endItem = if opNum == 0 then "" else alias.value[opNum - 1];

// If elements in first array are checked, it will uncheck elements in the second one and vice versa
local uncheckPairs =
    [ [["not"], purge(alias.value, ["not"])]
    ];

local uncheck = function(pair)
    local arr1 = pair[0];
    local arr2 = pair[1];

    if std.member(arr1, endItem) then
        arr2
    else if std.member(arr2, endItem) then
        arr1
    else
        [];

local uncheckOps = std.flattenArrays(std.map(uncheck, uncheckPairs));

// Always clear data of disabled and uncheck options
local value = purge(alias.value, uncheckOps + disabledOps);

local e1 = 
    // This condition is to avoid rule looping
    if intersect(uncheckOps + disabledOps, alias.value) != [] then 
        atd.add("alias", "value", value)
    else
        [];

local e2 = atd.add("alias", "disabledOptions", disabledOps);

e1 + e2