{"Combine_Name": "Combine name elements based on this order: First name + Middle name + Last name + Suffix and prefill to hidden fields", "Combine_Address": "Combine address elements based on this order: Number and Street + City + State + Country + Zip Code.", "Radio__DisableOptions": "[Radio] Enable/Disable options", "Radio__Uncheck": "[Radio][Uncheck] If a radio is checked, the other's value is reset", "Radio__Uncheck__ErrorMessage": "[Radio][Uncheck][Error Message] Show error message 'This field is required' in none of the fields is checked", "Show_if_Select__US": "Show/Hide based on if selecting US or Non-US", "Checkbox_Mapping__UncheckAndDisableOptions": "[Checkbox][Mapping] Enable/Disable options and uncheck options if other options are checked, and vice versa.", "Functions": "Useful Functions", "Show_if_Check__Radio": "[Radio] Show/Hide with radio of yes/no", "Show_if_Check__Single_Checkbox": "[Checkbox] Show if Single checkbox is checked", "Validate_Date": "Must not be later than today.", "Validate_Day": "Day must be valid.", "Validate_Integer": "Must be greater than 0", "Validate_Money": "Must be greater than $0", "Validate_Percent": "Must be greater than 0%", "Validate_State_and_Country": "Validate State and Country", "Repeatable__Validate_Share_Profits": "[Repeatable] The total share of profits must not exceed 100% or repeatable.", "Repeatable__Validate_Date": "[Repeatable] Must not be later than today.", "Repeatable__Validate_Integer": "[Repeatable] Must be greater than 0", "Repeatable__Validate_State_and_Country": "[Repeatable] Validate State and Country", "Repeatable__Show_if_Check__Radio": "[Repeatable][Radio] Show/Hide with radio of yes/no", "Repeatable__Show_if_Check__Single_Checkbox": "[Repeatable][Checkbox] Show if Single checkbox is checked", "Prefill_Tax_Year_End": "Prefill Tax Year End based on start month"}