function(<%= repeatable_alias %>)

local f = function(index, element)
    local isUS = element.<%= country_alias %>.value == "United States";

    // atd.add("<%= repeatable_alias %>", ["value", index, "us_group", "hide"], !isUS)
    // + atd.add("<%= repeatable_alias %>", ["value", index, "non_us_group", "hide"], isUS)
    []
    ;

local e1 = std.flattenArrays(std.mapWithIndex(f, <%= repeatable_alias %>.value));

e1