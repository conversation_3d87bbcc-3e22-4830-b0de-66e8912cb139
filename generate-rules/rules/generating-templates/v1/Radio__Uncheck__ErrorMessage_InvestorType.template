function(<%= individual_alias %>, <%= entity_alias %>)

local isIndividual = <%= individual_alias %>.value != "";
local isEntity = <%= entity_alias %>.value != "";

local message = if !isIndividual && !isEntity then "This field is required" else null;

local v = if isIndividual then "indi" else if isEntity then "entity" else "";

atd.add("<%= individual_alias %>", "error", message)
+ atd.add("shortcut", "value", v)
