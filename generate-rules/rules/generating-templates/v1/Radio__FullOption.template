function(<%= alias %>)

<% options.forEach((option, index) => { -%>
local op<%- index + 1 %> = <%= alias %>.value == "<%- option.id %>";
<% }) -%>

<% if (disable_output) { 
    const condition = `op${disable_output.option_index + 1}`
%>
atd.add("<%= disable_output.alias %>", "disable", !<%= condition %>)
<% for (let i = 0; i < disable_output.disable_fields.length; i++) { -%>
+ ( if !<%= condition %> then atd.add("<%= disable_output.disable_fields[i].alias %>", "value", "") else [] )
<% } -%>
<%
}
%>
//atd.add("alias_op1", "hide", !op1)
//+ atd.add("alias_op2", "hide", !op2)
[]

<% 
    options.forEach(option => {
        if (option.description) { %>
// <%- option.id %> => <%- option.description %>
<%
        }
    })
%>

