function(<%= tin_alias %>)

local isIndividual = shortcut.value == "indi";
local isEntity = shortcut.value == "entity";

local isTrust = is_trust.value == "yes";
local isIra = is_ira.value == "yes";
local isLlc = is_llc.value == "yes";

local disabledOps = 
    if isIra || isLlc || isTrust then
        []
    if isIndividual then
        ["<%= ein_option %>"]
    else if isEntity then
        ["<%= ssn_option %>"]
    else 
        []
    ;

local value = if std.member(disabledOps, <%= tin_alias %>.value) then "" else <%= tin_alias %>.value;

local e1 = atd.add("<%= tin_alias %>", "value", value);
local e2 = atd.add("<%= tin_alias %>", "disabledOptions", disabledOps);

e1 + e2