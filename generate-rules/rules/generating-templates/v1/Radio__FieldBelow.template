function(<%= alias %>)

<% options.forEach((option) => { -%>
local op<%- option.index + 1 %> = <%= alias %>.value == "<%- option.id %>";
<% }) -%>

<% for (let i = 0; i < outputs.length; i++) { 
    const hide = outputs[i].disable_fields.length == 0
    const condition = `op${outputs[i].option_index + 1}`
    const plus = i == 0 ? "" : "+ "
%>
<% if (hide) { -%>
<%= plus %>atd.add("<%= outputs[i].alias %>", "hide", !<%= condition %>)
<% } else { -%>
<%= plus %>atd.add("<%= outputs[i].alias %>", "disable", !<%= condition %>)
<% for (let j = 0; j < outputs[i].disable_fields.length; j++) { -%>
+ ( if !<%= condition %> then atd.add("<%= outputs[i].disable_fields[j].alias %>", "value", "") else [] )
<% } -%>
<% } -%>
<% } %>