# Guide

This project is to support applying rules faster for forms. 
Make sure you are on `generate-rules` folder while following below instruction.

### Table of contents
- [Requirement](#requirement)
- [Before starting](#before-starting)
- [Folder structure](#folder-structure)
- [Generate rules](#generate-rules)

### Requirement
- NodeJs

### Before starting
- Install NodeJs package by running
    ```
    npm install
    ```

### Folder structure
- Extracted files from given JSON file:
    - Path: `./data/[FORM_NAME]/extract`
    - Elements:
        1. components.json: form structure
        1. components_alias.json: form structure BUT with alias only
        2. ui.json: form components' UI
        3. rules_.lua: all rules in one file
        4. rules.json: extracted rules in JSON format           
- Analyzed data from given JSON file:
    - Path: `./data/[FORM_NAME]/analyze`
    - Elements:
        1. step0.json: output of executing script step0.js         
        1. step1.json: output of executing script step1.js         
        1. Folder `rules` : output of executing script step3.js         
        1. step9.json: output of executing script step9.js         

### Generate rules 

This feature supports logic V1 only.

1. Create a folder with name `data` or anything up to you

2. Go to a form builder page on `pantheon` dashboard, click on `Export form` 
    - Click on download files for importing this form back after generating rules
        - A zip file will be downloaded 
        - Extract it to a folder which has all PDF files there
    - Click to the Text Area to have JSON data copied to clipboard

3. Paste this json to a file in folder `data` or anywhere that convenience for you
    - Example
        ```
        pbpaste > ./data/[FORM_NAME]/form.json
        ```
    - Remember this path for next processing steps

4. Run following command to generate rules
    ```
    sh scripts/extract.sh [FORM_NAME] ./data/[FORM_NAME]/form.json
    sh scripts/generate-rules.sh [FORM_NAME] ./data/[FORM_NAME]/form.json
    
5. All generated rules will locate here `./data/[FORM_NAME]/analyze/rules`
